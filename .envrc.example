# Rename this file `.envrc` and use it during development.
# Secrets should be kept in a vault or passed through environments
# and never stored in Git.

# If you want use these, remember to start VSCode or your IDE after `direnv allow`

# use `sandbox` (localhost), `devl`, `stage`, `prod`
export SPRING_PROFILES_ACTIVE=sandbox
export sandbox_DRH_UDI_DS_PRIME_JDBC_URL=jdbc:sqlite:${HOME}/db/drh/FileName.sqlite.db

#Log file path
export ORG_DRH_HUB_PRIME_LOG_FILE=${HOME}/log/drh/hub-prime/hub-prime-application

#Primary DB Base path
export sandbox_DRH_UDI_DS_PRIME_DB_BASE_PATH=${HOME}/db/drh/

#GitHub Authentication Credential
export SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GITHUB_CLIENT_ID= client_id from git
export SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GITHUB_CLIENT_SECRET= client_secret from git

#Orcid Authentication Credential
export SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ORCID_CLIENT_ID=client_id from orcid
export SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ORCID_CLIENT_SECRET=client_secret from orcid
export SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ORCID_REDIRECT_URI={baseUrl}/login/oauth2/code/orcid

#Audit DB
export sandbox_DRH_UDI_DS_AUDIT_BASE_URL=${HOME}/db/drh/audit/audit.sqlite.db

export ORG_DRH_SERVICE_HTTP_GITHUB_AUTHZ_USERS_YAML_URL=url_to_sandbox/oauth2-privilege-authz.yml
export ORG_DRH_SERVICE_HTTP_GITHUB_API_AUTHN_TOKEN=Auth_token

export ORG_DRH_SERVICE_HTTP_AI_BASE_URL=http://10.10.11.230:8000/

#ClientServer DB   
export sandbox_DRH_UDI_DS_CLIENT_SERVER_BASE_URL=${HOME}/db/drh/ClientServer/FileName.sqlite.db

export ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_END_POINT=******
export ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_AUTHN_TOKEN=*****
export ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_STREAM_NAME=sanbox_drh
export ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION=sandbox

export ORG_DRH_SERVICE_HTTP_CREDENTIALS_ACCESS_KEY=******
export ORG_DRH_SERVICE_HTTP_CREDENTIALS_SECRET_KEY=******
export ORG_DRH_SERVICE_HTTP_AWS_S3_BUCKET=bucket_name

export ORG_DRH_SERVICE_HTTP_TEMPORARY_FILE_PATH=${HOME}/drh/tmp/

// For the time being we are using the default env name of postgres , because DuckDB demands the same env for PostgeeSQL DB attach
// It has to be changed to DRH Specific env once we fond a solution
export PGPASSWORD="*****"
export PGHOST=*****
export PGUSER=***
export PGDATABASE=***
export PGPORT=****


export ORG_DRH_SERVICE_SUPER_ADMIN_PASSWORD='encoded_password'
export ORG_DRH_SERVICE_SUPER_ADMIN_ORGANIZATION_PARTY_ID='organization_party_id'
export ORG_DRH_SERVICE_ALLOWED_ORIGINS=http://localhost:8081

export ORG_DRH_SERVICE_EMAIL_HOST=smtp.****.org
export ORG_DRH_SERVICE_EMAIL_USERNAME=****@***.***.***
export ORG_DRH_SERVICE_EMAIL_APP_PASSWORD=****-****-***
export ORG_DRH_SERVICE_EMAIL_FROM=**-**@***.***.***
export ORG_DRH_SERVICE_EMAIL_PORT=***

export ORG_DRH_SERVICE_OTP_VERIFY_URL=http://localhost:8080/verifyOtp