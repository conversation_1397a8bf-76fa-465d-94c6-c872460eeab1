package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

import org.diabetestechnology.drh.service.http.pg.request.OrganizationRequest;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fasterxml.jackson.core.JsonProcessingException;

public class OrganizationServiceTest {
    @Mock
    private DSLContext dsl;

    @Mock
    private ResultQuery<Record1<String>> queryMock;

    @InjectMocks
    private OrganizationService organizationService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this); // Initialize mocks

        // Initialize common mocks
        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveOrganization() throws Exception {
        Map<String, String> identifierSystemValue = new HashMap<>();
        identifierSystemValue.put("system", "test");

        OrganizationRequest request = new OrganizationRequest("Test Org", identifierSystemValue, "Alias", "123",
                "Test Type", "City", "State", "Country", "http://example.com", "admin", "10.0", "20.0");
        JSONB mockJsonb = JSONB.valueOf("{\"message\":\"Organization created successfully\",\"status\":\"success\"}");

        SelectSelectStep<Record1<JSONB>> queryMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(queryMock);
        when(queryMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = organizationService.saveOrganization(request);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveOrganization_AlreadyExists() throws Exception {
        Map<String, String> identifierSystemValue = new HashMap<>();
        identifierSystemValue.put("system", "test");

        OrganizationRequest request = new OrganizationRequest("Test Org", identifierSystemValue, "Alias", "123",
                "Test Type", "City", "State", "Country", "http://example.com", "admin", "10.0", "20.0");
        JSONB mockJsonb = JSONB.valueOf("{\"message\":\"Organization already exists\",\"status\":\"error\"}");
        String mockPartyId = "12345";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);
        when(dsl.select(DSL.field("organization_party_id"))
                .from("drh_stateless_research_study.organization_party_view")
                .where(DSL.field("organization_name").eq(request.name()))
                .fetchOneInto(String.class))
                .thenReturn(mockPartyId);

        Object result = organizationService.saveOrganization(request);

        assertNotNull(result);
        JSONObject jsonResult = new JSONObject(result.toString());
        assertEquals("Organization already exists", jsonResult.getString("message"));
        assertEquals(mockPartyId, jsonResult.getString("organizationPartyId"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveOrganization_ExceptionHandling() throws JsonProcessingException {
        Map<String, String> identifierSystemValue = new HashMap<>();
        identifierSystemValue.put("system", "test");

        OrganizationRequest request = new OrganizationRequest("Test Org", identifierSystemValue, "Alias", "123",
                "Test Type", "City", "State", "Country", "http://example.com", "admin", "10.0", "20.0");

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        Object result = organizationService.saveOrganization(request);

        assertNotNull(result);
        assertEquals("{}", result.toString()); // Expecting empty JSON response
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSearchOrganization() throws Exception {
        String request = "Test Org";
        JSONB mockJsonb = JSONB.valueOf("[{\"organization_id\":1,\"organization_name\":\"Test Org\"}]");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = organizationService.searchOrganization(request);

        assertNotNull(result);
        assertTrue(result.data().contains("Test Org"));
        verify(dsl, times(1)).select(any(Field.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSearchOrganization_NoResult() {
        String request = "Nonexistent Org";
        String expectedJson = "{}";
        JSONB expectedResult = JSONB.valueOf(expectedJson);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB result = organizationService.searchOrganization(request);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @Test
    void testSearchOrganization_ExceptionHandling() {
        String request = "Error Org";
        JSONB invalidJsonb = JSONB.valueOf("invalid json data");

        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(invalidJsonb);

        JSONB result = organizationService.searchOrganization(request);
        assertNotNull(result);
        assertEquals(invalidJsonb, result);
    }

}
