package org.diabetestechnology.drh.service.http.hub.prime.service;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserNameServiceTest {
    @Mock
    private UserNameService userNameService;

    @BeforeEach
    void setUp() {
        userNameService = new UserNameService(null, null, null);
    }

    @Test
    void getUserName_DefaultOAuth2User() {
        Map<String, Object> attributes = Collections.singletonMap("name", "<PERSON>");
        var defaultOAuth2User = new DefaultOAuth2User(null, attributes, "name");

        mockSecurityContext(defaultOAuth2User);

        String userName = userNameService.getUserName();
        assertEquals("John Doe", userName);
    }

    @Test
    void getUserName_StringPrincipal() {
        mockSecurityContext("admin");

        String userName = userNameService.getUserName();
        assertEquals("admin", userName);
    }

    @Test
    void getUserName_Anonymous() {
        mockSecurityContext("anonymousUser");

        String userName = userNameService.getUserName();
        assertEquals("Anonymous", userName);
    }

    @Test
    void getUserId_FromHttpRequest() {
        Map<String, Object> attributes = Collections.singletonMap("login", "John_Doe");
        var defaultOAuth2User = new DefaultOAuth2User(null, attributes, "login");
        mockSecurityContext(defaultOAuth2User);
        HttpServletRequest request = mock(HttpServletRequest.class);
        String userId = userNameService.getUserId();
        assertEquals("John_Doe", userId);
    }

    @Test
    void getUserId_Anonymous() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getUserPrincipal()).thenReturn(null);

        String userId = userNameService.getUserId();
        assertEquals("Anonymous", userId);
    }

    @Test
    void getUserEmail_DefaultOAuth2User() {
        Map<String, Object> attributes = Collections.singletonMap("email", "<EMAIL>");
        var defaultOAuth2User = new DefaultOAuth2User(null, attributes, "email");

        mockSecurityContext(defaultOAuth2User);

        String userEmail = userNameService.getUserEmail();
        assertEquals("<EMAIL>", userEmail);
    }

    @Test
    void getUserEmail_EmptyFallback() {
        mockSecurityContext("admin");

        String userEmail = userNameService.getUserEmail();
        assertEquals("", userEmail);
    }

    @Test
    void getUserInstitution_DefaultOAuth2User() {
        Map<String, Object> attributes = Collections.singletonMap("institution", "Test Institution");
        var defaultOAuth2User = new DefaultOAuth2User(null, attributes, "institution");

        mockSecurityContext(defaultOAuth2User);

        String userInstitution = userNameService.getUserInstitution();
        assertEquals("Test Institution", userInstitution);
    }

    @Test
    void getUserInstitution_EmptyFallback() {
        mockSecurityContext("admin");

        String userInstitution = userNameService.getUserInstitution();
        assertEquals("", userInstitution);
    }

    @Test
    void getUserProvider_DefaultOAuth2User() {
        Map<String, Object> attributes = Collections.singletonMap("provider", "Google");
        var defaultOAuth2User = new DefaultOAuth2User(null, attributes, "provider");

        mockSecurityContext(defaultOAuth2User);

        String userProvider = userNameService.getUserProvider();
        assertEquals("Google", userProvider);
    }

    @Test
    void getUserProvider_EmptyFallback() {
        mockSecurityContext("admin");

        String userProvider = userNameService.getUserProvider();
        assertEquals("", userProvider);
    }

    private void mockSecurityContext(Object principal) {
        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(principal);

        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);

        SecurityContextHolder.setContext(securityContext);
    }
}
