package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.TableLike;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PopulationPercentageServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private AuditService auditService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private Result<Record1<Integer>> mockResult;

    @InjectMocks
    private PopulationPercentageService service;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");
        when(partyService.getOrganizationPartyIdByUser("user-123")).thenReturn("mockOrgId");
        SelectSelectStep<Record1<Integer>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Integer>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Integer>> conditionStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<Integer>> andConditionMock = mock(SelectConditionStep.class);

        // Define query behavior
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.and(any(org.jooq.Condition.class))).thenReturn(andConditionMock);
        when(andConditionMock.and(any(org.jooq.Condition.class))).thenReturn(andConditionMock);
        when(andConditionMock.fetchOneInto(Integer.class)).thenReturn(10);
    }

    @Test
    void testGetTotalParticipantsWithData() throws Exception {
        Integer result = service.getTotalParticipantsWithData().get();
        assertNotNull(result);
        assertEquals(10, result);
    }

    @Test
    void testGetTotalCgmFileCount() throws Exception {
        Integer result = service.getTotalCgmFileCount().get();
        assertNotNull(result);
        assertEquals(10, result);
    }

    @Test
    void testGetTotalDataPoints() throws Exception {
        Object result = service.getTotalDataPoints();
        assertNotNull(result);
        assertTrue(result instanceof String);
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_WhenExceptionOccurs() throws Exception {
        // Mock JOOQ query to throw an exception
        SelectSelectStep<Record1<Integer>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Integer>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Integer>> conditionStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<Integer>> andConditionMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.and(any(org.jooq.Condition.class))).thenReturn(andConditionMock);
        when(andConditionMock.and(any(org.jooq.Condition.class))).thenReturn(andConditionMock);

        when(andConditionMock.fetchOneInto(Integer.class)).thenThrow(new RuntimeException("Database error"));

        Integer result = service.getTotalCgmFileCount().get();
        assertNotNull(result);
        assertEquals(0, result);
    }

}
