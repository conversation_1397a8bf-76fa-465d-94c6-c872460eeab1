package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ParticipantMetricsServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private ResultQuery<Record1<String>> queryMock;

    @InjectMocks
    private ParticipantMetricsService participantMetricsService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Initialize common mocks
        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetParticipantDateTimeRange() {
        String studyId = "study-123";
        String participantId = "participant-456";
        JSONB mockJsonb = JSONB.valueOf("{\"start_date\":\"2023-01-01\", \"end_date\":\"2023-12-31\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = participantMetricsService.getParticipantDateTimeRange(studyId, participantId);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAmbulatoryGlucoseProfile() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"agp_data\":\"some_values\"}");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = participantMetricsService.getAmbulatoryGlucoseProfile(studyId, participantId, startDate,
                endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetAmbulatoryGlucoseProfile_DateParseError() throws Exception {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        JSONB rawResult = participantMetricsService.getAmbulatoryGlucoseProfile(
                studyId, participantId, invalidStartDate, endDate);

        assertNotNull(rawResult);
        String data = rawResult.data();
        String content = data.substring(1, data.length() - 1);
        String[] pairs = content.split(", ");

        Map<String, String> resultMap = new HashMap<>();
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }

        assertEquals("error", resultMap.get("status"));
        assertEquals("Error parsing date formats", resultMap.get("message"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetParticipantMetrics() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"agp_data\":\"some_values\"}");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = participantMetricsService.getParticipantMetrics(studyId, participantId, startDate,
                endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetParticipantMetrics_DateParseError() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        Object rawResult = participantMetricsService.getParticipantMetrics(studyId, participantId, invalidStartDate,
                endDate);

        assertNotNull(rawResult);
        JSONB result = (JSONB) rawResult;

        String data = result.data();
        String content = data.substring(1, data.length() - 1);
        String[] pairs = content.split(", ");

        Map<String, String> resultMap = new HashMap<>();
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        assertEquals("error", resultMap.get("status"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetTimeRangeStackedData_Success() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"stacked_data\":\"some_values\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = participantMetricsService.getTimeRangeStackedData(studyId, participantId, startDate, endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetTimeRangeStackedData_DateParseError() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        Object rawResult = participantMetricsService.getTimeRangeStackedData(studyId, participantId, invalidStartDate,
                endDate);

        assertNotNull(rawResult);
        JSONB result = (JSONB) rawResult;

        String data = result.data();
        String content = data.substring(1, data.length() - 1);
        String[] pairs = content.split(", ");

        Map<String, String> resultMap = new HashMap<>();
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }

        assertEquals("error", resultMap.get("status"));
        assertEquals("Error processing request", resultMap.get("message"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAdvancedMetrics_Success() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"advanced_metrics\":\"some_values\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = participantMetricsService.getAdvancedMetrics(studyId, participantId, startDate, endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetAdvancedMetrics_DateParseError() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        JSONB result = participantMetricsService.getAdvancedMetrics(studyId, participantId, invalidStartDate, endDate);

        assertNotNull(result);
        assertTrue(result.toString().contains("error"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetDailyGlucoseProfile_Success() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"daily_glucose\":\"some_values\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = participantMetricsService.getDailyGlucoseProfile(studyId, participantId, startDate, endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetDailyGlucoseProfile_DateParseError() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        JSONB result = participantMetricsService.getDailyGlucoseProfile(studyId, participantId, invalidStartDate,
                endDate);

        assertNotNull(result);
        assertTrue(result.toString().contains("error"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetGlycemicRiskIndicator_Success() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockJsonb = JSONB.valueOf("{\"glycemic_risk\":\"some_values\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = participantMetricsService.getGlycemicRiskIndicator(studyId, participantId, startDate, endDate);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetGlycemicRiskIndicator_DateParseError() {
        String studyId = "study-123";
        String participantId = "participant-456";
        String invalidStartDate = "invalid-date";
        String endDate = "2023-12-31";

        JSONB result = participantMetricsService.getGlycemicRiskIndicator(studyId, participantId, invalidStartDate,
                endDate);

        assertNotNull(result);
        assertTrue(result.toString().contains("error"));
    }

}
