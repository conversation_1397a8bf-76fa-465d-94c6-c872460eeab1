package org.diabetestechnology.drh.service.http.hub.prime.service.cohortFilter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.mock.web.MockHttpServletRequest;

import com.fasterxml.jackson.databind.ObjectMapper;

class CohortFilterServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private DataBaseAttachService databaseAttachService;

    @Mock
    UserNameService userNameService;

    @Mock
    AuditService auditService;

    @InjectMocks
    private CohortFilterService cohortFilterService;

    @Mock
    DataAccessService dataAccessService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSaveCohortFilter_Success() {
        // Arrange
        CohortFilterRequest request = new CohortFilterRequest(
                "testViewMode",
                "testUser",
                "testUser",
                "testFilter",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));

        CohortFilterService spyCohortFilterService = spy(cohortFilterService);

        // Mock isDuplicateFilter to return false to avoid extra calls to
        // refreshClientServerDb
        doReturn(false).when(spyCohortFilterService).isDuplicateFilter(anyString(), anyString());

        // Mock userNameService.getUserId to return a non-null value
        when(userNameService.getUserId()).thenReturn("testUser");
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenReturn(1);

        // Act
        JdbcResponse result = spyCohortFilterService.saveCohortFilter(request);

        // Assert
        assertEquals("Success", result.getMessage());
        verify(databaseAttachService).refreshClientServerDb();
        verify(jdbcTemplate).update(
                eq("INSERT INTO client.filter_interaction (filter_name, filter_description, view_mode, created_by, updated_by, created_at,updated_at,filter) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"),
                eq("testFilter"),
                eq("Test Description"),
                eq("testViewMode"),
                eq("testUser"),
                eq("testUser"),
                anyString(), // created_at
                anyString(), // updated_at
                eq(request.filterOption().toString()));
    }

    @Test
    void testSaveCohortFilter_DuplicateFilter() {
        // Arrange
        CohortFilterRequest request = new CohortFilterRequest(
                "testViewMode",
                "createdBy",
                "updatedBy",
                "duplicateFilterName",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));

        // Mocking dependencies
        when(userNameService.getUserId()).thenReturn("testUser");
        when(auditService.getCurrentRequest()).thenReturn(new MockHttpServletRequest());
        when(databaseAttachService.refreshClientServerDb()).thenReturn(true);
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenReturn(1);

        // Mocking isDuplicateFilter to return true for this test case
        when(cohortFilterService.isDuplicateFilter("duplicateFilterName", "testUser")).thenReturn(true);

        // Act & Assert
        JdbcResponse result = cohortFilterService.saveCohortFilter(request);
        assertEquals("Error", result.getMessage());
        assertEquals(
                "A filter with the name " + request.filterName()
                        + " already exists for the user testUser. Please choose a different name or update the existing filter.",
                result.getErrors());

    }

    @Test
    void testSaveCohortFilter_Exception() {
        // Arrange
        CohortFilterRequest request = new CohortFilterRequest(
                "testViewMode",
                "testUser",
                "testUser",
                "testFilter",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));

        CohortFilterService spyCohortFilterService = spy(cohortFilterService);

        // Mock isDuplicateFilter to return false to avoid extra calls to
        // refreshClientServerDb
        doReturn(false).when(spyCohortFilterService).isDuplicateFilter(anyString(), anyString());

        // Mock userNameService.getUserId to return a non-null value
        when(userNameService.getUserId()).thenReturn("testUser");
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenThrow(new RuntimeException("Database error"));

        // Act
        JdbcResponse result = spyCohortFilterService.saveCohortFilter(request);

        // Assert
        assertEquals("Error", result.getMessage());
        verify(databaseAttachService, times(3)).refreshClientServerDb(); // Retry logic only
        verify(jdbcTemplate, times(3)).update(anyString(), any(Object[].class)); // Verify that update was attempted
    }

    @Test
    void testConvertToJson_ValidInput() {
        // Arrange
        String input = "FilterOption[studyIds=[study1, study2], filters={filter1=FilterModel[property1=value1, property2=[list1, list2]]}]";

        // Act
        String resultJson = CohortFilterService.convertToJson(input);

        // Assert
        String expectedJson = """
                {
                  "studyIds": ["study1", "study2"],
                  "filters": {
                    "filter1": {
                      "property1": "value1",
                      "property2": ["list1", "list2"]
                    }
                  }
                }
                """;

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            assertEquals(
                    objectMapper.readTree(expectedJson),
                    objectMapper.readTree(resultJson),
                    "JSON output doesn't match expected format.");
        } catch (Exception e) {
            fail("Exception during JSON comparison: " + e.getMessage());
        }
    }

    // @Test
    // void testConvertToJson_InvalidInput() {
    // // Arrange
    // String invalidInput = "InvalidFilterOption[studyIds=[study1]]";

    // // Act & Assert
    // assertThrows(RuntimeException.class, () ->
    // CohortFilterService.convertToJson(invalidInput));
    // }

    @Test
    void testUpdateCohortFilter_Exception() {
        // Arrange
        CohortFilterRequest request = new CohortFilterRequest(
                "testViewMode",
                "testUser",
                "testUser",
                "testFilter",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));

        CohortFilterService spyCohortFilterService = spy(cohortFilterService);

        doReturn(false).when(spyCohortFilterService).isDuplicateFilter(anyString(), anyString());

        when(userNameService.getUserId()).thenReturn("testUser");
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenThrow(new RuntimeException("Database error"));

        // Act
        JdbcResponse result = spyCohortFilterService.saveCohortFilter(request);

        // Assert
        assertEquals("Error", result.getMessage());
        verify(databaseAttachService, times(3)).refreshClientServerDb(); // Retry logic only
        verify(jdbcTemplate, times(3)).update(anyString(), any(Object[].class)); // Verify that update was attempted
    }

    @Test
    void testUpdateCohortFilter_SuccessfulUpdate() {
        // Arrange
        CohortFilterRequest request = new CohortFilterRequest(
                "testViewMode",
                "createdBy",
                "createdBy",
                "testFilter",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));
        int filterId = 1;

        CohortFilterService spyService = spy(cohortFilterService);

        doReturn(true).when(spyService).filterExists(filterId);

        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq(filterId))).thenReturn(1);

        when(jdbcTemplate.queryForMap(anyString(), eq(filterId)))
                .thenReturn(Map.of("created_by", "createdBy", "updated_by", "createdBy"));
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenReturn(1);
        when(userNameService.getUserId()).thenReturn("createdBy");
        JdbcResponse result = spyService.updateCohortFilter(request, filterId);

        assertEquals("Success", result.getMessage());

        // verify(databaseAttachService).refreshClientServerDb();
        verify(databaseAttachService, times(2)).refreshClientServerDb();

    }

    @Test
    void testUpdateCohortFilter_InsertNewRow() {

        CohortFilterRequest request = new CohortFilterRequest(
                "private",
                "user1",
                "user1",
                "testFilter",
                "Test Description",
                new CohortFilterRequest.FilterOption(List.of("study1", "study2"), null));
        int filterId = 1;

        when(cohortFilterService.filterExists(eq(filterId))).thenReturn(true);

        when(jdbcTemplate.queryForMap(anyString(), anyInt()))
                .thenReturn(Map.of("created_by", "user1"));

        CohortFilterService spyCohortFilterService = spy(cohortFilterService);
        doReturn(true).when(spyCohortFilterService)
                .filterExists(filterId);
        when(userNameService.getUserId()).thenReturn("anotherUser");
        JdbcResponse expectedResponse = JdbcResponse.builder()
                .status("success")
                .message("SaveCohortFilter successfully fetched")
                .build();
        doReturn(expectedResponse).when(spyCohortFilterService)
                .saveCohortFilter(any(CohortFilterRequest.class));

        JdbcResponse result = spyCohortFilterService.updateCohortFilter(request, filterId);

        assertEquals("Success", result.getMessage());
        verify(databaseAttachService).refreshClientServerDb();
    }

    @Test
    void testGetCohortFilter_Success() {
        int filterId = 1;
        String createdBy = "testUser";
        String updatedBy = "testUser";
        String filterName = "testFilter";
        String filterDescription = "Test Description";
        String filterOption = "FilterOption[studyIds=[study1, study2],filters={filter1=FilterModel[property1=value1, property2=[list1, list2]]}]";
        String viewMode = "public";

        when(jdbcTemplate.queryForMap(anyString(), eq(filterId)))
                .thenReturn(Map.of(
                        "created_by", createdBy,
                        "updated_by", updatedBy,
                        "filter_name", filterName,
                        "filter_description", filterDescription,
                        "filter", filterOption,
                        "view_mode", viewMode));

        JdbcResponse response = cohortFilterService.getCohortFilter();
        assertEquals("success", response.getMessage());
        assertNotNull(response.getData().get("cohortFilters"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCohortFilter_InterruptedDuringRetry() {
        String userId = "testUser";
        when(userNameService.getUserId()).thenReturn(userId);

        when(jdbcTemplate.query(anyString(), any(PreparedStatementSetter.class), any(RowMapper.class)))
                .thenThrow(new RuntimeException("Database error"));

        doAnswer(invocation -> {
            Thread.currentThread().interrupt();
            return null;
        }).when(databaseAttachService).refreshClientServerDb();

        assertThrows(RuntimeException.class, () -> cohortFilterService.getCohortFilter());
        verify(databaseAttachService).refreshClientServerDb();
    }

}
