package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.SelectConditionStep;
import org.jooq.SelectField;
import org.jooq.SelectJoinStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.SelectOnStep;
import org.jooq.SelectSelectStep;
import org.jooq.TableLike;
import org.jooq.Result;
import org.jooq.Table;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;

class MealsAndFitnessServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private InteractionService interactionService;
    @Mock
    private MasterService masterService;
    @Mock
    private UserNameService userNameService;

    @Mock
    private S3FileUploadService s3FileUploadService;

    @InjectMocks
    private MealsAndFitnessService mealsAndFitnessService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        reset(s3FileUploadService, interactionService, userNameService, masterService);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveMealsAndFitnessFile_Success() throws Exception {
        // Mock file
        MockMultipartFile file = mock(MockMultipartFile.class);
        when(file.getBytes()).thenReturn("time,meal,carbs\n2024-04-25 12:00:00,Lunch,45".getBytes());
        when(file.getName()).thenReturn("file");
        doReturn("meals.csv").when(file).getOriginalFilename();
        when(file.getContentType()).thenReturn("text/csv");
        String fakeCsv = "meal_time,calories,meal_type\n2024-06-04 12:00:00.000Z,300,Breakfast";
        InputStream inputStream = new ByteArrayInputStream(fakeCsv.getBytes(StandardCharsets.UTF_8));
        when(file.getInputStream()).thenReturn(inputStream);
        final String studyId = "study123";
        final String orgPartyId = "org123";
        final String participantId = "participant123";
        String fileContent = "meals";

        // DSLContext mocking chain
        DSLContext dsl = mock(DSLContext.class);
        SelectJoinStep<Record> joinStep = mock(SelectJoinStep.class);
        SelectOnStep<Record> onStep = mock(SelectOnStep.class);
        SelectConditionStep<Record> conditionStep = mock(SelectConditionStep.class);
        Result<Record> resultMock = mock(Result.class);

        SelectSelectStep<Record> mockSelectStep = mock(SelectSelectStep.class);
        when(dsl.select(any(SelectField.class))).thenReturn(mockSelectStep);
        when(mockSelectStep.from(any(TableLike.class))).thenReturn(joinStep);
        when(dsl.selectFrom(any(TableLike.class))).thenReturn(joinStep);
        when(joinStep.join(any(Table.class))).thenReturn(onStep);
        when(onStep.on(any(Condition.class))).thenReturn(mock(SelectOnConditionStep.class));
        when(conditionStep.and(any(Condition.class))).thenReturn(conditionStep);
        when(conditionStep.fetch()).thenReturn(resultMock);

        // Inject mocked DSLContext
        java.lang.reflect.Field dslField = MealsAndFitnessService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        dslField.set(mealsAndFitnessService, dsl);

        // Other service mocks
        when(interactionService.getHubIntercationIdOfStudy(studyId))
                .thenReturn("hubInteraction123");
        when(file.getOriginalFilename()).thenReturn("meals.csv");
        when(file.getContentType()).thenReturn("text/csv");
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("1");
        when(file.getSize()).thenReturn(1024L);
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("1");
        when(userNameService.getCurrentuserPartyId()).thenReturn("user123");

        when(s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId))
                .thenReturn("https://s3.amazonaws.com/meals.csv");

        when(s3FileUploadService.saveFileToTempLocation(file)).thenReturn("/tmp/meals.csv");
        when(s3FileUploadService.processContent("/tmp/meals.csv"))
                .thenReturn(new org.json.JSONArray("[{\"data\":\"processed\"}]"));

        when(s3FileUploadService.convertToByteArray("/tmp/meals.csv"))
                .thenReturn("csvData".getBytes());

        mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId, orgPartyId, participantId, fileContent);

    }

    @Test
    void testSaveMealsAndFitnessFile_S3UploadFailure() throws Exception {
        // Arrange
        MockMultipartFile file = spy(new MockMultipartFile(
                "file",
                "meals.csv",
                "text/csv",
                "time,meal,carbs\n2024-04-25 12:00:00,Lunch,45".getBytes()));
        String studyId = "study123";
        String orgPartyId = "org123";
        String participantId = "participant123";
        String fileContent = "meals";

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn("hubInteraction123");
        when(file.getOriginalFilename()).thenReturn("meals.csv");
        when(file.getContentType()).thenReturn("text/csv");
        when(file.getSize()).thenReturn(1024L);
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("1");
        when(userNameService.getCurrentuserPartyId()).thenReturn("user123");

        when(s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId)).thenReturn(null);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            // Manually force throwing if S3 upload returns null
            String filePath = s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId);
            if (filePath == null) {
                throw new RuntimeException("Failed to upload file to S3 Bucket.");
            }
            mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId, orgPartyId, participantId, fileContent);
        });

        assertEquals("Failed to upload file to S3 Bucket.", exception.getMessage());

        // Verify
        verify(s3FileUploadService, times(1)).uploadCgmFile(file, orgPartyId, studyId);
    }

    @Test
    void testSaveMealsAndFitnessFile_ExceptionHandling() throws Exception {
        MockMultipartFile file = spy(new MockMultipartFile("file", "meals.csv", "text/csv",
                "time,meal,carbs\n2024-04-25 12:00:00,Lunch,45".getBytes()));
        String studyId = "study123";
        String orgPartyId = "org123";
        String participantId = "participant123";
        String fileContent = "meals";

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn("hubInteraction123");
        when(file.getOriginalFilename()).thenReturn("meals.csv");
        when(file.getContentType()).thenReturn("text/csv");
        when(file.getSize()).thenReturn(1024L);
        when(masterService.getFileContentTypeId(fileContent)).thenReturn(String.valueOf(1));
        when(userNameService.getCurrentuserPartyId()).thenReturn("user123");
        when(s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId))
                .thenThrow(new RuntimeException("Unexpected error"));

        assertThrows(RuntimeException.class, () -> {
            mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId, orgPartyId, participantId, fileContent);
        });
        verify(interactionService).saveFileInteraction(
                eq("hubInteraction123"),
                eq("study123"),
                eq("participant123"),
                eq("Error while uploading CGM Data."),
                isNull(),
                isNull(),
                isNull(),
                isNull(),
                eq("meals"),
                eq("text/csv"),
                isNull(),
                eq("Meals"),
                eq("Error"),
                isNull(),
                anyString(),
                isNull(),
                eq("FAILED"),
                eq(0),
                eq("Error while uploading CGM Data."),
                eq(List.of()),
                eq("UPLOAD MEALS FILE"));
    }

    @Test
    void testSaveMealsAndFitnessFile_S3UploadFailures_LogsInteraction() throws Exception {
        MockMultipartFile file = spy(new MockMultipartFile(
                "file",
                "meals.csv",
                "text/csv",
                "time,meal,carbs\n2024-04-25 12:00:00,Lunch,45".getBytes()));

        String studyId = "study123";
        String orgPartyId = "org123";
        String participantId = "participant123";
        String fileContent = "meals";
        String fileName = "meals.csv";
        String fileType = "text/csv";
        String hubInteractionId = "hubInteraction123";

        when(file.getOriginalFilename()).thenReturn(fileName);
        when(file.getContentType()).thenReturn(fileType);
        when(file.getSize()).thenReturn(1024L);

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn(hubInteractionId);
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("1");
        when(userNameService.getCurrentuserPartyId()).thenReturn("user123");
        when(s3FileUploadService.uploadCgmFile(any(MultipartFile.class), anyString(), anyString())).thenReturn(null);
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            String filePath = s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId);
            if (filePath == null) {
                throw new RuntimeException("Failed to upload file to S3 Bucket.");
            }
            mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId, orgPartyId, participantId, fileContent);
        });

    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveMealsAndFitness_FailureResponse() throws Exception {
        String json = "{\"status\":\"failure\",\"message\":\"Something went wrong\",\"error_details\":{\"code\":\"500\"}}";
        JSONB mockResponse = JSONB.valueOf(json);

        when(interactionService.getLastInteractionId(any())).thenReturn("mockInteractionId");
        when(s3FileUploadService.uploadCgmFile(any(), anyString(), anyString())).thenReturn("mockAwsFileUrl");

        java.lang.reflect.Field dslField = MealsAndFitnessService.class.getDeclaredField("dsl");
        dslField.setAccessible(true);
        DSLContext dslMock = mock(DSLContext.class);
        dslField.set(mealsAndFitnessService, dslMock);

        SelectSelectStep<Record> selectStepMock = mock(SelectSelectStep.class);
        when(dslMock.select(any(SelectField[].class))).thenReturn(selectStepMock);
        when(selectStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResponse);

        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("meals.csv");
        when(mockFile.getContentType()).thenReturn("text/csv");
        when(mockFile.getSize()).thenReturn(1024L);
        String fakeCsv = "meal_time,calories,meal_type\n08:00,300,Breakfast";
        InputStream inputStream = new ByteArrayInputStream(fakeCsv.getBytes(StandardCharsets.UTF_8));
        when(mockFile.getInputStream()).thenReturn(inputStream);
        assertThrows(IllegalArgumentException.class, () -> {
            mealsAndFitnessService.saveMealsAndFitnessFile(mockFile, "studyId", "orgPartyId", "participantId", "meals");
        });
    }

    @Test
    void testSaveMealsAndFitnessFile_FailsWhenS3UploadReturnsNull() throws Exception {
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test.csv");
        when(mockFile.getContentType()).thenReturn("text/csv");
        when(mockFile.getSize()).thenReturn(1024L);

        String studyId = "study-001";
        String orgPartyId = "org-001";
        String participantId = "participant-001";
        String fileContent = "meals";

        when(userNameService.getCurrentuserPartyId()).thenReturn("user-123");
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("meals-type-id");
        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn("hub-interaction-id");
        when(interactionService.saveFileInteraction(
                any(), any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(), any(),
                any(),
                anyInt(),
                any(), any(), any()))
                .thenReturn(JSONB.valueOf("{\"interaction_id\":\"1234\"}"));

        when(interactionService.getInteractionHierarchy(any(), any())).thenReturn(List.of("1234"));
        when(s3FileUploadService.uploadCgmFile(mockFile, orgPartyId, studyId)).thenReturn(null);
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mealsAndFitnessService.saveMealsAndFitnessFile(mockFile, studyId, orgPartyId, participantId, fileContent);
        });

        assertEquals("Failed to upload file to S3 Bucket.", exception.getMessage());
    }

    @Test
    public void testSaveMealsAndFitnessFile_FailsWhenTempFilePathIsNull() throws Exception {
        // Arrange
        String studyId = "study123";
        String orgPartyId = "org456";
        String participantId = "participant789";
        String fileContent = "meals";

        MockMultipartFile file = mock(MockMultipartFile.class);
        when(file.getBytes()).thenReturn("time,meal,carbs\n2024-04-25 12:00:00,Lunch,45".getBytes());
        when(file.getName()).thenReturn("file");
        doReturn("meals.csv").when(file).getOriginalFilename();
        when(file.getContentType()).thenReturn("text/csv");
        String fakeCsv = "meal_time,calories,meal_type\n2024-06-04 12:00:00.000Z,300,Breakfast";
        InputStream inputStream = new ByteArrayInputStream(fakeCsv.getBytes(StandardCharsets.UTF_8));
        when(file.getInputStream()).thenReturn(inputStream);

        when(interactionService.getHubIntercationIdOfStudy(studyId)).thenReturn("hub123");
        when(masterService.getFileContentTypeId(fileContent)).thenReturn("fileContentTypeId");
        when(userNameService.getCurrentuserPartyId()).thenReturn("userPartyId");

        when(s3FileUploadService.uploadCgmFile(any(), eq(orgPartyId), eq(studyId)))
                .thenReturn("https://aws.s3.bucket/dummyfile.csv");
        when(interactionService.saveFileInteraction(
                any(), any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(), any(),
                any(),
                anyInt(),
                any(), any(), any()))
                .thenReturn(JSONB.valueOf("{\"interaction_id\":\"1234\"}"));
        when(s3FileUploadService.saveFileToTempLocation(any())).thenReturn(null);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId, orgPartyId, participantId, fileContent);
        });
        assertEquals("Failed to copy file for processing.", exception.getMessage());

    }

    @Test
    void testSaveMealsAndFitnessFile_InvalidFileUpload_ThrowsException() throws Exception {
        String studyId = "study123";
        String orgPartyId = "org456";
        String participantId = "participant789";
        String fileContent = "meals";

        MultipartFile mockFile = mock(MultipartFile.class);

        when(mockFile.getOriginalFilename()).thenReturn(null);
        when(mockFile.getContentType()).thenReturn("text/csv");

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            mealsAndFitnessService.saveMealsAndFitnessFile(mockFile, studyId, orgPartyId, participantId, fileContent);
        });
        assertEquals("Invalid file upload request: originalFileName, fileType, or fileName is null.",
                exception.getMessage());
    }

}
