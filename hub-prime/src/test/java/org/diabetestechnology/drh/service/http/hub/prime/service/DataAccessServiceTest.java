package org.diabetestechnology.drh.service.http.hub.prime.service;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.SortOrder;
import org.jooq.impl.DSL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import lib.aide.tabular.TabularRowsRequest.FilterModel;
import lib.aide.tabular.TabularRowsRequest.SortModel;

@Disabled
public class DataAccessServiceTest {

    @InjectMocks
    private DataAccessService dataAccessService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private JdbcTemplate jdbcTemplate;

    private DataAccessService spyDataAccessService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        spyDataAccessService = spy(dataAccessService);
        doNothing().when(spyDataAccessService).refreshDatabase(anyString());
    }

    @SuppressWarnings("unused")
    private void setupCommonScenario(String studyId, String participantId, String startDate, String endDate) {
        dataAccessService.getDatabaseAliases().add(studyId);
        doNothing().when(jdbcTemplate).execute(eq("PRAGMA \"" + studyId.toLowerCase() + "\".busy_timeout = 5000;"));

    }

    private void setAttachedDatabases(Map<String, String> map) throws Exception {
        Field field = DataAccessService.class.getDeclaredField("attachedDatabases");
        ConcurrentHashMap<String, String> concurrentMap = new ConcurrentHashMap<>(map);
        field.setAccessible(true);
        field.set(dataAccessService, concurrentMap);
    }

    // -------------------------------------attachDatabase()-----------------//
    @SuppressWarnings("unchecked")
    @Test
    public void testAttachDatabase() throws Exception {
        String attachPath = "path/to/database";
        String alias = "testDb";
        when(jdbcTemplate.queryForList(anyString())).thenReturn(List.of(Map.of("name", alias)));
        setAttachedDatabases(new ConcurrentHashMap<>());
        Method method = DataAccessService.class.getDeclaredMethod("attachDatabase", String.class, String.class);
        method.setAccessible(true);
        method.invoke(dataAccessService, attachPath, alias);
        Field attachedDatabasesField = DataAccessService.class.getDeclaredField("attachedDatabases");
        attachedDatabasesField.setAccessible(true);
        Map<String, String> attachedDatabases = (Map<String, String>) attachedDatabasesField.get(dataAccessService);
        assertTrue(attachedDatabases.containsKey(alias));
        assertEquals(attachPath, attachedDatabases.get(alias));

    }

    @SuppressWarnings("unchecked")
    @Test
    public void testAttachDatabase_Success() throws Exception {
        String attachPath = "path/to/database";
        String alias = "testDB";

        when(jdbcTemplate.queryForList(anyString())).thenReturn(List.of(Map.of("name",
                alias)));

        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            TransactionCallback<Void> callback = invocation.getArgument(0);
            return callback.doInTransaction(null);
        });

        when(jdbcTemplate.queryForObject(anyString(),
                eq(Integer.class))).thenReturn(1);
        doNothing().when(jdbcTemplate).execute(anyString());

        Method method = DataAccessService.class.getDeclaredMethod("attachDatabase",
                String.class, String.class);
        method.setAccessible(true);

        method.invoke(dataAccessService, attachPath, alias);
        Field attachedDatabasesField = DataAccessService.class.getDeclaredField("attachedDatabases");
        attachedDatabasesField.setAccessible(true);
        Map<String, String> attachedDatabases = (Map<String, String>) attachedDatabasesField.get(dataAccessService);
        assertTrue(attachedDatabases.containsKey(alias));
        assertEquals(attachPath, attachedDatabases.get(alias));
    }

    @Test
    public void testAttachDatabase_WhenFails() throws Exception {
        String attachPath = "path/to/nonexistent/database";
        String alias = "testDB";

        doThrow(new DataAccessException("Simulated attach failure") {
        })
                .when(jdbcTemplate).execute(anyString());

        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            Method method = DataAccessService.class.getDeclaredMethod("attachDatabase",
                    String.class, String.class);
            method.setAccessible(true);
            method.invoke(dataAccessService, attachPath, alias);
        });

        Throwable cause = exception.getCause();
        assertTrue(cause instanceof RuntimeException);
        assertEquals("Failed to attach database 'testDB' after 3 attempts",
                cause.getMessage());
    }

    // ------------------------------checkDatabaseAttachment()-----------------------------//
    @Test
    public void testCheckDatabaseAttachment_WhenNotFound() throws Exception {
        String dbName = "nonExistentDB";

        when(jdbcTemplate.queryForList(anyString())).thenReturn(List.of(Map.of("name", "otherDB")));

        Method method = DataAccessService.class.getDeclaredMethod("checkDatabaseAttachment", String.class);
        method.setAccessible(true); // Make the method accessible

        boolean isAttached = (boolean) method.invoke(dataAccessService, dbName);
        assertFalse(isAttached);
    }

    // -------------------------response()------------------------------------------//
    @Test
    public void testResponse() {

        List<String> aliases = List.of("db1", "db2", "db3");
        String tableName = "users";

        String result = dataAccessService.response(aliases, tableName);

        String expected = "SELECT * FROM db1.users UNION ALL SELECT * FROM db2.users UNION ALL SELECT * FROM db3.users";
        assertEquals(expected, result);
    }

    // ----------------------refreshDatabase()--------------//
    @Test
    public void testRefreshDatabase_WhenNotAttached() throws Exception {
        String dbName = "testDB";

        when(jdbcTemplate.queryForList("PRAGMA database_list;"))
                .thenReturn(List.of(Map.of("name", "otherDB")));

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbName, "path/to/database")));
        dataAccessService.refreshDatabase(dbName);

    }

    @Test
    public void testRefreshDatabase_WhenAlreadyAttached() throws Exception {
        String dbName = "testDB";

        when(jdbcTemplate.queryForList("PRAGMA database_list;"))
                .thenReturn(List.of(Map.of("name", dbName)));
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbName, "path/to/database")));
        dataAccessService.refreshDatabase(dbName);

    }
    // ---------------------------GetParticipantCGMDates------------//

    @SuppressWarnings("unchecked")
    @Test
    public void testGetParticipantCGMDates() throws Exception {
        String dbName = "teststudy";
        String studyId = "testStudy";
        String participantId = "testParticipant";
        Map<String, Object> expectedResult = Map.of(
                "participant_cgm_start_date", "2023-01-01",
                "participant_cgm_end_date", "2023-12-31");

        Field databaseAliasesField = DataAccessService.class.getDeclaredField("databaseAliases");
        databaseAliasesField.setAccessible(true);
        Set<String> databaseAliases = (Set<String>) databaseAliasesField.get(dataAccessService);
        databaseAliases.add(studyId.toLowerCase());

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId)))
                .thenReturn(expectedResult);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbName, "path/to/database")));
        var futureResult = dataAccessService.getParticipantCGMDates(studyId, participantId);
        var result = futureResult.get();

        assertEquals("success", result.getStatus());
        assertEquals(expectedResult, ((Map<String, Object>) result.getData().get("participantCGMDates")));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetParticipantCGMDates_NoDataFound() throws Exception {
        String studyId = "testStudy";
        String participantId = "testParticipant";

        Field databaseAliasesField = DataAccessService.class.getDeclaredField("databaseAliases");
        databaseAliasesField.setAccessible(true);
        Set<String> databaseAliases = (Set<String>) databaseAliasesField.get(dataAccessService);
        databaseAliases.add(studyId.toLowerCase());

        // Mock the queryForObject to throw EmptyResultDataAccessException
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId)))
                .thenThrow(new EmptyResultDataAccessException(1));

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        var futureResult = dataAccessService.getParticipantCGMDates(studyId, participantId);
        var result = futureResult.get();

        assertEquals("success", result.getStatus());
        assertTrue(((Map<String, Object>) result.getData()).isEmpty());
    }

    @Test
    public void testGetParticipantCGMDates_DBNotAttached() {
        String studyId = "nonExistentStudy";
        String participantId = "testParticipant";

        ExecutionException exception = assertThrows(ExecutionException.class, () -> {
            dataAccessService.getParticipantCGMDates(studyId, participantId).get();
        });

        Throwable cause = exception.getCause();
        assertTrue(cause instanceof IllegalArgumentException);
        assertEquals("Database 'nonexistentstudy' is not attached.", cause.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetParticipantCGMDates_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getParticipantCGMDates(studyId,
                participantId);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch participantCGMDates", response.getMessage());
    }

    // ----------------------------GetCoefficientOfVariation()-----------------------------//
    @Test
    public void testGetCoefficientOfVariation_Success() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        String expectedCoefficient = "15.75";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(expectedCoefficient);

        CompletableFuture<JdbcResponse> future = spyDataAccessService.getCoefficientOfVariation(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = future.get();

        assertEquals("success", response.getStatus());
        assertEquals("coefficient_of_variation successfully fetched", response.getMessage());
        assertEquals(expectedCoefficient, response.getData().get("coefficient_of_variation"));
        assertTrue(((String) response.getErrors()).isEmpty());
    }

    @Test
    public void testGetCoefficientOfVariation_DatabaseNotAttached() {
        String studyId = "nonexistentStudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> future = spyDataAccessService.getCoefficientOfVariation(studyId, participantId,
                startDate, endDate);

        Exception exception = assertThrows(ExecutionException.class, () -> future.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Database 'nonexistentstudy' is not attached.", exception.getCause().getMessage());
    }

    @Test
    public void testGetCoefficientOfVariation_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        CompletableFuture<JdbcResponse> future = spyDataAccessService.getCoefficientOfVariation(studyId, participantId,
                startDate, endDate);

        Exception exception = assertThrows(ExecutionException.class, () -> future.get());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Error executing SQL query for coefficient of variation", exception.getCause().getMessage());
    }

    // --------------------- CalculateTimeAboveRangeVeryHigh() ------------------//
    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeVeryHigh() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        Map<String, Object> expectedMap = new HashMap<>();
        expectedMap.put("time_above_vh_percentage", "15.5");
        expectedMap.put("time_above_vh", "100");
        expectedMap.put("time_range_string", "01 hours, 40 minutes");
        expectedMap.put("CGM_Value", "260");

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenReturn(expectedMap);

        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeVeryHigh(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("time_above_vh_percentage successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);

    }

    @Test
    public void testCalculateTimeAboveRangeVeryHigh_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeVeryHigh(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeVeryHigh_EmptyResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeVeryHigh(studyId,
                participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("time_above_vh_percentage successfully fetched", response.getMessage());
        assertEquals(Map.of(), response.getData());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeVeryHigh_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeVeryHigh(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch time_above_vh_percentage", response.getMessage());
    }

    // -----------------------------------calculateTimeInTightRange()-------------------------------//
    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInTightRange() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("participant_id", "10");
        resultMap.put("time_in_tight_range_percentage", "100");
        resultMap.put("time_in_tight_range", "one hour");
        resultMap.put("time_range_string", "one hour");
        resultMap.put("CGM_Value", "150");

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate))).thenReturn(resultMap);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeInTightRange(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("timeInTightRange successfully fetched", response.getMessage());
        assertTrue(response.getData() instanceof Map);
        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        Map<String, Object> result = (Map<String, Object>) responseData.get("timeInTightRange");
        assertEquals("100", result.get("time_in_tight_range_percentage"));
        assertEquals("10", result.get("participant_id"));
        assertEquals("150", result.get("CGM_Value"));
        assertEquals("one hour", result.get("time_in_tight_range"));
        assertEquals("one hour", result.get("time_range_string"));
    }

    @Test
    public void testCalculateTimeInTightRange_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeInTightRange(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInTightRange_EmptyResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeInTightRange(studyId,
                participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("timeInTightRange successfully fetched", response.getMessage());
        assertEquals(Map.of(), response.getData());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInTightRange_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeInTightRange(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch timeInTightRange", response.getMessage());
    }
    // ---------------------------------CalculateTimeAboveRangeHigh()-----------------//

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeHigh() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        Map<String, Object> expectedMap = new HashMap<>();
        expectedMap.put("time_above_h_percentage", "15.5");
        expectedMap.put("time_above_h", "100");
        expectedMap.put("time_range_string", "01 hours, 40 minutes");
        expectedMap.put("CGM_Value", "260");

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenReturn(expectedMap);

        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeHigh(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("time_above_range_high_percentage successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);
        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        Map<String, Object> resultMap = (Map<String, Object>) responseData.get("time_above_range_high_percentage");
        assertEquals("15.5", resultMap.get("time_above_h_percentage"));
        assertEquals("100", resultMap.get("time_above_h"));
        assertEquals("01 hours, 40 minutes", resultMap.get("time_range_string"));
        assertEquals("260", resultMap.get("CGM_Value"));
    }

    @Test
    public void testCalculateTimeAboveRangeHigh_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeHigh(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeHigh_EmptyResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeHigh(studyId,
                participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("time_above_range_high_percentage successfully fetched", response.getMessage());
        assertEquals(Map.of(), response.getData());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeAboveRangeHigh_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeAboveRangeHigh(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch time_above_range_high_percentage", response.getMessage());
    }

    // --------------------------------GlycemicRiskIndicator()----------------------------//
    @Test
    public void testGlycemicRiskIndicator() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Map<String, Object> resultMap = new HashMap<>();
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(resultMap);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.glycemicRiskIndicator(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("glycemicRiskIndicator successfully fetched", response.getMessage());
    }

    @Test
    public void testGlycemicRiskIndicator_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.glycemicRiskIndicator(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGlycemicRiskIndicator_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.glycemicRiskIndicator(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch glycemicRiskIndicator", response.getMessage());
    }

    // --------------------------------LiabilityIndex()-----------------------------------//
    @Test
    public void testLiabilityIndex() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Map<String, Object> resultMap = new HashMap<>();
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(resultMap);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.liabilityIndex(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("liabilityIndex successfully fetched", response.getMessage());
    }

    @Test
    public void testLiabilityIndex_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.liabilityIndex(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testLiabilityIndex_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.liabilityIndex(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch liabilityIndex", response.getMessage());
    }

    // --------------------------------GetMeanAmplitude()------------------------------//
    @Test
    public void testGetMeanAmplitude() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        BigDecimal bigDecimalFromString = new BigDecimal("123.45");
        when(jdbcTemplate.queryForObject(anyString(), eq(BigDecimal.class), eq(participantId), eq(startDate),
                eq(endDate))).thenReturn(bigDecimalFromString);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanAmplitude(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("meanAmplitude successfully fetched", response.getMessage());

    }

    @Test
    public void testGetMeanAmplitude_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanAmplitude(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetMeanAmplitude_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(BigDecimal.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanAmplitude(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch meanAmplitude", response.getMessage());
    }

    @Test
    public void testGetMeanAmplitude_EmptyResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), eq(BigDecimal.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanAmplitude(studyId, participantId,
                startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("meanAmplitude successfully fetched", response.getMessage());
    }
    // --------------------------------CalculateMValue()-------------------//

    @Test
    public void testCalculateMValue() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Double mValue = 0.5;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mValue);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMValue(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("mValue successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateMValue_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMValue(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateMValue_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMValue(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("Failed to fetch mValue", response.getMessage());
    }

    @Test
    public void testCalculateMValue_EmptyResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);

        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMValue(studyId, participantId,
                startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("mValue successfully fetched", response.getMessage());
    }
    // --------------------------------GetMeanGlucose()----------------------------//

    @Test
    public void testGetMeanGlucose() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        String meanGlucose = "7.5";
        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(meanGlucose);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanGlucose(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("mean_glucose successfully fetched", response.getMessage());
    }

    @Test
    public void testGetMeanGlucose_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanGlucose(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetMeanGlucose_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getMeanGlucose(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("java.lang.RuntimeException: Error executing SQL query for mean glucose", exception.getMessage());
    }

    // ------------------------getNumberOfDaysCGMWorn()----------------//
    @Test
    public void testGetNumberOfDaysCGMWorn() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Integer numberOfDaysCGMWorn = 5;
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(numberOfDaysCGMWorn);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getNumberOfDaysCGMWorn(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("number_of_days_cgm_worn successfully fetched", response.getMessage());
    }

    @Test
    public void testGetNumberOfDaysCGMWorn_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getNumberOfDaysCGMWorn(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetNumberOfDaysCGMWorn_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getNumberOfDaysCGMWorn(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("java.lang.RuntimeException: Error executing SQL query for number of days CGM worn",
                exception.getMessage());
    }
    // -------------------------------------getPercentageTimeCGMActive()-----------------//

    @Test
    public void testGetPercentageTimeCGMActive() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Double percentageTimeCGMActive = 0.75;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(percentageTimeCGMActive);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getPercentageTimeCGMActive(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("percentage_active successfully fetched", response.getMessage());
    }

    @Test
    public void testGetPercentageTimeCGMActive_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getPercentageTimeCGMActive(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetPercentageTimeCGMActive_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getPercentageTimeCGMActive(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("java.lang.RuntimeException: Error executing SQL query for percentage time CGM active",
                exception.getMessage());
    }

    // ----------------------------getGlucoseManagementIndicator()-------------------//
    @Test
    public void testGetGlucoseManagementIndicator() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        Double glucoseManagementIndicator = 0.25;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(glucoseManagementIndicator);
        setupCommonScenario(studyId, participantId, startDate, endDate);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getGlucoseManagementIndicator(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("gmi (glucose-management-indicator) successfully fetched", response.getMessage());
    }

    @Test
    public void testGetGlucoseManagementIndicator_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getGlucoseManagementIndicator(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetGlucoseManagementIndicator_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getGlucoseManagementIndicator(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("java.lang.RuntimeException: Error executing SQL query for GMI", exception.getMessage());
    }

    // ------------------------------getStudyParticipantDashboard()------------------------//
    @Test
    public void testGetStudyParticipantDashboard() throws Exception {
        String studyId = "teststudy";
        int limit = 2;
        int offset = 2;
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        List<Map<String, Object>> expectedMapList = new ArrayList<>();
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("participantId", "testParticipant");
        expectedMapList.add(mockData);
        when(jdbcTemplate.queryForList(anyString())).thenReturn(expectedMapList);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        CompletableFuture<List<Map<String, Object>>> futureResult = dataAccessService
                .getStudyParticipantDashboard(studyId, limit, offset);
        List<Map<String, Object>> result = futureResult.get();
        assertEquals(expectedMapList, result);
    }

    @Test
    public void testGetStudyParticipantDashboard_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        int limit = 2;
        int offset = 2;

        CompletableFuture<List<Map<String, Object>>> futureResult = dataAccessService
                .getStudyParticipantDashboard(studyId, limit, offset);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    // -------------------------------------getStudyDetails()----------------------//

    @Test
    public void testGetStudyDetails() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("study_id", studyId);
        mockData.put("study_name", "Test Study");

        when(jdbcTemplate.queryForMap(anyString())).thenReturn(mockData);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<Map<String, Object>> futureResult = dataAccessService.getStudyDetails(studyId);
        Map<String, Object> result = futureResult.get();

        assertEquals("Test Study", result.get("study_name"));

    }

    @Test
    public void testGetStudyDetails_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";

        CompletableFuture<Map<String, Object>> futureResult = dataAccessService.getStudyDetails(studyId);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    // -----------------------------------getParticipantMetrics()-----------------------//

    @Test
    public void testGetParticipantMetrics() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("participantId", participantId);
        mockData.put("total_data_points", 100);
        mockData.put("total_time_cgms", 50);
        mockData.put("percentage_time_cgms", 0.5);

        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate))).thenReturn(mockData);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getParticipantMetrics(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("participantMetrics successfully fetched", response.getMessage());

    }

    @Test
    public void testGetParticipantMetrics_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getParticipantMetrics(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetParticipantMetrics_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getParticipantMetrics(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch participantMetrics", response.getMessage());
    }

    // -----------------------------------------getTimeRangeStackedData()---------------------//
    @Test
    public void testGetTimeRangeStackedData() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("participantId", participantId);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate))).thenReturn(mockData);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getTimeRangeStackedData(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("timeRangeStackedData successfully fetched", response.getMessage());
    }

    @Test
    public void testGetTimeRangeStackedData_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getTimeRangeStackedData(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testGetTimeRangeStackedData_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.getTimeRangeStackedData(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch timeRangeStackedData", response.getMessage());
    }
    // ------------------------------------calculateAverageDailyRisk()-------------------------//

    @Test
    public void testCalculateAverageDailyRisk() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Double mockData = 2.0;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mockData);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateAverageDailyRisk(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("averageDailyRisk successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateAverageDailyRisk_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateAverageDailyRisk(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateAverageDailyRisk_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(startDate), eq(endDate), eq(participantId)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateAverageDailyRisk(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch averageDailyRisk", response.getMessage());
    }

    @Test
    public void testCalculateAverageDailyRisk_NullResult() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class),
                eq(startDate), eq(endDate), eq(participantId)))
                .thenReturn(null);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy",
                "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateAverageDailyRisk(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("averageDailyRisk successfully fetched", response.getMessage());
    }

    // ------------------------------------calculateJIndex()--------------------//
    @Test
    public void testCalculateJIndex() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Double mockJIndex = 5.0;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mockJIndex);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateJIndex(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("jIndex successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateJIndex_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateJIndex(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateJIndex_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateJIndex(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch jIndex", response.getMessage());
    }

    // ----------------------------------calculateLBGIandHBGI()-------------------//
    @Test
    public void testCalculateLBGIandHBGI() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("participantId", participantId);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate))).thenReturn(mockData);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateLBGIandHBGI(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("lbgiAndHbgi successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateLBGIandHBGI_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateLBGIandHBGI(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateLBGIandHBGI_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForMap(anyString(), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateLBGIandHBGI(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch lbgiAndHbgi", response.getMessage());
    }

    // -------------------------------calculateGRADE()---------------------//
    @Test
    public void testCalculateGRADE() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Double mockJIndex = 2.5;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mockJIndex);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateGRADE(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("grade successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateGRADE_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateGRADE(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateGRADE_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateGRADE(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch grade", response.getMessage());
    }

    // ------------------------------calculateMeanOfDailyDifferences()----------------------------//
    @Test
    public void testCalculateMeanOfDailyDifferences() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Double mockMean = 2.5;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mockMean);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMeanOfDailyDifferences(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("meanOfDailyDifferences successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateMeanOfDailyDifferences_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMeanOfDailyDifferences(studyId,
                participantId, startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateMeanOfDailyDifferences_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateMeanOfDailyDifferences(studyId,
                participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch meanOfDailyDifferences", response.getMessage());
    }
    // -------------------------------calculateCONGA()-------------//

    @Test
    public void testCalculateCONGA() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        Double mockMean = 2.5;
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenReturn(mockMean);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateCONGA(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("congaHourlyMean successfully fetched", response.getMessage());
    }

    @Test
    public void testCalculateCONGA_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateCONGA(studyId, participantId,
                startDate, endDate);
        Exception exception = assertThrows(ExecutionException.class, () -> futureResult.get());
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("java.lang.IllegalArgumentException: Database 'nonexistentstudy' is not attached.",
                exception.getMessage());
    }

    @Test
    public void testCalculateCONGA_DataAccessException() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateCONGA(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch congaHourlyMean", response.getMessage());
    }

    // ----------------------------------replacePlaceholders()-----------------------------//

    @Test
    public void testReplacePlaceholders() {
        String query = "SELECT * FROM table WHERE id = :id AND name = :name";
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("id", "1");
        placeholders.put("name", "John");

        String result = DataAccessService.replacePlaceholders(query, placeholders);

        assertEquals("SELECT * FROM table WHERE 1 = :1 AND John = :John", result);

    }

    // ----------------------------------getParticipantInfo()-----------------------------//

    @Test
    public void testGetParticipantInfos() {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String sqlQuery = "SELECT tenant_id, participant_id, gender, age, race_ethnicity, bmi, baseline_hba1c, diabetes_type, study_arm "
                +
                "FROM " + studyId + ".drh_participant WHERE participant_id = ?";

        Map<String, Object> mockResult = Map.of(
                "tenant_id,", "testTenantId",
                "participant_id", participantId,
                "gender", "M",
                "age", 30,
                "race_ethnicity", "Caucasian",
                "bmi", 22.5,
                "baseline_hba1c", 7.2,
                "diabetes_type", "Type 1",
                "study_arm", "Control");

        when(jdbcTemplate.queryForMap(sqlQuery, participantId)).thenReturn(mockResult);

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.getParticipantInfo(studyId, participantId);

        JdbcResponse response = futureResponse.join();
        assertNotNull(response);
        assertEquals(mockResult, response.getData().get("participantInfo"));
        assertEquals("success", response.getStatus());

        String invalidStudyId = "invalid_study";

        CompletionException exception = assertThrows(CompletionException.class,
                () -> dataAccessService.getParticipantInfo(invalidStudyId, participantId).join());

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Database 'invalid_study' is not attached.", exception.getCause().getMessage());
    }

    @Test
    public void testCalculateTimeBelowRangeLow_NoData() throws InterruptedException, ExecutionException {

        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        } catch (Exception e) {

            e.printStackTrace();
        }

        Map<String, Object> mockResult = new LinkedHashMap<>();
        mockResult.put("time_below_range_low_percentage", "5.0");
        mockResult.put("time_below_range_low", "10");
        mockResult.put("time_range_string", "00 hours, 50 minutes");
        mockResult.put("CGM_Value", "65");

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.calculateTimeBelowRangeLow(studyId,
                participantId, startDate, endDate);

        JdbcResponse response = futureResponse.get();
        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals(0, response.getData().size());
    }

    @SuppressWarnings({ "deprecation", "unchecked" })
    @Test
    public void testCalculateTimeBelowRangeLow_RetryLogic() throws InterruptedException, ExecutionException {

        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        } catch (Exception e) {

            e.printStackTrace();
        }

        DataAccessException exception = new DataAccessException("SQL error") {
        };

        Map<String, Object> mockResult = new LinkedHashMap<>();
        mockResult.put("time_below_range_low_percentage", "5.0");
        mockResult.put("time_below_range_low", "10");
        mockResult.put("time_range_string", "00 hours, 50 minutes");
        mockResult.put("CGM_Value", "65");

        final var sql = "";

        RowMapper<Map<String, Object>> rowMapper = (rs, rowNum) -> {
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("time_below_range_low_percentage", rs.getString("time_below_range_low_percentage"));
            result.put("time_below_range_low", rs.getInt("time_below_range_low"));
            result.put("time_range_string", rs.getString("time_range_string"));
            result.put("CGM_Value", rs.getString("CGM_Value"));
            return result;
        };

        when(jdbcTemplate.queryForObject(anyString(),
                any(RowMapper.class),
                eq(participantId),
                eq(startDate),
                eq(endDate)))
                .thenReturn(mockResult);

        CompletableFuture<JdbcResponse> futureResponse = null;

        try {

            futureResponse = dataAccessService.calculateTimeBelowRangeLow(studyId, participantId, startDate, endDate);
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        assertNotNull(futureResponse, "The futureResponse should not be null");

        JdbcResponse response = futureResponse.get();

        assertNotNull(response, "Response should not be null");
        assertEquals("success", response.getStatus());

    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeBelowRangeLow_Success() throws Exception {

        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";
        setupCommonScenario(studyId, participantId, startDate, endDate);
        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        Map<String, Object> mockResult = new LinkedHashMap<>();
        mockResult.put("time_below_range_low_percentage", "5.0");
        mockResult.put("time_below_range_low", "10");
        mockResult.put("time_range_string", "00 hours, 50 minutes");
        mockResult.put("CGM_Value", "65");

        String dbAlias = studyId.toLowerCase();
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        when(jdbcTemplate.queryForObject(anyString(),
                any(RowMapper.class),
                eq(participantId),
                eq(startDate),
                eq(endDate)))
                .thenReturn(mockResult);

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.calculateTimeBelowRangeLow(studyId,
                participantId, startDate, endDate);

        JdbcResponse response = futureResponse.get();

        assertEquals("success", response.getStatus());
        assertEquals("time_below_range_low_percentage successfully fetched", response.getMessage());

        assertNotNull(response.getData());

    }

    @Test
    public void testCalculateTimeBelowRangeLow_DatabaseNotAttached() throws Exception {
        String studyId = "nonexistentstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        when(jdbcTemplate.queryForObject(anyString(), eq(Double.class), eq(participantId), eq(startDate), eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of("teststudy", "path/to/database")));
        } catch (Exception e) {
            e.printStackTrace();
        }

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeBelowRangeLow(studyId,
                participantId, startDate, endDate);

        ExecutionException exception = assertThrows(ExecutionException.class, () -> futureResult.get());

        Throwable cause = exception.getCause();
        assertEquals(IllegalArgumentException.class, cause.getClass());
        assertEquals("Database 'nonexistentstudy' is not attached.", cause.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetFieldName_Success() throws SQLException {

        String studyId = "test_study";
        String cgmTable = "combined_cgm_tracing_cached";

        String expectedSql = "PRAGMA " + studyId.toLowerCase() + ".table_info(" + cgmTable + ");";

        List<String> expectedColumnNames = Arrays.asList("participant_id", "Date_Time", "CGM_Value");

        when(jdbcTemplate.query(eq(expectedSql), any(ResultSetExtractor.class)))
                .thenAnswer(invocation -> {
                    ResultSetExtractor<List<String>> resultSetExtractor = invocation.getArgument(1);
                    ResultSet rs = mock(ResultSet.class);

                    when(rs.next()).thenReturn(true, true, true, false);
                    when(rs.getString("name"))
                            .thenReturn("participant_id")
                            .thenReturn("Date_Time")
                            .thenReturn("CGM_Value");

                    return resultSetExtractor.extractData(rs);
                });

        try {
            setAttachedDatabases(new ConcurrentHashMap<>(Map.of(studyId.toLowerCase(), "path/to/database")));
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        Object result = dataAccessService.getFieldName(studyId, cgmTable);

        assertNotNull(result);
        assertTrue(result instanceof List);
        assertEquals(expectedColumnNames, result);

        verify(jdbcTemplate, times(1)).query(eq(expectedSql), any(ResultSetExtractor.class));
    }

    @Test
    public void testCommonResponseBuilder_Success() throws Exception {

        String key = "test_key";

        Method method = DataAccessService.class.getDeclaredMethod("commonResponseBuilder", String.class);
        method.setAccessible(true);

        JdbcResponse response = (JdbcResponse) method.invoke(dataAccessService, key);

        assertNotNull(response);
        assertEquals("error", response.getStatus());
        assertEquals(key + ": Failed to execute SQL query after maximum retries", response.getMessage());
        assertEquals("Unexpected error", response.getErrors());
        assertNotNull(response.getData());
        assertTrue(response.getData().isEmpty());
    }

    @Test
    public void testResponseBuilder_SuccessWithData() {

        Map<String, Object> mockData = Map.of("key", "value");
        String status = "success";
        String messageKey = "fetchData";
        Object error = null;

        JdbcResponse response = dataAccessService.responseBuilder(mockData, status, messageKey, error);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("fetchData successfully fetched", response.getMessage());
        assertFalse(response.getData().isEmpty());
        assertEquals("value", response.getData().get("key"));
        assertEquals("", response.getErrors());
    }

    @Test
    public void testResponseBuilder_SuccessNoData() {

        Map<String, Object> mockData = Map.of();
        String status = "success";
        String messageKey = "fetchData";
        Object error = null;

        JdbcResponse response = dataAccessService.responseBuilder(mockData, status, messageKey, error);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("fetchData successfully fetched", response.getMessage());
        assertTrue(response.getData().isEmpty());
        assertEquals("No data available for this duration", response.getErrors());
    }

    @Test
    public void testResponseBuilder_Error() {

        Map<String, Object> mockData = Map.of();
        String status = "error";
        String messageKey = "fetchData";
        Object error = null;

        JdbcResponse response = dataAccessService.responseBuilder(mockData, status, messageKey, error);

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("Failed to fetch fetchData", response.getMessage());
        assertEquals("Failed to fetch fetchData", response.getErrors());
    }

    @Test
    public void testGetSortModel_AscendingSort() {

        SortModel sortModel = Mockito.mock(SortModel.class);
        Mockito.when(sortModel.colId()).thenReturn("column1");
        Mockito.when(sortModel.sort()).thenReturn("asc");

        SortField<?>[] sortFields = dataAccessService.getSortModel(List.of(sortModel));

        assertNotNull(sortFields);
        assertEquals(1, sortFields.length);
        assertEquals(SortOrder.ASC, sortFields[0].getOrder());
        assertEquals(DSL.field("column1").asc(), sortFields[0]);
    }

    @Test
    public void testGetSortModel_DescendingSort() {

        SortModel sortModel = Mockito.mock(SortModel.class);
        Mockito.when(sortModel.colId()).thenReturn("column1");
        Mockito.when(sortModel.sort()).thenReturn("desc");

        SortField<?>[] sortFields = dataAccessService.getSortModel(List.of(sortModel));

        assertNotNull(sortFields);
        assertEquals(1, sortFields.length);
        assertEquals(SortOrder.DESC, sortFields[0].getOrder());
        assertEquals(DSL.field("column1").desc(), sortFields[0]);
    }

    @Test
    public void testCreateCondition_LikeFilter() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("like");
        when(filterModel.toString()).thenReturn("testValue");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        List<Condition> conditions = dataAccessService.createCondition(filters);

        assertNotNull(conditions, "Conditions list should not be null");
        assertEquals(1, conditions.size(), "Conditions list should contain 1 element");
        assertEquals(DSL.field("column1").like("%testValue%"), conditions.get(0),
                "Condition should match LIKE expression");
    }

    @Test
    public void testCreateCondition_EqualsFilter() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("equals");
        when(filterModel.toString()).thenReturn("testValue");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        List<Condition> conditions = dataAccessService.createCondition(filters);

        assertNotNull(conditions, "Conditions list should not be null");
        assertEquals(1, conditions.size(), "Conditions list should contain 1 element");

        assertEquals(DSL.field("column1").eq("testValue").toString(),
                conditions.get(0).toString(),
                "Condition should match EQUALS expression");
    }

    @Test
    public void testCreateCondition_NumberFilter() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("number");
        when(filterModel.toString()).thenReturn("100");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        List<Condition> conditions = dataAccessService.createCondition(filters);

        assertNotNull(conditions, "Conditions list should not be null");
        assertEquals(1, conditions.size(), "Conditions list should contain 1 element");

        Condition condition = conditions.get(0);
        String expectedConditionString = DSL.field("column1").eq("100").toString();

        assertEquals(expectedConditionString, condition.toString(), "Condition should match NUMBER expression");
    }

    @Test
    public void testCreateCondition_DateFilter() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("date");
        when(filterModel.toString()).thenReturn("2023-01-01");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        List<Condition> conditions = dataAccessService.createCondition(filters);

        assertNotNull(conditions, "Conditions list should not be null");
        assertEquals(1, conditions.size(), "Conditions list should contain 1 element");

        Condition condition = conditions.get(0);
        String expectedCondition = DSL.field("column1").eq("2023-01-01").toString();

        assertEquals(expectedCondition, condition.toString(), "Condition should match expected SQL condition");
    }

    @Test
    public void testCreateCondition_ContainsFilter() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("contains");
        when(filterModel.toString()).thenReturn("value");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        List<Condition> conditions = dataAccessService.createCondition(filters);

        assertNotNull(conditions, "Conditions list should not be null");
        assertEquals(1, conditions.size(), "Conditions list should contain 1 element");
        assertEquals(DSL.field("column1").like("%value%"), conditions.get(0),
                "Condition should match CONTAINS expression");
    }

    @Test
    public void testCreateCondition_InvalidFilterType() {

        FilterModel filterModel = Mockito.mock(FilterModel.class);
        when(filterModel.type()).thenReturn("invalidType");

        Map<String, FilterModel> filters = new HashMap<>();
        filters.put("column1", filterModel);

        assertThrows(IllegalArgumentException.class, () -> dataAccessService.createCondition(filters),
                "IllegalArgumentException should be thrown for invalid filter type");
    }

    @Test
    public void testCreateCondition_NullFilterModel() {

        List<Condition> conditions = dataAccessService.createCondition(null);

        assertNotNull(conditions, "Conditions list should not be null even when filterModel is null");
        assertEquals(0, conditions.size(), "Conditions list should be empty when filterModel is null");
    }

    @Test
    public void testComputeAllMetrics_Success() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        String dbAlias = studyId.toLowerCase();
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        Field databaseAliasesField = DataAccessService.class.getDeclaredField("databaseAliases");
        databaseAliasesField.setAccessible(true);
        @SuppressWarnings("unchecked")
        CopyOnWriteArraySet<String> databaseAliases = (CopyOnWriteArraySet<String>) databaseAliasesField
                .get(dataAccessService);
        databaseAliases.add(dbAlias);
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString(), anyString(), anyString()))
                .thenReturn(1);

        Map<String, Object> queryResult = new HashMap<>();
        queryResult.put("time_in_tight_range_percentage", 75);
        queryResult.put("liability_index", 0.5);
        when(jdbcTemplate.queryForMap(anyString())).thenReturn(queryResult);

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.computeAllMetrics(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertNotNull(response.getData());
        assertEquals(75, ((Map<?, ?>) response.getData().get("allMetrics")).get("time_in_tight_range_percentage"));
    }

    @Test
    public void testComputeAllMetrics_NoRecordsFound() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        String dbAlias = studyId.toLowerCase();
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        Field databaseAliasesField = DataAccessService.class.getDeclaredField("databaseAliases");
        databaseAliasesField.setAccessible(true);
        @SuppressWarnings("unchecked")
        CopyOnWriteArraySet<String> databaseAliases = (CopyOnWriteArraySet<String>) databaseAliasesField
                .get(dataAccessService);
        databaseAliases.add(dbAlias);

        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString(), anyString(), anyString()))
                .thenReturn(0);

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.computeAllMetrics(studyId, participantId,
                startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("allMetrics successfully fetched", response.getMessage());
        assertNotNull(response.getData());

        Map<?, ?> allMetrics = (Map<?, ?>) response.getData().get("allMetrics");
        assertNotNull(allMetrics);

    }

    @Test
    public void testComputeAllMetrics_InvalidDatabaseAlias() {
        String studyId = "invalidstudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.computeAllMetrics(studyId, participantId,
                startDate, endDate);

        ExecutionException exception = assertThrows(ExecutionException.class, futureResult::get);
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Database 'invalidstudy' is not attached.", exception.getCause().getMessage());
    }

    @Test
    public void testComputeAllMetrics_DataAccessException_MaxRetriesExceeded() throws Exception {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";

        String dbAlias = studyId.toLowerCase();
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString(), anyString(), anyString()))
                .thenThrow(new DataAccessException("Database error") {
                });

        CompletionException thrown = assertThrows(CompletionException.class, () -> {
            CompletableFuture<JdbcResponse> response = dataAccessService.computeAllMetrics(studyId, participantId,
                    startDate, endDate);
            response.join();
        });

        Throwable actualCause = thrown.getCause();

        System.out.println("Actual error message: " + actualCause.getMessage());

        assertTrue(actualCause instanceof IllegalArgumentException,
                "Expected IllegalArgumentException but got: " + actualCause.getClass().getSimpleName());

        assertTrue(actualCause.getMessage().contains("Database '" + dbAlias + "' is not attached."),
                "Expected error message not found. Actual: " + actualCause.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetDailyGlucoseProfile_Success() {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";

        String dbAlias = studyId.toLowerCase();
        attachDatabase(dbAlias);

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("date_time", "2024-01-01 08:00:00");
        mockResult.put("date", "2024-01-01");
        mockResult.put("hour", "08:00");
        mockResult.put("glucose", 5.5);

        when(jdbcTemplate.query(anyString(),
                any(RowMapper.class),
                eq(participantId),
                eq(startDate),
                eq(endDate)))
                .thenReturn(Collections.singletonList(mockResult));

        CompletableFuture<JdbcResponse> futureResponse = spyDataAccessService.getDailyGlucoseProfile(studyId,
                participantId, startDate, endDate);

        JdbcResponse response = futureResponse.join();

        assertEquals("success", response.getStatus());
        assertEquals("daily_glucose_profile successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().containsKey("daily_glucose_profile"));

        List<Map<String, Object>> dailyGlucoseProfile = (List<Map<String, Object>>) response.getData()
                .get("daily_glucose_profile");
        assertNotNull(dailyGlucoseProfile);
        assertFalse(dailyGlucoseProfile.isEmpty());
        assertEquals(mockResult, dailyGlucoseProfile.get(0));
    }

    private void attachDatabase(String dbAlias) {

        try {
            Field field = DataAccessService.class.getDeclaredField("databaseAliases");
            field.setAccessible(true);
            CopyOnWriteArraySet<String> aliases = (CopyOnWriteArraySet<String>) field.get(dataAccessService);
            aliases.add(dbAlias); // Add the alias
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace(); // Handle or log appropriately
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetDailyGlucoseProfile_NoData() {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";

        String dbAlias = studyId.toLowerCase();
        addDatabaseAlias(dbAlias);

        try {
            Field field = DataAccessService.class.getDeclaredField("databaseAliases");
            field.setAccessible(true);
            CopyOnWriteArraySet<String> aliases = (CopyOnWriteArraySet<String>) field.get(spyDataAccessService);

            assertNotNull(aliases, "databaseAliases should not be null");
            assertTrue(aliases.contains(dbAlias), "Alias was not added correctly!");
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }

        when(jdbcTemplate.query(anyString(),
                any(RowMapper.class),
                eq(participantId),
                eq(startDate),
                eq(endDate)))
                .thenReturn(Collections.emptyList());

        CompletableFuture<JdbcResponse> futureResponse = spyDataAccessService.getDailyGlucoseProfile(studyId,
                participantId, startDate, endDate);

        JdbcResponse response = futureResponse.join();

        assertEquals("success", response.getStatus());
        assertEquals("daily_glucose_profile successfully fetched", response.getMessage());
        assertEquals("No data available for this duration", response.getErrors());
    }

    @SuppressWarnings("unchecked")
    private void addDatabaseAlias(String dbAlias) {
        try {
            Field field = DataAccessService.class.getDeclaredField("databaseAliases");
            field.setAccessible(true);
            CopyOnWriteArraySet<String> aliases = (CopyOnWriteArraySet<String>) field.get(dataAccessService);

            if (aliases == null) {
                System.out.println("Database alias set is null!");
                aliases = new CopyOnWriteArraySet<>();
                field.set(dataAccessService, aliases);
            }

            aliases.add(dbAlias);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetDailyGlucoseProfile_InvalidStudyId() {

        String invalidStudyId = "invalidStudy";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.getDailyGlucoseProfile(invalidStudyId,
                participantId, startDate, endDate);
        CompletionException thrown = assertThrows(CompletionException.class, futureResponse::join);
        assertTrue(thrown.getCause() instanceof IllegalArgumentException);
        assertEquals("Database 'invalidstudy' is not attached.", thrown.getCause().getMessage());
    }

    @Test
    public void testGetAmbulatoryGlucoseProfile_DatabaseNotAttached() {
        String studyId = "non_existing_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.getAmbulatoryGlucoseProfile(
                studyId, participantId, startDate, endDate);

        CompletionException thrown = assertThrows(CompletionException.class, () -> {
            futureResponse.join();
        });

        Throwable cause = thrown.getCause();
        assertTrue(cause instanceof IllegalArgumentException);
        assertTrue(cause.getMessage().contains("Database 'non_existing_study' is not attached."));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetAmbulatoryGlucoseProfile_DataAccessExceptions() {

        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-01";
        String dbAlias = studyId.toLowerCase();

        try {
            Field databaseAliasesField = DataAccessService.class.getDeclaredField("databaseAliases");
            databaseAliasesField.setAccessible(true);
            CopyOnWriteArraySet<String> databaseAliases = (CopyOnWriteArraySet<String>) databaseAliasesField
                    .get(dataAccessService);
            databaseAliases.remove(dbAlias);

            System.out.println("Database aliases after removal: " + databaseAliases);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }

        assertFalse(dataAccessService.getDatabaseAliases().contains(dbAlias),
                "Database alias should NOT be present: " + dbAlias);

        AtomicInteger retryCounter = new AtomicInteger(0);
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), anyString(), anyString(), anyString()))
                .thenAnswer(invocation -> {
                    int attempt = retryCounter.incrementAndGet();
                    System.out.println("Query attempt: " + attempt);
                    if (attempt <= 3) {
                        throw new DataAccessException("Database error") {
                        };
                    }
                    return null;
                });

        CompletableFuture<JdbcResponse> futureResponse = dataAccessService.getAmbulatoryGlucoseProfile(
                studyId, participantId, startDate, endDate);

        CompletionException thrown = assertThrows(CompletionException.class, futureResponse::join);

        Throwable cause = thrown.getCause();
        assertNotNull(cause, "Expected cause to be non-null");

        System.out.println("Actual cause class: " + cause.getClass().getName());

        assertTrue(cause instanceof IllegalArgumentException,
                "Expected cause to be IllegalArgumentException, but was: " + cause.getClass().getSimpleName());

    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInRange_succcess() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        Map<String, Object> expectedMap = new HashMap<>();
        expectedMap.put("time_in_range_percentage", "75.0");
        expectedMap.put("time_in_range", "15");
        expectedMap.put("time_range_string", "02 hours, 30 minutes");
        expectedMap.put("CGM_Value", "100");

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class),
                eq(participantId), eq(startDate),
                eq(endDate)))
                .thenReturn(expectedMap);

        attachDatabase(studyId);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(studyId,
                "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeInRange(
                studyId, participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("time_in_range_percentage successfully fetched",
                response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);

        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        Map<String, Object> resultMap = (Map<String, Object>) responseData.get("time_in_range_percentage");

        assertEquals("75.0", resultMap.get("time_in_range_percentage"));
        assertEquals("15", resultMap.get("time_in_range"));
        assertEquals("02 hours, 30 minutes", resultMap.get("time_range_string"));
        assertEquals("100", resultMap.get("CGM_Value"));
    }

    @Test
    public void testCalculateTimeInRange_databaseNotAttached() {
        String studyId = "missingStudyId";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        ExecutionException exception = assertThrows(ExecutionException.class, () -> {
            dataAccessService.calculateTimeInRange(studyId, participantId, startDate, endDate).get();
        });

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Database '" + studyId.toLowerCase() + "' is not attached.", exception.getCause().getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInRange_noDataFound() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        String dbAlias = studyId.toLowerCase();

        attachDatabase(dbAlias);

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException(1)); // Mocking no result found

        CompletableFuture<JdbcResponse> futureResult = spyDataAccessService.calculateTimeInRange(studyId, participantId,
                startDate, endDate);

        JdbcResponse response = futureResult.get();

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("time_in_range_percentage successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);
        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        assertTrue(responseData.isEmpty());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeInRange_queryFailureWithRetries() throws Exception {
        String studyId = "teststudy";
        String participantId = "testParticipant";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";
        String dbAlias = studyId.toLowerCase();

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        CompletionException thrown = assertThrows(CompletionException.class, () -> {
            CompletableFuture<JdbcResponse> futureResult = spyDataAccessService.calculateTimeInRange(studyId,
                    participantId, startDate, endDate);
            futureResult.join();
        });

        Throwable actualCause = thrown.getCause();

        System.out.println("Actual error message: " + actualCause.getMessage());

        assertTrue(actualCause instanceof IllegalArgumentException,
                "Expected IllegalArgumentException but got: " + actualCause.getClass().getSimpleName());

        assertTrue(actualCause.getMessage().contains("Database '" + dbAlias + "' is not attached."),
                "Expected error message not found. Actual: " + actualCause.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeBelowRangeVeryLow_Success() throws Exception {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";

        Map<String, Object> expectedMap = new HashMap<>();
        expectedMap.put("time_below_range_very_low_percentage", "10.0");
        expectedMap.put("time_below_range_very_low", "5");
        expectedMap.put("time_range_string", "00 hours, 05 minutes");
        expectedMap.put("CGM_Value", "50");

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class),
                eq(participantId), eq(startDate),
                eq(endDate)))
                .thenReturn(expectedMap);

        attachDatabase(studyId);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(studyId, "path/to/database")));

        CompletableFuture<JdbcResponse> futureResult = dataAccessService.calculateTimeBelowRangeVeryLow(
                studyId, participantId, startDate, endDate);
        JdbcResponse response = futureResult.get();

        assertEquals("success", response.getStatus());
        assertEquals("time_below_range_very_low_percentage successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);

        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        Map<String, Object> resultMap = (Map<String, Object>) responseData.get("time_below_range_very_low_percentage");

        assertEquals("10.0", resultMap.get("time_below_range_very_low_percentage"));
        assertEquals("5", resultMap.get("time_below_range_very_low"));
        assertEquals("00 hours, 05 minutes", resultMap.get("time_range_string"));
        assertEquals("50", resultMap.get("CGM_Value"));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeBelowRangeVeryLow_NoData() throws Exception {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        String dbAlias = studyId.toLowerCase();

        attachDatabase(dbAlias);

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new EmptyResultDataAccessException("No data found", 1));

        CompletableFuture<JdbcResponse> futureResult = spyDataAccessService.calculateTimeBelowRangeVeryLow(
                studyId, participantId, startDate, endDate);

        JdbcResponse response = futureResult.get();

        assertNotNull(response);
        assertEquals("success", response.getStatus());
        assertEquals("time_below_range_very_low_percentage successfully fetched", response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData() instanceof Map);
        Map<String, Object> responseData = (Map<String, Object>) response.getData();
        assertTrue(responseData.isEmpty());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testCalculateTimeBelowRangeVeryLow_MaxRetriesExceeded() throws Exception {
        String studyId = "test_study";
        String participantId = "test_participant";
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        String dbAlias = studyId.toLowerCase();

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbAlias, "path/to/database")));

        when(jdbcTemplate.queryForObject(anyString(), any(RowMapper.class), eq(participantId), eq(startDate),
                eq(endDate)))
                .thenThrow(new DataAccessException("Database error") {
                });

        CompletionException thrown = assertThrows(CompletionException.class, () -> {
            CompletableFuture<JdbcResponse> futureResult = spyDataAccessService.calculateTimeBelowRangeVeryLow(
                    studyId, participantId, startDate, endDate);
            futureResult.join();
        });

        Throwable actualCause = thrown.getCause();

        System.out.println("Actual error message: " + actualCause.getMessage());

        assertTrue(actualCause instanceof IllegalArgumentException,
                "Expected IllegalArgumentException but got: " + actualCause.getClass().getSimpleName());
        assertTrue(actualCause.getMessage().contains("Database '" + dbAlias + "' is not attached."),
                "Expected error message not found. Actual: " + actualCause.getMessage());
    }

    @Test
    public void testGetStudyTotalCgmFiles() throws Exception {
        String dbName = "testdb";
        Map<String, Object> mockResult = Map.of("total_count", 500);
        when(jdbcTemplate
                .queryForMap("SELECT total_count FROM " + dbName.toLowerCase() +
                        ".study_cgm_file_count_cached;"))
                .thenReturn(mockResult);
        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbName, "path/to/database")));

        Map<String, Object> result = dataAccessService.getStudyTotalCgmFiles(dbName);
        verify(jdbcTemplate)
                .queryForMap("SELECT total_count FROM " + dbName.toLowerCase() +
                        ".study_cgm_file_count_cached;");
        assert result.equals(mockResult);
    }

    @Test
    public void testGetIndividualStudyDetails() throws Exception {
        String dbName = "testdb";
        Map<String, Object> mockResult = Map.of(
                "study_id", 1,
                "study_name", "Test Study",
                "study_description", "Description",
                "start_date", "2024-01-01",
                "end_date", "2024-12-31",
                "nct_number", "NCT123456",
                "total_number_of_participants", 100,
                "average_age", 50,
                "percentage_of_females", "40%",
                "investigators", "Dr. Smith");
        when(jdbcTemplate.queryForMap(
                "SELECT study_id, study_name, study_description, start_date, end_date, nct_number, total_number_of_participants, average_age, CONCAT(percentage_of_females,'%') as percentage_of_females, investigators FROM "
                        + dbName.toLowerCase() + ".study_details_cached ;"))
                .thenReturn(mockResult);

        setAttachedDatabases(new ConcurrentHashMap<>(Map.of(dbName, "path/to/database")));

        Map<String, Object> result = dataAccessService.getIndividualStudyDetails(dbName);

        verify(jdbcTemplate).queryForMap(
                "SELECT study_id, study_name, study_description, start_date, end_date, nct_number, total_number_of_participants, average_age, CONCAT(percentage_of_females,'%') as percentage_of_females, investigators FROM "
                        + dbName.toLowerCase() + ".study_details_cached ;");
        assert result.equals(mockResult);
    }

}
