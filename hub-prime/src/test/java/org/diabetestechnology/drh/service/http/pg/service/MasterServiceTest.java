package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.Condition;

public class MasterServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private ResultQuery<Record1<String>> queryMock;

    @InjectMocks
    private MasterService masterService;

    @Mock
    private ObjectMapper mockObjectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyVisibility_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"visibility_id\": 1, \"visibility_name\": \"Public\", \"visibility_description\": \"Visible to all\", \"rec_status_id\": 1}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getStudyVisibility();

        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> expected = mapper.readValue(jsonResponse, new TypeReference<>() {
        });

        assertEquals(expected, actualResult);
    }

    @SuppressWarnings("unchecked")

    @Test
    void testGetStudyVisibility_ReturnsEmptyJson_WhenJsonProcessingFails() {

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null); // Simulate no result

        Object actualResult = masterService.getStudyVisibility();

        assertEquals("{}", actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRaceType_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"race_type_id\": 1, \"code\": \"A\", \"system_uri\": \"uri\", \"system_oid\": \"oid\", \"display\": \"Display1\", \"race_text\": \"Text1\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getRaceType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = mapper.writeValueAsString(mapper.readValue(jsonResponse, Object.class));

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRaceType_ReturnsEmptyJson_WhenExceptionOccursDuringJsonProcessing() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getRaceType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);

        String expectedJson = "{}";

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetEthnicityType_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"ethnicity_type_id\": 1, \"code\": \"A\", \"system_uri\": \"uri\", \"system_oid\": \"oid\", \"display\": \"Display1\", \"ethnicity_text\": \"Text1\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getEthnicityType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);

        Object expectedObject = mapper.readValue(jsonResponse, Object.class);
        String expectedJson = mapper.writeValueAsString(expectedObject);

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetEthnicityType_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getEthnicityType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = "{}";

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetGenderType_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"gender_type_id\": 1, \"code\": \"M\", \"system_uri\": \"uri1\", \"system_oid\": \"oid1\", \"display\": \"Male\", \"gender_text\": \"Male Text\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        Object actualResult = masterService.getGenderType();
        ObjectMapper mapper = new ObjectMapper();

        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = mapper.writeValueAsString(mapper.readValue(jsonResponse, Object.class));

        assertEquals(expectedJson, actualJson);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetGenderType_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);
        Object actualResult = masterService.getEthnicityType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = "{}";

        assertEquals(expectedJson, actualJson);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetProfileStatusType_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockObjectMapper.readValue(any(String.class), eq(Object.class)))
                .thenThrow(new RuntimeException("Test exception"));

        Object actualResult = masterService.getProfileStatusType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = "{}";

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetProfileStatusType_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"profile_status_type_id\": 1, \"code\": \"ACTIVE\", \"description\": \"Active Status\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getProfileStatusType();
        ObjectMapper mapper = new ObjectMapper();

        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = mapper.writeValueAsString(mapper.readValue(jsonResponse, Object.class));

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetProfileStatusType_ReturnsEmptyJson_WhenJsonProcessingFailss() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockObjectMapper.readValue(any(String.class), eq(Object.class)))
                .thenThrow(new RuntimeException("Test exception"));
        Object actualResult = masterService.getProfileStatusType();

        ObjectMapper mapper = new ObjectMapper();
        String actualJson = mapper.writeValueAsString(actualResult);
        String expectedJson = "{}";

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCitationStatus_ReturnsJson_WhenQuerySucceeds() throws Exception {
        String jsonResponse = "[{\"code\": \"CITATION1\", \"display_name\": \"Citation One\", \"definition\": \"Definition 1\", \"system_url\": \"url1\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getCitationStatus();

        Object expectedObject = new ObjectMapper().readValue(jsonResponse, Object.class);

        assertEquals(expectedObject, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCitationStatus_ThrowsException_WhenJsonProcessingFails() throws Exception {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("Test exception"));

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        assertThrows(RuntimeException.class, () -> masterService.getCitationStatus());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointSystem_ReturnsJson_WhenDataIsValid() throws Exception {

        String jsonResponse = "[{\"id\": 1, \"code\": \"CONTACT1\", \"system\": \"SYSTEM1\", \"value\": \"value1\", \"description\": \"Description1\"}]";
        JSONB mockResult = mock(JSONB.class);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockResult.data()).thenReturn(jsonResponse);

        Object actualResult = masterService.getContactPointSystem();

        Object expectedResult = JsonUtils.jsonStringToMapOrList(jsonResponse);

        assertEquals(expectedResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointSystem_ThrowsException_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("Test exception"));

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        assertThrows(RuntimeException.class, () -> masterService.getContactPointSystem());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigatorStudyRole_ReturnsJson_WhenDataIsValid() throws Exception {

        String jsonResponse = "[{\"code\": \"INV1\", \"role\": \"Principal Investigator\"}]";
        JSONB mockResult = mock(JSONB.class);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockResult.data()).thenReturn(jsonResponse);

        Object actualResult = masterService.getInvestigatorStudyRole();

        Object expectedObject = new ObjectMapper().readValue(jsonResponse, Object.class);

        assertEquals(expectedObject, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetInvestigatorStudyRole_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null); // Simulate JSON processing failure

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getInvestigatorStudyRole();

        ObjectMapper objectMapper = new ObjectMapper();
        String expectedJson = objectMapper.writeValueAsString(objectMapper.readValue("{}", Object.class));
        String actualJson = objectMapper.writeValueAsString(actualResult);

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoincCodes_ReturnsJson_WhenDataIsValid() throws Exception {

        String jsonResponse = "[{\"loinc_code_id\": 1, \"loinc_code\": \"12345-6\", \"loinc_description\": \"Glucose [Mass/volume] in Blood\", \"loinc_class\": \"CHEM\", \"loinc_type\": \"Lab\", \"rec_status_id\": 1}]";
        JSONB mockResult = mock(JSONB.class);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockResult.data()).thenReturn(jsonResponse);

        Object actualResult = masterService.getLoincCodes();

        ObjectMapper objectMapper = new ObjectMapper();
        String expectedJson = objectMapper.writeValueAsString(objectMapper.readValue(jsonResponse, Object.class));
        String actualJson = objectMapper.writeValueAsString(actualResult);

        assertEquals(expectedJson, actualJson);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoincCodes_ReturnsEmptyJson_WhenJsonProcessingFails() throws Exception {

        JSONB mockResult = null;

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult); // Return null

        Object actualResult = masterService.getLoincCodes();

        assertEquals("{}", actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetMetricDefinitions_ReturnsJson_WhenDataIsValid() throws Exception {

        String jsonResponse = "[{\"metric_id\": 1, \"metric_name\": \"Blood Pressure\", \"metric_info\": \"Systolic and Diastolic values\"}]";
        JSONB mockResult = mock(JSONB.class);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        when(mockResult.data()).thenReturn(jsonResponse);

        Object actualResult = masterService.getMetricDefinitions();

        ObjectMapper objectMapper = new ObjectMapper();
        Object expectedResult = objectMapper.readValue(jsonResponse, Object.class);

        assertEquals(expectedResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetMetricDefinitions_ThrowsException_WhenJsonProcessingFails() {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("Test exception"));

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        assertThrows(RuntimeException.class, () -> masterService.getMetricDefinitions());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetOrganizationType_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"organization_type_id\": 1, \"code\": \"Code1\", \"system_uri\": \"uri1\", \"display\": \"Display1\", \"description\": \"Desc1\", \"rec_status_id\": 1}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getOrganizationType();

        ObjectMapper mapper = new ObjectMapper();
        Object expectedJson = mapper.readValue(jsonResponse, Object.class);

        assertEquals(expectedJson, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetOrganizationType_ThrowsException_WhenJsonProcessingFails() {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("Test Exception")); // Simulate failure

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        assertThrows(RuntimeException.class, () -> masterService.getOrganizationType());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyCondition_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"id\": 1, \"coding_system\": \"ICD-10\", \"code\": \"A00\", \"display\": \"Cholera\", \"rec_status_id\": 1}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyCondition();

        ObjectMapper mapper = new ObjectMapper();
        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class); // Normalize actual result

        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyCondition_ReturnsEmptyJson_WhenJsonProcessingFails() throws JsonProcessingException {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyCondition();
        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyFocus_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"id\": 1, \"coding_system\": \"LOINC\", \"code\": \"12345-6\", \"display\": \"Blood Pressure\", \"rec_status_id\": 1}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyFocus();

        ObjectMapper mapper = new ObjectMapper();
        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class); // Normalize actual result

        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyFocus_ReturnsEmptyJson_WhenJsonProcessingFails()
            throws JsonMappingException, JsonProcessingException {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyCondition();
        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyPartyRole_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"study_party_role_id\": 1, \"code\": \"INV\", \"system\": \"http://example.com\", \"display\": \"Investigator\", \"description\": \"Lead investigator of the study\", \"rec_status_id\": 1}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyPartyRole();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class);
        assertEquals(expectedJsonObject, actualJsonObject);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchStudyPartyRole_ReturnsEmptyJson_WhenJsonProcessingFails()
            throws JsonMappingException, JsonProcessingException {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchStudyCondition();
        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchSubjectStatus_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"code\": \"active\", \"display_name\": \"Active\", \"definition\": \"Subject is actively participating\", \"system_url\": \"http://example.com\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchSubjectStatus();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class); // Normalize actual result

        // Assert both are equivalent JSON objects
        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetResearchSubjectStatus_ReturnsEmptyJson_WhenJsonProcessingFails()
            throws JsonMappingException, JsonProcessingException {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getResearchSubjectStatus();

        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyStatus_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"code\": \"ongoing\", \"display_name\": \"Ongoing\", \"definition\": \"Study is currently in progress\", \"system_url\": \"http://example.com\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getStudyStatus();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class); // Normalize actual result

        assertEquals(expectedJsonObject, actualJsonObject);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetStudyStatus_ReturnsEmptyJson_WhenResultIsNull() {

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object actualResult = masterService.getStudyStatus();
        assertNotNull(actualResult);

        assertEquals("{}", actualResult);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointUse_ReturnsEmptyJson_WhenResultIsNull() {

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null); // Mock null result

        Object result = masterService.getContactpointuse();

        assertNotNull(result);
        assertEquals("{}", result);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactpointuse_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"id\": 1, \"code\": \"home\", \"system\": \"phone\", \"description\": \"Home phone\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getContactpointuse();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class); // Normalize actual result

        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointAddressuse_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[{\"id\": 1, \"code\": \"home\", \"system\": \"phone\", \"value\": \"12345\", \"description\": \"Home phone\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getContactPointAddressuse();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class);

        // Assert both are equivalent JSON objects
        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointAddressuse_ReturnsEmptyJson_WhenJsonProcessingFails()
            throws JsonMappingException, JsonProcessingException {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getContactPointAddressuse();

        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointUseView_ReturnsFormattedJson() throws Exception {
        String jsonResponse = "[{\"id\": 1, \"code\": \"work\", \"system\": \"phone\", \"value\": \"98765\", \"description\": \"Work phone\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getcontactpointuseview();
        ObjectMapper mapper = new ObjectMapper();

        Object expectedJsonObject = mapper.readValue(jsonResponse, Object.class);
        Object actualJsonObject = mapper.convertValue(actualResult, Object.class);

        assertEquals(expectedJsonObject, actualJsonObject);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetContactPointUseView_ReturnsEmptyJson_WhenJsonProcessingFails()
            throws JsonMappingException, JsonProcessingException {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = masterService.getcontactpointuseview();

        ObjectMapper mapper = new ObjectMapper();
        assertEquals(mapper.readValue("{}", Object.class), actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadGenderType_ReturnsValidJson() {
        String jsonResponse = "[{\"gender_type_id\": 1, \"code\": \"M\", \"value\": \"Male\"}, " +
                "{\"gender_type_id\": 2, \"code\": \"F\", \"value\": \"Female\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        JSONB actualResult = masterService.readGenderType();

        assertNotNull(actualResult);
        assertEquals(mockResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadGenderType_ReturnsNull_WhenNoDataFound() {
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB actualResult = masterService.readGenderType();

        assertNull(actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadRaceType_ReturnsValidJson() {
        String jsonResponse = "[{\"race_type_id\": 1, \"code\": \"AS\", \"system_uri\": \"http://race-system\", " +
                "\"system_oid\": \"2.16.840.1.113883.6.238\", \"display\": \"Asian\", \"race_text\": \"Asian\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        JSONB actualResult = masterService.readRaceType();

        assertNotNull(actualResult);
        assertEquals(mockResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadRaceType_ReturnsNull_WhenNoDataFound() {
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB actualResult = masterService.readRaceType();

        assertNull(actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadEthnicityType_ReturnsValidJson() {
        String jsonResponse = "[{\"ethnicity_type_id\": 1, \"code\": \"H\", \"system_uri\": \"http://ethnicity-system\", "
                +
                "\"system_oid\": \"2.16.840.1.113883.6.238\", \"display\": \"Hispanic\", \"ethnicity_text\": \"Hispanic or Latino\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        JSONB actualResult = masterService.readEthnicityType();

        assertNotNull(actualResult);
        assertEquals(mockResult, actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testReadEthnicityType_ReturnsNull_WhenNoDataFound() {
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB actualResult = masterService.readEthnicityType();

        assertNull(actualResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRaceIdByName_ReturnsRaceId() {
        String raceName = "Asian";
        String expectedRaceId = "123";

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(expectedRaceId);

        Object actualRaceId = masterService.getRaceIdByName(raceName);

        assertNotNull(actualRaceId);
        assertEquals(expectedRaceId, actualRaceId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRaceIdByName_ReturnsNull_WhenRaceNotFound() {

        String raceName = "NonExistentRace";

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(null);

        Object actualRaceId = masterService.getRaceIdByName(raceName);

        assertNull(actualRaceId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetEthnicityIdByName_ReturnsEthnicityId() {

        String ethnicityName = "Hispanic";
        String expectedEthnicityId = "456";

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(expectedEthnicityId);

        Object actualEthnicityId = masterService.getEthnicityIdByName(ethnicityName);

        assertNotNull(actualEthnicityId);
        assertEquals(expectedEthnicityId, actualEthnicityId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetEthnicityIdByName_ReturnsNull_WhenEthnicityNotFound() {

        String ethnicityName = "NonExistentEthnicity";

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(null);

        Object actualEthnicityId = masterService.getEthnicityIdByName(ethnicityName);

        assertNull(actualEthnicityId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCollaboratorStudyRole() {

        String code = "code";
        String expectedRole = "author";

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(expectedRole);

        String role = masterService.getCollaboratorStudyRole(code);
        assertEquals(role, expectedRole);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

}
