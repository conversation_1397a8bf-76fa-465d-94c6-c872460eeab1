package org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

@ExtendWith(MockitoExtension.class)
public class AIServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private UserNameService userNameService;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private AIService aiService;

    @Mock
    private DataBaseAttachService databaseAttachService;

    final String alias = "client";

    @Test
    public void testSaveAIInteraction() {
        VannaAIRequest payload = new VannaAIRequest("sample question", "sample sql", "sample result");
        when(userNameService.getUserId()).thenReturn("testUserId");
        aiService.saveAIInteraction(payload, request);

        verify(jdbcTemplate, times(1)).update(
                anyString(),
                anyString(), // UUID
                eq(payload.question()),
                eq(payload.sql()),
                eq(payload.result()),
                anyString(), // created_at
                anyString(), // updated_at
                eq("testUserId"));
    }

    @Test
    public void testGetAIInteraction() {
        String userId = "testUserId";
        String sql = "SELECT id, question, sql_query, results, created_at, updated_at, created_by FROM " + alias
                + ".vanna_ai_request_respose where created_by= ? ORDER BY created_at";

        when(userNameService.getUserId()).thenReturn(userId);

        List<Map<String, Object>> expectedResults = new ArrayList<>();
        expectedResults.add(Map.of(
                "id", "1",
                "question", "sample question",
                "sql_query", "SELECT * FROM example",
                "results", "sample result",
                "created_at", "2023-01-01 10:00:00",
                "updated_at", "2023-01-02 12:00:00",
                "created_by", userId));

        when(jdbcTemplate.queryForList(sql, userId)).thenReturn(expectedResults);

        // Call the method
        List<Map<String, Object>> actualResults = aiService.getAIInteraction(request);

        // Assertions
        assertEquals(expectedResults, actualResults);

        // Verify interactions
        verify(databaseAttachService, times(1)).refreshClientServerDb();
        verify(userNameService, times(1)).getUserId();
        verify(jdbcTemplate, times(1)).queryForList(sql, userId);
    }

    @Test
    public void testGetAIInteraction_ErrorHandling() {
        when(userNameService.getUserId()).thenReturn("testUserId");
        when(jdbcTemplate.queryForList(anyString(), anyString())).thenThrow(new RuntimeException("Database error"));

        List<Map<String, Object>> results = aiService.getAIInteraction(request);

        assertEquals(new ArrayList<>(), results);
        verify(jdbcTemplate, times(1)).queryForList(anyString(), eq("testUserId"));
    }

    @Test
    public void testGetAllAIInteraction() {
        List<Map<String, Object>> expectedResults = new ArrayList<>();
        expectedResults.add(Map.of("id", "1", "question", "sample question"));
        when(jdbcTemplate.queryForList(anyString())).thenReturn(expectedResults);

        List<Map<String, Object>> results = aiService.getAllAIInteraction();

        assertEquals(expectedResults, results);
        verify(jdbcTemplate, times(1)).queryForList(anyString());
    }

    @Test
    public void testGetAllAIInteraction_ErrorHandling() {
        when(jdbcTemplate.queryForList(anyString())).thenThrow(new RuntimeException("Database error"));

        List<Map<String, Object>> results = aiService.getAllAIInteraction();

        assertEquals(new ArrayList<>(), results);
        verify(databaseAttachService, times(1)).refreshClientServerDb();

        verify(jdbcTemplate, times(1)).queryForList(anyString());
    }
}
