package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.diabetestechnology.drh.service.http.util.JsonUtils;

import org.jooq.DSLContext;
import org.jooq.Condition;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.ResultQuery;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.jooq.Field;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

public class CgmMasterServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private ResultQuery<Record1<String>> queryMock;

    @InjectMocks
    private CgmMasterService cgmMasterService;

    @Mock
    private ObjectMapper mockObjectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetSourcePlatformList_ReturnsFormattedJson() throws Exception {

        String jsonResponse = "[\"XYZ Corp\", \"ABC Medical\"]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = cgmMasterService.getSourcePlatformList();
        ObjectMapper mapper = new ObjectMapper();
        Object json = mapper.readValue(jsonResponse, Object.class);
        ObjectWriter writer = mapper.writerWithDefaultPrettyPrinter();
        String expectedResult = writer.writeValueAsString(json);

        assertEquals(List.of("XYZ Corp", "ABC Medical"), actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetSourcePlatformList_ReturnsEmptyJson_WhenJsonProcessingFails() {
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = cgmMasterService.getSourcePlatformList();

        assertEquals(Collections.emptyMap(), actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(org.jooq.Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCgmDevicesList_ReturnsFormattedJson() {
        String manufacturer = "XYZ Corp";
        String jsonResponse = "[{\"id\": 1, \"device_name\": \"Device A\"}]";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.and(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = cgmMasterService.getCgmDevicesList(manufacturer);

        assertEquals(JsonUtils.jsonStringToMapOrList(mockResult.data()), actualResult);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).and(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetCgmDevicesList_ReturnsEmptyJson_WhenNoDataFound() {
        String manufacturer = "XYZ Corp";
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenReturn(null);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.and(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        Object actualResult = cgmMasterService.getCgmDevicesList(manufacturer);

        Object expectedEmptyJson = JsonUtils.jsonStringToMapOrList(null);

        assertEquals(expectedEmptyJson, actualResult);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).and(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

}
