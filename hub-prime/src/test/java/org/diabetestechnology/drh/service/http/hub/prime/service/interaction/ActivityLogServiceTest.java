package org.diabetestechnology.drh.service.http.hub.prime.service.interaction;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogDetails;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogLevel;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogMap;
import org.diabetestechnology.drh.service.http.hub.prime.service.request.ActivityLogRequest;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.SelectConditionStep;
import org.jooq.SelectField;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSeekStep1;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@ExtendWith(MockitoExtension.class)
class ActivityLogServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ObservabilityRequestFilter observabilityRequestFilter;

    @Mock
    private Presentation presentation;

    @Mock
    private DataBaseAttachService databaseAttachService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private DSLContext dsl;

    @Mock
    private HttpSession session;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private AuditService auditService;

    @Mock
    private PartyService partyService;

    @Mock
    private ActivityLogRequest activityLogRequest;

    @Mock
    private LogMap logMap;

    @Mock
    private ContentCachingRequestWrapper cachingRequest;

    @InjectMocks
    private ActivityLogService activityLogService;

    private ActivityLog activityLog;

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @BeforeEach
    void setUp() {
        ServletRequestAttributes attributes = mock(ServletRequestAttributes.class);
        lenient().when(attributes.getRequest()).thenReturn(request);
        lenient().when(attributes.getResponse()).thenReturn(response);

        RequestContextHolder.setRequestAttributes(attributes);
        lenient().when(request.getSession()).thenReturn(session);

        // Initialize the activityLog object with mock data
        activityLog = new ActivityLog();
        activityLog.setHierarchyPath("some/hierarchy/path");
        activityLog.setSessionUniqueId("unique-id");
        activityLog.setActivityName("test-activity");
        activityLog.setActivityType("test-type");
        activityLog.setActivityDescription("test-description");
        activityLog.setCreatedBy("test-user");
        activityLog.setActivityData(new HashMap());
        activityLog.setRequestUrl("http://example.com");
        activityLog.setActivityLogLevel(0);
        activityLog.setSessionId("session-id");
        activityLog.setIpAddress("***********");
        activityLog.setUserName("test-username");
        activityLog.setAppVersion("1.0");
        activityLog.setActivityHierarchy("test-hierarchy");

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAuditDataFromRequestAndResponse() throws Exception {
        // Given
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";
        String uniqueSessionId = "unique-session-id";
        String userId = "test-user";
        String userName = "Test User";
        String version = "1.0.0";

        SelectSelectStep<Record2<Object, Object>> selectStep = mock(SelectSelectStep.class);
        SelectJoinStep<Record2<Object, Object>> fromStep = mock(SelectJoinStep.class);
        SelectConditionStep<Record2<Object, Object>> whereStep = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep2 = mock(SelectConditionStep.class);
        SelectSeekStep1<Record2<Object, Object>, Object> orderStep = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record2<Object, Object>> limitStep = mock(SelectLimitPercentStep.class);

        // Mock DSL chain
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId)))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("session_unique_id").eq(uniqueSessionId)))
                .thenReturn(andStep1);
        when(andStep1.and(DSL.field("activity_log_level").ne("Level 6")))
                .thenReturn(andStep2);
        when(andStep2.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);

        when(request.getMethod()).thenReturn("GET");
        when(request.getHeader("User-Agent")).thenReturn("test-agent");
        when(request.getRemoteUser()).thenReturn("remote-user");
        when(request.getRemoteHost()).thenReturn("remote-host");
        when(session.getId()).thenReturn(sessionId);
        when(response.getStatus()).thenReturn(200);

        when(userNameService.getUserId()).thenReturn(userId);
        when(userNameService.getUserName()).thenReturn(userName);
        when(presentation.getVersion()).thenReturn(version);
        when(observabilityRequestFilter.getUniqueSession(eq(requestUrl), any(ContentCachingRequestWrapper.class)))
                .thenReturn(uniqueSessionId);

        Map<String, Object> queryResult = Map.of(
                "hierarchy_path", "parent/path",
                "activity_hierarchy", "parent/activity");
        when(limitStep.fetchMaps()).thenReturn(List.of(queryResult));

        ActivityLog result = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);

        assertNotNull(result);

        assertEquals(requestUrl, result.getRequestUrl());
        assertEquals(userId, result.getCreatedBy());
        assertEquals(userName, result.getUserName());
        assertEquals(sessionId, result.getSessionId());
        assertEquals(uniqueSessionId, result.getSessionUniqueId());
        assertEquals(version, result.getAppVersion());

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAuditDataWithDatabaseError() throws Exception {
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";
        String uniqueSessionId = "unique-session-id";

        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn(sessionId);
        when(observabilityRequestFilter.getUniqueSession(eq(requestUrl), any(ContentCachingRequestWrapper.class)))
                .thenReturn(uniqueSessionId);
        lenient().when(presentation.getVersion()).thenReturn("1.0.0");

        SelectSelectStep<Record2<Object, Object>> selectStep = mock(SelectSelectStep.class);
        SelectJoinStep<Record2<Object, Object>> fromStep = mock(SelectJoinStep.class);
        SelectConditionStep<Record2<Object, Object>> whereStep = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep2 = mock(SelectConditionStep.class);
        SelectSeekStep1<Record2<Object, Object>, Object> orderStep = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record2<Object, Object>> limitStep = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy"))).thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log")).thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId))).thenReturn(whereStep);
        when(whereStep.and(DSL.field("session_unique_id").eq(uniqueSessionId))).thenReturn(andStep1);
        when(andStep1.and(DSL.field("activity_log_level").ne("Level 6"))).thenReturn(andStep2);
        when(andStep2.orderBy(DSL.field("created_at").desc())).thenReturn(orderStep);
        when(orderStep.limit(1)).thenReturn(limitStep);

        when(limitStep.fetchMaps())
                .thenThrow(new RuntimeException("Database error"))
                .thenReturn(Collections.emptyList());

        ActivityLog result = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);

        assertNotNull(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAuditDataWithLogDetails() throws Exception {
        // Given
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";
        String uniqueSessionId = "unique-session-id";
        String version = "0.1.0";

        LogDetails logDetails = new LogDetails("Test Activity", "TEST", "Test Description", requestUrl,
                LogLevel.DEFAULT);
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Map.of(requestUrl, logDetails));
        activityLogService.logMap = logMap;

        when(request.getMethod()).thenReturn("GET");
        when(request.getRemoteUser()).thenReturn("remote-user");
        when(request.getRemoteHost()).thenReturn("remote-host");
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn(sessionId);
        when(response.getStatus()).thenReturn(200);

        when(observabilityRequestFilter.getUniqueSession(eq(requestUrl), any(ContentCachingRequestWrapper.class)))
                .thenReturn(uniqueSessionId);
        when(presentation.getVersion()).thenReturn(version);

        SelectSelectStep<Record2<Object, Object>> selectStep = mock(SelectSelectStep.class);
        SelectJoinStep<Record2<Object, Object>> fromStep = mock(SelectJoinStep.class);
        SelectConditionStep<Record2<Object, Object>> whereStep = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStep2 = mock(SelectConditionStep.class);
        SelectSeekStep1<Record2<Object, Object>, Object> orderStep = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record2<Object, Object>> limitStep = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy"))).thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log")).thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId))).thenReturn(whereStep);
        when(whereStep.and(DSL.field("session_unique_id").eq(uniqueSessionId))).thenReturn(andStep1);
        when(andStep1.and(DSL.field("activity_log_level").ne("Level 6"))).thenReturn(andStep2);
        when(andStep2.orderBy(DSL.field("created_at").desc())).thenReturn(orderStep);
        when(orderStep.limit(1)).thenReturn(limitStep);

        when(limitStep.fetchMaps()).thenReturn(Collections.emptyList());

        ActivityLog result = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);

        assertNotNull(result);
        assertEquals(logDetails.getActivityName(), result.getActivityName());
        assertEquals(logDetails.getActivityType(), result.getActivityType());
        assertEquals(logDetails.getActivityDescription(), result.getActivityDescription());
        assertEquals(logDetails.getActivityLogLevel(), result.getActivityLogLevel());
        assertEquals(version, result.getAppVersion());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveActivityLog_Success()
            throws JsonProcessingException, UnsupportedEncodingException, UnknownHostException, InterruptedException {
        ActivityLog activityLog = new ActivityLog();
        activityLog.setSessionUniqueId("");
        activityLog.setActivityName("test-activity");
        activityLog.setActivityType("test-type");
        activityLog.setActivityDescription("test-description");
        activityLog.setCreatedBy("test-user");
        activityLog.setActivityData(new HashMap<>());
        activityLog.setRequestUrl("http://example.com");
        activityLog.setActivityLogLevel(0);
        activityLog.setSessionId("session-id");
        activityLog.setIpAddress("***********");
        activityLog.setUserName("test-username");
        activityLog.setAppVersion("1.0");
        activityLog.setActivityHierarchy("test-hierarchy");
        activityLog.setHierarchyPath("some/hierarchy/path");

        when(auditService.getCurrentRequest()).thenReturn(request);
        when(userNameService.getUserId()).thenReturn("userId123");
        when(partyService.getPartyIdByUserId("userId123")).thenReturn("partyId123");
        when(partyService.getOrganizationPartyIdByUser("userId123")).thenReturn("orgParty");
        when(observabilityRequestFilter.getUniqueSession(anyString(), any())).thenReturn("unique-session-id");
        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(SelectField.class))).thenReturn(selectMock);
        activityLogService.saveActivityLog(activityLog);
        verify(dsl, times(1)).select(any(SelectField.class));
    }

    @Test
    void testSaveActivityLog_WhenJsonProcessingExceptionOccurs()
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException, JsonProcessingException {
        ActivityLog activityLog = new ActivityLog();
        activityLog.setSessionUniqueId("unique-id");
        activityLog.setActivityName("test-activity");

        lenient().when(objectMapper.writeValueAsString(any()))
                .thenThrow(new JsonProcessingException("JSON processing error") {
                });
        assertDoesNotThrow(() -> activityLogService.saveActivityLog(activityLog));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAuditDataFromUrlRequestAndResponse_Success()
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException {

        SelectSelectStep<Record2<Object, Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record2<Object, Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record2<Object, Object>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStepMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record2<Object, Object>> andStepMock2 = mock(SelectConditionStep.class);
        SelectSeekStep1<Record2<Object, Object>, Object> seekStepMock = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record2<Object, Object>> limitStepMock = mock(SelectLimitPercentStep.class);
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn("session-id");
        when(request.getMethod()).thenReturn("GET");
        when(request.getHeader("User-Agent")).thenReturn("Mozilla/5.0");
        when(request.getRemoteUser()).thenReturn("testUser");
        when(request.getRemoteHost()).thenReturn("127.0.0.1");
        when(response.getStatus()).thenReturn(200);
        when(activityLogRequest.requestUrl()).thenReturn("http://test-url.com");
        when(userNameService.getUserId()).thenReturn("userId123");
        when(userNameService.getUserName()).thenReturn("test-username");
        when(presentation.getVersion()).thenReturn(1.0);
        when(activityLogService.getCurrentRequest()).thenReturn(request);
        String expectedUniqueSessionId = "unique-session-id";
        when(observabilityRequestFilter.getUniqueSession(anyString(), any()))
                .thenReturn(expectedUniqueSessionId);
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy"))).thenReturn(selectMock);
        when(selectMock.from("drh_stateful_activity_audit.activity_log")).thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("session_id").eq(DSL.val(session.getId())))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("session_unique_id").eq(DSL.val(
                expectedUniqueSessionId))))
                .thenReturn(andStepMock1);
        when(andStepMock1.and(DSL.field("activity_log_level").ne(DSL.val("Level 6")))).thenReturn(andStepMock2);
        when(andStepMock2.orderBy(DSL.field("created_at").desc())).thenReturn(seekStepMock);

        when(seekStepMock.limit(1)).thenReturn(limitStepMock);

        when(limitStepMock.fetchMaps()).thenReturn(List.of(
                Map.of("hierarchy_path", "some_path", "activity_hierarchy", "some_hierarchy")));

        ActivityLog activityLog = activityLogService.setActivityDataForRequestUrl(activityLogRequest, request,
                response);

        assertNotNull(activityLog);
        assertEquals("http://test-url.com", activityLog.getRequestUrl());
        assertEquals("session-id", activityLog.getSessionId());
        assertEquals("unique-session-id", activityLog.getSessionUniqueId());
        assertEquals("test-username", activityLog.getUserName());
        assertEquals("testUser", activityLog.getActivityData().get("initiator"));
        assertEquals(200, activityLog.getActivityData().get("statusCode"));
        assertEquals("GET", activityLog.getActivityData().get("httpMethod"));
    }

    @Test
    void testExtractSessionContent_NoSession() {
        when(request.getSession()).thenReturn(null);
        activityLogService.extractSessionContent(request);
        verify(request, times(1)).getSession();
    }

    @Test
    void testExtractSessionContent_NullSession() {
        when(session.isNew()).thenReturn(true);
        activityLogService.extractSessionContent(request);
        verify(session, times(1)).isNew();
    }

    @Test
    void testExtractSessionContent_ExistingSession_WithAttributes() {
        when(session.isNew()).thenReturn(false);
        when(session.getId()).thenReturn("existing-session-id");
        when(session.getCreationTime()).thenReturn(3000L);
        when(session.getLastAccessedTime()).thenReturn(4000L);

        Enumeration<String> attributeNames = Collections.enumeration(Collections.singletonList("user"));
        when(session.getAttributeNames()).thenReturn(attributeNames);
        when(session.getAttribute("user")).thenReturn("testUser");

        activityLogService.extractSessionContent(request);

        verify(session, times(1)).isNew();
        verify(session, times(1)).getId();
        verify(session, times(1)).getCreationTime();
        verify(session, times(1)).getLastAccessedTime();
        verify(session, times(1)).getAttributeNames();
        verify(session, times(1)).getAttribute("user");
    }
}