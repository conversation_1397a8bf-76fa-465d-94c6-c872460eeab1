### Test Get Cohort Total Participant
POST http://localhost:8080/svm/cohort/totalParticipant.html
Content-Type: application/json

{"studyIds":["DCLP1","DCLP3","DSS1","NTLT","CTR3","DFA","IEOGC"],"filters":[]}
?? status == 200
?? response.body  != null




### Test Get Cohort Female Percentage with Multiple Filters
POST http://localhost:8080/svm/cohort/femalePercentage.html
Content-Type: application/json

{"studyIds":["DCLP1","DCLP3","DSS1"],"filters":["age  > '24'"]}
?? status == 200
?? response.body  != null



### Test Get Cohort cgm count 
POST http://localhost:8080/svm/cohort/cgm-count.html
Content-Type: application/json

{"studyIds":["CTR3"],"filters":[]}
?? status == 200
?? response.body  != null



### Test Get Cohort Average age
POST http://localhost:8080/svm/cohort/average-age.html
Content-Type: application/json

{"studyIds":["CTR3"],"filters":[]}
?? status == 200
?? response.body  != null




## Test all participant dashboard cached
POST http://localhost:8080/api/ux/tabular/jooq/all_participant_dashboard_cached.json
Content-Type: application/json
{
  "startRow": 0,
  "endRow": 100,
  "rowGroupCols": [],
  "valueCols": [],
  "pivotCols": [],
  "pivotMode": false,
  "groupKeys": [],
  "filterModel": {
    "study_id": {
      "filterType": "text",
      "type": "equals",
      "filter": "CTR3"
    }
  },
  "sortModel": []
}
?? status == 200
?? response.body  != null





## Test get cohort info
GET http://localhost:8080/cohort/info
Accept: text/html
?? status == 200
?? response.body  != null

GET http://localhost:8080/cohort/detail/DCLP1    
Content-Type: text/html
?? status == 200
?? response.body  != null