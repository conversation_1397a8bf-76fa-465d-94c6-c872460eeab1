package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CgmMasterService {
    private static final Logger LOG = LoggerFactory.getLogger(CgmMasterService.class);
    @Autowired
    @Qualifier("secondaryDsl")
    private DSLContext dsl;

    @Transactional
    public Object getSourcePlatformList() {
        LOG.info("Fetching list of available devices");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(DISTINCT manufacturer)", JSONB.class))
                .from("drh_stateless_raw_observation.device_view")
                .where(DSL.lower(DSL.field("status", String.class)).eq(
                        DSL.lower(DSL.val("Active"))));
        LOG.info("Fetching list of available devices: {}", query);
        JSONB jsonbResult = query
                .fetchOneInto(JSONB.class);
        LOG.info("Fetching list of available devices: {}", jsonbResult);
        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getCgmDevicesList(String manufacturer) {
        LOG.info("Fetching list of available cgm devices");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'device_name', device_name " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_raw_observation.device_view")
                .where(DSL.lower(DSL.field("manufacturer", String.class)).eq(
                        DSL.lower(DSL.val(manufacturer))))
                .and(DSL.lower(DSL.field("status", String.class)).eq(
                        DSL.lower(DSL.val("Active"))));

        LOG.info("Executing Query: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public String getCgmDevicesName(String deviceId) {
        LOG.info("Fetching Device name for deviceId {}", deviceId);
        final var query = dsl
                .select(DSL.field("device_name"))
                .from("drh_stateless_raw_observation.device_view")
                .where(DSL.lower(DSL.field("id", String.class)).eq(
                        DSL.lower(DSL.val(
                                deviceId))));

        LOG.info("Executing Query: {}", query);
        String deviceName = query.fetchOneInto(String.class);
        LOG.info("Device Name: {}", deviceName);
        return deviceName;
    }

}
