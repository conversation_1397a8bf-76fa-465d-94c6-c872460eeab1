package org.diabetestechnology.drh.service.http.pg.service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.constant.FileUploadStatusStatus;
import org.diabetestechnology.drh.service.http.util.FileValidationUtil;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class MealsAndFitnessService {
    private final DSLContext dsl;
    private final S3FileUploadService s3FileUploadService;
    private final MasterService masterService;
    private final UserNameService userNameService;
    private final InteractionService interactionService;
    private final DbActivityService activityLogService;

    private static final Logger LOG = LoggerFactory.getLogger(MealsAndFitnessService.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public MealsAndFitnessService(@Qualifier("secondaryDsl") DSLContext dsl, S3FileUploadService s3FileUploadService,
            MasterService masterService,
            UserNameService userNameService, InteractionService interactionService,
            DbActivityService activityLogService) {
        this.dsl = dsl;
        this.s3FileUploadService = s3FileUploadService;
        this.masterService = masterService;
        this.userNameService = userNameService;
        this.interactionService = interactionService;
        this.activityLogService = activityLogService;
    }

    public Map<String, Object> saveMealsAndFitnessFile(MultipartFile file, String studyId, String orgPartyId,
            String participantId, String fileContent) throws Exception {
        LOG.info("Starting " + fileContent + " file content copying.");
        List<String> hierarchyJsonArray = null;
        final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);

        final var originalFileName = file.getOriginalFilename();
        final var fileType = file.getContentType();
        final var fileName = FilenameUtils.getBaseName(originalFileName);
        if (originalFileName == null || fileType == null || fileName == null) {
            LOG.error("Invalid file upload request: originalFileName, fileType, or fileName is null.");
            throw new IllegalArgumentException(
                    "Invalid file upload request: originalFileName, fileType, or fileName is null.");
        }
        final var fileFormat = originalFileName.substring(originalFileName.lastIndexOf('.') + 1);
        LOG.info("File Format is : {}", fileFormat);
        final var fileSize = file.getSize(); // size in bytes
        final var uploadTimestamp = ZonedDateTime.now(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_INSTANT);

        final var fileContentTypeId = masterService.getFileContentTypeId(fileContent);
        LOG.info("File Content Type: {}", fileContentTypeId);
        final var status = "Pending";

        final var uploadedBy = userNameService.getCurrentuserPartyId();
        try {

            JSONB lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    fileContent.equalsIgnoreCase("meals") ? ActionDescription.START_MEALS_FILE_UPLOAD
                            : ActionDescription.START_FITNESS_FILE_UPLOAD,
                    null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS : FileType.FITNESS,
                    FileUploadStatusStatus.PENDING,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, fileContent.equalsIgnoreCase("meals") ? ActionType.UPLOAD_MEALS_FILE
                            : ActionType.UPLOAD_FITNESS_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.S3_BUCKET_UPLOAD, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.IN_PROGRESS,
                    "null",
                    uploadTimestamp, null,
                    ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var awsFileUrl = s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId);
            if (awsFileUrl == null) {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.S3_BUCKET_UPLOAD, null,
                        null,
                        null, null,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                                : FileType.FITNESS,
                        FileUploadStatusStatus.FAILURE,
                        "null",
                        uploadTimestamp, null,
                        ActionStatus.FAILED, 0,
                        null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
                throw new RuntimeException("Failed to upload file to S3 Bucket.");
            } else {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.S3_BUCKET_UPLOAD, null,
                        null,
                        null, awsFileUrl,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                                : FileType.FITNESS,
                        FileUploadStatusStatus.SUCCESS,
                        "null",
                        uploadTimestamp, null,
                        ActionStatus.SUCCESS, 0,
                        null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
                hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            }
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.IN_PROGRESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray,
                    fileContent.equalsIgnoreCase("meals") ? ActionType.VALIDATE_MEALS_FILE_CONTENT
                            : ActionType.VALIDATE_FITNESS_FILE_CONTENT);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            FileValidationUtil.validateMealsAndFitnessFile(file, fileContent);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.IN_PROGRESS,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray,
                    fileContent.equalsIgnoreCase("meals") ? ActionType.VALIDATE_MEALS_FILE_CONTENT
                            : ActionType.VALIDATE_FITNESS_FILE_CONTENT);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var tempFilePath = s3FileUploadService.saveFileToTempLocation(file);
            if (tempFilePath == null) {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                        null,
                        null, awsFileUrl,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                                : FileType.FITNESS,
                        FileUploadStatusStatus.SUCCESS,
                        null,
                        uploadTimestamp, null, ActionStatus.FAILED, 0,
                        null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);

                throw new RuntimeException("Failed to copy file for processing.");
            } else {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                        null,
                        null, awsFileUrl,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                                : FileType.FITNESS,
                        FileUploadStatusStatus.SUCCESS,
                        null,
                        uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                        null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
                hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            }
            LOG.info("Temp File Path: {}", tempFilePath);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.VALIDATE_AND_PROCESS_FILE_CONTENT, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var subjectObservationDataJson = s3FileUploadService.processContent(tempFilePath);

            LOG.info("Subject Observation Data: {}", subjectObservationDataJson);
            final var currentUserId = uploadedBy;
            final var mealsFitnessByteArray = s3FileUploadService.convertToByteArray(tempFilePath);
            JsonNode subjectObservationDataJsonNode = OBJECT_MAPPER.readTree(subjectObservationDataJson.toString());
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.VALIDATE_AND_PROCESS_FILE_CONTENT, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                            : FileType.FITNESS,
                    FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            String lastInteractionId = interactionService.getLastInteractionId(lastInteraction);
            ObjectNode mealsFitnessDataMap = OBJECT_MAPPER.createObjectNode();
            mealsFitnessDataMap.put("study_id", studyId);
            mealsFitnessDataMap.put("participant_sid", participantId);
            mealsFitnessDataMap.put("file_content_type_id", fileContentTypeId);
            mealsFitnessDataMap.put("last_file_interaction_id", lastInteractionId);

            mealsFitnessDataMap.put("file_name", originalFileName);
            mealsFitnessDataMap.put("file_url", awsFileUrl);
            mealsFitnessDataMap.set("subject_observation_data_json", subjectObservationDataJsonNode);
            mealsFitnessDataMap.put("upload_timestamp", uploadTimestamp);
            mealsFitnessDataMap.put("uploaded_by", uploadedBy);
            mealsFitnessDataMap.put("file_size", fileSize);
            mealsFitnessDataMap.put("status", status);
            mealsFitnessDataMap.put("file_type", fileFormat);
            mealsFitnessDataMap.put("org_party_id", orgPartyId);
            mealsFitnessDataMap.put("current_user_id", currentUserId);
            mealsFitnessDataMap.set("subject_observation_data", subjectObservationDataJsonNode);
            mealsFitnessDataMap.put("subject_observation_data_csv", mealsFitnessByteArray);
            LOG.info("Meals and Fitness Data Map: {}", mealsFitnessDataMap);
            final var mealsFitnessData = OBJECT_MAPPER.writeValueAsString(mealsFitnessDataMap);
            LOG.info("Meals and Fitness Data: {}", mealsFitnessData);
            JSONB mealsFitnessJsonbData = JSONB.valueOf(mealsFitnessData);
            LOG.info("Meals and Fitness Data: {}", mealsFitnessJsonbData.data());
            String activityData = activityLogService.prepareActivityLogMetadata();
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_raw_observation.save_subject_observation_data({0}, {1})",
                            String.class,
                            DSL.val(mealsFitnessJsonbData),
                            DSL.cast(DSL.val(activityData), JSONB.class)));
            LOG.info("Mealts or Fitness Save Query: {}", query);
            final var response = query.fetchOneInto(JSONB.class);
            LOG.info("Mealts or Fitness Save Response: {}", response);
            if (response != null) {
                JsonNode jsonNode = OBJECT_MAPPER.readTree(response.toString());
                LOG.info("DB Result: {}", jsonNode);

                String resultStatus = jsonNode.path("status").asText("success");
                if ("failure".equals(resultStatus)) {
                    return Map.of(
                            "status", "failure",
                            "message",
                            jsonNode.path("message").asText("Error occurred during meals and fitness data save"),
                            "result", OBJECT_MAPPER.convertValue(jsonNode.path("error_details"), Map.class));
                }

                return Map.of(
                        "status", "success",
                        "message", "Meals and fitness Data saved successfully",
                        "result", jsonNode);
            } else {
                LOG.error("Meals and fitness Data save returned null.");
                return Map.of(
                        "status", "error",
                        "message", "Failed to save Meals and fitness Data. No response from the database.");
            }
        } catch (Exception e) {
            LOG.error("Error occurred during meals and fitness data save: {}", e.getMessage());
            if (e instanceof IllegalArgumentException) {
                interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        fileContent.equalsIgnoreCase("meals") ? ActionDescription.MEALS_FILE_VALIDATION_ERROR
                                : ActionDescription.FITNESS_FILE_VALIDATION_ERROR,
                        null,
                        null,
                        null, null,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS
                                : FileType.FITNESS,
                        FileUploadStatusStatus.ERROR,
                        null,
                        uploadTimestamp, null, ActionStatus.FAILED, 0,
                        fileContent.equalsIgnoreCase("meals") ? ActionDescription.MEALS_FILE_VALIDATION_ERROR
                                : ActionDescription.FITNESS_FILE_VALIDATION_ERROR,
                        hierarchyJsonArray,
                        fileContent.equalsIgnoreCase("meals") ? ActionType.VALIDATE_MEALS_FILE_CONTENT
                                : ActionType.VALIDATE_FITNESS_FILE_CONTENT);

            } else {
                LOG.info("Failed to save Mealsor Fitness Data");
                interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        fileContent.equalsIgnoreCase("meals") ? ActionDescription.MEALS_ERROR
                                : ActionDescription.FITNESS_ERROR,
                        null,
                        null,
                        null, null,
                        fileName,
                        fileType, null, fileContent.equalsIgnoreCase("meals") ? FileType.MEALS : FileType.FITNESS,
                        FileUploadStatusStatus.ERROR,
                        null,
                        uploadTimestamp, null, ActionStatus.FAILED, 0,
                        fileContent.equalsIgnoreCase("meals") ? ActionDescription.MEALS_ERROR
                                : ActionDescription.FITNESS_ERROR,
                        hierarchyJsonArray, fileContent.equalsIgnoreCase("meals") ? ActionType.UPLOAD_MEALS_FILE
                                : ActionType.UPLOAD_FITNESS_FILE);
            }
            throw e;

        }

    }

}
