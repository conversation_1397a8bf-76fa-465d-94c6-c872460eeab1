package org.diabetestechnology.drh.service.http.pg.service;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.CollaborationMap;
import org.diabetestechnology.drh.service.http.pg.constant.ResearchStudyState;
import org.diabetestechnology.drh.service.http.pg.constant.StudyInteractionType;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.request.CollaborationTeamRequest;
import org.diabetestechnology.drh.service.http.pg.request.InvestigatorRequest;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class InvestigatorService {
    private static final Logger LOG = LoggerFactory.getLogger(InvestigatorService.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final DSLContext dsl;
    private final InteractionService interactionService;
    private final DbActivityService activityLogService;

    public InvestigatorService(@Qualifier("secondaryDsl") DSLContext dsl, InteractionService interactionService,
            DbActivityService activityLogService) {
        this.dsl = dsl;
        this.interactionService = interactionService;
        this.activityLogService = activityLogService;
    }

    @Transactional
    public JSONB getInvestigator() {
        LOG.info("Read all available investigators");
        LOG.info("Query for fetching all investigators: {}", dsl.select(DSL.field(
                "jsonb_agg(" +
                        "jsonb_build_object(" +
                        "'investigator_name', name " +
                        ")" +
                        ")",
                JSONB.class))
                .from("drh_stateless_research_study.existing_team_collabrators_view")
                .where(DSL.field("role_code").ne("co-author").and(DSL.field("role_code").ne("sponsor"))));

        JSONB jsonbResult = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'investigator_name', name " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.existing_team_collabrators_view")
                .where(DSL.field("role_code").ne("co-author").and(DSL.field("role_code").ne("sponsor")))
                .fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return JSONB.valueOf("{}"); // Return empty JSON if the query result is null
        }

        return jsonbResult == null ? JSONB.valueOf("{}") : jsonbResult;
    }

    @Transactional
    public JSONB saveInvestigator(InvestigatorRequest request) {
        LOG.info("Save {}: {}", request.type().name(), request);
        String activityData = activityLogService.prepareActivityLogMetadata();
        LOG.info("Query: {}", dsl.select(DSL.field(
                "drh_stateless_research_study.save_study_team_members({0}, {1}, {2}::drh_stateless_research_study.collab_team_type, {3})",
                JSONB.class,
                DSL.val(request.studyId()),
                DSL.val(request.practitionerNames()),
                DSL.val(request.type().getValue()),
                DSL.cast(DSL.val(activityData), JSONB.class))));
        JSONB result = dsl.select(DSL.field(
                "drh_stateless_research_study.save_study_team_members({0}, {1}, {2}::drh_stateless_research_study.collab_team_type, {3})",
                JSONB.class,
                DSL.val(request.studyId()),
                DSL.val(request.practitionerNames()),
                DSL.val(request.type().getValue()),
                DSL.cast(DSL.val(activityData), JSONB.class)))
                .fetchOneInto(JSONB.class);

        return result;
    }

    public Response prapareCollabRequest(CollaborationTeamRequest request) {
        String hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());

        try {
            LOG.info("  Saving Principal investigator: {}", request);
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, StudyInteractionType.UPDATE,
                    "Save Principal investigator ",
                    ResearchStudyState.ACTIVE, ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                    null, null, 200,
                    ActionStatus.IN_PROGRESS, ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.IN_PROGRESS);

            if (request.principalInvestigatorName() != null) {
                LOG.info("Save Principal investigator: {}", request);
                saveCollaborationTeam(new InvestigatorRequest(
                        request.studyId(), // studyId
                        new String[] { request.principalInvestigatorName() }, // names
                        InvestigatorRequest.CollabTeamType.investigator // type
                ));
            }

            LOG.info("Saving Nominated Principal investigator: {}",
                    request.nominatedPrincipalInvestigatorName());
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, "Update",
                    "Save Nominated Principal investigator ", null, "ACTIVE", JsonUtils.toJson(request),
                    null, null, 200, "Success", ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.IN_PROGRESS);

            if (request.nominatedPrincipalInvestigatorName() != null) {
                LOG.info("Save Nominated Principal investigator: {}", request.principalInvestigatorName());
                saveCollaborationTeam(new InvestigatorRequest(
                        request.studyId(), // studyId
                        new String[] { request.nominatedPrincipalInvestigatorName() }, // names

                        InvestigatorRequest.CollabTeamType.nominatedPrincipalInvestigator // type
                ));

            }
            LOG.info(" Saving  co investigator: {}", (Object[]) request.coInvestigatorsName());
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, "Update",
                    "Save co investigator ", null, "ACTIVE", JsonUtils.toJson(request),
                    null, null, 200, "Success", ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.IN_PROGRESS);

            if (request.coInvestigatorsName() != null) {
                LOG.info("Save co investigator: {}", request);
                saveCollaborationTeam(new InvestigatorRequest(
                        request.studyId(), // studyId
                        request.coInvestigatorsName(), // names
                        InvestigatorRequest.CollabTeamType.coInvestigator // type
                ));
            }
            LOG.info(" Saving study teams: {}", (Object[]) request.studyTeamNames());
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, "Update",
                    "Save study teams ", null, "ACTIVE", JsonUtils.toJson(request),
                    null, null, 200, "Success", ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.IN_PROGRESS);
            if (request.studyTeamNames() != null) {
                LOG.info("Save study teams: {}", request);
                saveCollaborationTeam(new InvestigatorRequest(
                        request.studyId(), // studyId
                        request.studyTeamNames(), // names
                        InvestigatorRequest.CollabTeamType.studyTeam // type
                ));
            }

            return Response.builder()
                    .data(new HashMap<>(Map.of()))
                    .status("success")
                    .message(" Successfully added practioners to collaboration team.")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error creating an collaboration team: {}", e.getMessage());
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, StudyInteractionType.UPDATE,
                    "Error creating an collaboration team: " + e.getMessage(),
                    ResearchStudyState.ACTIVE, ResearchStudyState.ACTIVE,
                    JsonUtils.toJson(request),
                    null, e
                            .getMessage(),
                    500, "Error", ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.FAILED);
            return Response.builder()
                    .data(new HashMap<>(Map.of()))
                    .status("error")
                    .message(" failed to create an collaboration team: " + e.getMessage())
                    .errors(null)
                    .build();
        }

    }

    public Response saveCollaborationTeam(InvestigatorRequest request) {
        CollaborationMap collaborationMap = new CollaborationMap();
        String message = collaborationMap.getCollaborationMap().get(request.type().name()).getActivityDescription();
        try {
            LOG.info("Save collab team for the request : {}", request);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            JSONB result = saveInvestigator(request);
            JsonNode responseJson = OBJECT_MAPPER.readTree(result.toString());

            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                interactionService.saveStudyInteraction(request.studyId(),
                        hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.CREATE_OR_UPDATE_INVESTIGATOR + message + ".",
                        ResearchStudyState.ACTIVE,
                        ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                        null, result
                                .toString(),
                        500, "Failed", ActionType.SAVE_COLLABORATION_TEAM,
                        ActionStatus.FAILED);
                LOG.error("Error saving " + message + ": " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.CREATE_OR_UPDATE_INVESTIGATOR
                            + message
                            + ".", // activity name
                    ResearchStudyState.ACTIVE, ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                    result.toString(), null, 200, ActionStatus.SUCCESS, ActionType.SAVE_COLLABORATION_TEAM,
                    ActionStatus.SUCCESS);
            LOG.info("Successfully saved " + message);
            return Response.builder()
                    .data(new HashMap<>(Map.of("InvestigatorDetails", result == null ? null : result.toString())))
                    .status("success")
                    .message(request.type().name() + " created successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error creating an " + message, e);
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to create an " + message)
                    .errors("Error creating an " + message + ": " + e.getMessage())
                    .build();
        }
    }

    @Transactional
    public JSONB getAuthors() {
        LOG.info("Read all available Authors");
        LOG.info("Query for fetching all Authors: <AUTHORS>
                "jsonb_agg(" +
                        "jsonb_build_object(" +
                        "'author_name', name " +
                        ")" +
                        ")",
                JSONB.class))
                .from("drh_stateless_research_study.existing_team_collabrators_view")
                .where(DSL.field("role_code").eq("co-author")));

        JSONB jsonbResult = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'author_name', name " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.existing_team_collabrators_view")
                .where(DSL.field("role_code").eq("co-author"))
                .fetchOneInto(JSONB.class);
        // try {
        // String jsonString = jsonbResult.data();

        // ObjectMapper mapper = new ObjectMapper();
        // Object json = mapper.readValue(jsonString, Object.class);
        // ObjectWriter writer = mapper.writerWithDefaultPrettyPrinter();
        // return writer.writeValueAsString(json);
        // } catch (Exception e) {
        // LOG.error("Error while processing JSON", e);
        // e.printStackTrace();
        // return "{}";
        // }
        return jsonbResult == null ? JSONB.valueOf("{}") : jsonbResult;
    }
}
