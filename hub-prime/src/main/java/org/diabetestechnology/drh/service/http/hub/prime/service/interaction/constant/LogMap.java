package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

import java.util.HashMap;
import java.util.Map;

public class LogMap {
    Map<String, LogDetails> logMap = new HashMap<>();

    public Map<String, LogDetails> getLogMap() {
        // LEVEL 0
        logMap.put(LogMappingUrl.ERROR, new LogDetails(LogName.ERROR, LogType.ERROR, LogDescription.ERROR,
                LogMappingUrl.ERROR, LogLevel.ERROR));

        // LEVEL 1
        logMap.put(LogMappingUrl.SKIP_LOGIN,
                new LogDetails(LogName.SKIP_LOGIN, LogType.SKIP_LOGIN, LogDescription.SKIP_LOGIN,
                        LogMappingUrl.SKIP_LOGIN, LogLevel.SKIP_LOGIN));
        logMap.put(LogMappingUrl.LOGIN, new LogDetails(LogName.LOGIN, LogType.LOGIN, LogDescription.LOGIN,
                LogMappingUrl.LOGIN, LogLevel.LOGIN));
        logMap.put(LogMappingUrl.ORCID_LOGIN,
                new LogDetails(LogName.ORCID_LOGIN, LogType.ORCID_LOGIN, LogDescription.ORCID_LOGIN,
                        LogMappingUrl.ORCID_LOGIN, LogLevel.ORCID_LOGIN));
        logMap.put(LogMappingUrl.GIT_LOGIN,
                new LogDetails(LogName.GIT_LOGIN, LogType.GIT_LOGIN, LogDescription.GIT_LOGIN,
                        LogMappingUrl.GIT_LOGIN, LogLevel.GIT_LOGIN));
        logMap.put(LogMappingUrl.HOME,
                new LogDetails(LogName.HOME, LogType.HOME, LogDescription.HOME, LogMappingUrl.HOME, LogLevel.HOME));
        logMap.put(LogMappingUrl.STUDIES, new LogDetails(LogName.STUDIES, LogType.STUDIES, LogDescription.STUDIES,
                LogMappingUrl.STUDIES, LogLevel.STUDIES));
        logMap.put(LogMappingUrl.COHORT, new LogDetails(LogName.COHORT, LogType.COHORT, LogDescription.COHORT,
                LogMappingUrl.COHORT, LogLevel.COHORT));
        logMap.put(LogMappingUrl.ASK_DRH, new LogDetails(LogName.ASK_DRH, LogType.ASK_DRH, LogDescription.ASK_DRH,
                LogMappingUrl.ASK_DRH, LogLevel.ASK_DRH));
        logMap.put(LogMappingUrl.CONSOLE, new LogDetails(LogName.CONSOLE, LogType.CONSOLE, LogDescription.CONSOLE,
                LogMappingUrl.CONSOLE, LogLevel.CONSOLE));
        logMap.put(LogMappingUrl.DOCUMENTATION, new LogDetails(LogName.DOCUMENTATION, LogType.DOCUMENTATION,
                LogDescription.DOCUMENTATION, LogMappingUrl.DOCUMENTATION, LogLevel.DOCUMENTATION));
        logMap.put(LogMappingUrl.PROFILE, new LogDetails(LogName.PROFILE, LogType.PROFILE, LogDescription.PROFILE,
                LogMappingUrl.PROFILE, LogLevel.PROFILE));

        // LEVEL 2
        logMap.put(LogMappingUrl.DASHBOARD, new LogDetails(LogName.DASHBOARD, LogType.DASHBOARD,
                LogDescription.DASHBOARD, LogMappingUrl.DASHBOARD, LogLevel.DASHBOARD));
        logMap.put(LogMappingUrl.ALL_STUDIES, new LogDetails(LogName.ALL_STUDIES, LogType.ALL_STUDIES,
                LogDescription.ALL_STUDIES, LogMappingUrl.ALL_STUDIES, LogLevel.ALL_STUDIES));
        logMap.put(LogMappingUrl.POPULATION_PERCENTAGE,
                new LogDetails(LogName.POPULATION_PERCENTAGE, LogType.POPULATION_PERCENTAGE,
                        LogDescription.POPULATION_PERCENTAGE, LogMappingUrl.POPULATION_PERCENTAGE,
                        LogLevel.POPULATION_PERCENTAGE));
        logMap.put(LogMappingUrl.MY_STUDIES, new LogDetails(LogName.MY_STUDIES, LogType.MY_STUDIES,
                LogDescription.MY_STUDIES, LogMappingUrl.MY_STUDIES, LogLevel.MY_STUDIES));

        logMap.put(LogMappingUrl.COHORT, new LogDetails(LogName.COHORT, LogType.COHORT,
                LogDescription.COHORT, LogMappingUrl.COHORT, LogLevel.COHORT));

        logMap.put(LogMappingUrl.ASK_DRH_DATA, new LogDetails(LogName.ASK_DRH_DATA, LogType.ASK_DRH_DATA,
                LogDescription.ASK_DRH_DATA, LogMappingUrl.ASK_DRH_DATA, LogLevel.ASK_DRH_DATA));
        logMap.put(LogMappingUrl.ASK_DRH_RESEARCH_JOURNAL,
                new LogDetails(LogName.ASK_DRH_RESEARCH_JOURNAL, LogType.ASK_DRH_RESEARCH_JOURNAL,
                        LogDescription.ASK_DRH_RESEARCH_JOURNAL, LogMappingUrl.ASK_DRH_RESEARCH_JOURNAL,
                        LogLevel.ASK_DRH_RESEARCH_JOURNAL));
        logMap.put(LogMappingUrl.ASK_DRH_ICODE, new LogDetails(LogName.ASK_DRH_ICODE, LogType.ASK_DRH_ICODE,
                LogDescription.ASK_DRH_ICODE, LogMappingUrl.ASK_DRH_ICODE, LogLevel.ASK_DRH_ICODE));

        logMap.put(LogMappingUrl.CONSOLE_PROJECT, new LogDetails(LogName.CONSOLE_PROJECT, LogType.CONSOLE_PROJECT,
                LogDescription.CONSOLE_PROJECT, LogMappingUrl.CONSOLE_PROJECT, LogLevel.CONSOLE_PROJECT));
        logMap.put(LogMappingUrl.CONSOLE_HEALTH_INFORMATION,
                new LogDetails(LogName.CONSOLE_HEALTH_INFORMATION, LogType.CONSOLE_HEALTH_INFORMATION,
                        LogDescription.CONSOLE_HEALTH_INFORMATION, LogMappingUrl.CONSOLE_HEALTH_INFORMATION,
                        LogLevel.CONSOLE_HEALTH_INFORMATION));
        logMap.put(LogMappingUrl.CONSOLE_SCHEMA, new LogDetails(LogName.CONSOLE_SCHEMA, LogType.CONSOLE_SCHEMA,
                LogDescription.CONSOLE_SCHEMA, LogMappingUrl.CONSOLE_SCHEMA, LogLevel.CONSOLE_SCHEMA));

        logMap.put(LogMappingUrl.DOCUMENTATION_OPEN_API,
                new LogDetails(LogName.DOCUMENTATION_OPEN_API, LogType.DOCUMENTATION_OPEN_API,
                        LogDescription.DOCUMENTATION_OPEN_API, LogMappingUrl.DOCUMENTATION_OPEN_API,
                        LogLevel.DOCUMENTATION_OPEN_API));
        logMap.put(LogMappingUrl.DOCUMENTATION_ANNOUNCEMENTS,
                new LogDetails(LogName.DOCUMENTATION_ANNOUNCEMENTS, LogType.DOCUMENTATION_OPEN_API,
                        LogDescription.DOCUMENTATION_ANNOUNCEMENTS, LogMappingUrl.DOCUMENTATION_ANNOUNCEMENTS,
                        LogLevel.DOCUMENTATION_ANNOUNCEMENTS));

        // LEVEL 3
        logMap.put(LogMappingUrl.STUDIES_INDIVIDUAL,
                new LogDetails(LogName.STUDIES_INDIVIDUAL, LogType.STUDIES_INDIVIDUAL,
                        LogDescription.STUDIES_INDIVIDUAL, LogMappingUrl.STUDIES_INDIVIDUAL,
                        LogLevel.STUDIES_INDIVIDUAL));
        logMap.put(LogMappingUrl.STUDIES_PARTICIPANT,
                new LogDetails(LogName.STUDIES_PARTICIPANT, LogType.STUDIES_PARTICIPANT,
                        LogDescription.STUDIES_PARTICIPANT, LogMappingUrl.STUDIES_PARTICIPANT,
                        LogLevel.STUDIES_PARTICIPANT));
        logMap.put(LogMappingUrl.ALL_STUDIES_CGM, new LogDetails(LogName.ALL_STUDIES_CGM, LogType.ALL_STUDIES_CGM,
                LogDescription.ALL_STUDIES_CGM, LogMappingUrl.ALL_STUDIES_CGM, LogLevel.ALL_STUDIES_CGM));
        logMap.put(LogMappingUrl.STUDIES_CGM, new LogDetails(LogName.STUDIES_CGM, LogType.STUDIES_CGM,
                LogDescription.STUDIES_CGM, LogMappingUrl.STUDIES_CGM, LogLevel.STUDIES_CGM));

        // LEVEL 4
        logMap.put(LogMappingUrl.AI_ASK_DRH_DATA, new LogDetails(LogName.AI_ASK_DRH_DATA, LogType.AI_ASK_DRH_DATA,
                LogDescription.AI_ASK_DRH_DATA, LogMappingUrl.AI_ASK_DRH_DATA, LogLevel.AI_ASK_DRH_DATA));
        logMap.put(LogMappingUrl.AI_ASK_DRH_RESEARCH_JOURNAL,
                new LogDetails(LogName.AI_ASK_DRH_RESEARCH_JOURNAL, LogType.AI_ASK_DRH_RESEARCH_JOURNAL,
                        LogDescription.AI_ASK_DRH_RESEARCH_JOURNAL, LogMappingUrl.AI_ASK_DRH_RESEARCH_JOURNAL,
                        LogLevel.AI_ASK_DRH_RESEARCH_JOURNAL));
        logMap.put(LogMappingUrl.AI_ASK_DRH_ICODE, new LogDetails(LogName.AI_ASK_DRH_ICODE, LogType.AI_ASK_DRH_ICODE,
                LogDescription.AI_ASK_DRH_ICODE, LogMappingUrl.AI_ASK_DRH_ICODE, LogLevel.AI_ASK_DRH_ICODE));

        // LEVEL 5
        logMap.put(LogMappingUrl.ACTIVITY_LOG, new LogDetails(LogName.ACTIVITY_LOG, LogType.ACTIVITY_LOG,
                LogDescription.ACTIVITY_LOG, LogMappingUrl.ACTIVITY_LOG, LogLevel.ACTIVITY_LOG));

        // LEVEL 6
        logMap.put(LogMappingUrl.SAVE_ACTIVITY_LOG, new LogDetails(LogName.SAVE_ACTIVITY_LOG, LogType.SAVE_ACTIVITY_LOG,
                LogDescription.SAVE_ACTIVITY_LOG, LogMappingUrl.SAVE_ACTIVITY_LOG, LogLevel.SAVE_ACTIVITY_LOG));

        return logMap;
    }
}
