This code was consumed and refactored from
[ag-grid-server-side-oracle-example](https://github.com/ag-grid/ag-grid-server-side-oracle-example).

See https://ag-grid.com/javascript-data-grid/server-side-operations-oracle/ for
more information.

## TODO

- Review SqlQueryBuilder.java for an example of how to do full server-side
  querying, filtering, sorting, pivoting, etc. It could be used as-is for single
  tables and views.
