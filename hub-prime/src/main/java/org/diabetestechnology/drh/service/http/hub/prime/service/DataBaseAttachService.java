package org.diabetestechnology.drh.service.http.hub.prime.service;

// import java.util.List;
// import java.util.Map;

// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.dao.DataAccessException;
// import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class DataBaseAttachService {
    // private static final Logger LOG =
    // LoggerFactory.getLogger(DataBaseAttachService.class.getName());

    // @Value("${${spring.profiles.active}_DRH_UDI_DS_AUDIT_BASE_URL:}")
    // private String auditFile;
    // @Value("${${spring.profiles.active}_DRH_UDI_DS_CLIENT_SERVER_BASE_URL:}")
    // private String clientServerFile;

    // @Autowired
    // private JdbcTemplate jdbcTemplate;

    // final String auditAlias = "audit";
    // final String clientServerAlias = "client";

    @PostConstruct
    public void init() {

        // LOG.info("Attach Database {} alias {}", auditFile, auditAlias);
        // attachActivityDatabase();
        // LOG.info("Attach Database {} alias {}", clientServerFile, clientServerAlias);
        // attachClientServerDatabase();
    }

    private void attachClientServerDatabase() {
        // int retryCount = 3;
        // int attempts = 0;
        // boolean success = false;

        // while (attempts < retryCount && !success) {
        // try {
        // jdbcTemplate.execute("ATTACH DATABASE '" + clientServerFile + "' AS \"" +
        // clientServerAlias + "\"");
        // // Enable WAL mode for the attached database
        // jdbcTemplate.execute("PRAGMA " + clientServerAlias + ".journal_mode=WAL;");
        // success = true; // Mark as success if no exception is thrown

        // } catch (DataAccessException e) {
        // attempts++;
        // LOG.error("Error attaching database '{}': {}. Attempt {}/{}",
        // clientServerAlias, e.getMessage(),
        // attempts, retryCount, e);
        // if (attempts >= retryCount) {
        // throw new RuntimeException(
        // "Error attaching database '" + clientServerAlias + "' after " + attempts + "
        // attempts", e);
        // }
        // LOG.error("Error attaching database '{}': {}", clientServerAlias,
        // e.getMessage(), e);
        // // Optionally, you can add a sleep here to wait before retrying
        // try {
        // Thread.sleep(1000); // Wait for 1 second before retrying
        // } catch (InterruptedException ie) {
        // Thread.currentThread().interrupt(); // Restore the interrupted status
        // }
        // throw new RuntimeException("Error attaching database '" + clientServerAlias +
        // "'", e);
        // }
        // }
    }

    protected void attachActivityDatabase() {
        // int retryCount = 3;
        // int attempts = 0;
        // boolean success = false;

        // while (attempts < retryCount && !success) {
        // try {
        // jdbcTemplate.execute("ATTACH DATABASE '" + auditFile + "' AS \"" + auditAlias
        // + "\"");
        // // Enable WAL mode for the attached database
        // jdbcTemplate.execute("PRAGMA " + auditAlias + ".journal_mode=WAL;");
        // success = true; // Mark as success if no exception is thrown

        // } catch (DataAccessException e) {
        // attempts++;
        // LOG.error("Error attaching database '{}': {}. Attempt {}/{}", auditAlias,
        // e.getMessage(),
        // attempts, retryCount, e);
        // if (attempts >= retryCount) {
        // throw new RuntimeException(
        // "Error attaching database '" + auditAlias + "' after " + attempts + "
        // attempts", e);
        // }
        // LOG.error("Error attaching database '{}': {}", auditAlias, e.getMessage(),
        // e);
        // try {
        // Thread.sleep(1000); // Wait for 1 second before retrying
        // } catch (InterruptedException ie) {
        // Thread.currentThread().interrupt(); // Restore the interrupted status
        // }
        // throw new RuntimeException("Error attaching database '" + auditAlias + "'",
        // e);
        // }
        // }
    }

    public boolean refreshActivityDb() {
        // int retryCount = 3;
        // int attempts = 0;
        // boolean success = false;

        // while (attempts < retryCount && !success) {
        // try {
        // final var sql = "PRAGMA database_list;";
        // List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        // for (Map<String, Object> row : result) {
        // if (auditAlias.equals(row.get("name")))
        // return true;
        // }
        // attachActivityDatabase();
        // return true;
        // } catch (Exception e) {
        // attempts++;
        // LOG.error("Error refreshing activity database:. Attempt {}/{}", attempts,
        // retryCount);
        // if (attempts >= retryCount) {
        // LOG.error("Error refreshing activity database: {}", e.getMessage(), e);
        // }
        // // Optionally, you can add a sleep here to wait before retrying
        // try {
        // Thread.sleep(1000); // Wait for 1 second before retrying
        // } catch (InterruptedException ie) {
        // Thread.currentThread().interrupt(); // Restore the interrupted status
        // }
        // }
        // }
        return false;
    }

    public boolean refreshClientServerDb() {
        // LOG.info("Refresh Client Server Database");
        // int retryCount = 3;
        // int attempts = 0;
        // boolean success = false;

        // while (attempts < retryCount && !success) {
        // try {
        // final var sql = "PRAGMA database_list;";
        // List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        // for (Map<String, Object> row : result) {
        // if (clientServerAlias.equals(row.get("name")))
        // return true;
        // }
        // attachClientServerDatabase();
        // success = true; // Mark as success if no exception is thrown
        // return true;
        // } catch (Exception e) {
        // attempts++;
        // LOG.error("Error refreshing client server database:. Attempt {}/{}",
        // attempts, retryCount);
        // if (attempts >= retryCount) {
        // LOG.error("Error refreshing client server database: {}", e.getMessage(), e);
        // }
        // // Optionally, you can add a sleep here to wait before retrying
        // try {
        // Thread.sleep(1000); // Wait for 1 second before retrying
        // } catch (InterruptedException ie) {
        // Thread.currentThread().interrupt(); // Restore the interrupted status
        // }
        // }
        // }
        return false;
    }
}
