package org.diabetestechnology.drh.service.http.hub.prime.service;

import static org.jooq.impl.DSL.field;

// import java.io.File;

// import java.io.FilenameFilter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.locks.ReentrantLock;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.util.MetricsQueries;

import org.jooq.SortField;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;

import jakarta.annotation.PostConstruct;
import lib.aide.tabular.TabularRowsRequest.FilterModel;
import lib.aide.tabular.TabularRowsRequest.SortModel;

@Service
public class DataAccessService {

    private static final Logger LOG = LoggerFactory.getLogger(DataAccessService.class.getName());

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Value("${${spring.profiles.active}_DRH_UDI_DS_PRIME_DB_BASE_PATH:}")
    private String dbBasePath;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private final ReentrantLock lock = new ReentrantLock();

    // Keeping the declaration for future reference
    // private List<String> databaseAliases = new ArrayList<>();
    // private Map<String, String> attachedDatabases = new HashMap<>();

    private CopyOnWriteArraySet<String> databaseAliases = new CopyOnWriteArraySet<>();
    private ConcurrentHashMap<String, String> attachedDatabases = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // File dbDirectory = new File(dbBasePath);
        // LOG.info("DB File path {}", dbDirectory);
        // if (!dbDirectory.exists() || !dbDirectory.isDirectory()) {
        // throw new IllegalArgumentException("Invalid database directory path");
        // }

        // File[] dbFiles = dbDirectory.listFiles(new FilenameFilter() {
        // @Override
        // public boolean accept(File dir, String name) {
        // return name.endsWith(".sqlite.db");
        // }
        // });

        // if (dbFiles == null) {
        // LOG.info("No DB Files available");
        // throw new IllegalStateException("Error reading database files");
        // }

        // // Step 2: Extract alias and attach databases
        // for (File dbFile : dbFiles) {
        // LOG.info("DB File {}", dbFile);
        // String fileName = dbFile.getName();
        // String alias = extractAlias(fileName).toLowerCase();
        // String dbPath = dbFile.getAbsolutePath();
        // LOG.info("Alias DB {} and DB path {}", alias, dbPath);
        // if (alias != null) {
        // databaseAliases.add(alias);
        // attachedDatabases.put(alias, dbPath);
        // }
        // // Keeping the comment code for a reference purpose
        // // createViews(alias);
        // }
    }

    // private String extractAlias(String fileName) {
    // String[] parts = fileName.split("\\.");
    // if (parts.length >= 3) {
    // return parts[1];
    // }
    // return null;
    // }

    private synchronized void attachDatabase(String attach, String alias) {
        lock.lock();
        try {
            LOG.info("Attaching database '{}' with alias '{}'", attach, alias);
            int maxAttempts = 3;
            int attempt = 0;
            boolean attached = false;

            while (attempt < maxAttempts && !attached) {
                try {
                    attempt++;
                    LOG.info("Attempt {} to Attach Database {}", attempt, alias);
                    transactionTemplate.execute(status -> {
                        boolean isAttached = checkDatabaseAttachment(alias);
                        LOG.info("DB {} Attach Status: {}", alias, isAttached);
                        if (!isAttached) {
                            // Attempt to attach the database
                            jdbcTemplate.execute("ATTACH DATABASE '" + attach + "' AS \"" + alias + "\"");
                            // Verify that the database is attached by querying a system table
                            jdbcTemplate.queryForObject("SELECT 1 FROM \"" + alias + "\".sqlite_master LIMIT 1",
                                    Integer.class);
                        }
                        return null;
                    });
                    // Enable WAL mode for the attached database
                    jdbcTemplate.execute("PRAGMA \"" + alias + "\".journal_mode=WAL;");
                    jdbcTemplate.execute("PRAGMA " + alias + ".busy_timeout = 5000;");
                    LOG.info("Database '{}' successfully attached as '{}' on attempt {}", attach, alias, attempt);
                    attached = true;

                    // Enable WAL mode for the attached database

                    if (!databaseAliases.contains(alias)) {
                        databaseAliases.add(alias);
                    }
                    if (!attachedDatabases.containsKey(alias)) {
                        attachedDatabases.put(alias, attach);
                    }

                } catch (DataAccessException e) {
                    LOG.error("Attempt {} to attach database '{}' failed: {}", attempt, alias,
                            e.getMessage());

                    if (attempt >= maxAttempts) {
                        throw new RuntimeException(
                                "Failed to attach database '" + alias + "' after " + maxAttempts + " attempts", e);
                    }

                    try {
                        // Optional: Add a short delay between retries
                        Thread.sleep(1000); // 1 second delay before retrying
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } finally {
            lock.unlock();
        }
    }

    public String response(List<String> alias, String tableName) {
        StringBuilder unionQuery = new StringBuilder();
        for (String databaseAliases : alias) {
            if (unionQuery.length() > 0) {
                unionQuery.append(" UNION ALL ");
            }
            unionQuery.append("SELECT * FROM ").append(databaseAliases).append("." + tableName);
        }
        final var sql = unionQuery.toString();
        return sql;
    }

    @Async
    public CompletableFuture<JdbcResponse> getParticipantCGMDates(String studyId, String participantId) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = MetricsQueries.participant_cgm_dates_query.replace("{dbName}", dbName);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getParticipantCGMDates: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();

                try {

                    LOG.info("Executing query: {}", sql);
                    final var result = jdbcTemplate.queryForObject(sql, (resultSet, rowNum) -> {
                        Map<String, Object> participantCGMDates = new HashMap<>();
                        participantCGMDates.put("participant_cgm_start_date",
                                resultSet.getString("participant_cgm_start_date"));
                        participantCGMDates.put("participant_cgm_end_date",
                                resultSet.getString("participant_cgm_end_date"));
                        return participantCGMDates;
                    }, participantId);
                    return responseBuilder(result != null
                            ? Map.of("participantCGMDates", result)
                            : Map.of(), "success", "participantCGMDates", null);
                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No CGM dates found for participantId {}", participantId);
                    // Return a map indicating no data is available
                    Map<String, Object> noDataMap = new HashMap<>();
                    noDataMap.put("message", "No CGM dates found for participantId " + participantId);
                    // return noDataMap;
                    return responseBuilder(Map.of(), "Success", "participantCGMDates", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "participantCGMDates", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getCoefficientOfVariation(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // SQL query to calculate coefficient of variation
            final var sql = String.format(
                    "SELECT ROUND((SQRT(AVG(CGM_Value * CGM_Value) - AVG(CGM_Value) * AVG(CGM_Value)) / AVG(CGM_Value)) * 100, 2) AS coefficient_of_variation "
                            +
                            "FROM %s.combined_cgm_tracing_cached " +
                            "WHERE participant_id = ? " +
                            "AND DATE(Date_Time) BETWEEN ? AND ?;",
                    dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("getCoefficientOfVariation, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                try {

                    // Execute the query and return the result
                    String result = jdbcTemplate.queryForObject(sql, String.class, participantId, startDate, endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (result != null)
                        dataMap.put("coefficient_of_variation", result);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("coefficient_of_variation successfully fetched")
                            .errors((result == null)
                                    ? "No data available for this duration"
                                    : "")
                            .build();
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for coefficient of variation", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException(
                    "Failed to execute SQL query after maximum retries for coefficient of variation");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getAmbulatoryGlucoseProfile(String studyId, String participantId,
            String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query for AGP calculation
            final var sql = MetricsQueries.agp_optim_maxim_query.replace("{dbName}", dbName);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getAmbulatoryGlucoseProfile: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    System.out.println("query: " + sql);
                    final var result = jdbcTemplate.query(sql, (resultSet, rowNum) -> {
                        Map<String, Object> agpData = new LinkedHashMap<>();
                        // agpData.put("date", resultSet.getString("date"));
                        agpData.put("hour", resultSet.getString("hour"));
                        agpData.put("p5", resultSet.getDouble("p5"));
                        agpData.put("p25", resultSet.getDouble("p25"));
                        agpData.put("p50", resultSet.getDouble("p50"));
                        agpData.put("p75", resultSet.getDouble("p75"));
                        agpData.put("p95", resultSet.getDouble("p95"));
                        return agpData;
                    }, startDate, endDate, participantId);
                    return responseBuilder(result != null && !result.isEmpty()
                            ? Map.of("ambulatoryGlucoseProfile", result)
                            : Map.of(), "success", "ambulatoryGlucoseProfile", null);
                } catch (DataAccessException e) {
                    if (e.getCause() instanceof AsyncRequestTimeoutException) {
                        LOG.warn("Query timed out on attempt {}: {}", attempt, e.getMessage());
                    }

                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "ambulatoryGlucoseProfile", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");

        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getParticipantInfo(String studyId, String participantId) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = MetricsQueries.participant_info_query.replace("{dbName}", dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getParticipantInfo: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("getParticipantInfo query: {} ", sql);
                    final var result = jdbcTemplate.queryForMap(sql, participantId);
                    return responseBuilder(result != null
                            ? Map.of("participantInfo", result)
                            : Map.of(), "success", "participantInfo", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "participantInfo", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeBelowRangeLow(String studyId, String participantId,
            String startDate, String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_below_range_low_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) AS time_below_range_low, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateTimeBelowRangeLow, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("Executing query: {}", sql);
                    final var result = jdbcTemplate.queryForObject(sql,
                            (resultSet, rowNum) -> {
                                Map<String, Object> timeBelowRangeLowData = new LinkedHashMap<>();
                                timeBelowRangeLowData.put("time_below_range_low_percentage",
                                        resultSet.getString("time_below_range_low_percentage"));
                                timeBelowRangeLowData.put("time_below_range_low",
                                        resultSet.getString("time_below_range_low"));
                                timeBelowRangeLowData.put("time_range_string",
                                        resultSet.getString("time_range_string"));
                                timeBelowRangeLowData.put("CGM_Value", resultSet.getString("CGM_Value"));
                                return timeBelowRangeLowData;
                            }, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("time_below_range_low_percentage", result)
                            : Map.of(), "success", "time_below_range_low_percentage", null);
                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available during this duration for participantId {}: {} - {}", participantId,
                            startDate,
                            endDate);
                    // Return a map indicating no data available
                    return responseBuilder(Map.of(), "success", "time_below_range_low_percentage", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "time_below_range_low_percentage", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("time_below_range_low_percentage");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeBelowRangeVeryLow(String studyId, String participantId,
            String startDate,
            String endDate) throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_below_range_very_low_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value < 54 THEN 1 ELSE 0 END) AS time_below_range_very_low, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value < 54 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value < 54 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateTimeBelowRangeVeryLow: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("Executing query: {}", sql);
                    final var result = jdbcTemplate.queryForObject(sql,
                            (resultSet, rowNum) -> {
                                Map<String, Object> timeBelowRangeVeryLowData = new LinkedHashMap<>();
                                timeBelowRangeVeryLowData.put("time_below_range_very_low_percentage",
                                        resultSet.getString("time_below_range_very_low_percentage"));
                                timeBelowRangeVeryLowData.put("time_below_range_very_low",
                                        resultSet.getString("time_below_range_very_low"));
                                timeBelowRangeVeryLowData.put("time_range_string",
                                        resultSet.getString("time_range_string"));
                                timeBelowRangeVeryLowData.put("CGM_Value", resultSet.getString("CGM_Value"));
                                return timeBelowRangeVeryLowData;
                            }, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("time_below_range_very_low_percentage", result)
                            : Map.of(), "success", "time_below_range_very_low_percentage", null);

                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available during this duration for participantId {}: {} - {}", participantId,
                            startDate,
                            endDate);
                    // Return a map indicating no data available
                    return responseBuilder(Map.of(), "success", "time_below_range_very_low_percentage", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "time_below_range_very_low_percentage",
                                e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("time_below_range_very_low_percentage");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeInRange(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_in_range_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS time_in_range, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateTimeInRange: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("Executing query: {}", sql);
                    final var result = jdbcTemplate.queryForObject(sql,
                            (resultSet, rowNum) -> {
                                Map<String, Object> timeInRangeData = new HashMap<>();
                                timeInRangeData.put("time_in_range_percentage",
                                        resultSet.getString("time_in_range_percentage"));
                                timeInRangeData.put("time_in_range", resultSet.getString("time_in_range"));
                                timeInRangeData.put("time_range_string", resultSet.getString("time_range_string"));
                                timeInRangeData.put("CGM_Value", resultSet.getString("CGM_Value"));
                                return timeInRangeData;
                            }, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("time_in_range_percentage", result)
                            : Map.of(), "success", "time_in_range_percentage", null);
                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available during this duration for participantId {}: {} - {}", participantId,
                            startDate,
                            endDate);
                    // Return a map indicating no data is available
                    return responseBuilder(Map.of(), "success", "time_in_range_percentage", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "time_in_range_percentage", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("time_in_range_percentage");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeAboveRangeVeryHigh(String studyId, String participantId,
            String startDate,
            String endDate) throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_above_vh_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value > 250 THEN 1 ELSE 0 END) AS time_above_vh, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value > 250 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value > 250 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getAllStudySummary: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("Executing query: {}", sql);
                    final var result = jdbcTemplate.queryForObject(sql,
                            (resultSet, rowNum) -> {
                                Map<String, Object> timeAboveVHData = new HashMap<>();
                                timeAboveVHData.put("time_above_vh_percentage",
                                        resultSet.getString("time_above_vh_percentage"));
                                timeAboveVHData.put("time_above_vh", resultSet.getString("time_above_vh"));
                                timeAboveVHData.put("time_range_string", resultSet.getString("time_range_string"));
                                timeAboveVHData.put("CGM_Value", resultSet.getString("CGM_Value"));
                                return timeAboveVHData;
                            }, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("time_above_vh_percentage", result)
                            : Map.of(), "success", "time_above_vh_percentage", null);
                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available during this duration for participantId {}: {} - {}", participantId,
                            startDate,
                            endDate);
                    // Return a map indicating no data is available
                    return responseBuilder(Map.of(), "success", "time_above_vh_percentage", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "time_above_vh_percentage", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("time_above_vh_percentage");
        });

    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeInTightRange(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_in_tight_range_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value BETWEEN 70 AND 140 THEN 1 ELSE 0 END) AS time_in_tight_range, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";

            final var maxRetries = 3;
            int attempt = 0;

            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateTimeInTightRange: retry attempt: {}", attempt);

                try {

                    LOG.info("TimeInTightRange, Executing query: {}", sql);

                    Map<String, Object> result = jdbcTemplate.queryForObject(sql, (resultSet, rowNum) -> {
                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("participant_id", resultSet.getString("participant_id"));
                        resultMap.put("time_in_tight_range_percentage",
                                resultSet.getString("time_in_tight_range_percentage"));
                        resultMap.put("time_in_tight_range", resultSet.getString("time_in_tight_range"));
                        resultMap.put("time_range_string", resultSet.getString("time_range_string"));
                        resultMap.put("CGM_Value", resultSet.getString("CGM_Value"));
                        return resultMap;
                    }, participantId, startDate, endDate);

                    return responseBuilder(result != null
                            ? Map.of("timeInTightRange", result)
                            : Map.of(), "success", "timeInTightRange", null);

                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available for this duration for participantId {}: {} - {}", participantId,
                            startDate, endDate);
                    return responseBuilder(Map.of(), "success", "timeInTightRange", null);

                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "timeInTightRange", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff with exponential backoff
                    try {
                        Thread.sleep(100 * (long) Math.pow(2, attempt));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }

            // Should never reach here as retries should handle all cases
            return commonResponseBuilder("timeInTightRange");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateTimeAboveRangeHigh(String studyId, String participantId,
            String startDate, String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    " participant_id, \n" +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_above_range_high_percentage,\n "
                    +
                    " SUM(CASE WHEN CGM_Value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) AS time_above_range_high, " +
                    " printf('%02d hours, %02d minutes', " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 5) / 60, " +
                    " (SUM(CASE WHEN CGM_Value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 5) % 60 " +
                    " ) AS time_range_string, \n" +
                    " CGM_Value \n" +
                    " FROM " + dbName + ".combined_cgm_tracing_cached \n" +
                    " WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?  \n" +
                    " GROUP BY participant_id";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateTimeAboveRangeHigh: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("Executing query: {}", sql);

                    Map<String, Object> response = jdbcTemplate.queryForObject(sql,
                            (resultSet, rowNum) -> {
                                Map<String, Object> result = new HashMap<>();
                                result.put("time_above_range_high_percentage",
                                        resultSet.getString("time_above_range_high_percentage"));
                                result.put("time_above_range_high", resultSet.getString("time_above_range_high"));
                                result.put("time_range_string", resultSet.getString("time_range_string"));
                                result.put("CGM_Value", resultSet.getString("CGM_Value"));
                                return result;
                            }, participantId, startDate, endDate);
                    return responseBuilder(response != null
                            ? Map.of("time_above_range_high_percentage", response)
                            : Map.of(), "success", "time_above_range_high_percentage", null);
                } catch (EmptyResultDataAccessException e) {
                    LOG.warn("No data available during this duration for participantId {}: {} - {}", participantId,
                            startDate,
                            endDate);
                    // Return a map indicating no data is available
                    return responseBuilder(Map.of(), "success", "time_above_range_high_percentage", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "time_above_range_high_percentage", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("time_above_range_high_percentage");
        });

    }

    @Async
    public CompletableFuture<JdbcResponse> glycemicRiskIndicator(String studyId, String participantId, String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            LOG.info(
                    "Request received for glycemicRiskIndicator with studyId: {}, participantId: {}, startDate: {}, endDate: {}",
                    studyId, participantId, startDate, endDate);

            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();
            LOG.debug("Converted studyId to lowercase: {}", dbName);

            if (!databaseAliases.contains(dbName)) {
                LOG.error("Database '{}' is not attached.", dbName);
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT\n" +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2) AS time_above_VH_percentage,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2) AS time_above_H_percentage,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2) AS time_in_range_percentage,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2) AS time_below_low_percentage,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2) AS time_below_VL_percentage,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.8 * (SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))), 0), 2) AS Hypoglycemia_Component,\n"
                    +
                    "    ROUND(COALESCE((SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.5 * (SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))), 0), 2) AS Hyperglycemia_Component,\n"
                    +
                    "    ROUND(COALESCE((3.0 * ((SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.8 * (SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))) +\n"
                    +
                    "                     (1.6 * ((SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.5 * (SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))), 0), 2) AS GRI\n"
                    +
                    "FROM (SELECT 1) AS dummy\n" +
                    "LEFT JOIN " + dbName + ".combined_cgm_tracing_cached\n" +
                    "ON participant_id = ? AND Date(Date_Time) BETWEEN ? AND ?\n" +
                    "GROUP BY participant_id;";

            LOG.debug("Executing SQL query for glycemicRiskIndicator: {}", sql);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("glycemicRiskIndicator: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();

                try {

                    Map<String, Object> result = jdbcTemplate.queryForMap(sql, participantId, startDate, endDate);
                    LOG.info("Query executed successfully for participantId: {} and studyId: {}", participantId,
                            studyId);
                    return responseBuilder(result != null
                            ? Map.of("glycemicRiskIndicator", result)
                            : Map.of(), "success", "glycemicRiskIndicator", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "glycemicRiskIndicator", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> liabilityIndex(String studyId, String participantId, String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            LOG.info(
                    "Request received for liabilityIndex with studyId: {}, participantId: {}, startDate: {}, endDate: {}",
                    studyId, participantId, startDate, endDate);

            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();
            LOG.debug("Converted studyId to lowercase: {}", dbName);

            if (!databaseAliases.contains(dbName)) {
                LOG.error("Database '{}' is not attached.", dbName);
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    "SUM(CASE WHEN CGM_Value < 70 THEN 1 ELSE 0 END) AS hypoglycemic_episodes, " +
                    "SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS euglycemic_episodes, " +
                    "SUM(CASE WHEN CGM_Value > 180 THEN 1 ELSE 0 END) AS hyperglycemic_episodes, " +
                    "ROUND(CAST((SUM(CASE WHEN CGM_Value < 70 THEN 1 ELSE 0 END) + SUM(CASE WHEN CGM_Value > 180 THEN 1 ELSE 0 END)) AS REAL) / COUNT(*), 2) AS liability_index "
                    +
                    "FROM " + dbName + ".combined_cgm_tracing_cached cct " +
                    "WHERE participant_id = ? AND Date(Date_Time) BETWEEN ? AND ? " +
                    "GROUP BY participant_id";

            LOG.debug("Executing SQL query for liabilityIndex: {}", sql);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("liabilityIndex: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();

                try {

                    Map<String, Object> result = jdbcTemplate.queryForMap(sql, participantId, startDate, endDate);
                    LOG.info("Query executed successfully for participantId: {} and studyId: {}", participantId,
                            studyId);
                    return responseBuilder(result != null
                            ? Map.of("liabilityIndex", result)
                            : Map.of(), "success", "liabilityIndex", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "liabilityIndex", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("liabilityIndex");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getMeanAmplitude(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query for mean amplitude calculation
            final var sql = "SELECT " +
                    "    AVG(amplitude) AS mean_amplitude " +
                    "FROM ( " +
                    "    SELECT " +
                    "        ABS(MAX(CGM_Value) - MIN(CGM_Value)) AS amplitude " +
                    "    FROM " +
                    "        " + dbName + ".combined_cgm_tracing_cached " +
                    "    WHERE " +
                    "        participant_id = ? " +
                    "        AND DATE(Date_Time) BETWEEN ? AND ? " +
                    "    GROUP BY " +
                    "        DATE(Date_Time) " +
                    ") AS excursion_data";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getMeanAmplitude: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();

                try {

                    BigDecimal meanAmplitude = jdbcTemplate.queryForObject(sql, BigDecimal.class, participantId,
                            startDate,
                            endDate);
                    if (meanAmplitude != null) {
                        // Round to 2 decimal places
                        BigDecimal result = meanAmplitude.setScale(2, RoundingMode.HALF_UP);
                        return responseBuilder(result != null
                                ? Map.of("meanAmplitude", result)
                                : Map.of(), "success", "meanAmplitude", null);

                    } else {
                        return responseBuilder(Map.of(), "success", "meanAmplitude", null);
                    }
                } catch (EmptyResultDataAccessException e) {
                    LOG.error("meanAmplitude: Empty Result: {}", e);
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "meanAmplitude", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("meanAmplitude: Error executing SQL query after {} attempts: {}", attempt,
                                e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "meanAmplitude", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            // Should never reach here as retries should handle all cases
            return commonResponseBuilder("meanAmplitude");
        });

    }

    @Async
    public CompletableFuture<JdbcResponse> calculateMValue(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {

        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query for M-value calculation
            final var sql = "WITH ParticipantMinMax AS ( " +
                    "    SELECT " +
                    "        participant_id, " +
                    "        MIN(CGM_Value) AS min_glucose, " +
                    "        MAX(CGM_Value) AS max_glucose, " +
                    "        MIN(DATETIME(Date_Time)) AS start_time, " +
                    "        MAX(DATETIME(Date_Time)) AS end_time " +
                    "    FROM " +
                    "        " + dbName + ".combined_cgm_tracing_cached " +
                    "    WHERE " +
                    "        participant_id = ? " +
                    "        AND DATETIME(Date_Time) BETWEEN DATETIME(?, '00:00:00') AND DATETIME(?, '23:59:59') " +
                    "    GROUP BY " +
                    "        participant_id " +
                    ") " +
                    "SELECT " +
                    "    (max_glucose - min_glucose) / ((strftime('%s', end_time) - strftime('%s', start_time)) / 60.0) AS m_value "
                    +
                    "FROM " +
                    "    ParticipantMinMax";
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("calculateMValue: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    LOG.info("calculateMValue: Executing SQL query : {}", sql);
                    // Execute the query and retrieve the M-value as Double
                    Double mValue = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            participantId,
                            startDate,
                            endDate);

                    if (mValue != null) {
                        // Round to 2 decimal places
                        // BigDecimal roundedMValue = BigDecimal.valueOf(mValue).setScale(2,
                        // BigDecimal.ROUND_HALF_UP);
                        // return roundedMValue.doubleValue();
                        return responseBuilder(mValue.toString() != null
                                ? Map.of("mValue", mValue.toString())
                                : Map.of(), "success", "mValue", null);
                    } else {
                        return responseBuilder(Map.of(), "success", "mValue", null);
                    }
                } catch (EmptyResultDataAccessException e) {
                    LOG.info("calculateMValue: EmptyResultDataAccessException{}", e);
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "mValue", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "mValue", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            // Should never reach here as retries should handle all cases
            return commonResponseBuilder("timeInTightRange");
        });
    }

    // get mean glucose for a participant in a date range
    // startDate and endDate must be in YYYY-MM-DD format

    @Async
    public CompletableFuture<JdbcResponse> getMeanGlucose(String studyId, String participantId, String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // SQL to get the average CGM value rounded to 2 decimal places
            final var sql = String.format(
                    "SELECT ROUND(AVG(CGM_Value), 2) AS mean_glucose " +
                            "FROM %s.combined_cgm_tracing_cached " +
                            "WHERE participant_id = ? " +
                            "AND DATE(Date_Time) BETWEEN ? AND ?;",
                    dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getMeanGlucose, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    // Execute the query and return the result
                    String meanGlucose = jdbcTemplate.queryForObject(sql, String.class, participantId, startDate,
                            endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (meanGlucose != null)
                        dataMap.put("mean_glucose", meanGlucose);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("mean_glucose successfully fetched")
                            .errors((meanGlucose == null) ? "No data available for this duration" : "")
                            .build();
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for mean glucose", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries for mean glucose");
        });
    }

    // startDate and endDate must be in YYYY-MM-DD format

    @Async
    public CompletableFuture<JdbcResponse> getNumberOfDaysCGMWorn(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL to count distinct days CGM was worn
            final var sql = String.format("SELECT COUNT(DISTINCT DATE(Date_Time)) AS number_of_days_cgm_worn " +
                    "FROM %s.combined_cgm_tracing_cached " +
                    "WHERE participant_id = ? " +
                    "AND DATE(Date_Time) BETWEEN ? AND ?;", dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getNumberOfDaysCGMWorn, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    Integer numberOfDays = jdbcTemplate.queryForObject(sql, Integer.class, participantId, startDate,
                            endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (numberOfDays != null)
                        dataMap.put("number_of_days_cgm_worn", numberOfDays);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("number_of_days_cgm_worn successfully fetched")
                            .errors((numberOfDays == null) ? "No data available for this duration" : "")
                            .build();
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for number of days CGM worn", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries for number of days CGM worn");
        });
    }

    // Method to calculate percentage time CGM was active for a participant
    // startDate and endDate must be in YYYY-MM-DD format

    @Async
    public CompletableFuture<JdbcResponse> getPercentageTimeCGMActive(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // SQLite query to calculate percentage time CGM was active
            final var sql = "SELECT " +
                    "   ROUND((" +
                    "       COUNT(DISTINCT DATE(Date_Time)) / " +
                    "       ROUND((julianday(MAX(Date_Time)) - julianday(MIN(Date_Time)) + 1))" +
                    "   ) * 100, 2) AS percentage_active " +
                    "FROM " + dbName + ".combined_cgm_tracing_cached " +
                    "WHERE participant_id = ? " +
                    "   AND DATE(Date_Time) BETWEEN ? AND ?";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getPercentageTimeCGMActive, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    Double percentageActive = jdbcTemplate.queryForObject(sql, Double.class, participantId, startDate,
                            endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (percentageActive != null)
                        dataMap.put("percentage_active", percentageActive);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("percentage_active successfully fetched")
                            .errors((percentageActive == null) ? "No data available for this duration" : "")
                            .build();
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for percentage time CGM active", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException(
                    "Failed to execute SQL query after maximum retries for percentage time CGM active");
        });
    }

    // returns GMI for a participant over a date range
    // startDate and endDate must be in YYYY-MM-DD format

    @Async
    public CompletableFuture<JdbcResponse> getGlucoseManagementIndicator(String studyId, String participantId,
            String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // SQLite query to calculate GMI for a participant
            final var sql = "SELECT " +
                    "   ROUND(AVG(CGM_Value) * 0.155 + 95, 2) AS gmi " +
                    "FROM " + dbName + ".combined_cgm_tracing_cached " +
                    "WHERE participant_id = ? " +
                    "   AND DATE(Date_Time) BETWEEN ? AND ?";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getGlucoseManagementIndicator, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    Double gmi = jdbcTemplate.queryForObject(sql, Double.class, participantId, startDate, endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (gmi != null)
                        dataMap.put("gmi", gmi);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("gmi (glucose-management-indicator) successfully fetched")
                            .errors((gmi == null) ? "No data available for this duration" : "")
                            .build();
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for GMI", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries for GMI");
        });
    }

    @Async
    public CompletableFuture<List<Map<String, Object>>> getStudyParticipantDashboard(String studyId, int limit,
            int offset) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT * FROM " + dbName + ".participant_dashboard_cached limit " + limit + " offset "
                    + offset;

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getStudyParticipantDashboard, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    return jdbcTemplate.queryForList(sql);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for database '" + dbName + "'", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException(
                    "Failed to execute SQL query after maximum retries for database '" + dbName + "'");
        });
    }

    @Async
    public CompletableFuture<Map<String, Object>> getStudyDetails(String studyId) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = "SELECT " +
                    "COALESCE(study_id, '') AS study_id, " +
                    "COALESCE(study_name, '') AS study_name, " +
                    "COALESCE(study_description, '') AS study_description, " +
                    "COALESCE(start_date, '') AS start_date, " +
                    "COALESCE(end_date, '') AS end_date, " +
                    "COALESCE(nct_number, '') AS nct_number, " +
                    "COALESCE(total_number_of_participants, '') AS total_number_of_participants, " +
                    "COALESCE(average_age, '') AS average_age, " +
                    "COALESCE(percentage_of_females, '') AS percentage_of_females, " +
                    "COALESCE(investigators, '') AS investigators" +
                    " FROM " + dbName + ".study_details_cached ;";

            LOG.info("getStudyDetails, Query:{} ", sql);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getStudyDetails, Retry Attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    return jdbcTemplate.queryForMap(sql);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query for study ID '" + studyId + "'", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException(
                    "Failed to execute SQL query after maximum retries for study ID '" + studyId + "'");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getParticipantMetrics(String studyId, String participantId, String startDate,
            String endDate)
            throws SQLException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }
            final var sql = MetricsQueries.participant_metrics_query.replace("{dbName}", dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("getParticipantMetrics: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    final var result = jdbcTemplate.queryForMap(sql, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("participantMetrics", result)
                            : Map.of(), "success", "participantMetrics", null);

                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "participantMetrics", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getTimeRangeStackedData(String studyId, String participantId,
            String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            // Check if database is attached
            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = MetricsQueries.time_range_stacked_data_query.replace("{dbName}", dbName);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("getTimeRangeStackedData: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    final var result = jdbcTemplate.queryForMap(sql, participantId, startDate, endDate);
                    return responseBuilder(result != null
                            ? Map.of("timeRangeStackedData", result)
                            : Map.of(), "success", "timeRangeStackedData", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "timeRangeStackedData", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    public void refreshDatabase(String dbName) {
        boolean isAttached = checkDatabaseAttachment(dbName);
        LOG.info("DB {} Attach Status: {}", dbName, isAttached);
        if (!isAttached) {
            try {
                // Attach database
                attachDatabase(attachedDatabases.get(dbName), dbName);
                // PRAGMA statement for schema version is optional
                LOG.info("Database '{}' refreshed successfully.", dbName);
            } catch (DataAccessException e) {
                LOG.error("Error attaching or refreshing database '{}': {}", dbName, e.getMessage(), e);
                throw new RuntimeException("Error attaching or refreshing database '" + dbName + "'", e);
            }
        }
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateAverageDailyRisk(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            final var sql = MetricsQueries.average_daily_risk.replace("{dbName}", dbName);
            // Construct SQL query

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateAverageDailyRisk: retry attempt: {}", attempt);
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    LOG.info("averageDailyRisk: Executing SQL query : {}", sql);

                    final var averageDailyRisk = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            startDate,
                            endDate,
                            participantId);

                    return responseBuilder(averageDailyRisk != null
                            ? Map.of("averageDailyRisk", averageDailyRisk)
                            : Map.of(), "success", "averageDailyRisk", null);

                } catch (EmptyResultDataAccessException e) {
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "averageDailyRisk", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("averageDailyRisk: Error executing SQL query after {} attempts: {}", attempt,
                                e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "averageDailyRisk", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep(100 * (long) Math.pow(2, attempt));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            // Return a default response if all attempts fail
            return commonResponseBuilder("averageDailyRisk");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateJIndex(String studyId, String participantId, String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = "WITH glucose_stats AS (\n" +
                    "    SELECT\n" +
                    "        participant_id,\n" +
                    "        AVG(CGM_Value) AS mean_glucose,\n" +
                    "        (AVG(CGM_Value * CGM_Value) - AVG(CGM_Value) * AVG(CGM_Value)) AS variance_glucose\n" +
                    "    FROM\n" +
                    "        " + dbName + ".combined_cgm_tracing_cached\n" +
                    "    WHERE\n" +
                    "        participant_id = ?\n" + // parameterized participant_id
                    "        AND DATE(Date_Time) BETWEEN ? AND ?\n" + // parameterized startDate and endDate
                    "    GROUP BY\n" +
                    "        participant_id\n" +
                    ")\n" +
                    "SELECT\n" +
                    "    Round(0.001 * (mean_glucose + sqrt(variance_glucose)) * (mean_glucose + sqrt(variance_glucose)), 2) AS j_index\n"
                    +
                    "FROM\n" +
                    "    glucose_stats;";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateJIndex: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    LOG.info("Executing SQL query : {}", sql);

                    Double jIndex = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            participantId,
                            startDate,
                            endDate);

                    return responseBuilder(jIndex != null
                            ? Map.of("jIndex", jIndex)
                            : Map.of(), "success", "jIndex", null);
                } catch (EmptyResultDataAccessException e) {
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "jIndex", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "jIndex", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            // Return a default response if all attempts fail
            return commonResponseBuilder("jIndex");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateLBGIandHBGI(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            String sql = "SELECT \n" +
                    "    Round(SUM(CASE WHEN (CGM_Value - 2.5) / 2.5 > 0 THEN ((CGM_Value - 2.5) / 2.5) * ((CGM_Value - 2.5) / 2.5) ELSE 0 END) * 5, 2) AS lbgi, \n"
                    +
                    "    Round(SUM(CASE WHEN (CGM_Value - 9.5) / 9.5 > 0 THEN ((CGM_Value - 9.5) / 9.5) * ((CGM_Value - 9.5) / 9.5) ELSE 0 END) * 5, 2) AS hbgi \n"
                    +
                    "FROM \n" +
                    "    " + dbName + ".combined_cgm_tracing_cached \n" +
                    "WHERE \n" +
                    "    participant_id = ? \n" +
                    "    AND DATE(Date_Time) BETWEEN ? AND ?";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateLBGIandHBGI: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    LOG.info("Executing SQL query : {}", sql);

                    final var result = jdbcTemplate.queryForMap(sql, participantId, startDate, endDate);
                    return responseBuilder(result != null && result.get("lbgi") != null && result.get("hbgi") != null
                            ? Map.of("lbgiAndHbgi", result)
                            : Map.of(), "success", "lbgiAndHbgi", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "lbgiAndHbgi", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("lbgiAndHbgi");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateGRADE(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = "WITH risk_scores AS (\n" +
                    "    SELECT participant_id,\n" +
                    "           CGM_Value,\n" +
                    "           CASE\n" +
                    "               WHEN CGM_Value < 90 THEN 10 * (5 - (CGM_Value / 18.0)) * (5 - (CGM_Value / 18.0))\n"
                    +
                    "               WHEN CGM_Value > 180 THEN 10 * ((CGM_Value / 18.0) - 10) * ((CGM_Value / 18.0) - 10)\n"
                    +
                    "               ELSE 0\n" +
                    "           END AS risk_score\n" +
                    "    FROM " + dbName + ".combined_cgm_tracing_cached\n" +
                    "    WHERE participant_id = ?\n" +
                    "      AND DATE(Date_Time) BETWEEN ? AND ?\n" +
                    "),\n" +
                    "average_risk AS (\n" +
                    "    SELECT participant_id,\n" +
                    "           AVG(risk_score) AS avg_risk_score\n" +
                    "    FROM risk_scores\n" +
                    "    GROUP BY participant_id\n" +
                    ")\n" +
                    "SELECT \n" +
                    "       avg_risk_score AS GRADE\n" +
                    "FROM average_risk;";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateGRADE: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    LOG.info("calculateGRADE: Executing SQL query : {}", sql);

                    final var grade = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            participantId,
                            startDate,
                            endDate);

                    return responseBuilder(grade != null
                            ? Map.of("grade", grade)
                            : Map.of(), "success", "grade", null);
                } catch (EmptyResultDataAccessException e) {
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "grade", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "grade", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("grade");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateMeanOfDailyDifferences(String studyId, String participantId,
            String startDate,
            String endDate) throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = "WITH daily_diffs AS (\n" +
                    "    SELECT\n" +
                    "        participant_id,\n" +
                    "        date(Date_Time) AS date,\n" +
                    "        CGM_Value ,\n" +
                    "        CGM_Value - LAG(CGM_Value) OVER (PARTITION BY participant_id ORDER BY date(Date_Time)) AS daily_diff\n"
                    +
                    "    FROM\n" +
                    "        " + dbName + ".combined_cgm_tracing_cached\n" +
                    "    WHERE participant_id = ?\n" +
                    "    AND DATE(Date_Time) BETWEEN ? AND ?\n" +
                    "),\n" +
                    "mean_daily_diff AS (\n" +
                    "    SELECT\n" +
                    "        participant_id,\n" +
                    "        AVG(daily_diff) AS mean_daily_diff\n" +
                    "    FROM\n" +
                    "        daily_diffs\n" +
                    "    WHERE\n" +
                    "        daily_diff IS NOT NULL  -- Exclude NULL differences\n" +
                    "    GROUP BY\n" +
                    "        participant_id\n" +
                    ")\n" +
                    "SELECT\n" +
                    "    mean_daily_diff\n" +
                    "FROM\n" +
                    "    mean_daily_diff;";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateMeanOfDailyDifferences: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    LOG.info("Executing SQL query : {}", sql);
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    final var meanDailyDiff = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            participantId,
                            startDate,
                            endDate);

                    return responseBuilder(meanDailyDiff != null
                            ? Map.of("meanOfDailyDifferences", meanDailyDiff)
                            : Map.of(), "success", "meanOfDailyDifferences", null);
                } catch (EmptyResultDataAccessException e) {
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "meanOfDailyDifferences", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "meanOfDailyDifferences", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("meanOfDailyDifferences");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> calculateCONGA(String studyId, String participantId, String startDate,
            String endDate)
            throws InterruptedException, ExecutionException {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = "WITH lag_values AS (\n" +
                    "    SELECT \n" +
                    "        participant_id,\n" +
                    "        Date_Time,\n" +
                    "        CGM_Value,\n" +
                    "        LAG(CGM_Value) OVER (PARTITION BY participant_id ORDER BY Date_Time) AS lag_CGM_Value\n" +
                    "    FROM \n" +
                    "        " + dbName + ".combined_cgm_tracing_cached \n" +
                    "    WHERE\n" +
                    "        participant_id = ?\n" +
                    "        AND DATE(date_time) BETWEEN ? AND ?\n" +
                    "),\n" +
                    "conga_hourly AS (\n" +
                    "    SELECT \n" +
                    "        participant_id,\n" +
                    "        SQRT(\n" +
                    "            AVG(\n" +
                    "                (CGM_Value - lag_CGM_Value) * (CGM_Value - lag_CGM_Value)\n" +
                    "            ) OVER (PARTITION BY participant_id ORDER BY Date_Time)\n" +
                    "        ) AS conga_hourly\n" +
                    "    FROM \n" +
                    "        lag_values\n" +
                    "    WHERE \n" +
                    "        lag_CGM_Value IS NOT NULL\n" +
                    ")\n" +
                    "SELECT \n" +
                    "    AVG(conga_hourly) AS conga_hourly_mean\n" +
                    "FROM \n" +
                    "    conga_hourly;";

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("calculateCONGA: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    synchronized (this) {
                        refreshDatabase(dbName);
                    }

                    LOG.info("Executing SQL query : {}", sql);

                    final var conga = jdbcTemplate.queryForObject(
                            sql,
                            Double.class,
                            participantId,
                            startDate,
                            endDate);

                    return responseBuilder(conga != null
                            ? Map.of("congaHourlyMean", conga)
                            : Map.of(), "success", "congaHourlyMean", null);
                } catch (EmptyResultDataAccessException e) {
                    // Handle case where no data is found
                    return responseBuilder(Map.of(), "success", "conga", null);
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "congaHourlyMean", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return commonResponseBuilder("congaHourlyMean");
        });
    }

    private boolean checkDatabaseAttachment(String dbName) {
        final var sql = "PRAGMA database_list;";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> row : result) {
            if (dbName.equals(row.get("name"))) {
                return true;
            }
        }
        return false;
    }

    public static String replacePlaceholders(String query, Map<String, String> replacements) {
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            query = query.replace(entry.getKey(), entry.getValue());
        }
        return query;
    }

    @Async
    public CompletableFuture<JdbcResponse> computeAllMetrics(String studyId, String participantId, String startDate,
            String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            String dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Query to check if there are any records
            String checkRecordsSql = MetricsQueries.check_records.replace("{dbName}", dbName);

            // Map of placeholders and their replacements
            Map<String, String> replacements = new HashMap<>();
            replacements.put("{dbName}", dbName);
            replacements.put("{participantId}", participantId);
            replacements.put("{startDate}", startDate);
            replacements.put("{endDate}", endDate);

            String sql = replacePlaceholders(MetricsQueries.combined_all_metrics, replacements);
            System.out.println("SQL query: " + sql);

            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                LOG.info("ComputeAllMetrics: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {
                    int recordCount = 0;
                    try {

                        recordCount = jdbcTemplate.queryForObject(checkRecordsSql, Integer.class, participantId,
                                startDate, endDate);
                        LOG.info("recordCount: {}", recordCount);
                    } catch (DataAccessException e) {
                        LOG.error("Error executing record count query: {}", e.getMessage(), e);
                        throw new RuntimeException("Error executing record count query", e);
                    }

                    // If no records found, return null values
                    if (recordCount == 0) {
                        Map<String, Object> nullResults = new HashMap<>();
                        nullResults.put("time_in_tight_range_percentage", null);
                        nullResults.put("liability_index", null);
                        nullResults.put("hypoglycemic_episodes", null);
                        nullResults.put("euglycemic_episodes", null);
                        nullResults.put("hyperglycemic_episodes", null);
                        nullResults.put("m_value", null);
                        nullResults.put("mean_amplitude", null);
                        nullResults.put("average_daily_risk", null);
                        nullResults.put("j_index", null);
                        nullResults.put("lbgi", null);
                        nullResults.put("hbgi", null);
                        nullResults.put("grade", null);
                        nullResults.put("conga", null);
                        nullResults.put("mean_daily_diff", null);
                        return responseBuilder(nullResults != null
                                ? Map.of("allMetrics", nullResults)
                                : Map.of(), "success", "allMetrics", null);
                    } else {
                        LOG.info("Executing SQL query: {}", sql);
                        final var result = jdbcTemplate.queryForMap(sql);
                        return responseBuilder(result != null
                                ? Map.of("allMetrics", result)
                                : Map.of(), "success", "allMetrics", null);

                    }
                } catch (DataAccessException e) {
                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        return responseBuilder(Map.of(), "error", "allMetrics", e.getMessage());
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");
        });
    }

    @Async
    public CompletableFuture<JdbcResponse> getDailyGlucoseProfile(String studyId, String participantId,
            String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            // Convert studyId to lowercase
            final var dbName = studyId.toLowerCase();

            if (!databaseAliases.contains(dbName)) {
                throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
            }

            // Construct SQL query
            final var sql = MetricsQueries.daily_profile_query.replace("{dbName}", dbName);
            final var maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                synchronized (this) {
                    refreshDatabase(dbName);
                }
                LOG.info("getDailyGlucoseProfile: retry attempt: {}", attempt);
                // Keeping the code for future reference
                // dbLock.lock();
                try {

                    System.out.println("query: " + sql);
                    final var result = jdbcTemplate.query(sql, (resultSet, rowNum) -> {
                        Map<String, Object> dailyprofileData = new LinkedHashMap<>();
                        dailyprofileData.put("date_time", resultSet.getString("date_time"));
                        dailyprofileData.put("date", resultSet.getString("date"));
                        dailyprofileData.put("hour", resultSet.getString("hour"));
                        dailyprofileData.put("glucose", resultSet.getDouble("glucose"));
                        return dailyprofileData;
                    }, participantId, startDate, endDate);
                    Map<String, Object> dataMap = new HashMap<>();
                    if (result != null)
                        dataMap.put("daily_glucose_profile", result);

                    return JdbcResponse.builder()
                            .data(dataMap)
                            .status("success")
                            .message("daily_glucose_profile successfully fetched")
                            .errors((result == null || result.size() == 0)
                                    ? "No data available for this duration"
                                    : "")
                            .build();
                } catch (DataAccessException e) {
                    if (e.getCause() instanceof AsyncRequestTimeoutException) {
                        LOG.warn("Query timed out on attempt {}: {}", attempt, e.getMessage());
                    }

                    attempt++;
                    if (attempt >= maxRetries) {
                        LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                        throw new RuntimeException("Error executing SQL query", e);
                    }
                    // Wait a bit before retrying with exponential backoff
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            throw new RuntimeException("Failed to execute SQL query after maximum retries");

        });
    }

    public List<org.jooq.Condition> createCondition(Map<String, FilterModel> filterModel) {
        List<org.jooq.Condition> conditions = new ArrayList<>();
        if (filterModel != null)
            filterModel.forEach((field, filter) -> {
                switch (filter.type()) {
                    case "like" -> conditions.add(field(field).like("%" + filter + "%"));
                    case "equals" -> conditions.add(field(field).eq(filter));
                    case "number" -> conditions.add(field(field).eq(filter));
                    case "date" -> conditions.add(field(field).eq(filter));
                    case "contains" -> conditions.add(field(field).like("%" + filter + "%"));
                    default -> throw new IllegalArgumentException(
                            "Unknown filter type '" + filter.filterType()
                                    + "' in filter for field '" + field
                                    + "' see DataAccessService::getEachStudyParticipantDashboard");
                }
            });

        return conditions;

    }

    public SortField<?>[] getSortModel(List<SortModel> sortModel) {
        final var sortFields = (sortModel != null) ? sortModel.stream()
                .map(sort -> {
                    String colId = sort.colId();
                    switch (sort.sort()) {
                        case "asc":
                            return field(colId).asc();
                        case "desc":
                            return field(colId).desc();
                        default:
                            throw new IllegalArgumentException(
                                    "Unknown sort direction: "
                                            + sort.sort());
                    }
                })
                .toArray(SortField<?>[]::new)
                : new SortField<?>[0];
        return sortFields;

    }

    public JdbcResponse responseBuilder(Map<String, Object> data, String status, String messageKey, Object error) {
        LOG.info("Preparing response for: {}", messageKey);
        return JdbcResponse.builder()
                .data(data)
                .status("success")
                .message(status.equalsIgnoreCase("success") ? messageKey + " successfully fetched"
                        : "Failed to fetch " + messageKey)
                .errors(data.isEmpty() && status.equalsIgnoreCase("success")
                        ? "No data available for this duration"
                        : status.equalsIgnoreCase("error") ? "Failed to fetch " + messageKey : "")
                .build();

    }

    public JdbcResponse commonResponseBuilder(String key) {
        return JdbcResponse.builder()
                .data(Map.of())
                .status("error")
                .message(key + ": Failed to execute SQL query after maximum retries")
                .errors("Unexpected error")
                .build();

    }

    public Object getFieldName(String studyId, String cgmTable) {
        refreshDatabase(studyId.toLowerCase());
        String sql = "PRAGMA " + studyId.toLowerCase() + ".table_info(" + cgmTable + ");";

        return jdbcTemplate.query(
                sql,
                (ResultSet rs) -> {
                    List<String> columnNames = new ArrayList<>();
                    while (rs.next()) {
                        columnNames.add(rs.getString("name"));
                    }
                    return columnNames;
                });
    }

    public CopyOnWriteArraySet<String> getDatabaseAliases() {
        return databaseAliases;
    }

    public void setDatabaseAliases(CopyOnWriteArraySet<String> databaseAliases) {
        this.databaseAliases = databaseAliases;
    }

    public Map<String, Object> getStudyTotalCgmFiles(String dbName) {
        refreshDatabase(dbName.toLowerCase());
        final var sql = "SELECT total_count FROM " + dbName + ".study_cgm_file_count_cached;";
        LOG.info("DataAccessService:getStudyTotalCgmFiles, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    public Map<String, Object> getIndividualStudyDetails(String dbName) {
        final var sql = "SELECT " +
                "study_id, " +
                "study_name, " +
                "study_description, " +
                "start_date, " +
                "end_date, " +
                "nct_number, " +
                "total_number_of_participants, " +
                "average_age, " +
                "CONCAT(percentage_of_females,'%') as percentage_of_females, " +
                "investigators " +
                "FROM " + dbName + ".study_details_cached ;";
        LOG.info("CombineDataAccessService:geteachStudyMetrics, Executing SQL query: {}", sql);
        refreshDatabase(dbName.toLowerCase());
        return jdbcTemplate.queryForMap(sql);
    }

    @Async
    public Map<String, Object> getDeviceNameAndCount(String studyId) {
        LOG.info("Fetching details for Device");
        final var dbName = studyId.toLowerCase();
        if (!databaseAliases.contains(dbName)) {
            throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
        }
        final String sql = String.format(
                "SELECT 'Device Wise Raw CGM File Count' AS title, " +
                        "GROUP_CONCAT(' ' || devicename || ': ' || number_of_files || '') AS description " +
                        "FROM %s.drh_device_file_count_view",
                dbName);
        LOG.info("getDeviceNameAndCount, Query:{} ", sql);

        final var maxRetries = 3;
        int attempt = 0;
        while (attempt < maxRetries) {
            synchronized (this) {
                refreshDatabase(dbName);
            }
            LOG.info("getDeviceNameAndCount, Retry Attempt: {}", attempt);
            // Keeping the code for future reference
            // dbLock.lock();
            try {

                return jdbcTemplate.queryForMap(sql);

            } catch (DataAccessException e) {
                attempt++;
                if (attempt >= maxRetries) {
                    LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                    throw new RuntimeException("Error executing SQL query for study ID '" + studyId + "'", e);
                }
                // Wait a bit before retrying with exponential backoff
                try {
                    Thread.sleep((long) Math.pow(2, attempt) * 100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } finally {
                // Keeping the code for future reference
                // dbLock.unlock();
            }
        }
        throw new RuntimeException(
                "Failed to execute SQL query after maximum retries for study ID '" + studyId + "'");

    }

    public List<Map<String, Object>> getAllParticipants(String studyId) {
        final var dbName = studyId.toLowerCase();

        if (!databaseAliases.contains(dbName)) {
            throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
        }
        final var sql = "SELECT * FROM " + dbName + ".participant_dashboard_cached;";
        LOG.info("getAllParticipants, Query: {}", sql);
        final var maxRetries = 3;
        int attempt = 0;
        while (attempt < maxRetries) {
            synchronized (this) {
                refreshDatabase(dbName);
            }
            LOG.info("getAllParticipants, Retry Attempt: {}", attempt);

            try {
                return jdbcTemplate.queryForList(sql);
            } catch (DataAccessException e) {
                attempt++;
                if (attempt >= maxRetries) {
                    LOG.error("Error executing SQL query after {} attempts: {}", attempt, e.getMessage(), e);
                    throw new RuntimeException("Error executing SQL query for study ID '" + studyId + "'", e);
                }
                try {
                    Thread.sleep((long) Math.pow(2, attempt) * 100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        throw new RuntimeException(
                "Failed to execute SQL query after maximum retries for study ID '" + studyId + "'");
    }

}