package org.diabetestechnology.drh.service.http.hub.prime.session;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class SessionLoggingFilter extends OncePerRequestFilter {

    private static final Map<String, SessionDetails> sessionLog = new ConcurrentHashMap<>();

    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain)
            throws ServletException, IOException {

        String sessionId = request.getSession().getId();
        sessionLog.computeIfAbsent(sessionId, id -> new SessionDetails(id, System.currentTimeMillis()));

        filterChain.doFilter(request, response);

        // Optional: update end time on response
        sessionLog.get(sessionId).setEndTime(System.currentTimeMillis());
    }
}
