package org.diabetestechnology.drh.service.http.pg.constant;

public class ActionDescription {
    public static final String START_CGM_FILE_UPLOAD = "Start CGM File upload.";
    public static final String EMPTY_FILE_CONTENT = "Check content availability.";
    public static final String S3_BUCKET_UPLOAD = "Upload file to S3 Bucket.";
    public static final String COPY_FILE_FOR_PROCESSING = "Copy file for processing.";
    public static final String VALIDATE_FILE_CONTENT = "Validate file content.";
    public static final String PREPARE_METADATA_CONTENT = "Prepare metadata content.";
    public static final String CGM_ERROR = "Error while uploading CGM Data.";
    public static final String VALIDATE_CGM_DATE_AND_VALUE = "Validate date field and cgm value.";

    public static final String START_PARTICIPANT_FILE_UPLOAD = "Start Participant File upload.";
    public static final String EMPTY_FILE_CHECK_COMPLETED = "Empty file check completed.";
    public static final String FILE_TYPE_VALIDATION = "File type validation.";
    public static final String FILE_SAVED_TO_TEMP_LOCATION = "File saved to temp location.";
    public static final String JSON_VALIDATION_COMPLETED = "JSON validation completed.";
    public static final String DELETE_FILE_FROM_TEMP_LOCATION = "Delete file from temp location.";

    public static final String CREATE_PARTICIPANT = "Create a new participant.";
    public static final String CREATE_PARTICIPANT_FAILED = "Failed to create a new participant.";
    public static final String CREATE_PARTICIPANT_ERROR = "Error while creating a new participant.";
    public static final String UPDATE_PARTICIPANT = "Update participant details.";
    public static final String UPDATE_PARTICIPANT_FAILED = "Failed to update participant details.";
    public static final String UPDATE_PARTICIPANT_ERROR = "Error while updating participant details.";
    public static final String DELETE_PARTICIPANT = "Delete participant.";
    public static final String VIEW_PARTICIPANT = "View participant details.";

    public static final String CREATE_STUDY = "Create a new study.";
    public static final String UPDATE_STUDY = "Update study.";
    public static final String UPDATE_STUDY_SETTINGS = "Update study settings.";
    public static final String UPDATE_STUDY_PUBLICATION = "Update study publication.";
    public static final String UPDATE_STUDY_VISIBILITY = "Update study visibility ";
    public static final String UPDATE_ARCHIVE_STATUS = "Update Study Archive Status.";
    public static final String DELETE_STUDY = "Delete study.";

    public static final String CREATE_OR_UPDATE_INVESTIGATOR = "Create or update ";

    public static final String VALIDATE_AND_PROCESS_FILE_CONTENT = "Validte and Process file content.";

    public static final String START_MEALS_FILE_UPLOAD = "Start Meals File upload.";
    public static final String MEALS_ERROR = "Error while uploading CGM Data.";
    public static final String MEALS_FILE_VALIDATION_ERROR = "Validation Failed for Meals File.";

    public static final String START_FITNESS_FILE_UPLOAD = "Start Fitness File upload.";
    public static final String FITNESS_ERROR = "Error while uploading CGM Data.";
    public static final String FITNESS_FILE_VALIDATION_ERROR = "Validation Failed for Fitness File.";

}
