package org.diabetestechnology.drh.service.http;

import java.io.IOException;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.diabetestechnology.drh.service.http.pg.service.AuthUserDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@ConfigurationProperties(prefix = "org.drh.service.http.github")
@Service
public class OauthUsersService {
    private static final Logger LOG = LoggerFactory.getLogger(OauthUsersService.class);
    private final AuthUserDetailsService authUserDetailsService;

    public OauthUsersService(AuthUserDetailsService authUserDetailsService) {
        this.authUserDetailsService = authUserDetailsService;
        LOG.info("OauthUsersService created");
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record AuthorizedUser(String name, String emailPrimary, String profilePicUrl, String userId,
            String tenantId, Map<String, Resource> resources, boolean hasAdminMenu) {
        public boolean isMenuAdmin() {
            LOG.info("isMenuAdmin:{}", resources);
            LOG.info("isMenuAdmin:{Role: {} }", resources.get("adminMenu"));
            return Optional.ofNullable(resources().get("adminMenu"))
                    .map(adminMenu -> adminMenu.roles)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .anyMatch(role -> role.contains("ADMIN"));
        }
    }

    public record Resource(List<String> roles) {
    }

    /**
     * Check GitHub to see if the provided userName is an authorized user. This
     * method will always go back to GitHub and load all users before returning
     * result. User is responsible for caching to reduce performance hits.
     *
     * @param gitHubLoginID the GitHub userName to check
     * @return non-empty AuthenticatedUser if found authorized or empty if not
     *         authorized
     */
    @Deprecated
    public Optional<AuthorizedUser> isAuthorizedUser(final DefaultOAuth2User user) {
        final var authResult = getAuthorizedUsers().isAuthorizedUser(user.getAttribute("login"));
        boolean adminMenu = getAuthorizedUsers().isMenuAdminUser(user.getAttribute("login"));
        LOG.info("isAuthorizedUser %s in %s:%s".formatted(user.getAttribute("login"), authzUsersYamlUrl, authResult));
        LOG.info("User {}", user);

        final var result = new AuthorizedUser((String) user.getAttribute("name"),
                (String) user.getAttribute("email"),
                (String) user.getAttribute("avatar_url"),
                (String) user.getAttribute("login"), null, null, adminMenu);
        LOG.info("isAuthorizedUser {}", result);
        return Optional.of(result);
    }

    public Optional<AuthorizedUser> isAuthorizedUserDB(final DefaultOAuth2User user) {
        final var authRoles = authUserDetailsService.getRoles(user.getAttribute("login"),
                user.getAttribute("provider"));
        LOG.info("Roles of Logged in User {}", authRoles);
        boolean adminMenu = authRoles.stream().anyMatch(role -> role.toUpperCase().equals("ADMIN"));
        LOG.info("isAuthorizedUser {} with roles {} , IsAdminMenu {}", user.getAttribute("login"), authRoles,
                adminMenu);
        LOG.info("User {}", user);

        final var result = new AuthorizedUser((String) user.getAttribute("name"),
                (String) user.getAttribute("email"),
                (String) user.getAttribute("avatar_url"),
                (String) user.getAttribute("login"), null, null, adminMenu);
        LOG.info("isAuthorizedUser {}", result);
        return Optional.of(result);
    }

    @Deprecated
    public boolean hasAdminMenu(String userId) {
        return getAuthorizedUsers().isMenuAdminUser(userId);
    }

    public boolean hasAdminMenuDB(String userId, String provider) {
        return authUserDetailsService.getRoles(userId, provider).stream()
                .anyMatch(role -> role.toUpperCase().equals("ADMIN"));
    }

    private String gitHubApiAuthnToken = System.getenv("ORG_DRH_SERVICE_HTTP_GITHUB_API_AUTHN_TOKEN");
    private String authzUsersYamlUrl = System.getenv("ORG_DRH_SERVICE_HTTP_GITHUB_AUTHZ_USERS_YAML_URL");
    private final WebClient gitHubApiClient = WebClient.builder()
            .defaultHeader("Authorization", "token " + gitHubApiAuthnToken)
            .build();

    @Deprecated
    protected AuthorizedUsers getAuthorizedUsers() {
        LOG.info("Downloading users from %s".formatted(authzUsersYamlUrl));
        // Make a single request to the URL and get the YAML content directly
        final var responseBody = gitHubApiClient.get()
                .uri(authzUsersYamlUrl)
                .retrieve()
                .onStatus(status -> !status.is2xxSuccessful(), clientResponse -> {
                    LOG.error("Unexpected response code from GitHub API: {} ({})", clientResponse.statusCode(),
                            authzUsersYamlUrl);
                    return Mono
                            .error(new IOException(
                                    "Unexpected response code from GitHub API: " + clientResponse.statusCode()));
                })
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(10))
                .retryWhen(
                        Retry.backoff(3, Duration.ofSeconds(2))
                                .filter(throwable -> throwable instanceof IOException
                                        || throwable instanceof WebClientRequestException)
                                .doBeforeRetry(retrySignal -> LOG.warn("GitHub API retry attempt {} due to {}",
                                        retrySignal.totalRetries() + 1, retrySignal.failure().toString())) // Log retry
                                                                                                           // attempt
                                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> retrySignal.failure()))
                .block();

        if (responseBody == null) {
            LOG.error("GitHub API response body is null for " + authzUsersYamlUrl);
            return null;
        }

        // Parse the YAML content into an AuthorizedUsers object
        final var mapper = new ObjectMapper(new YAMLFactory());
        AuthorizedUsers users;
        try {
            users = mapper.readValue(responseBody, AuthorizedUsers.class);
            users.users.stream().forEach(item -> LOG.info("Item: {}", item));
            return users;
        } catch (Exception e) {
            LOG.error("Error transforming %s to AuthorizedUsers.class".formatted(authzUsersYamlUrl), e);
        }

        return new AuthorizedUsers(List.of());
    }

    public record AuthorizedUsers(List<AuthorizedUser> users) {
        public Optional<AuthorizedUser> isAuthorizedUser(final String userID) {
            return users.stream().filter(u -> u.userId().equals(userID)).findFirst();
        }

        public boolean isMenuAdminUser(final String userID) {
            LOG.info("userID: {}", userID);
            return users.stream()
                    .filter(u -> u.userId().equals(userID) && u.isMenuAdmin())
                    .findFirst().isPresent();
        }

    }

}
