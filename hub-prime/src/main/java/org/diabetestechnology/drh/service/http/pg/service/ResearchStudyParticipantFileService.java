package org.diabetestechnology.drh.service.http.pg.service;

import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.diabetestechnology.drh.service.http.hub.prime.exception.EmptyFileException;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.constant.FileUploadStatusStatus;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantFileDataRequest;
import org.diabetestechnology.drh.service.http.util.FileValidationUtil;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.FilenameUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;

@Service
public class ResearchStudyParticipantFileService {
    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyParticipantFileService.class);
    private final S3FileUploadService s3FileUploadService;
    private final ParticipantService participantService;
    private final MasterService masterService;
    private final InteractionService interactionService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final Set<String> REQUIRED_COLUMNS = Set.of("participant_id", "gender", "age");

    public ResearchStudyParticipantFileService(S3FileUploadService s3FileUploadService,
            ParticipantService participantService, MasterService masterService, InteractionService interactionService) {
        this.s3FileUploadService = s3FileUploadService;
        this.participantService = participantService;
        this.masterService = masterService;
        this.interactionService = interactionService;
    }

    public JSONB uploadAndSaveParticipantFile(MultipartFile file, ParticipantFileDataRequest request)
            throws JsonProcessingException {
        final var originalFileName = file.getOriginalFilename();
        // final var fileName = StringUtils.cleanPath(originalFileName);
        final var fileName = FilenameUtils.getBaseName(originalFileName);

        final var fileType = file.getContentType();
        @SuppressWarnings("null")
        final var fileFormat = originalFileName.substring(originalFileName.lastIndexOf('.') + 1);
        final var fileSize = file.getSize(); // size in bytes
        final var uploadTimestamp = ZonedDateTime.now(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_INSTANT);
        List<String> hierarchyJsonArray = null;

        try {
            LOG.info("Uploading file: {}", file.getOriginalFilename());
            LOG.info("save Participant file format: {}", fileFormat);
            LOG.info("save Participant file uploadTimestamp: {}", uploadTimestamp);
            LOG.info("save Participant file name: {}", fileName);
            LOG.info("save Participant file type: {}", fileType);
            LOG.info("save Participant file: {} bytes", fileSize);
            final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            JSONB lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request
                    .studyId(), null,
                    ActionDescription.START_PARTICIPANT_FILE_UPLOAD, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.PENDING,
                    null,
                    uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0,
                    null, null, ActionType.UPLOAD_PARTICIPANT_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            FileValidationUtil.validateFileHasData(file);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request
                    .studyId(), null,
                    ActionDescription.EMPTY_FILE_CONTENT, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.PENDING,
                    null,
                    uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, ActionType.VALIDATE_PARTICIPANT_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request
                    .studyId(),
                    null,
                    ActionDescription.S3_BUCKET_UPLOAD, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.IN_PROGRESS,
                    "null",
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            String responseUrl = s3FileUploadService.uploadParticipantFileToS3Bucket(file, request.orgPartyId(),
                    request.studyId());
            if (responseUrl == null) {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request
                        .studyId(),
                        null,
                        ActionDescription.S3_BUCKET_UPLOAD, null,
                        null,
                        null, null,
                        fileName,
                        fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.FAILURE,
                        "null",
                        uploadTimestamp, null,
                        ActionStatus.FAILED, 0,
                        null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
                throw new IllegalArgumentException("Error uploading file to S3");
            }
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.S3_BUCKET_UPLOAD, null, null, null,
                    responseUrl,
                    fileName, fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0, null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.FILE_TYPE_VALIDATION, null, null, null, responseUrl,
                    fileName,
                    fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.IN_PROGRESS, 0, null, hierarchyJsonArray,
                    ActionType.VALIDATE_PARTICIPANT_FILE_TYPE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            String valid = validateFileType(file);
            if (valid == null) {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                        ActionDescription.FILE_TYPE_VALIDATION, null, null, null,
                        responseUrl,
                        fileName,
                        fileType, null,
                        FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                        ActionStatus.FAILED, 0, null, hierarchyJsonArray,
                        ActionType.VALIDATE_PARTICIPANT_FILE_TYPE);
                throw new IllegalArgumentException("Invalid file type");
            }
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.FILE_TYPE_VALIDATION, null, null, null,
                    responseUrl,
                    fileName,
                    fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0, null, hierarchyJsonArray,
                    ActionType.VALIDATE_PARTICIPANT_FILE_TYPE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            if (!validateCsvFileField(file)) {
                LOG.error("Participant ID , Age and Gender are required fields.");
                throw new IllegalArgumentException("Participant ID , Age and Gender are required fields.");
            }

            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.FILE_SAVED_TO_TEMP_LOCATION, null, null, null,
                    responseUrl,
                    fileName, fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.IN_PROGRESS, 0, null, hierarchyJsonArray,
                    ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            String filePath = s3FileUploadService.saveFileToTempLocation(file);
            LOG.info("File uploaded to: {}", filePath);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.FILE_SAVED_TO_TEMP_LOCATION, null, null, null,
                    responseUrl,
                    fileName, fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0, null, hierarchyJsonArray,
                    ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            LOG.info("Hierarchy till Copy File For Processing: {}", hierarchyJsonArray);

            ArrayNode jsonArray = convertToJsonArray(filePath);
            if (jsonArray.size() == 0) {
                throw new IllegalArgumentException("No records found in the file.");
            }
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.JSON_VALIDATION_COMPLETED, null,
                    null, null,
                    responseUrl,
                    fileName, fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.IN_PROGRESS, 0, null, hierarchyJsonArray,
                    ActionType.PARTICIPANT_CONTENT_VALIDATION);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);

            String jsonValidateResponse = s3FileUploadService
                    .validateParticipantFileJson(jsonArray);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    ActionDescription.JSON_VALIDATION_COMPLETED, null,
                    jsonValidateResponse, null,
                    responseUrl,
                    fileName, fileType, null,
                    FileType.PARTICIPANT, FileUploadStatusStatus.SUCCESS, null, uploadTimestamp, null,
                    ActionStatus.SUCCESS, 0, null, hierarchyJsonArray,
                    ActionType.PARTICIPANT_CONTENT_VALIDATION);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            LOG.info("Validate JSON Array of Studies: {}", jsonValidateResponse);

            s3FileUploadService.deleteTempFile(filePath);

            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            if (!jsonValidateResponse.equals("All records are valid.")) {
                throw new IllegalArgumentException("Invalid records found in the file.");
            }
            validateValidateRaceAndEthnicity(jsonArray);
            for (JsonNode node : jsonArray) {
                String race = node.path("race").asText();
                String ethnicity = node.path("ethnicity").asText();
                String gender = node.path("gender").asText();
                System.out.println("Gender: " + gender + " ,Race: " + race + ", Ethnicity: " + ethnicity);
            }
            String lastFileInteractionId = hierarchyJsonArray.isEmpty() ? null
                    : hierarchyJsonArray.get(hierarchyJsonArray.size() - 1);
            JSONB result = participantService.saveParticipantsDataFromFile(request, responseUrl, jsonArray,
                    lastFileInteractionId);
            LOG.info("Participant data sent to Database. Response from Database: {}", result);
            if (result == null) {
                throw new IllegalArgumentException("Participant data upload failed.");
            }
            return result;
        } catch (EmptyFileException e) {
            LOG.error("Empty file error: {}", e.getMessage());
            final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    null,
                    "Error while uploading participant file.", JsonUtils.toJson(request), null,
                    null, null,
                    fileName,
                    fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.ERROR,
                    null,
                    null, null,
                    ActionStatus.FAILED, 0,
                    "Error while uploading participant file." + e.getMessage(),
                    hierarchyJsonArray,
                    ActionType.UPLOAD_PARTICIPANT_FILE);
            throw e; // Let Spring handle this exception via @ExceptionHandler
        } catch (Exception e) {
            LOG.error("Error uploading participant file", e);
            final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    null,
                    "Error while uploading participant file.", JsonUtils.toJson(request), null,
                    null, null,
                    fileName,
                    fileType, null, FileType.PARTICIPANT, FileUploadStatusStatus.ERROR,
                    null,
                    null, null,
                    ActionStatus.FAILED, 0,
                    "Error while uploading participant file." + e.getMessage(),
                    hierarchyJsonArray,
                    ActionType.UPLOAD_PARTICIPANT_FILE);
            throw new UnsupportedOperationException(e);
        }
    }

    private String validateFileType(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
            return "Invalid file type. Only CSV files are allowed.";
        }
        String contentType = file.getContentType();
        if (contentType != null && !contentType.equals("text/csv")) {
            return "Invalid content type. Only CSV files are allowed.";
        }
        if (file.isEmpty()) {
            return "File is empty";
        }
        return "valid";
    }

    private static boolean validateCsvFileField(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream();
                CSVReader reader = new CSVReader(new InputStreamReader(inputStream))) {

            List<String[]> rows = reader.readAll();

            if (rows.isEmpty()) {
                System.out.println("CSV file is empty.");
                return false;
            }

            // Read the header row (first row)
            String[] headers = rows.get(0);
            Set<String> headerSet = new HashSet<>(Arrays.asList(headers));

            // Check if all required columns exist
            if (!headerSet.containsAll(REQUIRED_COLUMNS)) {
                System.out.println("Missing required columns: " +
                        REQUIRED_COLUMNS.stream().filter(col -> !headerSet.contains(col)).toList());
                return false;
            }

            System.out.println("CSV file is valid.");
            return true;

        } catch (IOException | CsvException e) {
            System.out.println("Error reading CSV file: " + e.getMessage());
            return false;
        }
    }

    private ArrayNode convertToJsonArray(String filePath) throws CsvException {
        ArrayNode jsonArray = objectMapper.createArrayNode();
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            List<String[]> rows = reader.readAll();
            // Get headers
            String[] headers = rows.get(0);
            for (int i = 1; i < rows.size(); i++) {
                String[] row = rows.get(i);
                Map<String, String> rowMap = new HashMap<>();
                for (int j = 0; j < headers.length; j++) {
                    rowMap.put(headers[j], row[j]);
                }

                // Convert the row to JSON
                JsonNode jsonNode = objectMapper.valueToTree(rowMap);
                jsonArray.add(jsonNode);
                System.out.println("JSON representation of row " + i + ": " + jsonNode.toString());
            }
            System.out.println("JSON representation of array:  " + jsonArray.toString());
        } catch (IOException e) {
            LOG.error("Failed to convert CSV file: ", e);
        }
        return jsonArray;
    }

    private void validateValidateRaceAndEthnicity(ArrayNode jsonArray) {
        for (JsonNode node : jsonArray) {

            String race = node.path("race").asText();
            String ethnicity = node.path("ethnicity").asText();
            if (ethnicity != null)
                ((ObjectNode) node).put("ethnicity", validateEthinicity(ethnicity));
            else
                ((ObjectNode) node).put("ethnicity", validateEthinicity("unknown"));
            if (race != null)
                ((ObjectNode) node).put("race", validateRace(race));
            else
                ((ObjectNode) node).put("race", validateRace("unknown"));
        }
    }

    private String validateRace(String race) {
        // Fetch the possible race values from masterDataService
        JSONB raceData = masterService.readRaceType();

        // Parse the JSONB object to extract valid race values
        List<String> validRace = extractValidRace(raceData);
        Optional<String> unknownRace = validRace.stream()
                .filter(dbRace -> dbRace.equalsIgnoreCase("unknown"))
                .findFirst();
        if (StringUtils.hasText(race)) {
            Optional<String> matchedRace = validRace.stream()
                    .filter(dbRace -> dbRace.equalsIgnoreCase(race))
                    .findFirst();

            if (matchedRace.isPresent()) {
                System.out.println("Input race '" + race + "' matches DB value: " + matchedRace.get());
                return matchedRace.get(); // race is valid
            } else {

                System.out.println("Input race '" + race + "' matches DB value: " + unknownRace.get());
                System.out.println("Race is not valid for row . It should be one of: " + String.join(", ", validRace));
                return race;
            }
        } else {
            System.out.println("Race is null for row .");
            return unknownRace.get();
        }
    }

    private String validateEthinicity(String ethnicity) {
        // Fetch the possible ethnicity values from masterDataService
        JSONB ethnicityData = masterService.readEthnicityType();

        // Parse the JSONB object to extract valid Ethnicity values
        List<String> validEthnicity = extractValidEthnicity(ethnicityData);
        Optional<String> unknownEthnicity = validEthnicity.stream()
                .filter(dbEthnicity -> dbEthnicity.equalsIgnoreCase("unknown"))
                .findFirst();
        if (StringUtils.hasText(ethnicity)) {
            Optional<String> matchedEthnicity = validEthnicity.stream()
                    .filter(dbEthnicity -> dbEthnicity.equalsIgnoreCase(ethnicity))
                    .findFirst();

            if (matchedEthnicity.isPresent()) {
                System.out.println("Input Ethnicity '" + ethnicity + "' matches DB value: " + matchedEthnicity.get());
                return matchedEthnicity.get(); // Ethnicity is valid
            } else {

                System.out.println("Input Ethnicity '" + ethnicity + "' matches DB value: " + unknownEthnicity.get());
                System.out.println(
                        "Gender is not valid for row . It should be one of: " + String.join(", ", validEthnicity));
                return ethnicity;
            }
        } else {
            System.out.println("Ethnicity is null for row .");
            return unknownEthnicity.get();
        }
    }

    private List<String> extractValidEthnicity(JSONB ethnicityData) {
        if (ethnicityData == null || ethnicityData.data().isBlank() || ethnicityData.data().equals("null")) {
            throw new IllegalStateException("No valid ethnicity data found in masterService.");
        }

        try {
            // Parse the JSONB data into a list of maps
            List<Map<String, Object>> ethnicityList = objectMapper.readValue(
                    ethnicityData.data(), new TypeReference<List<Map<String, Object>>>() {
                    });

            // Extract the "display" field from each map
            return ethnicityList.stream()
                    .map(entry -> entry.get("display").toString())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Failed to parse ethnicity data from JSONB", e);
            throw new RuntimeException("Failed to parse ethnicity data from JSONB", e);
        }
    }

    private List<String> extractValidRace(JSONB raceData) {
        if (raceData == null || raceData.data().isBlank() || raceData.data().equals("null")) {
            throw new IllegalStateException("No valid race data found in masterService.");
        }

        try {
            // Parse the JSONB data into a list of maps
            List<Map<String, Object>> raceList = objectMapper.readValue(
                    raceData.data(), new TypeReference<List<Map<String, Object>>>() {
                    });

            // Extract the "display" field from each map
            return raceList.stream()
                    .map(entry -> entry.get("display").toString())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse race data from JSONB", e);
        }
    }

    public byte[] generateParticipantMealsCsvTemplate() {
        String csvContent = "meal_time,calories,meal_type\n";
        try {
            LOG.info("Generating CSV template...");
            return csvContent.getBytes();
        } catch (Exception e) {
            LOG.error("Error occurred while generating CSV template.", e);
            throw new RuntimeException("Failed to generate CSV template", e);
        }
    }

    public byte[] generateParticipantFitnessCsvTemplate() {
        String csvContent = "date,steps,exercise_minutes,calories_burned,distance,heart_rate\n";
        try {
            LOG.info("Generating CSV template...");
            return csvContent.getBytes();
        } catch (Exception e) {
            LOG.error("Error occurred while generating CSV template.", e);
            throw new RuntimeException("Failed to generate CSV template", e);
        }
    }
}
