package org.diabetestechnology.drh.service.http.hub.prime.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/superadmin")
public class DatabaseLoginController {

    @GetMapping("/login")
    public String loginPage() {
        return "database-login";
    }

    @PostMapping("/login")
    public String processLogin() {
        // Spring Security will handle the actual authentication
        // This method is just for mapping purposes
        return "redirect:/home";
    }
}