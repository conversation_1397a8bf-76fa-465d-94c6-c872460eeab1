package org.diabetestechnology.drh.service.http.pg.cache;

import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import lib.aide.tabular.TabularRowsRequest;

@Service
public class CacheService {

    public String getFilterTypesAsString(Map<String, TabularRowsRequest.FilterModel> filterModel) {
        if (filterModel == null || filterModel.isEmpty()) {
            return "no_filter";
        }

        return filterModel.values().stream()
                // Ensure you're explicitly specifying the type of FilterModel in the lambda
                .map(filter -> (String) filter.filter())
                .collect(Collectors.joining(","));
    }

    @CacheEvict(value = "tabularRowCache", allEntries = true)
    public void evictTabularRowCache() {
        // This method will clear all entries in the cache named "tabularRowCache"
    }

    @CacheEvict(value = "tabularRowCache", key = "#cacheKey")
    public void evictSpecificCache(String cacheKey) {
        // This will remove a specific cache entry
    }

}
