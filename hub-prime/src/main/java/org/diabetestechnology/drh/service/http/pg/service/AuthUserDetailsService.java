package org.diabetestechnology.drh.service.http.pg.service;

import java.util.List;
import java.util.Optional;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AuthUserDetailsService {
    private static final Logger LOG = LoggerFactory.getLogger(AuthUserDetailsService.class);
    private final DSLContext dsl;

    public AuthUserDetailsService(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    // get roles from database
    @Transactional
    public List<String> getRoles(String userId, String authProvider) {
        LOG.info("Getting roles for userId: {} and authProvider: {}", userId, authProvider);
        if (authProvider.equalsIgnoreCase("email")) {
            List<String> result = dsl.select(DSL.field("role_name"))
                    .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                    .where(DSL.field("email").eq(userId))
                    .fetch(DSL.field("role_name"), String.class);
            LOG.info("User roles: {}", result);
            if (result == null || result.isEmpty()) {
                return List.of();
            } else {
                return result;
            }
        } else {
            final var result = dsl.fetch(
                    """
                            SELECT elem ->> 'role_name' AS role_name
                            FROM drh_stateless_authentication.user_list_view eam
                            CROSS JOIN LATERAL jsonb_array_elements(eam.user_roles) AS elem
                            WHERE eam.provider_user_id = ?
                                AND eam.auth_provider ilike ?
                            """,
                    userId,
                    authProvider)
                    .getValues("role_name", String.class);
            LOG.info("User roles: {}", result);
            if (result == null || result.isEmpty()) {
                return List.of();
            } else {
                return result;
            }
        }

    }

    public boolean isSuperAdmin() {
        LOG.info("Checking if user is super admin");
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetails)) {
            LOG.info("Loggedin user is not SuperAdmin");
            return false;
        }
        final var userPrincipal = (UserDetails) principal;
        final var userId = (String) userPrincipal.getUsername() != null
                ? (String) userPrincipal.getUsername()
                : "Anonymous";
        boolean isSuperAdmin = dsl.fetchOne(
                DSL.select(DSL.exists(
                        DSL.selectOne()
                                .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                                .where(DSL.field("email").eq(userId))
                                .and(DSL.field("role_name").eq("Super Admin")))))
                .value1();
        LOG.info("User is super admin: {}", isSuperAdmin);
        return isSuperAdmin;
    }

    public String getEmailLoginUserFullName() {
        LOG.info("Read Full name of Super Admin User");
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetails)) {
            LOG.info("Loggedin user is not SuperAdmin");
            return "Anonymous";
        }
        final var userPrincipal = (UserDetails) principal;
        final var userId = (String) userPrincipal.getUsername() != null
                ? (String) userPrincipal.getUsername()
                : "Anonymous";
        String fullName = dsl.select(DSL.field("full_name"))
                .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                .where(DSL.field("email").eq(userId)).fetchOneInto(String.class);
        LOG.info("User is super admin: {}", fullName);
        return fullName;
    }

    public Object getLoginUserFullName() {
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();
        if ((principal instanceof UserDetails)) {
            LOG.info("Loggedin user is SuperAdmin");
            final var userPrincipal = (UserDetails) principal;
            final var userId = (String) userPrincipal.getUsername() != null
                    ? (String) userPrincipal.getUsername()
                    : "Anonymous";
            String fullName = dsl.select(DSL.field("full_name"))
                    .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                    .where(DSL.field("email").eq(userId)).fetchOneInto(String.class);
            LOG.info("User is super admin: {}", fullName);
            return fullName;
        } else if (principal instanceof DefaultOAuth2User) {
            DefaultOAuth2User oAuth2User = (DefaultOAuth2User) principal;
            final var userId = oAuth2User.getAttribute("login");
            Boolean exists = dsl
                    .select(DSL.value(true))
                    .from("drh_stateless_authentication.user_profile_view")
                    .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                    .limit(1) // To ensure we only check if at least one row exists
                    .fetchOptional() // Returns Optional<Boolean>
                    .isPresent(); // Check if the result is present
            if (exists) {
                final var userName = dsl.select(DSL.field("first_name"))
                        .from(DSL.table("drh_stateless_authentication.user_profile_view"))
                        .where(DSL.field("provider_user_id").eq(userId))
                        .fetchOneInto(String.class);
                LOG.info("User Name is : {}", userName);
                return userName;
            }
            final var fullName = Optional.ofNullable(oAuth2User.getAttribute("name"))
                    .or(() -> Optional.ofNullable(oAuth2User.getAttribute("login")))
                    .orElseThrow();
            LOG.info("Full Name: {}", fullName);
            return fullName;

        } else {
            return "Anonymous";
        }

    }

}
