package org.diabetestechnology.drh.service.http.hub.prime.ux;

import static org.diabetestechnology.drh.service.http.SecurityConfig.parseTimeout;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Value;

@RestController
@Tag(name = "DRH Hub Session Refresh API Endpoints")
public class KeepAliveSessionController {

    @Value("${server.servlet.session.timeout}")
    private String sessionTimeout;

    @PutMapping("/keep-alive")
    public ResponseEntity<?> keepSessionAlive(HttpSession session) {
        // Touch the session to extend its life
        session.setMaxInactiveInterval(parseTimeout(sessionTimeout));
        System.out.println(sessionTimeout);
        return ResponseEntity.ok("Session kept alive");
    }

}
