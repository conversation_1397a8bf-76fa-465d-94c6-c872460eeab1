package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.exception.AccessDeniedException;
import org.diabetestechnology.drh.service.http.hub.prime.exception.CgmFieldValidationException;
import org.diabetestechnology.drh.service.http.hub.prime.exception.DuplicateFilterException;
import org.diabetestechnology.drh.service.http.hub.prime.exception.EmptyFileException;
import org.diabetestechnology.drh.service.http.hub.prime.exception.FilterNotFoundException;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

// Handle global exceptions
@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(DuplicateFilterException.class)
    @ResponseBody
    public Response handleDuplicateFilterException(DuplicateFilterException ex) {
        Response response = Response.builder()
                .data(Map.of("message", "Conflict occured. Please try again later."))
                .status("error")
                .message(
                        "Conflict occured. Please try again later.")
                .errors(HttpStatus.CONFLICT + ".\n " + ex.getMessage())
                .build();
        return response;
    }

    @ExceptionHandler(FilterNotFoundException.class)
    @ResponseBody
    public Response handleFilterNotFoundException(FilterNotFoundException ex) {
        Response response = Response.builder()
                .data(Map.of("message", "Filter not found. Please try again later."))
                .status("error")
                .message(
                        "Filter not found. Please try again later.")
                .errors(HttpStatus.NOT_FOUND + ".\n " + ex.getMessage())
                .build();
        return response;
    }

    @ExceptionHandler(AccessDeniedException.class)
    @ResponseBody
    public Response handleFilterNotFoundException(AccessDeniedException ex) {
        Response response = Response.builder()
                .data(Map.of("message", "Access Denied. Please try again later."))
                .status("error")
                .message(
                        "Access Denied. Please try again later.")
                .errors(HttpStatus.FORBIDDEN + ".\n " + ex.getMessage())
                .build();
        return response;
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Response handleAllExceptions(Exception ex) {
        LOG.error("Error Message: {}", ex.getMessage());
        LOG.error("An error occurred: ", ex);
        Response response = Response.builder()
                .data(Map.of("message", "Something went wrong. Please try again later."))
                .status("error")
                .message(
                        "Something went wrong. Please try again later.")
                .errors(ex.getMessage())
                .build();
        return response;
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST) // This ensures a 400 response
    @ResponseBody
    public Response handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, Object> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors()
                .forEach(error -> errors.put(error.getField(), error.getDefaultMessage()));

        return Response.builder()
                .data(Map.of())
                .status("error")
                .message("Validation failed")
                .errors(errors)
                .build();
    }

    @ExceptionHandler(EmptyFileException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST) // Returning 400 Bad Request
    public Response handleEmptyFileException(EmptyFileException ex) {
        LOG.error("Empty file error: {}", ex.getMessage());

        return Response.builder()
                .data(Map.of("message", "File is empty. Please upload a valid file."))
                .status("error")
                .message("File is empty. Please upload a valid file.")
                .errors(HttpStatus.BAD_REQUEST + ".\n " + ex.getMessage())
                .build();
    }

    @ExceptionHandler(CgmFieldValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST) // Return 400 Bad Request
    public Response handleCgmFieldValidationException(CgmFieldValidationException ex) {
        LOG.warn("CGM Field Validation Error: {} -> Value: {}", ex.getField(), ex.getInvalidValue());

        return Response.builder()
                .data(Map.of(
                        "field", ex.getField(),
                        "invalid_value", ex.getInvalidValue(),
                        "message", ex.getMessage()))
                .status("error")
                .message("Incorrect CGM field Mapping")
                .errors(HttpStatus.BAD_REQUEST + ".\n " + ex.getMessage())
                .build();
    }

    // Other exception handlers can be added here
}
