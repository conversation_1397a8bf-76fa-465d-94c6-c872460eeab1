package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.service.CombineDataAccessService;
import org.diabetestechnology.drh.service.http.hub.prime.service.request.CohortRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;

@Controller
@Hidden
@Tag(name = "DRH Hub Cohort API")
public class CohortController {
    private static final Logger LOG = LoggerFactory.getLogger(CohortController.class.getName());

    @Autowired
    private CombineDataAccessService combineDataAccessService;

    @Hidden
    @PostMapping("/svm/cohort/participant")
    @ResponseBody
    public Object getCohort(final @RequestBody @Nonnull CohortRequest request) {
        LOG.info("Received CohortRequest: {}", request);
        return combineDataAccessService.getCohort(request);
    }

    @Hidden
    @PostMapping("/svm/cohort/report")
    @ResponseBody
    public Object getCohortReport(final @RequestBody @Nonnull CohortRequest request) {
        LOG.info("Received CohortRequest: {}", request);
        return combineDataAccessService.getCohortReport(request);
    }

    @Operation(summary = "Get Cohort Total Participant")
    @PostMapping(value = "/svm/cohort/totalParticipant.{extension}", produces = {
            "text/html" })
    @ResponseBody
    public ResponseEntity<?> getCohortTotalParticipant(final @RequestBody @Nonnull CohortRequest request,
            @PathVariable String extension) {
        LOG.info("Endpoint accessed: POST /svm/cohort/totalParticipant.{}", extension);
        LOG.info("getCohortTotalParticipant: Received CohortRequest: {}", request);
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                .body(combineDataAccessService.getCohortParticipantMetricsCount(request)
                        .get("total_number_of_participants"));
    }

    @Operation(summary = "Get Cohort Female Percentage")
    @PostMapping(value = "/svm/cohort/femalePercentage.{extension}", produces = {
            "text/html" })
    @ResponseBody
    public ResponseEntity<?> getCohortFemalePercentage(final @RequestBody @Nonnull CohortRequest request,
            @PathVariable String extension) {
        LOG.info("Endpoint accessed: POST  /svm/cohort/femalePercentage.{}", extension);
        LOG.info("getCohortFemalePercentage: Received CohortRequest: {}", request);
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN)
                .body(combineDataAccessService.getCohortParticipantMetricsCount(request).get("percent_female"));
    }

    @Operation(summary = "Get Cohort Average Age")
    @PostMapping(value = "/svm/cohort/average-age.{extension}", produces = {
            "text/html" })
    @ResponseBody
    public ResponseEntity<?> getCohortAverageAge(final @RequestBody @Nonnull CohortRequest request,
            @PathVariable String extension) {
        LOG.info("Endpoint accessed: POST /svm/cohort/average-age.{}", extension);
        LOG.info("getCohortAverageAge: Received CohortRequest: {}", request);
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                .body(combineDataAccessService.getCohortParticipantMetricsCount(request).get("average_age"));
    }

    @Operation(summary = "Get Cohort CGM Count")
    @PostMapping(value = "/svm/cohort/cgm-count.{extension}", produces = {
            "text/html" })
    @ResponseBody
    public ResponseEntity<?> getCohortCgmCount(final @RequestBody @Nonnull CohortRequest request,
            @PathVariable String extension) {
        LOG.info("Endpoint accessed: POST /svm/cohort/cgm-count.{}", extension);
        LOG.info("getCohortCgmCount: Received CohortRequest: {}", request);
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                .body(combineDataAccessService.getCohortCgmCount(request));
    }

    // @RouteMapping(label = "Cohort", siblingOrder = 40)
    // @GetMapping("/cohort")
    // public String cohort() {
    // return "redirect:/cohort/info";
    // }

    // @GetMapping("/cohort/info")
    // @RouteMapping(label = "Cohort", siblingOrder = 0)
    // public String cohortFinal(Model model, final HttpServletRequest request) {
    // LOG.info("Read Cohort");
    // String[] pageDescription = {
    // "This page allows users to explore participant data across multiple studies,
    // providing insights into demographics and health metrics."
    // };
    // String[] pageSubDescriptiontitle = {
    // "Includes overall metrics such as:",
    // };
    // String[] pageSubDescription = {
    // "Total participants",
    // "CGM files",
    // "Gender distribution",
    // "Average age",
    // "Filters based on study, gender, and other criteria.",
    // "Users can save filters for future sessions or clear them when no longer
    // needed."
    // };
    // String[] pageAttributestitle = {
    // "Displays key health information, such as:",
    // };
    // String[] pageAttributes = {
    // "HbA1c",
    // "Time in Range (TIR)",
    // "Time Above Range (TAR)",
    // "Glycemic Management Indicator (GMI)",
    // };
    // String[] notes = {
    // "The loading time for the Consolidated Metrics is currently a bit high due to
    // data fetching and calculations. It needs optimization."
    // };
    // model.addAttribute("pageDescription", pageDescription);
    // model.addAttribute("pagesubdescriptiontitle", pageSubDescriptiontitle);
    // model.addAttribute("pagesubdescription", pageSubDescription);
    // model.addAttribute("pageattributestitle", pageAttributestitle);
    // model.addAttribute("pageattributes", pageAttributes);
    // model.addAttribute("notes", notes);
    // return presentation.populateModel("page/cohort", model, request);
    // }
}
