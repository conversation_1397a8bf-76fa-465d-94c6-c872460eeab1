package org.diabetestechnology.drh.service.http.hub.prime.service.orcid;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class OrcidUserDetailService {
    private static final Logger LOG = LoggerFactory.getLogger(OrcidUserDetailService.class);

    private AuditService auditService;
    private UserNameService userNameService;

    @Value("${orcid.api.base-url:https://pub.orcid.org/v3.0}")
    private String orcidApiBaseUrl;

    private final RestTemplate restTemplate;

    public OrcidUserDetailService(RestTemplate restTemplate, AuditService auditService,
            UserNameService userNameService) {
        this.restTemplate = restTemplate;
        this.auditService = auditService;
        this.userNameService = userNameService;
    }

    public String getUserId() {
        LOG.info("Getting user ID");
        String provider = userNameService.getUserProvider();
        if (!provider.equalsIgnoreCase("orcid")) {
            return null;
        }
        String userId = userNameService.getUserId();
        return userId;
    }

    public Map<String, Object> getUserDetails() {
        LOG.info("Getting user details");
        String provider = userNameService.getUserProvider();
        if (!provider.equalsIgnoreCase("orcid")) {
            return Map.of("provider", provider);
        }
        String userId = userNameService.getUserId();
        String userName = userNameService.getUserName();
        String userEmail = userNameService.getUserEmail();
        String userInstitution = userNameService.getUserInstitution();
        Map<String, Object> userDetails = new HashMap<>(Map.of("userId", userId));
        userDetails.put("userName", userName);
        userDetails.put("userEmail", userEmail);
        userDetails.put("userInstitution", userInstitution);
        System.out.println("userDetails : " + userDetails);
        return userDetails;
    }

    public String extractFullName(String jsonString) {
        LOG.info("Extracting full name from ORCID JSON");
        // Parse the JSON string
        JSONObject jsonObject = new JSONObject(jsonString);
        // Navigate to the name object
        JSONObject name = jsonObject.getJSONObject("person").getJSONObject("name");
        // Extract given name and family name
        String givenName = name.getJSONObject("given-names").getString("value");
        String familyName = name.has("family-name") && !name.isNull("family-name")
                ? name.getJSONObject("family-name").getString("value")
                : "";
        // Combine into a full name
        return familyName.isEmpty() ? givenName : givenName + " " + familyName;
    }

    public String extractEmail(String jsonString) {
        LOG.info("Extracting email from ORCID JSON");
        // Parse the JSON string
        JSONObject jsonObject = new JSONObject(jsonString);
        // Navigate to the emails array
        JSONObject emails = jsonObject.getJSONObject("person").getJSONObject("emails");
        JSONArray emailArray = emails.getJSONArray("email");

        // Extract the first email value
        if (emailArray.length() > 0) {
            return emailArray.getJSONObject(0).getString("email");
        }

        // Return null if no emails are present
        return null;
    }

    public String extractInstitution(String jsonString) {
        LOG.info("Extracting institution from ORCID JSON");
        try {
            // Create ObjectMapper instance
            ObjectMapper objectMapper = new ObjectMapper();

            // Parse the JSON string
            JsonNode rootNode = objectMapper.readTree(jsonString);

            // Navigate to the organization name
            JsonNode organizationNameNode = rootNode
                    .path("activities-summary")
                    .path("employments")
                    .path("affiliation-group")
                    .path(0) // First affiliation group
                    .path("summaries")
                    .path(0) // First summary
                    .path("employment-summary")
                    .path("organization")
                    .path("name");

            // Return the organization name value
            return organizationNameNode.asText(null); // Return null if the value is missing
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("Error while parsing JSON", e);
            return null;
        }
    }

    public ResponseEntity<String> getOrcidUserInfo(String orcidId) {
        LOG.info("Fetching ORCID details for ORCID ID: {}", orcidId);
        try {
            // Validate ORCID ID format (basic check)
            if (!orcidId.matches("\\d{4}-\\d{4}-\\d{4}-\\d{3}[\\dX]")) {
                return ResponseEntity.badRequest().body("Invalid ORCID ID format");
            }

            // Build the URL to fetch ORCID details
            String url = String.format("%s/%s", orcidApiBaseUrl, orcidId);

            // Make the HTTP request
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            // Return the response
            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());

        } catch (Exception e) {
            LOG.error("Error fetching ORCID details", e);
            return ResponseEntity.status(500).body("Error fetching ORCID details: " + e.getMessage());
        }
    }

}
