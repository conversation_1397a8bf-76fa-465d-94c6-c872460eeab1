package org.diabetestechnology.drh.service.http.pg.service;

import java.util.List;

import org.diabetestechnology.drh.service.http.pg.ux.PartyController;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

@Service
public class PartyService {

    private final DSLContext dsl;
    private static final Logger LOG = LoggerFactory.getLogger(PartyController.class);

    public PartyService(
            @Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    @Transactional
    public String getPartyIdByUserId(String userId) {
        LOG.info("Fetching partyId for userId: {}", userId);
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();

        if (principal instanceof UserDetails) {
            final var query = dsl.select(DSL.field(
                    "party_id"))
                    .from("drh_stateless_authentication.super_admin_view")
                    .where(DSL.field("email").eq(DSL.val(userId)));
            String superUserPartyId = query.fetchOneInto(String.class);
            LOG.info("Super User Party ID: {}", superUserPartyId);
            return superUserPartyId;

        }
        final var query = dsl.select(DSL.field(
                "practitioner_party_id"))
                .from("drh_stateless_authentication.user_profile_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(userId)));
        String practitionerPartyId = query.fetchOneInto(String.class);

        LOG.info("practitionerPartyId: {}", practitionerPartyId);
        // Execute and fetch result
        return practitionerPartyId;
    }

    @Transactional

    public String getPartyIdByOrganization(String organizationId) {
        LOG.info("Fetching partyId for organizationId: {}", organizationId);
        final var query = dsl.select(DSL.field(
                "organization_party_id"))
                .from("drh_stateless_research_study.organization_party_view")
                .where(DSL.field("organization_id").eq(DSL.val(organizationId)));
        String partyIdByOrganization = query.fetchOneInto(String.class);
        LOG.info("getPartyIdByOrganization - query: {}", query);
        LOG.info("partyIdByOrganization: {}", partyIdByOrganization);
        // Execute and fetch result
        return partyIdByOrganization;
    }

    @Transactional

    public String getActiveUserPartyId(String userId) {
        LOG.info("Fetching practioner and organization partyId for userId: {}", userId);
        final var query = dsl.select(DSL.field(
                "jsonb_agg(" +
                        "jsonb_build_object(" +
                        "'practitioner_party_id', practitioner_party_id, " +
                        "'organization_party_id', organization_party_id " +
                        ")" +
                        ")",
                JSONB.class))
                .from("drh_stateless_authentication.user_profile_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(userId)));
        LOG.info("getActiveUserPartyId - query: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        LOG.info("activeUserPartyId: {}", jsonbResult);

        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }

        try {
            // Convert JSONB to String
            final var jsonString = jsonbResult.data();

            // Use Jackson to prettify
            ObjectMapper mapper = new ObjectMapper();
            Object json = mapper.readValue(jsonString, Object.class);
            ObjectWriter writer = mapper.writerWithDefaultPrettyPrinter();
            return writer.writeValueAsString(json);
        } catch (Exception e) {
            LOG.error("Error while processing JSON", e);
            // Handle any exceptions during JSON processing
            e.printStackTrace();
            return "{}"; // Return empty JSON in case of error
        }
    }

    @Transactional

    public String getOrganizationPartyIdByUser(String userId) {
        LOG.info("Fetching partyId for userId: {}", userId);

        String organizationPartyId = getUserOrganizationPartyId(userId);
        LOG.info("organizationPartyId: {}", organizationPartyId);
        LOG.info("getOrganizationPartyIdByUser - query: {}", organizationPartyId);
        return organizationPartyId;

        // final var query = dsl
        // .select(DSL.field(
        // "organization_party_id"))
        // .from("drh_stateless_authentication.user_profile_view")
        // .where(DSL.field("provider_user_id").eq(DSL.val(
        // userId)));
        // LOG.info("getOrganizationPartyIdByUser - query: {}", query);

        // String organizationPartyIdByUser = query
        // .fetchOneInto(String.class);

        // LOG.info("organizationPartyIdByUser: {}", organizationPartyIdByUser);
        // return organizationPartyIdByUser;
    }

    @Transactional
    public String getTenantIdByUserId(String userId) {
        LOG.info("Fetching tenantId for userId: {}", userId);
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();
        if ((principal instanceof UserDetails)) {
            return getSuperUserOrganizationTenantId(userId);
        }
        final var query = dsl.select(DSL.field(
                "tenant_id"))
                .from("drh_stateless_authentication.user_list_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                .limit(1);
        LOG.info("getTenantIdByUserId - query: {}", query);

        String tenantId = query.fetchOneInto(String.class);

        LOG.info("tenantId: {}", tenantId);
        return tenantId;
    }

    @Transactional
    public List<String> getRolesByUserId(String userId) {
        LOG.info("Fetching roles for userId: {}", userId);
        final var query = dsl.select(DSL.field(
                "user_roles"))
                .from("drh_stateless_authentication.user_list_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(userId)));
        LOG.info("getRolesByUserId - query: {}", query);

        List<String> roles = query.fetchInto(String.class);

        LOG.info("roles: {}", roles);
        return roles;
    }

    @Transactional
    public String getUserOrganizationPartyId(String userId) {

        try {
            final var authentication = SecurityContextHolder.getContext().getAuthentication();
            Object principal = authentication.getPrincipal();
            String organizationPartyId = "";
            if (principal instanceof UserDetails) {
                organizationPartyId = dsl
                        .select(DSL.field("organization_party_id"))
                        .from("drh_stateless_authentication.super_admin_view")
                        .where(DSL.field("email").eq(DSL.val(userId)))
                        .limit(1) // To ensure we only check if at least one row exists
                        .fetchOneInto(String.class);
            } else {

                LOG.info("Fetching user details for user ID: {}", userId);
                organizationPartyId = dsl
                        .select(DSL.field("organization_party_id"))
                        .from("drh_stateless_authentication.user_profile_view")
                        .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                        .limit(1) // To ensure we only check if at least one row exists
                        .fetchOneInto(String.class);
            }
            LOG.debug("Fetched organizationPartyId for user {}: {}", userId, organizationPartyId);
            return organizationPartyId;

        } catch (Exception e) {
            LOG.error("Error fetching organization party Id: {}", e.getMessage(), e);
            return "";
        }
    }

    @Transactional
    public String getSuperUserOrganizationTenantId(String userId) {

        try {
            final var organizationPartyId = getUserOrganizationPartyId(userId);

            LOG.debug("Fetched organizationPartyId for user {}: ", organizationPartyId);
            String organizationTenantId = dsl
                    .select(DSL.field("organization_id"))
                    .from("drh_stateless_research_study.organization_party_view")
                    .where(DSL.field("organization_party_id").eq(DSL.val(
                            organizationPartyId)))
                    .limit(1)
                    .fetchOneInto(String.class);

            LOG.debug("Fetched organizationPartyName for user {}: ", organizationTenantId);
            return organizationTenantId;

        } catch (Exception e) {
            LOG.error("Error fetching Tenant Id: {}", e.getMessage(), e);
            return "";
        }
    }
}
