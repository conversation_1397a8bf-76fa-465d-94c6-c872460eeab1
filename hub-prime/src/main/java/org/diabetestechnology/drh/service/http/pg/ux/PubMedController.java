package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.PubMedService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "PubMed API")
public class PubMedController {
    private static final Logger LOG = LoggerFactory.getLogger(PubMedController.class);
    private final PubMedService pubMedService;

    public PubMedController(PubMedService pubMedService) {
        this.pubMedService = pubMedService;
    }

    @GetMapping("/pubmed")
    @ResponseBody
    @Operation(summary = "find Pubmer Id from DOI")
    public Response getPubMed(@RequestParam String doi) {
        LOG.info("DOI: {}", doi);
        final var pubmedId = pubMedService.getPubmedId(doi);
        try {
            LOG.info("PubMed ID corresponds to the given is DOI: {}", pubmedId);
            if (pubmedId != null) {
                return Response.builder()
                        .data(new HashMap<>(Map.of("pubmedId",
                                pubmedId)))
                        .status("success")
                        .message("Pubmed Id found")
                        .build();
            }
            return Response.builder()
                    .data(new HashMap<>())
                    .status("failed")
                    .message("Pubmed Id not found")
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to read pubmed Id from DOI")
                    .errors("Error in reading pubmed Id from DOI: ")
                    .build();
        }

    }

    @GetMapping("/ncbi/metadata")
    @ResponseBody
    @Operation(summary = "find Study Meta Datafrom PUBMED ID")
    public Response getNcbiMetadata(@RequestParam String pubmedId) {
        LOG.info("PUBMED ID: {}", pubmedId);
        final var metadata = pubMedService.getMetadata(pubmedId);
        try {
            LOG.info("Metadata corresponds to the given is PUBMED ID: {}", metadata);
            if (metadata != null) {
                return Response.builder()
                        .data(new HashMap<>(Map.of("metadata",
                                metadata)))
                        .status("success")
                        .message("Metadata found")
                        .build();
            }
            return Response.builder()
                    .data(new HashMap<>())
                    .status("failed")
                    .message("Details not found")
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to read Metadata")
                    .errors("Error in reading Metadata: ")
                    .build();
        }

    }

    @GetMapping("/crossref/metadata")
    @ResponseBody
    @Operation(summary = "find Study Meta Datafrom DOI")
    public Response getCrossrefMetadata(@RequestParam String doi) {
        LOG.info("DOI: {}", doi);
        final var metadata = pubMedService.getCrossrefMetadata(doi);
        try {
            LOG.info("Metadata corresponds to the given is PUBMED ID: {}", metadata);
            if (metadata != null) {
                return Response.builder()
                        .data(new HashMap<>(Map.of("metadata",
                                metadata)))
                        .status("success")
                        .message("Metadata found")
                        .build();
            }
            return Response.builder()
                    .data(new HashMap<>())
                    .status("failed")
                    .message("Details not found")
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to read Metadata")
                    .errors("Error in reading Metadata: ")
                    .build();
        }

    }

    @GetMapping("/pubmed/details")
    @ResponseBody
    @Operation(summary = "Fetch PubMed IDs and Metadata using DOI or PubMed ID")
    public Response getPubMedDetails(
            @RequestParam(required = false) String doi,
            @RequestParam(required = false) String pubmedId) {

        LOG.info("Received Request: DOI={}, PubMed ID={}", doi, pubmedId);

        if ((doi == null || doi.isBlank()) && (pubmedId == null || pubmedId.isBlank())) {
            return Response.builder()
                    .data(Map.of())
                    .status("failed")
                    .message("Both DOI and PubMed ID cannot be null or empty")
                    .build();
        }

        if (pubmedId == null || pubmedId.isBlank()) {
            Object pubmedIdObj = pubMedService.getPubmedId(doi);
            LOG.info("Fetched PubMed ID: {}", pubmedIdObj);

            pubmedId = pubMedService.extractPubmedId(pubmedIdObj);
            if (pubmedId.isBlank()) {
                return Response.builder()
                        .data(Map.of())
                        .status("failed")
                        .message("PubMed ID not found for DOI: " + doi)
                        .build();
            }
        }

        var metadata = pubMedService.getMetadata(pubmedId);
        if (metadata == null) {
            return Response.builder()
                    .data(Map.of("pubmedId", pubmedId))
                    .status("failed")
                    .message("Metadata not found for PubMed ID: " + pubmedId)
                    .build();
        }

        return Response.builder()
                .data(Map.of("pubmedId", pubmedId, "metadata", metadata))
                .status("success")
                .message("PubMed details fetched successfully")
                .build();
    }
}
