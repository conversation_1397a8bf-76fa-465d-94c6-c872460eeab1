package org.diabetestechnology.drh.service.http.pg.request;

import java.util.List;

// @formatter:off
public record StudyCollaborationTeamRequest(
        String studyId,
        Collaborators coInvestigators,
        Investigator principalInvestigator,
        Collaborators coAuthors,
        Author principalAuthor) {

    public record Investigator(String id, String code, boolean isPrincipal) {
    }

    public record Author(String id, String code, boolean isPrincipal) {
    }

    public record Collaborators(List<String> ids, String code) {
    }
}
// @formatter:on
