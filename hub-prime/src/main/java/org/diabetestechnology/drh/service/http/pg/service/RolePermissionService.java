package org.diabetestechnology.drh.service.http.pg.service;

import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.oauth2.OrcidOAuth2UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RolePermissionService {

    private static final Logger LOG = LoggerFactory.getLogger(OrcidOAuth2UserService.class);

    private final UserRoleService userRoleService;
    private final UserNameService userNameService;
    private final PartyService partyService;

    public RolePermissionService(
            UserRoleService userRoleService, UserNameService userNameService,
            PartyService partyService) {
        this.userRoleService = userRoleService;
        this.userNameService = userNameService;
        this.partyService = partyService;
    }

    public List<String> getPermissionsForUser() {
        try {
            String userId = userNameService.getUserId();
            String partyId = partyService.getPartyIdByUserId(userId);
            return userRoleService.getFlatPermissionListByRoles(partyId);
        } catch (Exception e) {
            LOG.error("Failed to fetch permissions for current user", e);
            return List.of();
        }
    }
}
