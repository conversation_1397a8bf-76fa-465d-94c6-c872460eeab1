package org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class AIService {
    private static final Logger LOG = LoggerFactory.getLogger(AIService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    DataBaseAttachService databaseAttachService;

    @Autowired
    UserNameService userNameService;

    final String alias = "client";

    public void saveAIInteraction(VannaAIRequest payload, HttpServletRequest request) {
        try {
            LOG.info("Save AI Interaction with Payload : {}", payload);
            ZonedDateTime utcNow = ZonedDateTime.now(ZoneOffset.UTC);
            String utcTime = utcNow.toString();
            String sql = "INSERT INTO " + alias
                    + ".vanna_ai_request_respose (id, question, sql_query, results, created_at, updated_at, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)";

            databaseAttachService.refreshClientServerDb();
            jdbcTemplate.update(sql, UUID.randomUUID().toString(), payload.question(), payload.sql(), payload.result(),
                    utcTime, utcTime, userNameService.getUserId());
        } catch (Exception e) {
            LOG.error("Error while saving AI Interaction", e);
        }
    }

    public List<Map<String, Object>> getAIInteraction(HttpServletRequest request) {
        String sql = "SELECT id, question, sql_query, results, created_at, updated_at, created_by FROM " + alias
                + ".vanna_ai_request_respose where created_by= ? ORDER BY created_at";
        try {
            databaseAttachService.refreshClientServerDb();
            return jdbcTemplate.queryForList(sql, userNameService.getUserId());
        } catch (Exception e) {
            LOG.error("Error while fetching AI Interaction", e);
        }
        return new ArrayList<>();
    }

    public List<Map<String, Object>> getAllAIInteraction() {
        String sql = "SELECT id, question, sql_query, results, created_at, updated_at, created_by FROM " + alias
                + ".vanna_ai_request_respose ORDER BY created_at";
        try {
            databaseAttachService.refreshClientServerDb();
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            LOG.error("Error while fetching AI Interaction", e);
        }
        return new ArrayList<>();
    }

}
