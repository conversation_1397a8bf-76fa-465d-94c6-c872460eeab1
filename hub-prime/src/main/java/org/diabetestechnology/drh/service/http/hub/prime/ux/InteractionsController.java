package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Hidden
@Tag(name = "DRH Hub Interactions UX API", description = "DRH Hub Interactions UX API")
public class InteractionsController {

    private final Presentation presentation;
    private static final Logger LOG = LoggerFactory.getLogger(InteractionsController.class);

    public InteractionsController(final Presentation presentation) {
        this.presentation = presentation;
    }

    @RouteMapping(label = "Interactions", siblingOrder = 50)
    @GetMapping("/interaction")
    public String interactions() {
        return "redirect:/interaction/study/success";
    }

    @RouteMapping(label = "Study Interaction", title = "Study Interaction", siblingOrder = 10)
    @GetMapping("/interaction/study")
    public String studyInteraction(final Model model, final HttpServletRequest request) {
        LOG.debug("Study Interaction");
        return "redirect:/interaction/study/success";
    }

    @RouteMapping(label = "Successful Study Interaction", title = "Successful Study Interaction", siblingOrder = 10)
    @GetMapping("/interaction/study/success")
    public String successStudy(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page displays logs of successful study interactions, including study creation, edits, and updates. It tracks when these actions were completed, and by which service or user, helping maintain a clear record of changes made to study data." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/studyInteractionSuccess", model, request);
    }

    @RouteMapping(label = "Failed Study Interaction", title = "Failed Study Interaction", siblingOrder = 20)
    @GetMapping("/interaction/study/failed")
    public String failedStudy(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page lists failed attempts to create, edit, or update studies. It helps identify when and why a study-related operation failed, along with the service or user involved, supporting efficient troubleshooting and data consistency checks." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/studyInteractionFailed", model, request);
    }

    @RouteMapping(label = "Participant Interaction", title = "Participant Interaction", siblingOrder = 20)
    @GetMapping("/interaction/participant")
    public String participantInteraction(final Model model, final HttpServletRequest request) {
        return "redirect:/interaction/participant/success";
    }

    @RouteMapping(label = "Successful Participant Interaction", title = "Successful Study Interaction", siblingOrder = 10)
    @GetMapping("/interaction/participant/success")
    public String successParticipant(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page shows logs of successful participant interactions, including participant creation and updates. It tracks when these actions were performed and by which service or user, ensuring a clear record of changes made to participant data." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/participantInteractionSuccess", model, request);
    }

    @RouteMapping(label = "Failed Participant Interaction", title = "Failed Study Interaction", siblingOrder = 20)
    @GetMapping("/interaction/participant/failed")
    public String failedParticipant(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page lists failed attempts to create or update participants. It captures details of when the failure occurred and which service or user was involved, aiding in troubleshooting and ensuring the integrity of participant data." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/participantInteractionFailed", model, request);
    }

    @RouteMapping(label = "File Interaction", title = "Participant Interaction", siblingOrder = 30)
    @GetMapping("/interaction/file")
    public String fileInteraction(final Model model, final HttpServletRequest request) {
        return "redirect:/interaction/file/database";
    }

    @RouteMapping(label = "Database Interaction", title = "Database Interaction", siblingOrder = 10)
    @GetMapping("/interaction/file/database")
    public String databaseInteraction(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page provides a detailed view of all interactions with the database file, including read/write operations, file access events, and any errors or warnings encountered during usage. It helps track when and how the database was accessed, by which service or user, and provides useful context for debugging and auditing database activity." };

        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/fileInteraction", model, request);
    }

    @RouteMapping(label = "Successful Participant File Interaction", title = "Successful Participant File Interaction", siblingOrder = 20)
    @GetMapping("/interaction/file/success-participant")
    public String successParticipantFileInteraction(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page shows logs of all successfully uploaded participant files, including the time of upload and the service or user that performed the operation." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/participantFileInteractionSuccess", model, request);
    }

    @RouteMapping(label = "Failed Participant File Interaction", title = "Failed Participant File Interaction", siblingOrder = 30)
    @GetMapping("/interaction/file/failed-participant")
    public String failedParticipantFileInteraction(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page lists all failed attempts to upload participant files, providing details such as the time of failure and the service or user involved." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/participantFileInteractionFailed", model, request);
    }

    @RouteMapping(label = "Successful CGM File Interaction", title = "Successful Study Interaction", siblingOrder = 40)
    @GetMapping("/interaction/file/success-cgm")
    public String successCgmFileInteraction(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page shows logs of all successfully uploaded CGM files, including the upload time and the service or user responsible for the upload." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/cgmFileInteractionSuccess", model, request);
    }

    @RouteMapping(label = "Failed CGM File Interaction", title = "Failed Study Interaction", siblingOrder = 50)
    @GetMapping("/interaction/file/failed-cgm")
    public String failedCgmFileInteraction(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page lists all failed CGM file upload attempts, capturing the failure time and the service or user that attempted the upload." };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/cgmFileInteractionFailed", model, request);
    }

    @RouteMapping(label = "Activity Log", title = "Activity Log", siblingOrder = 40)
    @GetMapping("/interaction/activity")
    public String activity(final Model model, final HttpServletRequest request) {
        return "redirect:/interaction/activity/user";
    }

    @RouteMapping(label = "User Session", title = "User Session", siblingOrder = 10)
    @GetMapping("/interaction/activity/user")
    public String activityLog(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page displays user session activity logs within the application, including login and logout events, navigation patterns, and key user actions. It helps track how users interact with the system over time, supporting both usage analysis and auditing." };

        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/userSession", model, request);
    }

    // @RouteMapping(label = "Meals Or Fitness Interaction", title = "Meals Or
    // Fitness Interaction", siblingOrder = 60)
    // @GetMapping("/interaction/file/meals-fitness")
    // public String mealsFitness(final Model model, final HttpServletRequest
    // request) {
    // return "redirect:/interaction/file/success-meals-fitness";
    // }

    @RouteMapping(label = "Successful Meals Or Fitness Interaction", title = "Successful Meals Or Fitness Interaction", siblingOrder = 60)
    @GetMapping("/interaction/file/success-meals-fitness")
    public String mealsFitnessSuccessLog(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page displays successful meals or fitness interaction logs within the application." };

        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/mealsOrFitnessFileInteractionSuccess", model, request);
    }

    @RouteMapping(label = "Failed Meals Or Fitness Interaction", title = "Failed Meals Or Fitness Interaction", siblingOrder = 70)
    @GetMapping("/interaction/file/failed-meals-fitness")
    public String mealsFitnessFailedLog(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This page displays failed meals or fitness interaction logs within the application." };

        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/interactions/mealsOrFitnessFileInteractionFailed", model, request);
    }

}
