package org.diabetestechnology.drh.service.http.pg.ux;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.Map;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@Tag(name = "DRH Party APIs")
public class PartyController {
    @Autowired
    private PartyService partyService;
    @Autowired
    UserNameService userNameService;
    private static final Logger LOG = LoggerFactory.getLogger(PartyController.class);

    @GetMapping("/user-party-id")
    @Operation(summary = "get partyId of User")
    @ResponseBody
    @Hidden
    public Response getPartyIdByUserId() {
        LOG.info("get partyId of User");
        try {
            final var userId = userNameService.getUserId();

            String partyId = partyService.getPartyIdByUserId(userId);
            if (partyId == null) {

                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message("Party not found for user: "
                                + userId)
                        .errors(null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("partyId",
                            partyId))
                    .status("success")
                    .message("Successfully read partyId for user: "
                            + userId)
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("Error while retrieving partyId for userId: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving partyId")
                    .errors("Error retrieving partyId: " + e
                            .getMessage())
                    .build();
        }
    }

    @GetMapping("/organization-party-id")
    @Operation(summary = "get partyId of Organization ")
    @ResponseBody
    @Hidden
    public Response getPartyIdByOrganization(@RequestParam String organizationId) {
        LOG.info("get partyId of Organization");
        try {

            String partyId = partyService.getPartyIdByOrganization(organizationId);
            if (partyId == null) {
                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message("Party not found for the Organization")
                        .errors("Party not found for the Organization: "
                                + organizationId)
                        .build();
            }

            return Response.builder()
                    .data(Map.of("organizationPartyId",
                            partyId))
                    .status("success")
                    .message("Successfully read partyId for the Organization: "
                            + organizationId)
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("Error while retrieving partyId for organizationId: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving organizationPartyId")
                    .errors("Error retrieving organizationPartyId: " + e
                            .getMessage())
                    .build();
        }
    }

    @GetMapping("/active-user/party-id")
    @Operation(summary = "get partyId of Active User")
    @ResponseBody
    @Hidden
    public Response getActiveUserPartyId() {
        LOG.info("get partyId of Active User");
        try {
            final var userId = userNameService.getUserId();

            String partyId = partyService.getActiveUserPartyId(userId);
            if (partyId == null) {

                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message("Party not found for user: "
                                + userId)
                        .errors(null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("partyData",
                            partyId))
                    .status("success")
                    .message("Successfully read partyId for user: "
                            + userId)
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("Error while retrieving partyId for userId: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving partyId")
                    .errors("Error retrieving partyId: " + e
                            .getMessage())
                    .build();
        }
    }

}
