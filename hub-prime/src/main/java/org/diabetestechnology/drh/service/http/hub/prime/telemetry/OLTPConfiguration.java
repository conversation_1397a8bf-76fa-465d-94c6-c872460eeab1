package org.diabetestechnology.drh.service.http.hub.prime.telemetry;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.exporter.otlp.http.logs.OtlpHttpLogRecordExporter;
import io.opentelemetry.instrumentation.logback.appender.v1_0.OpenTelemetryAppender;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.logs.SdkLoggerProvider;
import io.opentelemetry.sdk.logs.export.BatchLogRecordProcessor;
import io.opentelemetry.sdk.resources.Resource;
import jakarta.annotation.PostConstruct;

@Configuration
public class OLTPConfiguration {
    @Value("${management.otlp.logging.endpoint}")
    private String endPoint;
    @Value("${management.otlp.logging.headers.Authorization}")
    private String token;
    @Value("${management.otlp.logging.headers.stream-name}")
    private String streamName;
    @Value("${management.otlp.logging.headers.service.name}")
    private String serviceName;
    @Value("${management.otlp.logging.headers.organization}")
    private String organization;

    @PostConstruct
    public void initializeOpenTelemetry() {
        // Configure OTLP Log Exporter
        OtlpHttpLogRecordExporter logExporter = OtlpHttpLogRecordExporter.builder()
                .setEndpoint(endPoint)
                .addHeader("stream-name", streamName)
                .addHeader("Authorization", token)
                .addHeader("organization", organization)
                .build();

        // Create a Log Record Processor
        BatchLogRecordProcessor logProcessor = BatchLogRecordProcessor.builder(logExporter).build();

        // Define the service name as part of the Resource
        Resource resource = Resource.getDefault()
                .merge(Resource.create(Attributes.builder()
                        .put("service.name", serviceName)
                        .build()));

        // Create LoggerProvider and OpenTelemetry SDK
        SdkLoggerProvider loggerProvider = SdkLoggerProvider.builder()
                .setResource(resource) // Set the resource with service name
                .addLogRecordProcessor(logProcessor)
                .build();

        OpenTelemetrySdk openTelemetrySdk = OpenTelemetrySdk.builder()
                .setLoggerProvider(loggerProvider)
                .build();

        // Install OpenTelemetrySdk into OpenTelemetryAppender
        OpenTelemetryAppender.install(openTelemetrySdk);
    }

}
