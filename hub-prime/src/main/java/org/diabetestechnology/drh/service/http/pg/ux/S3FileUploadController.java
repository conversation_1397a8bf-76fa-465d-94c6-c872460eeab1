package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.service.S3FileUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;

import java.io.IOException;
import org.diabetestechnology.drh.service.http.pg.Response;

@Controller
@Tag(name = "DRH Hub File API Endpoints")
public class S3FileUploadController {
    private static final Logger LOG = LoggerFactory.getLogger(S3FileUploadController.class);

    private final S3FileUploadService s3FileUploadService;

    public S3FileUploadController(S3FileUploadService s3FileUploadService) {
        this.s3FileUploadService = s3FileUploadService;
    }

    @PostMapping("/file/upload")
    @Operation(summary = "Upload a csv file to S3")
    @Hidden
    @ResponseBody
    public Response uploadFile(@RequestPart("file") @Nonnull MultipartFile file) throws IOException {
        LOG.info("Uploading file: {}", file.getOriginalFilename());
        return s3FileUploadService.uploadFile(file);
    }

    @GetMapping("/participants/file/template")
    @ResponseBody
    @Operation(summary = "Download CSV template for participant data")
    public ResponseEntity<byte[]> downloadParticipantTemplate(
            @RequestParam(required = false, defaultValue = "csv") String format) throws IOException {

        if (!"csv".equalsIgnoreCase(format)) {
            return ResponseEntity.badRequest().build();
        }
        byte[] csvBytes = s3FileUploadService.generateCsvTemplate();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"participant_template.csv\"")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .body(csvBytes);
    }

}
