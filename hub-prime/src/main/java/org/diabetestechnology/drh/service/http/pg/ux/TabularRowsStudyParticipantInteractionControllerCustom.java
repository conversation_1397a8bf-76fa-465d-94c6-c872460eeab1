package org.diabetestechnology.drh.service.http.pg.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.filter.CustomTabularFilter;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.jetbrains.annotations.NotNull;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.JooqRowsSupplier.TypableTable;

import lib.aide.tabular.TabularRowsRequest;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jooq.*;
import org.jooq.Record;

@Controller
@Tag(name = "DRH Hub Custom Tabular Row Study Participant Interaction API Endpoints for AG Grid")
@org.springframework.context.annotation.Configuration
public class TabularRowsStudyParticipantInteractionControllerCustom {
    static private final Logger LOG = LoggerFactory.getLogger(
            TabularRowsStudyParticipantInteractionControllerCustom.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final CustomTabularFilter customTabularFilter;

    @Bean
    public com.fasterxml.jackson.databind.Module jsonbModule() {
        com.fasterxml.jackson.databind.module.SimpleModule module = new SimpleModule();
        module.addSerializer(org.jooq.JSONB.class, new JsonSerializer<org.jooq.JSONB>() {
            @Override
            public void serialize(org.jooq.JSONB value, JsonGenerator gen, SerializerProvider serializers)
                    throws IOException {
                gen.writeRawValue(value.data()); // or gen.writeString(value.data());
            }
        });
        return module;
    }

    public TabularRowsStudyParticipantInteractionControllerCustom(final UdiSecondaryDbConfig udiPrimeDbConfig,
            UserNameService userNameService,
            CustomTabularFilter customTabularFilter) {
        this.customTabularFilter = customTabularFilter;
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Successful Study Participant Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/study/participant/interaction/success/parent/{schemaName}/{masterTableNameOrViewName}.json")
    @ResponseBody
    public Object parentSuccessStudyParticipantInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
            throws SQLException {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();
            // bindValues.add("[]");

            var queryString = "";
            if (userNameService.isAdmin()) {
                queryString = "(" +
                        "SELECT DISTINCT ON (study_id) * " +
                        "FROM drh_stateless_activity_audit.study_participant_interaction_view " +
                        "WHERE (CAST(interaction_hierarchy AS jsonb) = CAST('[]' AS jsonb) OR interaction_hierarchy IS NULL) "
                        +
                        "AND study_id IS NOT NULL " +
                        "AND study_display_id IS NOT NULL " +
                        "ORDER BY study_id, created_at" +
                        ") AS t";
            } else if (userNameService.isAuthenticatedUser()) {
                final var userId = userNameService.getCurrentuserPartyId();
                queryString = "(" +
                        "SELECT DISTINCT ON (study_id) * " +
                        "FROM drh_stateless_activity_audit.study_participant_interaction_view " +
                        "WHERE (CAST(interaction_hierarchy AS jsonb) = CAST('[]' AS jsonb) OR interaction_hierarchy IS NULL) "
                        +
                        "AND study_id IS NOT NULL " +
                        "AND study_display_id IS NOT NULL " +
                        "AND created_by = '" + userId + "' " +
                        "ORDER BY study_id, created_at" +
                        ") AS t";

            } else {
                final var userId = userNameService.getCurrentuserPartyId();
                queryString = "(" +
                        "SELECT DISTINCT ON (study_id) * " +
                        "FROM drh_stateless_activity_audit.study_participant_interaction_view " +
                        "WHERE (CAST(interaction_hierarchy AS jsonb) = CAST('[]' AS jsonb) OR interaction_hierarchy IS NULL) "
                        +
                        "AND study_id IS NOT NULL " +
                        "AND study_display_id IS NOT NULL " +
                        "AND created_by = '" + userId + "' " +
                        "ORDER BY study_id, created_at" +
                        ") AS t";

            }
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            var query = udiPrimeDbConfig.dsl()
                    .select()
                    .from(
                            DSL.table(queryString));
            if (finalCondition != null) {
                query = (@NotNull SelectJoinStep<Record>) query.where(finalCondition);
            }
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectJoinStep<Record>) query
                        .orderBy(
                                customTabularFilter
                                        .createSortCondition(payload.sortModel(),
                                                typableTable));
            }

            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "List of participants with successful interactions")
    @GetMapping("/api/ux/tabular/jooq/study/participant/interaction/success/sub/{schemaName}/{masterTableNameOrViewName}/study_id/{studyId}.json")
    @ResponseBody
    public Object subSuccessStudyParticipantInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyId) {

        var query = udiPrimeDbConfig.dsl()
                .select()
                .from(
                        DSL.table(
                                "(" +
                                        "SELECT DISTINCT ON (participant_id) * " +
                                        "FROM drh_stateless_activity_audit.study_participant_interaction_view " +
                                        "WHERE (CAST(interaction_hierarchy AS jsonb) = CAST('[]' AS jsonb) OR interaction_hierarchy IS NULL) "
                                        +
                                        "AND study_id = '" + studyId + "' " +
                                        "AND study_display_id IS NOT NULL " +
                                        "ORDER BY participant_id, created_at" +
                                        ") AS t"));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "Interaction details for a specific participant_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/study/participant/interaction/success/child/{schemaName}/{masterTableNameOrViewName}/participant_interaction_id/{studyParticipantInteractionId}.json")
    @ResponseBody
    public Object childSuccessStudyParticipantInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyParticipantInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("participant_interaction_id").eq(DSL.value(
                        studyParticipantInteractionId)))
                        .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                .eq(DSL.val("\"" + studyParticipantInteractionId + "\"")))))
                .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.SUCCESS)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "SQL rows for a specific participant_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/study/participant/interaction/child/modal/{schemaName}/{masterTableNameOrViewName}/participant_interaction_id/{studyParticipantInteractionId}.json")
    @ResponseBody
    public Object childStudyParticipantInteractionModal(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyParticipantInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where(typableTable.column("participant_interaction_id").eq(DSL.value(
                        studyParticipantInteractionId)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "List of Study with failed Participant Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/study/participant/interaction/failed/parent/{schemaName}/{masterTableNameOrViewName}.json")
    @ResponseBody
    public Object parentFailedStudyParticipantInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            TypableTable typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();

            final var userId = userNameService.getCurrentuserPartyId();
            final var organizationId = userNameService.getCurrentUserOrganizationPartyId();
            Field<String> studyParticipantInteractionId = DSL.field("participant_interaction_id", SQLDataType.VARCHAR);
            Field<String> studyId = DSL.field("study_id", SQLDataType.VARCHAR);
            Field<String> interactionStatus = DSL.field("interaction_status", SQLDataType.VARCHAR);
            Field<String> organizationPartyId = DSL.field("organization_party_id", SQLDataType.VARCHAR);
            Field<String> createdBy = DSL.field("created_by", SQLDataType.VARCHAR);
            Field<LocalDateTime> createdAt = DSL.field(
                    "(select created_at from drh_stateless_activity_audit.study_participant_interaction_view spiv where spiv.study_id=spv.study_id limit 1 ) as created_at",
                    SQLDataType.LOCALDATETIME);
            // Subquery: extract first element from interaction_hierarchy and compare
            Select<Record1<String>> subquery = DSL.select(
                    DSL.field("((jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]'))::jsonb #>> '{}')",
                            SQLDataType.VARCHAR))
                    .from(typableTable.table())
                    .where(interactionStatus.eq(ActionStatus.FAILED));
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            var query = udiPrimeDbConfig.dsl()
                    .selectDistinct(DSL.field("study_id"), DSL.field("organization_party_id"), DSL
                            .field("study_display_id"),
                            DSL.field(
                                    "study_title"),
                            DSL.field("organization_name"), DSL.field(
                                    "created_by_name"),
                            DSL.field(
                                    createdAt))

                    .from(typableTable
                            .table().as("spv"))
                    .where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and(
                    studyParticipantInteractionId.in(subquery)
                            .or(studyId.isNull()))
                    .and(createdBy.eq(userId))
                    .and(organizationPartyId.eq(DSL.value(organizationId)));
            bindValues.add(ActionStatus.FAILED);

            if (userNameService.isAdmin()) {
                query = udiPrimeDbConfig.dsl()
                        .selectDistinct(DSL.field("study_id"), DSL.field("organization_party_id"), DSL
                                .field("study_display_id"),
                                DSL.field(
                                        "study_title"),
                                DSL.field("organization_name"), DSL.field(
                                        "created_by_name"),
                                DSL.field(
                                        createdAt))

                        .from(typableTable
                                .table().as("spv"))
                        .where("1=1");
                if (finalCondition != null) {
                    query = query.and(finalCondition);
                }
                query = query.and(
                        studyParticipantInteractionId.in(subquery)
                                .or(studyId.isNull()))
                        .and(organizationPartyId.eq(DSL.value(organizationId)));

            }
            if (!userNameService.isAdmin()) {
                bindValues.add(userId);
            }
            bindValues.add(organizationId);
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectConditionStep<Record7<Object, Object, Object, Object, Object, Object, LocalDateTime>>) query
                        .orderBy(
                                customTabularFilter.createSortCondition(payload.sortModel(),
                                        typableTable));
            }
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "List of participants with failed interactions")
    @GetMapping("/api/ux/tabular/jooq/study/participant/interaction/failed/child/{schemaName}/{masterTableNameOrViewName}/participant_interaction_id/{studyParticipantInteractionId}.json")
    @ResponseBody
    public Object childFailedStudyParticipantInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyParticipantInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("participant_interaction_id").eq(DSL.value(
                        studyParticipantInteractionId)))
                        .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                .eq(DSL.val("\"" + studyParticipantInteractionId + "\"")))))
                .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.FAILED)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "List of participants with failed interactions")
    @GetMapping("/api/ux/tabular/jooq/study/participant/interaction/failed/sub/{schemaName}/{masterTableNameOrViewName}/study_id/{studyId}.json")
    @ResponseBody
    public Object subFailedStudyParticipantInteraction(
            final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyId) {

        var view = DSL.table("drh_stateless_activity_audit.study_participant_interaction_view");

        var t = DSL.table(
                "(" +
                        "SELECT DISTINCT ON (participant_id) * " +
                        "FROM drh_stateless_activity_audit.study_participant_interaction_view " +
                        "WHERE (CAST(interaction_hierarchy AS jsonb) = CAST('[]' AS jsonb) OR interaction_hierarchy IS NULL) "
                        +
                        "AND study_id = '" + studyId + "' " +
                        "AND study_display_id IS NOT NULL " +
                        "ORDER BY participant_id, created_at" +
                        ") AS t");

        // Subquery: participants with at least one FAILED interaction
        var failedParticipants = DSL
                .selectDistinct(DSL.field("participant_id"))
                .from(view)
                .where(DSL.field("study_id").eq(studyId))
                .and(DSL.field("interaction_status").eq(DSL.value(ActionStatus.FAILED)));

        var query = udiPrimeDbConfig.dsl()
                .select()
                .from(t)
                .where(DSL.field("participant_id").in(failedParticipants));

        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

        return query.fetch().intoMaps();
    }

}
