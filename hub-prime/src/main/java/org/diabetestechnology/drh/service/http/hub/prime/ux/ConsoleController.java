package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "Console API")
@ConfigurationProperties(prefix = "org.diabetestechnology.udi.prime")
public class ConsoleController {

    @Value("${org.diabetestechnology.udi.prime.schema.base-path}")
    private String schemaBasePath;

    private static final Logger LOG = LoggerFactory.getLogger(ConsoleController.class.getName());
    public static final ObjectMapper headersOM = JsonMapper.builder()
            .findAndAddModules()
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .build();

    private final Presentation presentation;

    public ConsoleController(final Presentation presentation) throws Exception {
        this.presentation = presentation;
    }

    @RouteMapping(label = "Console", siblingOrder = 80)
    @GetMapping("/console")
    public String console() {
        return "redirect:/console/project";
    }

    @RouteMapping(label = "Project", title = "Project", siblingOrder = 40)
    @GetMapping("/console/project")
    public String projects(final Model model, final HttpServletRequest request) {
        if (!presentation.isAuthenticatedUser()) {
            LOG.info("Unauthorized access attempt to /console/project");
            return presentation.populateModel("page/access-denied", model, request);
        } else if (!presentation.isMenuAdminUser()) {
            LOG.info("Authorized user has no permission to access /console/project");
            return presentation.populateModel("page/access-denied", model, request);
        }
        String[] pageDescription = {
                "A repository of resources, including setup instructions, usage guides, and best practices for developers and users."
        };
        model.addAttribute("pageDescription", pageDescription);
        return presentation.populateModel("page/console/project", model, request);
    }

    @RouteMapping(label = "Schema", title = "Schema", siblingOrder = 50)
    @GetMapping("/console/schema")
    public String schemaSpy(final Model model, final HttpServletRequest request) {
        if (!presentation.isAuthenticatedUser()) {
            LOG.info("Unauthorized access attempt to /console/schema");
            return presentation.populateModel("page/access-denied", model, request);
        } else if (!presentation.isMenuAdminUser()) {
            LOG.info("Authorized user has no permission to access /console/schema");
            return presentation.populateModel("page/access-denied", model, request);
        }
        String baseDir = System.getProperty("user.dir") + schemaBasePath;
        LOG.info("schemaBasePath: {}", baseDir);
        File directory = new File(baseDir);

        Map<String, String> folderMap = Arrays.stream(directory.listFiles())
                .filter(File::isDirectory)
                .collect(Collectors.toMap(
                        File::getName, // This will be the key (schema)
                        file -> "/maven-site/schemaSpy/" + file.getName() + "/index.html" // This will be the value
                                                                                          // (folderName)
                ));

        List<String> folderNames = Arrays.stream(directory.listFiles())
                .filter(File::isDirectory)
                .map(file -> "/maven-site/schemaSpy/" + file.getName() + "/index.html")
                .collect(Collectors.toList());

        String[] pageDescription = {
                "Presents the database schema, helping users understand the structure of data and relationships between tables."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("folders", folderNames);
        model.addAttribute("basePath", baseDir); // Base path for constructing URLs
        model.addAttribute("folderMap", folderMap);

        LOG.info("SchemaBase Path:{}", baseDir);
        folderNames.forEach(folder -> {
            LOG.info("Schema Folder Names : {}", folder);
        });

        return presentation.populateModel("page/console/schema", model, request);
    }

    @RouteMapping(label = "Health Information", title = "Health Information", siblingOrder = 30)
    @GetMapping("/console/health-info")
    public String healthInformation(final Model model, final HttpServletRequest request) {
        if (!presentation.isAuthenticatedUser()) {
            LOG.info("Unauthorized access attempt to /console/health-info");
            return presentation.populateModel("page/access-denied", model, request);
        } else if (!presentation.isMenuAdminUser()) {
            LOG.info("Authorized user has no permission to access /console/health-info");
            return presentation.populateModel("page/access-denied", model, request);
        }
        String[] pageDescription = {
                "Displays the current operational status and health metrics of the application, helping administrators monitor system performance."
        };
        model.addAttribute("pageDescription", pageDescription);

        return presentation.populateModel("page/console/health-info", model, request);
    }
}
