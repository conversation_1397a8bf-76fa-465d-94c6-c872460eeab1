package org.diabetestechnology.drh.service.http.pg.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.io.FilenameUtils;
import org.diabetestechnology.drh.service.http.hub.prime.exception.CgmFieldValidationException;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.CgmError;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.constant.FileUploadStatusStatus;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantRowFileRequest;
import org.diabetestechnology.drh.service.http.util.FileValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.time.*;

@Service
public class ParticipantRowFileService {

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantRowFileService.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final FileService fileService;
    private final S3FileUploadService s3FileUploadService;
    private final CgmMasterService cgmMasterService;
    private final InteractionService interactionService;
    private final DbActivityService activityLogService;
    ObjectMapper mapper = new ObjectMapper();

    public ParticipantRowFileService(
            @Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService,
            PartyService partyService,
            FileService fileService,
            S3FileUploadService s3FileUploadService, CgmMasterService cgmMasterService,
            InteractionService interactionService, DbActivityService activityLogService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.fileService = fileService;
        this.s3FileUploadService = s3FileUploadService;
        this.cgmMasterService = cgmMasterService;
        this.interactionService = interactionService;
        this.activityLogService = activityLogService;
    }

    public Map<String, Object> saveCgmRowFile(MultipartFile file, String studyId, String orgPartyId,
            String participantId) throws Exception {
        LOG.info("save cgm row file: ");

        final var originalFileName = file.getOriginalFilename();
        final var fileType = file.getContentType();
        final var fileName = FilenameUtils.getBaseName(originalFileName);
        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("Original file name cannot be null or empty.");
        }
        final var fileFormat = originalFileName.substring(originalFileName.lastIndexOf('.') + 1);
        final var fileSize = file.getSize(); // size in bytes
        final var uploadTimestamp = ZonedDateTime.now(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_INSTANT);
        LOG.info("save cgm row file format: {}", fileFormat);
        LOG.info("save cgm row file uploadTimestamp: {}", uploadTimestamp);
        LOG.info("save cgm row file: {}", fileName);
        LOG.info("save cgm row file: {}", fileType);
        LOG.info("save cgm row file: {} bytes", fileSize);
        List<String> hierarchyJsonArray = null;
        final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
        try {
            JSONB lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.START_CGM_FILE_UPLOAD, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.PENDING,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, null, ActionType.UPLOAD_CGM_FILE);

            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            FileValidationUtil.validateFileHasData(file);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.EMPTY_FILE_CONTENT, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.PENDING,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, ActionType.VALIDATE_CGM_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.S3_BUCKET_UPLOAD, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.IN_PROGRESS,
                    "null",
                    uploadTimestamp, null,
                    ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var awsFileUrl = s3FileUploadService.uploadCgmFile(file, orgPartyId, studyId);
            LOG.info("save cgm row file AWS URL: {}", awsFileUrl);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    "Upload file to S3 Bucket.", null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, ActionType.S3_FILE_UPLOAD);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var tempFilePath = s3FileUploadService.saveFileToTempLocation(file);
            if (tempFilePath == null) {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                        null,
                        null, awsFileUrl,
                        fileName,
                        fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                        null,
                        uploadTimestamp, null, ActionStatus.FAILED, 0,
                        null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);

                throw new RuntimeException("Failed to save file to temp location.");
            } else {
                lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                        participantId,
                        ActionDescription.COPY_FILE_FOR_PROCESSING, null,
                        null,
                        null, awsFileUrl,
                        fileName,
                        fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                        null,
                        uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                        null, hierarchyJsonArray, ActionType.COPY_FILE_FOR_PROCESSING);
                hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            }

            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.VALIDATE_FILE_CONTENT, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.VALIDATE_CGM_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var cgmRawDataByteArray = s3FileUploadService.convertToByteArray(tempFilePath);

            LOG.info("ByteArray CGM: {}", cgmRawDataByteArray);

            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userPartyId);

            List<String> fleHeaders = fileService.extractHeaders(tempFilePath);
            LOG.info("save cgm row file headers: {}", fleHeaders);

            Map<String, Object> cgmDataMap = new HashMap<>();
            cgmDataMap.put("originalFileName", originalFileName);
            cgmDataMap.put("fileName", fileName);
            cgmDataMap.put("fileType", fileType);
            cgmDataMap.put("fileFormat", fileFormat);
            cgmDataMap.put("fileSize", fileSize);
            cgmDataMap.put("uploadTimestamp", uploadTimestamp);
            cgmDataMap.put("userPartyId", userPartyId);
            cgmDataMap.put("organizationPartyId", organizationPartyId);
            cgmDataMap.put("fileUrl", awsFileUrl);
            if (!fileFormat.equals("xml") && !fileFormat.equals("json")) {
                cgmDataMap.put("cgmRawDataByteArray", cgmRawDataByteArray);
            }

            if (fileFormat.equals("json")) {
                cgmDataMap.put("cgmRawDataJson", OBJECT_MAPPER.readTree(new File(tempFilePath)));
            } else {
                cgmDataMap.put("cgmRawDataJson",
                        OBJECT_MAPPER.readValue((s3FileUploadService.processContent(tempFilePath)).toString(),
                                new TypeReference<List<Map<String, Object>>>() {
                                }));

            }
            cgmDataMap.put("fleHeaders", fleHeaders);
            if (fileFormat.equals("xml")) {
                cgmDataMap.put("cgmDataXmlContent", readXmlContent(tempFilePath));
            }

            LOG.info("save cgm row file Map: {}", cgmDataMap);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.VALIDATE_FILE_CONTENT, null,
                    null,
                    null, awsFileUrl,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    uploadTimestamp, null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, ActionType.VALIDATE_CGM_FILE);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            LOG.info("Hierarchy JSON Array to  cgmDataMap {}", hierarchyJsonArray);
            cgmDataMap.put("interactionHierarchy", hierarchyJsonArray);
            cgmDataMap.put("lastInteractionId", interactionService.getLastInteractionId(lastInteraction));
            s3FileUploadService.deleteTempFile(tempFilePath);
            return cgmDataMap;
        } catch (Exception e) {
            LOG.error("Unexpected error in saveCgmRowFile", e);
            interactionService.saveFileInteraction(hubInteractionId, studyId,
                    participantId,
                    ActionDescription.CGM_ERROR, null,
                    null,
                    null, null,
                    fileName,
                    fileType, null, FileType.CGM, FileUploadStatusStatus.ERROR,
                    null,
                    uploadTimestamp, null, ActionStatus.FAILED, 0,
                    ActionDescription.CGM_ERROR, hierarchyJsonArray, ActionType.UPLOAD_CGM_FILE);
            throw new RuntimeException("An unexpected error occurred while saving CGM file.", e);
        }
    }

    public String readXmlContent(String tempFilePath) throws IOException {
        try {
            // Read the XML content from the specified file path
            Path path = Path.of(tempFilePath);
            return Files.readString(path); // Reads the entire file content as a string
        } catch (IOException e) {
            throw new IOException("Failed to read the XML file: " + e.getMessage(), e);
        }
    }

    @Transactional
    public Map<String, Object> saveCgmRowData(ParticipantRowFileRequest request)
            throws JsonMappingException, JsonProcessingException {
        List<String> hierarchyJsonArray = request.fileMetadata().interactionHierarchy();
        LOG.info("save cgm row data hierarchyJsonArray: {}", hierarchyJsonArray);
        final var firstInteractionId = hierarchyJsonArray.get(0);
        JSONB interactionHierarchy = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);
        JsonNode lastFileInteracionResponseJson = mapper.readTree(interactionHierarchy.data());
        final var lastFileInteracionId = lastFileInteracionResponseJson.get("file_interaction_id").asText();
        LOG.info("save cgm row data lastFileInteracionId: {}", lastFileInteracionId);
        final var lastFileInteracionHierarchy = lastFileInteracionResponseJson.get("interaction_hierarchy").asText();
        LOG.info("save cgm row data lastFileInteracionHierarchy: {}", lastFileInteracionHierarchy);
        hierarchyJsonArray = interactionService.getLastInteractionHierarchy(firstInteractionId);
        hierarchyJsonArray.add(lastFileInteracionId);

        final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
        try {
            JSONB lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    request.participantId(),
                    ActionDescription.PREPARE_METADATA_CONTENT, null,
                    null,
                    null, request.fileUrl(),
                    request.fileName(),
                    request.fileType(), null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    request.uploadTimestamp().toString(), null, ActionStatus.IN_PROGRESS, 0,
                    null, hierarchyJsonArray, ActionType.PREPARE_METADATA);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            LOG.info("save cgm row data: {}", request);
            Map<String, Object> fileMetadataMap = convertToMap(request.fileMetadata());
            List<Map<String, Object>> cgmRawDataList = convertJsonNodeToList(request.cgmRawDataJson());
            Map<String, String> fileInteractionMap = new HashMap<>();
            fileInteractionMap.put("hubInteractionId", hubInteractionId);
            fileInteractionMap.put("studyId", request.studyId());
            fileInteractionMap.put("participantId", request.participantId());
            fileInteractionMap.put("fileUrl", request.fileUrl());
            fileInteractionMap.put("fileName", request.fileName());
            fileInteractionMap.put("fileType", request.fileType());
            fileInteractionMap.put("uploadTimestamp", request.uploadTimestamp().toString());
            lastInteraction = validateCgmMapping(fileMetadataMap, cgmRawDataList, fileInteractionMap,
                    firstInteractionId, lastInteraction, hierarchyJsonArray);
            hierarchyJsonArray = interactionService.getLastInteractionHierarchy(firstInteractionId);
            hierarchyJsonArray = interactionService.getInteractionHierarchy(lastInteraction, hierarchyJsonArray);
            final var lastInteractionId = interactionService.getLastInteractionId(lastInteraction);
            lastInteraction = interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    request.participantId(),
                    ActionDescription.PREPARE_METADATA_CONTENT, null,
                    null,
                    null, request.fileUrl(),
                    request.fileName(),
                    request.fileType(), null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    request.uploadTimestamp().toString(), null, ActionStatus.SUCCESS, 0,
                    null, hierarchyJsonArray, ActionType.PREPARE_METADATA);
            JSONB cgmJsonbData = JSONB.valueOf(prepareCgmRequestJson(request, cgmRawDataList, lastInteractionId));
            String activityData = activityLogService.prepareActivityLogMetadata();
            LOG.info("save cgm row data Query: {}", dsl
                    .select(DSL.field(
                            "drh_stateless_raw_observation.save_cgm_raw_data({0}, {1})",
                            String.class,
                            DSL.val(cgmJsonbData), DSL.cast(DSL.val(activityData), JSONB.class))));
            JSONB result = dsl
                    .select(DSL.field(
                            "drh_stateless_raw_observation.save_cgm_raw_data({0}, {1})",
                            String.class,
                            DSL.val(cgmJsonbData),
                            DSL.cast(DSL.val(activityData),
                                    JSONB.class)))
                    .fetchOneInto(JSONB.class);
            if (result != null) {
                JsonNode jsonNode = OBJECT_MAPPER.readTree(result.toString());
                LOG.info("DB Result: {}", jsonNode);

                String resultStatus = jsonNode.path("status").asText("success");
                if ("failure".equals(resultStatus)) {
                    return Map.of(
                            "status", "failure",
                            "message", jsonNode.path("message").asText("Error occurred during CGM observation save"),
                            "result", OBJECT_MAPPER.convertValue(jsonNode.path("error_details"), Map.class));
                }

                return Map.of(
                        "status", "success",
                        "message", "Data saved successfully",
                        "result", jsonNode);
            } else {
                LOG.error("CGM data save returned null.");
                return Map.of(
                        "status", "error",
                        "message", "Failed to save CGM data. No response from the database.");
            }
        } catch (CgmFieldValidationException exception) {
            LOG.error("CGM data validation failed: {}", exception.getMessage());
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    request.participantId(),
                    ActionDescription.VALIDATE_CGM_DATE_AND_VALUE, null,
                    "CGM data validation failed: ",
                    null, request.fileUrl(),
                    request.fileName(),
                    request.fileType(), null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    request.uploadTimestamp().toString(), null, ActionStatus.FAILED, 0,
                    exception.toString(), hierarchyJsonArray, ActionType.VALIDATE_CGM_DATE_AND_VALUE);
            throw exception;
        } catch (Exception e) {
            LOG.error("Unexpected error in saveCgmRowData", e);
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    request.participantId(),
                    ActionDescription.VALIDATE_CGM_DATE_AND_VALUE, null,
                    "CGM data validation failed ",
                    null, request.fileUrl(),
                    request.fileName(),
                    request.fileType(), null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                    null,
                    request.uploadTimestamp().toString(), null, ActionStatus.FAILED, 0,
                    e.toString(), hierarchyJsonArray, ActionType.VALIDATE_CGM_DATE_AND_VALUE);
            throw new RuntimeException("An unexpected error occurred while saving CGM data.", e);
        }

    }

    private JsonNode prepareMetadata(ParticipantRowFileRequest.ParticipantRowFileMetaDataRequest fileMetadata) {
        try {

            ObjectNode jsonObject = mapper.createObjectNode();
            jsonObject.put("device_name", cgmMasterService.getCgmDevicesName(fileMetadata.deviceId()));
            jsonObject.put("device_id", fileMetadata.deviceId());
            jsonObject.put("source_platform", fileMetadata.sourcePlatform());
            jsonObject.put("source_platform_id", fileMetadata.sourcePlatformId());
            jsonObject.put("file_name", fileMetadata.fileName());
            jsonObject.put("file_format", fileMetadata.fileFormat());
            jsonObject.put("file_upload_date", fileMetadata.fileUploadDate());
            jsonObject.put("map_field_of_cgm_date", fileMetadata.mapFieldOfCgmDate());
            jsonObject.put("map_field_of_cgm_value", fileMetadata.mapFieldOfCgmValue());
            return jsonObject;
        } catch (Exception e) {
            throw new RuntimeException("Error while preparing JSON", e);
        }
    }

    public String prepareCgmRequestJson(ParticipantRowFileRequest request, List<Map<String, Object>> cgmRawDataJson,
            String lastInteractionId) {
        final var userId = userNameService.getUserId();
        try {
            // Create an ObjectMapper instance for building the JSON object

            ObjectNode jsonObject = mapper.createObjectNode();

            if (!StringUtils.hasText(request.fileMetadata().deviceId())) {
                LOG.info("Device ID is required");
                throw new RuntimeException("Device ID is required");
            }
            JsonNode metadata = prepareMetadata(request.fileMetadata());
            // Map fields from the request to the JSON structure
            jsonObject.put("file_name", request.fileName());
            jsonObject.put("file_url", request.fileUrl());
            if (request.fileFormat().equals("json")) {
                jsonObject.set("cgm_raw_data_json", request.cgmRawDataJson());
            } else {
                jsonObject.set("cgm_raw_data_json", null);
            }
            jsonObject.put("upload_timestamp", request.uploadTimestamp().toString());
            jsonObject.put("uploaded_by", partyService.getPartyIdByUserId(userId));
            jsonObject.put("file_size", request.fileSize());
            jsonObject.put("status", "Pending");
            jsonObject.set("file_metadata", metadata);
            jsonObject.put("file_type", request.fileType());
            jsonObject.put("study_id", request.studyId());
            jsonObject.put("org_party_id", request.orgPartyId());
            jsonObject.put("current_user_id", partyService.getPartyIdByUserId(userId));
            jsonObject.put("participant_sid", request.participantId());
            jsonObject.set("cgm_data", OBJECT_MAPPER.valueToTree(cgmRawDataJson));
            if (request.fileFormat().equals("xml")) {
                jsonObject.put("cgm_raw_data_xml", request.cgmDataXml());
            }
            if (request.fileFormat().equals("txt")) {
                jsonObject.put("cgm_raw_data_text", request.cgmRawDataByteArray());
            } else if (request.fileFormat().equals("csv")) {
                jsonObject.put("cgm_raw_data_csv", request.cgmRawDataByteArray());
            } else if (request.fileFormat().equals("excel")) {
                jsonObject.put("cgm_raw_data_excel", request.cgmRawDataByteArray());
            } else if (request.fileFormat().equals("zip")) {

                jsonObject.put("cgm_raw_zip_data", request.cgmRawDataByteArray());
            }

            jsonObject.put("last_file_interaction_id", lastInteractionId);

            // Convert the ObjectNode to a JSON string
            return mapper.writeValueAsString(jsonObject);

        } catch (Exception e) {
            throw new RuntimeException("Error while preparing JSON", e);
        }
    }

    public JSONB validateCgmMapping(Map<String, Object> fileMetadata, List<Map<String, Object>> cgmRawDataJson,
            Map<String, String> fileInteractionMap, String firstInteractionId, JSONB interaction,
            List<String> hierarchyJsonArray)
            throws JsonProcessingException {
        String hubInteractionId = (String) fileInteractionMap.get("hubInteractionId");
        String studyId = (String) fileInteractionMap.get("studyId");
        String participantId = (String) fileInteractionMap.get("participantId");
        String fileUrl = (String) fileInteractionMap.get("fileUrl");
        String fileName = (String) fileInteractionMap.get("fileName");
        String fileType = (String) fileInteractionMap.get("fileType");
        String uploadTimestamp = (String) fileInteractionMap.get("uploadTimestamp");
        final JSONB lastInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                participantId,
                ActionDescription.VALIDATE_CGM_DATE_AND_VALUE, null,
                null,
                null, fileUrl,
                fileName,
                fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                null,
                uploadTimestamp.toString(), null, ActionStatus.IN_PROGRESS, 0,
                null, hierarchyJsonArray, ActionType.VALIDATE_CGM_DATE_AND_VALUE);

        hierarchyJsonArray = interactionService.getLastInteractionHierarchy(firstInteractionId);
        hierarchyJsonArray.add(interactionService.getLastInteractionId(lastInteraction));
        String mapFieldOfCgmDate = Objects.toString(fileMetadata.get("mapFieldOfCgmDate"), "").trim();
        String mapFieldOfCgmValue = Objects.toString(fileMetadata.get("mapFieldOfCgmValue"), "").trim();

        if (mapFieldOfCgmDate.isEmpty()) {
            LOG.error("CGM date field is missing: {}", mapFieldOfCgmDate);
            throw new CgmFieldValidationException("mapFieldOfCgmDate", null, CgmError.CGM_DATE_MISSING);

        }
        if (mapFieldOfCgmValue.isEmpty()) {
            LOG.error("CGM value field is missing: {}", mapFieldOfCgmValue);
            throw new CgmFieldValidationException("mapFieldOfCgmValue", null,
                    CgmError.CGM_VALUE_MISSING);
        }
        if (cgmRawDataJson == null || cgmRawDataJson.isEmpty()) {
            throw new CgmFieldValidationException("cgmRawDataJson", null, CgmError.EMPTY_CGM_RAW_DATA);
        }

        cgmRawDataJson.forEach(row -> {
            String dateValue = Objects.toString(row.get(mapFieldOfCgmDate), "").trim();
            String numericValue = Objects.toString(row.get(mapFieldOfCgmValue), "").trim();

            if (!isValidNumeric(numericValue)) {
                LOG.error("Error while processing CGM value: {}", dateValue);

                throw new CgmFieldValidationException(mapFieldOfCgmValue,
                        numericValue,
                        CgmError.INVALID_NUMERIC_FIELD + String.format(" '%s' -> Value: '%s'",
                                mapFieldOfCgmValue,
                                numericValue));

            }
            if (isValidDate(dateValue)) {
                try {
                    ZonedDateTime utcDateTime;
                    try {
                        utcDateTime = OffsetDateTime.parse(dateValue, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                                .withOffsetSameInstant(ZoneOffset.UTC)
                                .toZonedDateTime();
                    } catch (DateTimeParseException ex) {
                        if (dateValue.matches("\\d{4}-\\d{2}-\\d{2}") || // yyyy-MM-dd
                                dateValue.matches("\\d{2}-\\d{2}-\\d{4}") || // dd-MM-yyyy
                                dateValue.matches("\\d{2}/\\d{2}/\\d{4}")) { // MM/dd/yyyy or dd/MM/yyyy
                            dateValue += " 00:00:00"; // Append midnight time
                        }

                        // Identify correct date format
                        DateTimeFormatter inputFormatter = getFormatter(dateValue);
                        LocalDateTime localDateTime = LocalDateTime.parse(dateValue, inputFormatter);

                        utcDateTime = localDateTime.atZone(ZoneId.systemDefault())
                                .withZoneSameInstant(ZoneId.of("UTC"));
                    }

                    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String utcFormattedDate = utcDateTime.format(outputFormatter);

                    LOG.info("UTC Zone Date: {}", utcDateTime);
                    LOG.info("UTC Date: {}", utcFormattedDate);

                    row.put(mapFieldOfCgmDate, utcFormattedDate);

                } catch (DateTimeParseException e) {

                    throw new CgmFieldValidationException(
                            mapFieldOfCgmDate,
                            dateValue,
                            CgmError.INVALID_DATE_FIELD + String.format(" '%s' -> Value: '%s'", mapFieldOfCgmDate,
                                    dateValue));
                }
            } else {
                LOG.error("Invalid date format: {}", dateValue);
                throw new CgmFieldValidationException(
                        mapFieldOfCgmDate,
                        dateValue,
                        CgmError.INVALID_DATE_FIELD
                                + String.format(" '%s' -> Value: '%s'", mapFieldOfCgmDate,
                                        dateValue));
            }

        });
        hierarchyJsonArray = interactionService.getLastInteractionHierarchy(firstInteractionId);
        hierarchyJsonArray.add(interactionService.getLastInteractionId(lastInteraction));
        JSONB lastValidateInteraction = interactionService.saveFileInteraction(hubInteractionId, studyId,
                participantId,
                ActionDescription.VALIDATE_CGM_DATE_AND_VALUE, null,
                null,
                null, fileUrl,
                fileName,
                fileType, null, FileType.CGM, FileUploadStatusStatus.SUCCESS,
                null,
                uploadTimestamp.toString(), null, ActionStatus.SUCCESS, 0,
                null, hierarchyJsonArray,
                ActionType.VALIDATE_CGM_DATE_AND_VALUE);
        return lastValidateInteraction;

    }

    private boolean isValidDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty())
            return false;

        DateTimeFormatter[] dateOnlyFormatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                DateTimeFormatter.ofPattern("dd-MM-yyyy"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy")
        };

        DateTimeFormatter[] dateTimeFormatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss a"),
                DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss zzz"),
                DateTimeFormatter.ofPattern("dd-MM-yyyy'T'HH:mm:ssX") };
        try {
            OffsetDateTime.parse(dateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            return true;
        } catch (DateTimeParseException ignored) {
        }

        if (Arrays.stream(dateOnlyFormatters).anyMatch(formatter -> {
            try {
                LocalDate.parse(dateStr, formatter);
                return true;
            } catch (DateTimeParseException ignored) {
            }
            return false;
        })) {
            return true;
        }
        return Arrays.stream(dateTimeFormatters).anyMatch(formatter -> {
            try {
                LocalDateTime.parse(dateStr, formatter);
                return true;
            } catch (DateTimeParseException ignored) {
            }
            return false;
        });
    }

    private boolean isValidNumeric(String numStr) {
        if (numStr == null || numStr.isEmpty())
            return false;
        return numStr.matches("-?\\d+(\\.\\d+)?");
    }

    private Map<String, Object> convertToMap(Object obj) {
        return OBJECT_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {
        });
    }

    private List<Map<String, Object>> convertJsonNodeToList(JsonNode jsonNode) {
        return OBJECT_MAPPER.convertValue(jsonNode, new TypeReference<List<Map<String, Object>>>() {
        });
    }

    private static DateTimeFormatter getFormatter(String dateValue) {
        if (dateValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
            return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        } else if (dateValue.matches("\\d{2}-\\d{2}-\\d{4} \\d{2}:\\d{2}:\\d{2}")) {
            return DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        } else if (dateValue.matches("\\d{2}/\\d{2}/\\d{4} \\d{2}:\\d{2}:\\d{2}")) {
            return DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
        } else {
            throw new DateTimeParseException("Unsupported date format", dateValue, 0);
        }
    }
}
