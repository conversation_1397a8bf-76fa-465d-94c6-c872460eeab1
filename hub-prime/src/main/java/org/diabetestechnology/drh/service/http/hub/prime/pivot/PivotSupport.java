package org.diabetestechnology.drh.service.http.hub.prime.pivot;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.aggrid.ColumnVO;

public class PivotSupport {
    public Map<String, List<String>> getPivotValues(List<ColumnVO> pivotCols) {
        Map<String, List<String>> pivotValues = new HashMap<>();
        for (ColumnVO column : pivotCols) {
            List<String> values = getValuesForField(column.getField());
            pivotValues.put(column.getField(), values);
        }
        return pivotValues;
    }

    public List<String> getValuesForField(String field) {
        List<String> values = Arrays.asList(field);
        return values;
    }

}
