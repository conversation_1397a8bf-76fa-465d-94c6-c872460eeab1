package org.diabetestechnology.drh.service.http.hub.prime.service.interaction;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class AuditService {

    private static final Logger LOG = LoggerFactory.getLogger(AuditService.class.getName());
    @Autowired
    UserNameService userNameService;
    @Autowired
    ObservabilityRequestFilter observabilityRequestFilter;

    public HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();
    }

    public Object FormatActivityDataResponse(List<Map<String, Object>> result) {
        // Initialize ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            // Iterate over each entry in the result
            for (Map<String, Object> entry : result) {
                // Extract the JSON string from activity_data
                String jsonString = (String) entry.get("activity_data");

                // Check if jsonString is not null or empty
                if (jsonString != null && !jsonString.isEmpty()) {
                    // Parse the JSON string into a Map
                    @SuppressWarnings("unchecked")
                    Map<String, Object> activityDataMap = objectMapper.readValue(jsonString, Map.class);
                    if (activityDataMap.containsKey("requestBody")) {
                        String requestBodyString = (String) activityDataMap.get("requestBody");

                        // If requestBody is not null or empty, parse it into a Map
                        if (requestBodyString != null && !requestBodyString.isEmpty()) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> requestBodyMap = objectMapper.readValue(requestBodyString,
                                    Map.class);

                            // Replace the original requestBody entry with the formatted map
                            activityDataMap.put("requestBody", requestBodyMap);
                        }
                    }
                    // Replace the original activity_data entry with the formatted map
                    entry.put("activity_data", activityDataMap);
                }
            }

            // Print the updated result
            System.out.println("Updated Result: " + result);
        } catch (Exception e) {
            e.printStackTrace(); // Handle exceptions
        }
        return result;
    }

}
