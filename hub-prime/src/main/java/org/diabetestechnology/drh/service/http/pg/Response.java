package org.diabetestechnology.drh.service.http.pg;

import java.util.Map;

import lombok.Data;

@Data
public class Response {
    private Map<String, Object> data;
    private String status;
    private String message;
    private Object errors;

    public Response(Builder builder) {
        this.data = builder.data;
        this.status = builder.status;
        this.message = builder.message;
        this.errors = builder.errors;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private Map<String, Object> data;
        private String status;
        private String message;
        private Object errors;

        public Builder data(Map<String, Object> data) {
            this.data = data;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder errors(Object errors) {
            this.errors = errors;
            return this;
        }

        public Response build() {
            return new Response(this);
        }
    }
}
