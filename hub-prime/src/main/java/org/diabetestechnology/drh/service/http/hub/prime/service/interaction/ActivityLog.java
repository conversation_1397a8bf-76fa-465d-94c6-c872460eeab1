package org.diabetestechnology.drh.service.http.hub.prime.service.interaction;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties
public class ActivityLog {
    private Integer activityId;
    private String activityName;
    private String activityType;
    private String activityDescription;
    private Integer rootId;
    private Integer parentId;
    private String activityHierarchy;
    private String hierarchyPath;
    private String requestUrl;
    private Integer tenantId;
    private String platform;
    private String environment;
    private String createdBy;
    private String userName;
    private String createdAt;
    private String appVersion;
    private String testCase;
    private String sessionId;
    private String linkageId;
    private String ipAddress;
    private String locationLatitude;
    private String locationLongitude;
    private Map<String, Object> activityData;
    private int activityLogLevel;
    private String sessionUniqueId;
}
