package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.diabetestechnology.drh.service.http.hub.prime.service.orcid.OrcidUserDetailService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH ORCID User Details API")
public class OrcidUserController {
    private static final Logger LOG = LoggerFactory.getLogger(OrcidUserController.class);
    @Value("${orcid.api.base-url:https://pub.orcid.org/v3.0}")
    private String orcidApiBaseUrl;

    @Autowired
    OrcidUserDetailService orcidUserDetailService;

    private final RestTemplate restTemplate;
    private final WebClient webClient;

    public OrcidUserController(WebClient.Builder webClientBuilder, RestTemplate restTemplate) {
        LOG.info("Creating OrcidUserController");
        this.restTemplate = restTemplate;
        this.webClient = webClientBuilder.baseUrl("https://api.ror.org/v2/organizations").build();
    }

    @GetMapping("/orcid/user-info")
    @Operation(summary = "Get Orcid User Info")
    @ResponseBody
    public Response getUserInfo() {
        String orcidId = orcidUserDetailService.getUserId();
        LOG.info("ORCID ID: {}", orcidId);
        try {
            if (orcidId == null) {
                return Response.builder()
                        .data(new HashMap<>())
                        .status("success")
                        .message("Error fetching ORCID details: ORCID ID not found")
                        .errors("No data available")
                        .build();
            }
            // Make the HTTP request
            ResponseEntity<String> response = orcidUserDetailService.getOrcidUserInfo(orcidId);

            String name = orcidUserDetailService.extractFullName(response.getBody().toString());
            String email = orcidUserDetailService.extractEmail(response.getBody().toString());
            String institution = orcidUserDetailService.extractInstitution(response.getBody().toString());
            Map<String, Object> userDetails = new HashMap<>(Map.of("orcidId", orcidId));
            userDetails.put("userName", name);
            userDetails.put("userEmail", email);
            userDetails.put("userInstitution", institution);
            // Return the response
            return Response.builder()
                    .data(userDetails)
                    .status("success")
                    .message("Successfully read ORCID details")
                    .errors(null)
                    .build();

        } catch (Exception e) {
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Error fetching ORCID details")
                    .errors("Error fetching ORCID details: " + e
                            .getMessage())
                    .build();
        }
    }

    @SuppressWarnings("unchecked")
    @GetMapping("/orcid/institution")
    @ResponseBody
    @Operation(summary = "Get ORCID Institutions")
    public ResponseEntity<Object> getDiscoFeed() {
        String url = "https://orcid.org/Shibboleth.sso/DiscoFeed";

        try {
            // Set up headers with the cookie
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.COOKIE, "geolocation=US");

            // Create the HTTP entity with headers
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            // Make the API call with the custom headers
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    String.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // Parse the JSON response
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, Object>> jsonResponse = objectMapper.readValue(
                        response.getBody(),
                        new TypeReference<List<Map<String, Object>>>() {
                        });

                // Extract 'value' from 'DisplayNames'
                List<String> displayNames = jsonResponse.stream()
                        .map(entry -> (List<Map<String, String>>) entry.get("DisplayNames"))
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .filter(displayName -> "en".equals(displayName.get("lang")))
                        .map(displayName -> displayName.get("value"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                Map<String, Object> nameMap = new HashMap<>(Map.of("institutions", displayNames));
                return ResponseEntity.ok().body(nameMap);
            }

            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to fetch DiscoFeed: " + e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    @GetMapping("/orcid/institution/search")
    @ResponseBody
    @Operation(summary = "Search ORCID Institutions")
    public ResponseEntity<Object> searchDiscoFeed(@RequestParam("query") String query) {
        String url = "https://orcid.org/Shibboleth.sso/DiscoFeed";

        try {
            // Set up headers with the cookie
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.COOKIE, "geolocation=US");

            // Create the HTTP entity with headers
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            // Make the API call with the custom headers
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // Parse the JSON response
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, Object>> jsonResponse = objectMapper.readValue(
                        response.getBody(),
                        new TypeReference<List<Map<String, Object>>>() {
                        });

                // Extract and filter 'DisplayNames' based on the query
                List<String> matchingInstitutions = jsonResponse.stream()
                        .map(entry -> (List<Map<String, String>>) entry.get("DisplayNames"))
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .filter(displayName -> "en".equals(displayName.get("lang")))
                        .map(displayName -> displayName.get("value"))
                        .filter(Objects::nonNull)
                        .filter(name -> name.toLowerCase().contains(query.toLowerCase())) // Match query
                        .collect(Collectors.toList());

                Map<String, Object> resultMap = new HashMap<>(Map.of("matchingInstitutions", matchingInstitutions));
                return ResponseEntity.ok().body(resultMap);
            }

            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to search DiscoFeed: " + e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Get ROR Institutions")
    @GetMapping("/ror/institution/search")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> fetchOrganizations(@RequestParam String query)
            throws JsonMappingException, JsonProcessingException {
        String response = webClient.get()
                .uri(uriBuilder -> uriBuilder.queryParam("query", query).build())
                .retrieve()
                .bodyToMono(String.class)
                .block();

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);
        List<Map<String, Object>> items = (List<Map<String, Object>>) responseMap.get("items");

        List<Map<String, Object>> institutions = new ArrayList<>();

        if (items != null) {
            for (Map<String, Object> item : items) {
                Map<String, Object> institution = new LinkedHashMap<>();

                // Extract name
                List<Map<String, Object>> names = (List<Map<String, Object>>) item.get("names");
                if (names != null) {
                    for (Map<String, Object> name : names) {
                        List<String> types = (List<String>) name.get("types");
                        String lang = (String) name.get("lang");
                        if (types != null && !types.contains("acronym") && types.contains("ror_display")
                                && "en".equals(lang)) {
                            institution.put("name", name.get("value"));
                            break;
                        }
                    }
                    // Extract alias dynamically
                    for (Map<String, Object> name : names) {
                        List<String> types = (List<String>) name.get("types");
                        if (types != null && types.contains("acronym")) {
                            institution.put("alias", name.get("value"));
                            break;
                        }
                    }
                }
                // Extract identifiers
                List<Map<String, Object>> externalIds = (List<Map<String, Object>>) item.get("external_ids");
                if (externalIds != null) {
                    Map<String, String> identifierSystemValue = new LinkedHashMap<>();
                    for (Map<String, Object> id : externalIds) {
                        String type = (String) id.get("type");
                        String preferred = (String) id.get("preferred");
                        if (preferred != null) {
                            identifierSystemValue.put(type, preferred);
                        }
                        if ("isni".equalsIgnoreCase(type) && id.get("all") != null) {
                            List<String> all = (List<String>) id.get("all");
                            if (!all.isEmpty()) {
                                identifierSystemValue.put("isni", all.get(0));
                            }
                        }
                    }
                    institution.put("identifier_system_value", identifierSystemValue);

                    // Extract type_code and type_display
                    List<String> types = (List<String>) item.get("types");
                    if (types != null) {
                        institution.put("type_code", String.join(", ", types));
                        institution.put("type_display", String.join(", ", types));
                    }
                    // Extract city, state, country, and location (lat, lng)
                    List<Map<String, Object>> locations = (List<Map<String, Object>>) item.get("locations");
                    if (locations != null && !locations.isEmpty()) {
                        Map<String, Object> location = locations.get(0);
                        Map<String, Object> geonamesDetails = (Map<String, Object>) location.get("geonames_details");
                        if (geonamesDetails != null) {
                            institution.put("city", geonamesDetails.get("name"));
                            institution.put("state", geonamesDetails.get("country_subdivision_name"));
                            institution.put("country", geonamesDetails.get("country_name"));

                            Map<String, Object> latLng = new LinkedHashMap<>();
                            latLng.put("lat", geonamesDetails.get("lat"));
                            latLng.put("lng", geonamesDetails.get("lng"));
                            institution.put("locations", latLng);
                        }
                    }
                    List<Map<String, Object>> links = (List<Map<String, Object>>) item.get("links");
                    if (links != null) {
                        for (Map<String, Object> link : links) {
                            if ("website".equals(link.get("type"))) {
                                institution.put("website_url", link.get("value"));
                                break;
                            }
                        }
                    }
                    institutions.add(institution);
                }
            }
        }
        return ResponseEntity.ok(institutions);
    }

    @GetMapping("/orcid/{orcidId}")
    @Operation(summary = "Get Orcid User Info")
    @ResponseBody
    public ResponseEntity<String> getOrcidUserInfo(@PathVariable String orcidId) {
        return orcidUserDetailService.getOrcidUserInfo(orcidId);
    }

    @GetMapping("/orcid/user-details/{orcidId}")
    @Operation(summary = "Get Orcid User Info")
    @ResponseBody
    public Response getOrcidUserFormatedInfo(@PathVariable String orcidId) {
        try {
            // Make the HTTP request
            ResponseEntity<String> response = orcidUserDetailService.getOrcidUserInfo(orcidId);

            String name = orcidUserDetailService.extractFullName(response.getBody().toString());
            String email = orcidUserDetailService.extractEmail(response.getBody().toString());
            String institution = orcidUserDetailService.extractInstitution(response.getBody().toString());
            Map<String, Object> userDetails = new HashMap<>(Map.of("orcidId", orcidId));
            userDetails.put("userName", name);
            userDetails.put("userEmail", email);
            userDetails.put("userInstitution", institution);
            // Return the response
            return Response.builder()
                    .data(userDetails)
                    .status("success")
                    .message("Successfully read ORCID details")
                    .errors(null)
                    .build();

        } catch (Exception e) {

            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to read ORCID details")
                    .errors("Error fetching ORCID details: " + e
                            .getMessage())
                    .build();
        }
    }

}
