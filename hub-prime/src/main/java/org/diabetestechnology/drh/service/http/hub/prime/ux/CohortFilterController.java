package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.hub.prime.service.cohortFilter.CohortFilterRequest;
import org.diabetestechnology.drh.service.http.hub.prime.service.cohortFilter.CohortFilterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;

@Controller
@Hidden
@Tag(name = "DRH Cohort Filter API")
public class CohortFilterController {
    @Autowired
    CohortFilterService cohortFilterService;
    private static final Logger LOG = LoggerFactory.getLogger(CohortFilterController.class);

    @Operation(summary = "Save Cohort Filter")
    @PostMapping(value = "/cohort-filter.json", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object saveCohortFilter(final @RequestBody @Nonnull CohortFilterRequest payload) {
        LOG.info("Save Cohortfilter with Payload : {}", payload);
        return cohortFilterService.saveCohortFilter(payload);

    }

    @Operation(summary = "Fetch Cohort Filter")
    @GetMapping(value = "/cohort-filter")
    @ResponseBody
    public JdbcResponse getCohortFilter() {
        LOG.info("Fetch Cohortfilte");
        try {
            return cohortFilterService.getCohortFilter();
        } catch (Exception e) {
            LOG.error("Error while getting cohort filter", e);
        }
        return null;

    }

    @Operation(summary = "Update Cohort Filter")
    @PutMapping(value = "/cohort-filter/{filterId}.json", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object updateCohortFilter(final @RequestBody @Nonnull CohortFilterRequest payload,
            @PathVariable int filterId) {
        LOG.info("Update Cohortfilter with Payload : {}", payload);
        return cohortFilterService.updateCohortFilter(payload, filterId);

    }
}
