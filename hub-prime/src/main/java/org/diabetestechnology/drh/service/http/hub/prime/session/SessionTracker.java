package org.diabetestechnology.drh.service.http.hub.prime.session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionEvent;
import jakarta.servlet.http.HttpSessionListener;

@Component
public class SessionTracker implements HttpSessionListener {
    private static final Logger LOG = LoggerFactory.getLogger(SessionTracker.class);

    private final DSLContext dsl;

    private static final Map<String, SessionDetails> sessions = new ConcurrentHashMap<>();

    public SessionTracker(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public void sessionCreated(HttpSessionEvent se) {
        HttpSession session = se.getSession();
        sessions.put(session.getId(), new SessionDetails(session.getId(), session.getCreationTime()));
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        String sessionId = se.getSession().getId();
        LOG.info("Session destroyed: {}", sessionId);
        var fnResponse = dsl.select(DSL.field(
                "drh_stateless_activity_audit.update_session_audit_log(?)",
                DSL.cast(DSL.val(sessionId), String.class)))
                .fetchOneInto(org.jooq.JSONB.class);
        LOG.info("Session activity updated on destroy: {}", fnResponse);

        HttpSession session = se.getSession();
        SessionDetails details = sessions.get(session.getId());
        if (details != null) {
            details.setEndTime(System.currentTimeMillis());
        }
    }

    public List<SessionDetails> getAllSessions() {
        return new ArrayList<>(sessions.values());
    }
}
