package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivityDetails {
    private String activityName;
    private String activityDescription;
    private String activityType;
    private int activityLogLevel;

    // Constructor for cases without log level
    public ActivityDetails(String activityType, String activityDescription) {
        this.activityName = activityType.toString();
        this.activityDescription = activityDescription;
        this.activityType = activityType;
    }

    // Constructor for cases with log level
    public ActivityDetails(String activityName, String activityDescription, int activityLogLevel) {
        this.activityName = activityName;
        this.activityDescription = activityDescription;
        this.activityLogLevel = activityLogLevel;
    }

}
