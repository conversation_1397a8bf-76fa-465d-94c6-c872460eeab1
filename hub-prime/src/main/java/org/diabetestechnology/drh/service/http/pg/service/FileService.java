package org.diabetestechnology.drh.service.http.pg.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;

import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.stereotype.Service;

@Service
public class FileService {

    public List<String> extractHeaders(String file) throws IOException {
        Path path = Paths.get(file);

        // Get the file name and extract the file extension
        String fileName = path.getFileName().toString();

        String fileType = getFileExtension(fileName);

        switch (fileType) {
            case "csv":
                return extractCsvHeaders(file);
            case "xlsx":
                return extractExcelHeaders(file);
            case "txt":
                return extractTextHeaders(file);
            case "xml":
                return extractXmlHeaders(file);
            case "json":
                return extractJsonHeaders(file);
            default:
                throw new IllegalArgumentException("Unsupported file type: " + fileType);
        }
    }

    private List<String> extractJsonHeaders(String file) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> keysList = new ArrayList<>();
        Set<String> keysSet = new HashSet<>(); // Using Set to avoid duplicates

        try {
            // Read the JSON file as a JsonNode
            JsonNode jsonArray = objectMapper.readTree(new File(file));

            // Ensure the root node is an array
            if (jsonArray.isArray()) {
                // Iterate through each object in the array
                for (JsonNode node : jsonArray) {
                    // Iterate through each field in the JSON object
                    node.fieldNames().forEachRemaining(fieldName -> keysSet.add(fieldName));
                }
            } else {
                System.out.println("The JSON structure is not an array.");
            }

            // Convert Set to List
            keysList.addAll(keysSet);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return keysList;
    }

    private List<String> extractCsvHeaders(String filePath) throws IOException {
        Path path = Path.of(filePath);

        try (BufferedReader reader = Files.newBufferedReader(path)) {
            String line = reader.readLine();
            if (line != null) {
                return Arrays.asList(line.split(","));
            } else {
                throw new IllegalArgumentException("CSV file is empty.");
            }
        }
    }

    private List<String> extractExcelHeaders(String filePath) throws IOException {
        Path path = Path.of(filePath);

        try (FileInputStream fis = new FileInputStream(path.toFile());
                Workbook workbook = new XSSFWorkbook(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel sheet has no header row.");
            }
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(cell.getStringCellValue());
            }
            return headers;
        }
    }

    private List<String> extractTextHeaders(String filePath) throws IOException {
        Path path = Path.of(filePath);

        try (BufferedReader reader = Files.newBufferedReader(path)) {
            String line = reader.readLine();
            if (line != null) {
                return Arrays.asList(line.split("[\\s,;\\t|]+"));
            } else {
                throw new IllegalArgumentException("Text file is empty.");
            }
        }
    }

    private List<String> extractXmlHeaders(String filePath) throws IOException {
        try {
            Path path = Path.of(filePath);
            File xmlFile = path.toFile();

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(xmlFile);
            document.getDocumentElement().normalize();

            // Set up XPath to extract all unique element names (headers)
            XPathFactory xPathFactory = XPathFactory.newInstance();
            XPath xpath = xPathFactory.newXPath();
            XPathExpression expr = xpath.compile("//Records/Record/*"); // Modify as per your XML structure

            // Evaluate the expression and extract element names
            NodeList nodeList = (NodeList) expr.evaluate(document, XPathConstants.NODESET);

            Set<String> headers = new HashSet<>(); // Use a Set to ensure uniqueness

            for (int i = 0; i < nodeList.getLength(); i++) {
                String nodeName = nodeList.item(i).getNodeName();
                System.out.println("Found node: " + nodeName); // Debugging line
                headers.add(nodeName); // Add the element name to the set
            }

            if (headers.isEmpty()) {
                throw new IllegalArgumentException("XML file has no headers.");
            }

            return new ArrayList<>(headers); // Convert Set back to List

        } catch (Exception e) {
            throw new IOException("Failed to parse XML file: " + e.getMessage(), e);
        }
    }

    private String getFileExtension(String fileName) {
        String fileExtension = "";

        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex > 0) {
            fileExtension = fileName.substring(dotIndex + 1);
        }
        return fileExtension;
    }
}
