package org.diabetestechnology.drh.service.http.pg.constant.email;

import java.time.LocalDateTime;

public class OtpVerificationState {
    private final String otp;
    private final LocalDateTime expiry;
    private int retryCount = 0;
    private final int maxRetries;

    public OtpVerificationState(String otp, int expiryMinutes, int maxRetries) {
        this.otp = otp;
        this.expiry = LocalDateTime.now().plusMinutes(expiryMinutes);
        this.maxRetries = maxRetries;
    }

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiry);
    }

    public boolean canRetry() {
        return retryCount < maxRetries;
    }

    public void incrementRetry() {
        this.retryCount++;
    }

    // Getters
    public String getOtp() {
        return otp;
    }
}
