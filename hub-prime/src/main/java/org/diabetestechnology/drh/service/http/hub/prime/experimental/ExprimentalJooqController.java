package org.diabetestechnology.drh.service.http.hub.prime.experimental;

// import org.diabetestechnology.drh.udi.UdiPrimeDbConfig;
// import org.diabetestechnology.drh.udi.auto.jooq.ingress.Tables;
// import org.jooq.Condition;
// import org.jooq.DSLContext;
// import org.jooq.Field;
// import org.jooq.Query;
// import org.jooq.Record;
// import org.jooq.SelectJoinStep;
// import org.jooq.SortField;
// import org.jooq.Table;
// import org.jooq.impl.DSL;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.stereotype.Controller;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestHeader;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseBody;

// import io.swagger.v3.oas.annotations.Hidden;
// import io.swagger.v3.oas.annotations.Parameter;
// import lib.aide.tabular.JooqRowsSupplier;
// import lib.aide.tabular.TabularRowsRequest;

// import static org.jooq.impl.DSL.field;
// import static org.jooq.impl.DSL.table;

// import java.util.ArrayList;
// import java.util.List;

// @Hidden
// @Controller
// public class ExprimentalJooqController {
// // @Autowired
// // private DataAccessService dataAccessService;
// private static final Logger LOG =
// LoggerFactory.getLogger(ExprimentalJooqController.class.getName());
// private final UdiPrimeDbConfig udiPrimeDbConfig;

// public ExprimentalJooqController(UdiPrimeDbConfig udiPrimeDbConfig) {
// this.udiPrimeDbConfig = udiPrimeDbConfig;
// }

// @SuppressWarnings("unchecked")
// @PostMapping("jooq/each-study-participant-dashboard")
// @ResponseBody
// public Object getEachStudyParticipantDashboard(
// @Parameter(description = "Study ID", required = true) @RequestParam String
// studyId,
// @RequestBody TabularRowsRequest payload,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required =
// false) boolean includeGeneratedSqlInResp,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required
// = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {
// String dbName = studyId.toLowerCase();
// DSLContext dsl = udiPrimeDbConfig.dsl();
// LOG.info("Endpoint accessed: POST /each-study-participant-dashboard, studyId:
// {}", studyId);

// final var tableName = "scdpv";
// Table<?> studyCombinedDashboardParticipantMetricsView = table(
// "`" + dbName +
// "`.study_combined_dashboard_participant_metrics_view").as(tableName);
// List<Condition> conditions = new ArrayList<>();
// if (payload.filterModel() != null)
// payload.filterModel().forEach((field, filter) -> {
// switch (filter.type()) {
// case "like" -> conditions.add(field(field).like("%" + filter + "%"));
// case "equals" -> conditions.add(field(field).eq(filter));
// case "number" -> conditions.add(field(field).eq(filter));
// case "date" -> conditions.add(field(field).eq(filter));
// case "contains" -> conditions.add(field(field).like("%" + filter + "%"));
// default -> throw new IllegalArgumentException(
// "Unknown filter type '" + filter.filterType()
// + "' in filter for field '" + field
// + "' see DataAccessService::getEachStudyParticipantDashboard");
// }
// });

// // Combine all conditions using AND
// Condition combinedCondition = DSL.trueCondition(); // Start with a neutral
// condition
// for (Condition condition : conditions) {
// combinedCondition = combinedCondition.and(condition);
// }

// final var sortFields = (payload.sortModel() != null)
// ? payload.sortModel().stream()
// .map(sort -> {
// String colId = sort.colId();
// switch (sort.sort()) {
// case "asc":
// return field(colId).asc();
// case "desc":
// return field(colId).desc();
// default:
// throw new IllegalArgumentException(
// "Unknown sort direction: "
// + sort.sort());
// }
// })
// .toArray(SortField<?>[]::new)
// : new SortField<?>[0];

// // final var groupByFields = new ArrayList<Field<?>>();
// final var selectFields = new ArrayList<Field<?>>();
// if (payload.rowGroupCols() != null)
// payload.rowGroupCols().forEach(col -> {
// final var field = field(col.field());
// // groupByFields.add(field);
// selectFields.add(field);
// });
// // Construct the jOOQ query
// var query = !selectFields.isEmpty() ? dsl
// .select(selectFields)
// .from(studyCombinedDashboardParticipantMetricsView)
// : dsl
// .select()
// .from(studyCombinedDashboardParticipantMetricsView);
// if (!conditions.isEmpty()) {
// query = (SelectJoinStep<Record>) query.where(combinedCondition);
// }

// if (sortFields.length > 0) {
// query = (SelectJoinStep<Record>) query.orderBy(sortFields);
// }

// LOG.info("Query for fetching data from
// study_combined_dashboard_participant_metrics_view in schema {}: {}",
// dbName, query.getSQL());

// var bindValues = new ArrayList<Object>();

// return new JooqRowsSupplier.Builder()
// .withRequest(payload)
// .withQuery(Tables.class, dbName, tableName,
// (Query) query, bindValues)
// .withDSL(udiPrimeDbConfig.dsl())
// .withLogger(LOG)
// .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
// .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
// .build()
// .response();
// }

// }
