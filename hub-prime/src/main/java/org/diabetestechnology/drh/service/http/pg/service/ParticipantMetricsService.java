package org.diabetestechnology.drh.service.http.pg.service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.ux.ParticipantMetricsController;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ParticipantMetricsService {

    @Autowired
    @Qualifier("secondaryDsl")
    private DSLContext dsl;

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantMetricsController.class);
    private final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public JSONB getParticipantDateTimeRange(String studyId, String participantId) {
        LOG.info("Fetching date-time range for Participant: {} in Study: {}", participantId, studyId);

        final var query = dsl.select(DSL.field(
                "drh_stateless_raw_observation.get_participant_cgm_dates({0}, {1})",
                JSONB.class,
                studyId,
                participantId));
        LOG.info("Participant DateTime Range, Query: {}", query);
        JSONB response = query.fetchOneInto(JSONB.class);
        LOG.info("Participant DateTime Range, Response: {}", response);
        return response;
    }

    @Transactional
    public JSONB getAmbulatoryGlucoseProfile(String studyId, String participantId, String startDate, String endDate) {
        LOG.info("Read Ambulatory Glucose Profile for Study: {}, Participant: {}, Start Date: {}, End Date: {}",
                studyId, participantId, startDate, endDate);
        try {
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_ambulatory_glucose_profile(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("AGP Query: {}", query);

            JSONB result = query.fetchOneInto(JSONB.class);
            LOG.info("AGP Response: {}", result);

            return result;
        } catch (Exception e) {
            LOG.error("Error executing function: {}", e.getMessage(), e);

            return JSONB
                    .jsonb(Map.of("status", "error", "message", "Error parsing date formats", "errors", e.getMessage())
                            .toString());
        }
    }

    @Transactional
    public JSONB getParticipantMetrics(String studyId, String participantId, String startDate, String endDate) {
        LOG.info("Read Participant Metrics for Study: {}, Participant: {}, Start Date: {}, End Date: {}",
                studyId, participantId, startDate, endDate);
        try {
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_participant_metrics(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("Participant Metrics, Query: {}", query);
            JSONB result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Participant Metrics, Response: {}", result);

            return result;
        } catch (Exception e) {
            LOG.error("Error executing function: {}", e.getMessage(), e);
            return JSONB.jsonb(Map
                    .of("status", "error", "message", "Error processing request", "errors", e.getMessage()).toString());
        }
    }

    @Transactional
    public JSONB getTimeRangeStackedData(String studyId, String participantId, String startDate, String endDate) {
        LOG.info("Read Time Range Stacked Data for Study: {}, Participant: {}, Start Date: {}, End Date: {}",
                studyId, participantId, startDate, endDate);
        try {
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_time_range_stacked_data(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("Time Range Stacked Data, Query: {}", query);
            JSONB result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Time Range Stacked Data, Response: {}", result);

            return result;
        } catch (Exception e) {
            LOG.error("Error executing function: {}", e.getMessage(), e);
            return JSONB.jsonb(Map
                    .of("status", "error", "message", "Error processing request", "errors", e.getMessage()).toString());
        }
    }

    @Transactional
    public JSONB getAdvancedMetrics(String studyId, String participantId, String startDate, String endDate) {
        LOG.info("Read Advanced Metrics for Study: {}, Participant: {}, Start Date: {}, End Date: {}",
                studyId, participantId, startDate, endDate);
        try {
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);
            LOG.info("Fetching advanced metrics for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, parsedStartDate, parsedEndDate);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_advanced_metrics(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("Advanced Metrics, Query: {}", query);
            JSONB result = query
                    .fetchOneInto(JSONB.class);

            return result;
        } catch (Exception e) {
            LOG.error("Error executing function get_advanced_metrics: {}", e.getMessage(), e);
            return JSONB.jsonb(Map
                    .of("status", "error", "message", "Error processing request", "errors", e.getMessage()).toString());
        }
    }

    @Transactional
    public JSONB getDailyGlucoseProfile(String studyId, String participantId, String startDate, String endDate) {
        try {
            LOG.info("Fetching daily glucose profile for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, startDate, endDate);
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);

            LOG.info("Fetching daily glucose profile for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, parsedStartDate, parsedEndDate);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_daily_glucose_profile(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("Daily Glucose Profile, Query: {}", query);
            JSONB response = query.fetchOneInto(JSONB.class);
            LOG.info("Daily Glucose Profile, Response: {}", response);
            return response;

        } catch (Exception e) {
            LOG.error("Error executing function get_daily_glucose_profile: {}", e.getMessage(), e);
            return JSONB.jsonb(Map
                    .of("status", "error", "message", "Error processing request", "errors", e.getMessage()).toString());
        }
    }

    @Transactional
    public JSONB getGlycemicRiskIndicator(String studyId, String participantId, String startDate, String endDate) {
        LOG.info("Glycemic risk indicator for Study: {}, Participant: {}, Start Date: {}, End Date: {}",
                studyId, participantId, startDate, endDate);
        try {
            LocalDate parsedStartDate = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate parsedEndDate = LocalDate.parse(endDate, DATE_FORMATTER);
            LOG.info("Fetching Glycemic risk indicator for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, parsedStartDate, parsedEndDate);

            final var query = dsl.select(DSL.field(
                    "drh_stateless_raw_observation.get_glycemic_risk_indicator(?, ?, ?, ?)",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(participantId),
                    DSL.val(parsedStartDate),
                    DSL.val(parsedEndDate)));
            LOG.info("Glycemic risk indicator, Query: {}", query);
            JSONB result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Glycemic risk indicator, Response: {}", result);

            return result;
        } catch (Exception e) {
            LOG.error("Error executing function get_glycemic_risk_indicator: {}", e.getMessage(), e);
            return JSONB.jsonb(Map
                    .of("status", "error", "message", "Error processing request", "errors", e.getMessage()).toString());
        }
    }
}
