package org.diabetestechnology.drh.service.http.pg.service;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionState;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.constant.ResponseCode;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantDataRequest;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantFileDataRequest;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;

@Service
public class ParticipantService {

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final MasterService masterService;
    private final InteractionService interactionService;
    private final DbActivityService activityLogService;

    public ParticipantService(@Qualifier("secondaryDsl") DSLContext dsl, UserNameService userNameService,
            PartyService partyService, MasterService masterService,
            InteractionService interactionService, DbActivityService activityLogService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.masterService = masterService;
        this.interactionService = interactionService;
        this.activityLogService = activityLogService;
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public JSONB saveParticipantData(ParticipantDataRequest request) {

        LOG.info("Starting to save participant data: studyId={}, participantDisplayId={}, genderId={}, age={}",
                request.studyId(), request.participantDisplayId(), request.genderId(), request.age());
        try {
            final var userId = userNameService.getUserId();
            LOG.debug("Retrieved userId: {}", userId);

            final var partyId = partyService.getPartyIdByUserId(userId);
            LOG.debug("Resolved partyId for userId {}: {}", userId, partyId);
            String activityData = activityLogService.prepareActivityLogMetadata();

            LOG.info("Executing database function: save_individual_participant_data with studyId={}, orgPartyId={}",
                    request.studyId(), request.orgPartyId());
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.save_individual_participant_data(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                            JSONB.class,
                            DSL.val(request.studyId()),
                            DSL.val(request.orgPartyId()),
                            DSL.val(request.participantDisplayId()),
                            DSL.val(request.genderId()),
                            DSL.val(request.age()),
                            DSL.val(partyId),
                            DSL.val(!StringUtils.hasText(request.diagnosisIcd()) ? null : request.diagnosisIcd()),
                            DSL.val(!StringUtils.hasText(request.medRxnorm()) ? null : request.medRxnorm()),
                            DSL.val(!StringUtils.hasText(request.treatmentModality()) ? null
                                    : request.treatmentModality()),
                            DSL.val(!StringUtils.hasText(request.raceId())
                                    ? masterService.getRaceIdByName("Unknown")
                                    : request.raceId()),
                            DSL.val(!StringUtils.hasText(request.ethnicityId())
                                    ? masterService.getEthnicityIdByName("Unknown")
                                    : request.ethnicityId()),
                            DSL.val(request.bmi()),
                            DSL.val(request.baselineHba1c()),
                            DSL.val(!StringUtils.hasText(request.diabetesType()) ? null : request.diabetesType()),
                            DSL.val(!StringUtils.hasText(request.studyArm()) ? null : request.studyArm()),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Save Participant Query: {}", query);
            JSONB result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Database function executed successfully. Result: {}", result);
            if (result != null) {
                Map<String, Object> resultMap = new ObjectMapper().readValue(result.data(), Map.class);
                String status = (String) resultMap.get("status");
                String message = (String) resultMap.get("message");

                LOG.info("Database status: {}, Message: {}", status, message);
                if ("failure".equalsIgnoreCase(status)) {
                    LOG.info("Database status is failure. Message: {}", message);
                    LOG.warn("Failed to save participant data: {}", message);
                    interactionService.saveStudyParticipantInteraction(request.studyId(), null,
                            null, ActionType.CREATE_PARTICIPANT, ActionDescription.CREATE_PARTICIPANT_FAILED,
                            null, null, JsonUtils.toJson(request), result.data(), message,
                            0, ActionStatus.FAILED, ActionType.CREATE_PARTICIPANT, ActionStatus.FAILED);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                } else if ("success".equalsIgnoreCase(status)) {
                    String participantId = (String) resultMap.get("participant_id");
                    LOG.info("Successfully saved participant data. Result: {}", result);
                    interactionService.saveStudyParticipantInteraction(request.studyId(),
                            participantId,
                            null, ActionType.CREATE_PARTICIPANT, ActionDescription.CREATE_PARTICIPANT,
                            null, ActionState.CREATED, JsonUtils.toJson(request), result.data(), null,
                            ResponseCode.SUCCESS, ActionStatus.SUCCESS, ActionType.CREATE_PARTICIPANT,
                            ActionStatus.SUCCESS);
                    return result;
                } else {
                    LOG.info("Database status is {}. Message: {}", status, message);
                    LOG.warn("Failed to save participant data: {}", message);
                    interactionService.saveStudyParticipantInteraction(request.studyId(), null,
                            null, ActionType.CREATE_PARTICIPANT, ActionDescription.CREATE_PARTICIPANT_FAILED,
                            null, null, JsonUtils.toJson(request), result.data(), message,
                            0, ActionStatus.FAILED, ActionType.CREATE_PARTICIPANT, ActionStatus.FAILED);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                }
            } else {
                LOG.warn("Database function returned null. Participant data may not have been saved.");
                final var message = "Database function returned null. Participant data may not have been saved.";
                interactionService.saveStudyParticipantInteraction(request.studyId(), null,
                        null, ActionType.CREATE_PARTICIPANT, ActionDescription.CREATE_PARTICIPANT_FAILED,
                        null, null, JsonUtils.toJson(request), (result != null ? result.data() : null), message,
                        0, ActionStatus.FAILED, ActionType.CREATE_PARTICIPANT, ActionStatus.FAILED);
                throw new IllegalArgumentException("Failed to save participant data: Database function returned null.");
            }

        } catch (Exception e) {
            LOG.error("Error while saving participant data: {}", e.getMessage(), e);
            final var message = "Error while saving participant data." + e.getMessage();
            interactionService.saveStudyParticipantInteraction(request.studyId(), null,
                    null, ActionType.CREATE_PARTICIPANT, ActionDescription.CREATE_PARTICIPANT_ERROR,
                    null, null, JsonUtils.toJson(request), null, message,
                    0, ActionStatus.FAILED, ActionType.CREATE_PARTICIPANT, ActionStatus.FAILED);

            throw new IllegalArgumentException("Failed to save participant data due to an error: " + e.getMessage(), e);
        }
    }

    @Transactional
    public JSONB updateParticipantDataInline(String participantId, JSONB jsonInput) {
        try {

            final var userId = userNameService.getUserId();
            LOG.debug("Retrieved userId: {}", userId);
            String activityData = activityLogService.prepareActivityLogMetadata();
            final var partyId = partyService.getPartyIdByUserId(userId);
            LOG.debug("Resolved partyId for userId {}: {}", userId, partyId);
            JSONObject jsonObject = new JSONObject(jsonInput.data());

            if (jsonObject.has("race_type_id")) {
                String raceTypeId = jsonObject.optString("race_type_id");
                if (!StringUtils.hasText(raceTypeId)) {
                    jsonObject.put("race_type_id", masterService.getRaceIdByName("Unknown"));
                }
            }

            if (jsonObject.has("ethnicity_type_id")) {
                String ethnicityTypeId = jsonObject.optString("ethnicity_type_id");
                if (!StringUtils.hasText(ethnicityTypeId)) {
                    jsonObject.put("ethnicity_type_id", masterService.getEthnicityIdByName("Unknown"));
                }
            }

            JSONB updatedJsonInput = JSONB.valueOf(jsonObject.toString());
            LOG.info("Updating participant data for researchSubjectId: {}", participantId);
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.update_individual_participant_data_inline({0}, {1}, {2}, {3})",
                            JSONB.class,
                            DSL.val(participantId),
                            DSL.val(updatedJsonInput),
                            DSL.val(partyId),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Update Participant Inline Data Query: {}", query);
            final var result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Edit participant Result: {}", result);
            if (result != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = new ObjectMapper().readValue(result.data(), Map.class);
                String status = (String) resultMap.get("status");
                String message = (String) resultMap.get("message");
                final var studyId = getStudyIdOfParticipant(participantId);
                String hubInteractionId = interactionService.getHubIntercationIdOfStudyParticipant(studyId);
                if ("failure".equalsIgnoreCase(status)) {
                    LOG.info("Database status is failure. Message: {}", message);
                    LOG.warn("Failed to edit participant data: {}", message);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                } else if ("success".equalsIgnoreCase(status)) {
                    interactionService.saveStudyParticipantInteraction(studyId, participantId,
                            hubInteractionId, ActionType.UPDATE_PARTICIPANT, ActionDescription.UPDATE_PARTICIPANT,
                            null, null, jsonInput.data(), result.data(), null,
                            ResponseCode.SUCCESS, ActionStatus.SUCCESS, ActionType.UPDATE_PARTICIPANT,
                            ActionStatus.SUCCESS);
                    return result;
                } else {
                    LOG.info("Database status is not success. Message: {} Status {}", message, status);
                    LOG.warn("Failed to edit participant data: {}", message);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                }
            } else {
                LOG.warn("Database function returned null. Participant data may not have been saved.");
                throw new IllegalArgumentException("Failed to save participant data: Database function returned null.");
            }
        } catch (Exception e) {
            LOG.error("Error while updating participant data for participantId={}: {}", participantId, e.getMessage(),
                    e);
            final var message = "Error while updating participant data for participantId: " + participantId + " . "
                    + e.getMessage();
            final var studyId = getStudyIdOfParticipant(participantId);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudyParticipant(studyId);
            interactionService.saveStudyParticipantInteraction(
                    studyId, participantId,
                    hubInteractionId, ActionType.UPDATE_PARTICIPANT, ActionDescription.UPDATE_PARTICIPANT_ERROR,
                    null, null, jsonInput.data(), null, message,
                    0, ActionStatus.FAILED, ActionType.UPDATE_PARTICIPANT, ActionStatus.FAILED);
            throw new RuntimeException("Failed to update participant data.", e);
        }
    }

    @Transactional
    public Map<String, Object> getParticipantDetails(String participantId) {
        LOG.info("Get participant details corresponding to the participantId: {}", participantId);
        LOG.info("Query for Participant Details: {}", dsl
                .select(DSL.field(
                        "jsonb_build_object(" +
                                "'organization_party_id', organization_party_id, " +
                                "'practitioner_party_id', practitioner_party_id, " +
                                "'study_id', study_id, " +
                                "'study_display_id', study_display_id, " +
                                "'participant_id', participant_id, " +
                                "'participant_display_id', participant_display_id, " +
                                "'participant_age', participant_age, " +
                                "'participant_gender', participant_gender, " +
                                "'participant_race', participant_race, " +
                                "'participant_ethnicity', participant_ethnicity, " +
                                "'diabetes_type', diabetes_type, " +
                                "'study_arm', study_arm, " +
                                "'bmi', bmi, " +
                                "'baseline_hba1c', baseline_hba1c, " +
                                "'diagnosis_icd', diagnosis_icd, " +
                                "'med_rxnorm', med_rxnorm, " +
                                "'treatment_modality', treatment_modality " +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.participant_data_view")
                .where(DSL.field("participant_id").eq(DSL.val(participantId))));
        JSONB jsonbResult = dsl
                .select(DSL.field(
                        "jsonb_build_object(" +
                                "'organization_party_id', organization_party_id, " +
                                "'practitioner_party_id', practitioner_party_id, " +
                                "'study_id', study_id, " +
                                "'study_display_id', study_display_id, " +
                                "'participant_id', participant_id, " +
                                "'participant_display_id', participant_display_id, " +
                                "'participant_age', participant_age, " +
                                "'participant_gender', participant_gender, " +
                                "'participant_race', participant_race, " +
                                "'participant_ethnicity', participant_ethnicity, " +
                                "'diabetes_type', diabetes_type, " +
                                "'study_arm', study_arm, " +
                                "'bmi', bmi, " +
                                "'baseline_hba1c', baseline_hba1c, " +
                                "'diagnosis_icd', diagnosis_icd, " +
                                "'med_rxnorm', med_rxnorm, " +
                                "'treatment_modality', treatment_modality " +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.participant_data_view")
                .where(DSL.field("participant_id").eq(DSL.val(participantId)))
                .fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return Map.of();
        }

        try {
            String jsonString = jsonbResult.data();

            return objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            LOG.error("Error while processing JSON", e);
            return Map.of();
        }
    }

    @Transactional
    public boolean checkIfParticipantDisplayIdExists(String studyId, String participantDisplayId) {
        return dsl.selectCount()
                .from("drh_stateful_research_study.research_subject")
                .where(DSL.field("participant_identifier").eq(participantDisplayId)
                        .and(DSL.field("study_reference").eq(studyId)))
                .fetchOne(0, int.class) > 0;
    }

    @Transactional
    public JSONB saveParticipantsDataFromFile(ParticipantFileDataRequest request, String fileUrl,
            ArrayNode fileContent, String lastInteractionID) {
        final var userId = userNameService.getUserId();
        LOG.info("Executing database function: insert_participants_through_file_upload with studyId={}, orgPartyId={}",
                request.studyId(), request.orgPartyId());
        String activityData = activityLogService.prepareActivityLogMetadata();
        try {
            LOG.info("Saving participants data from file.");
            ArrayNode cleanedJsonArray = cleanJsonArray(fileContent);
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.insert_participants_through_file_upload({0}, {1}, {2}, {3}, {4}, {5}, {6})",
                            JSONB.class,
                            DSL.value(request.studyId()),
                            DSL.value(fileUrl),
                            DSL.value(request.orgPartyId()),
                            partyService.getPartyIdByUserId(userId),
                            DSL.cast(DSL.value(cleanedJsonArray.toString()), JSONB.class),
                            DSL.value(lastInteractionID),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Saving participants data from file: Query: {}", query);

            JSONB result = query
                    .fetchOneInto(JSONB.class);

            // Convert JSONB to String for serialization
            return result != null ? result : null;
        } catch (Exception e) {
            LOG.error("Error while processing JSON", e);
            return null;
        }

    }

    @Transactional
    public JSONB updateParticipantData(String participantId, ParticipantDataRequest request) {
        LOG.info("Updating participant data for researchSubjectId: {}", participantId);
        var mappedFields = new HashMap<>();
        if (request.diagnosisIcd() != null)
            mappedFields.put("diagnosis_icd", request.diagnosisIcd());
        if (request.medRxnorm() != null)
            mappedFields.put("med_rxnorm", request.medRxnorm());
        if (request.treatmentModality() != null)
            mappedFields.put("treatment_modality", request.treatmentModality());
        if (request.genderId() != null)
            mappedFields.put("gender_type_id", request.genderId());
        if (request.age() != null)
            mappedFields.put("age", request.age());
        if (request.bmi() != null)
            mappedFields.put("bmi", request.bmi());
        if (request.baselineHba1c() != null)
            mappedFields.put("baseline_hba1c", request.baselineHba1c());
        if (request.diabetesType() != null)
            mappedFields.put("diabetes_type", request.diabetesType());
        if (request.studyArm() != null)
            mappedFields.put("study_arm", request.studyArm());
        if (!StringUtils.hasText(request.raceId())) {
            mappedFields.put("race_type_id", masterService.getRaceIdByName("Unknown"));
        } else {
            mappedFields.put("race_type_id", request.raceId());
        }
        if (!StringUtils.hasText(request.ethnicityId())) {
            mappedFields.put("ethnicity_type_id", masterService.getEthnicityIdByName("Unknown"));
        } else {
            mappedFields.put("ethnicity_type_id", request.ethnicityId());
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String activityData = activityLogService.prepareActivityLogMetadata();
        JSONB jsonInput;
        try {
            jsonInput = JSONB.valueOf(new ObjectMapper().writeValueAsString(mappedFields));
        } catch (JsonProcessingException e) {
            LOG.error("Error while converting ParticipantDataRequest to JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process participant data request.", e);
        }
        try {
            final var userId = userNameService.getUserId();
            final var partyId = partyService.getPartyIdByUserId(userId);
            LOG.debug("Resolved partyId for userId {}: {}", userId, partyId);
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.update_individual_participant_data_inline({0}, {1}, {2}, {3})",
                            JSONB.class,
                            DSL.value(participantId),
                            DSL.value(jsonInput),
                            DSL.value(partyId),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Update Participant Data Query: {}", query);
            final var result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Edit participant Result: {}", result);
            if (result != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = new ObjectMapper().readValue(result.data(), Map.class);
                String status = (String) resultMap.get("status");
                String message = (String) resultMap.get("message");
                final var studyId = getStudyIdOfParticipant(participantId);
                String hubInteractionId = interactionService.getHubIntercationIdOfStudyParticipant(studyId);
                if ("failure".equalsIgnoreCase(status)) {
                    LOG.info("Database status is failure. Message: {}", message);
                    LOG.warn("Failed to edit participant data: {}", message);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                } else if ("success".equalsIgnoreCase(status)) {
                    interactionService.saveStudyParticipantInteraction(studyId, participantId,
                            hubInteractionId, ActionType.UPDATE_PARTICIPANT, ActionDescription.UPDATE_PARTICIPANT,
                            null, null, jsonInput.data(), result.data(), null,
                            ResponseCode.SUCCESS, ActionStatus.SUCCESS, ActionType.UPDATE_PARTICIPANT,
                            ActionStatus.SUCCESS);
                    return result;
                } else {
                    LOG.info("Database status is not success. Message: {} Status {}", message, status);
                    LOG.warn("Failed to edit participant data: {}", message);
                    throw new IllegalArgumentException("Failed to save participant data: " + message);
                }
            } else {
                LOG.warn("Database function returned null. Participant data may not have been saved.");
                throw new IllegalArgumentException("Failed to save participant data: Database function returned null.");
            }
        } catch (Exception e) {
            LOG.error("Error while updating participant data for participantId={}: {}", participantId, e.getMessage(),
                    e);
            final var message = "Error while updating participant data for participantId : " + participantId + " . "
                    + e.getMessage();
            final var studyId = getStudyIdOfParticipant(participantId);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudyParticipant(studyId);
            interactionService.saveStudyParticipantInteraction(
                    studyId, participantId,
                    hubInteractionId, ActionType.UPDATE_PARTICIPANT, ActionDescription.UPDATE_PARTICIPANT_ERROR,
                    null, null, jsonInput.data(), null, message,
                    0, ActionStatus.FAILED, ActionType.UPDATE_PARTICIPANT, ActionStatus.FAILED);
            throw new RuntimeException("Failed to update participant data.", e);
        }
    }

    private ArrayNode cleanJsonArray(ArrayNode jsonArray) {
        ArrayNode cleanedArray = JsonNodeFactory.instance.arrayNode();
        for (JsonNode node : jsonArray) {
            ObjectNode cleanedNode = node.deepCopy();
            node.fieldNames().forEachRemaining(field -> {
                if (node.has(field) && node.get(field).asText().trim().isEmpty()) {
                    cleanedNode.set(field, null); // Convert empty strings to null
                }
            });
            cleanedArray.add(cleanedNode);
        }
        return cleanedArray;
    }

    private String getStudyIdOfParticipant(String participantId) {
        final var query = dsl.selectDistinct(DSL.field(
                "study_id"))
                .from("drh_stateless_research_study.participant_data_view")
                .where(DSL.field("participant_id").eq(DSL.val(participantId)))
                .limit(1);
        LOG.info("Get Hub study ID Query: {}", query);
        String studyId = query.fetchOneInto(String.class);
        LOG.info("Get Study Id Response: {}", studyId);
        return studyId;
    }

}
