package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.diabetestechnology.drh.service.http.pg.service.ChunkDatabaseMigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;

@Controller
@Tag(name = "DRH Hub Database Migration APIs")
public class ChunkDatabaseMigrationController {
    private static final Logger LOG = LoggerFactory.getLogger(ChunkDatabaseMigrationController.class);
    private final ChunkDatabaseMigrationService databaseMigrationService;

    public ChunkDatabaseMigrationController(ChunkDatabaseMigrationService databaseMigrationService) {
        this.databaseMigrationService = databaseMigrationService;
    }

    @PostMapping("/research-study/database/chunk/upload")
    @Operation(summary = "Upload and save a research srudy Database")
    @ResponseBody
    public Response uploadDatabaseFile(@RequestPart("file") @Nonnull MultipartFile file,
            @Valid @RequestBody DatabaseMigrationRequest request, @RequestParam("chunkNumber") int chunkNumber,
            @RequestParam("totalChunks") int totalChunks,
            @RequestParam("fileName") String fileName) throws Exception {
        LOG.info("Received request to upload database file: {}", request);
        final var chunkResponse = databaseMigrationService.uploadChunk(file, chunkNumber, totalChunks, fileName);
        final var fileSize = file.getSize();
        final var contentType = file.getContentType();

        if (chunkResponse.equals("Upload completed!")) {
            Map<String, Object> response = databaseMigrationService.uploadAndSaveDBFile(fileName, request, fileSize,
                    contentType);

            LOG.debug("Database Migration Response : {} , {}", response, fileName);
            final var message = response.getOrDefault("status", "").equals("Successfully migrated database")
                    ? "Database upload completed"
                    : response.getOrDefault("status", "Failed To Copy database.").toString();
            final var status = response.getOrDefault("status", "")
                    .equals("Transferring SQLite database contents is in progress.")
                            ? "success"
                            : "error";

            return Response.builder()
                    .data(Map.of("status", response))
                    .status(status)
                    .message(message)
                    .errors(null)
                    .build();
        } else {
            final var message = chunkResponse.toString() != null ? chunkResponse.toString() : "File upload failed";
            final var status = chunkResponse.toString() != null ? "Inprogress" : "error";
            return Response.builder()
                    .data(Map.of("status", chunkResponse))
                    .status(status)
                    .message(message)
                    .errors(null)
                    .build();
        }
    }

    @GetMapping("/research-study/database/processing/status")
    @Operation(summary = "CGM Row data Copy Process Completion Status")
    @ResponseBody
    public Response getCgmRowDataStatus(@RequestParam String studyId) {
        LOG.info("Read CGM Row data Copy Process Completion Status for the study: {}", studyId);
        final Map<String, Object> responseMap = new HashMap<>(Map.of("showProcess",
                databaseMigrationService.isCompletedCgmRowData(studyId)));
        responseMap.put("mealsAndFitnessMigrationExists",
                databaseMigrationService.isMealsAndMigrationDataStatusAvailable(studyId));
        return Response.builder()
                .data(responseMap)
                .status("success")
                .message("Successfully read process completion status")
                .errors(null)
                .build();
    }

    @GetMapping("/research-study/database/extraction/status")
    @Operation(summary = "CGM Row data Copy Process Completion Status")
    @ResponseBody
    public Response getDataExtractionStatus(@RequestParam String studyId) {
        LOG.info("Read CGM Row data Extraction Completion Status for the study: {}", studyId);
        final var response = databaseMigrationService.isCompletedDataExtraction(studyId);
        LOG.info("Read CGM Row data Extraction Completion Status for the study, response: {}", response);
        return Response.builder()
                .data(response)
                .status(response.containsKey(
                        "error") ? "error" : "success")
                .message("Successfully read extraction completion status")
                .errors(response.containsKey(
                        "error") ? "Failed to read Migration Status" : null)
                .build();
    }

    @PostMapping("/research-study/database/extraction")
    @Operation(summary = "CGM Row data Copy Process Completion Status")
    @ResponseBody
    public Response startDataExtraction(@RequestParam String studyId) {
        LOG.info("Start Data Extraction for the study: {}", studyId);
        try {
            final var response = databaseMigrationService.startExtraction(studyId);
            LOG.info("Start data Extraction study {} response: {}", studyId, response);
            return Response.builder()
                    .data(Map.of("response", response))
                    .status("success")
                    .message("Successfully read extraction completion status")
                    .errors(null)
                    .build();
        } catch (Exception ex) {
            LOG.error("Failed to start data extraction for study: {}", studyId, ex);
            return Response.builder()
                    .data(Map.of("response", "Failed to start data extraction"))
                    .status("error")
                    .message("Failed to start data extraction")
                    .errors(ex.getMessage())
                    .build();
        }
    }

}
