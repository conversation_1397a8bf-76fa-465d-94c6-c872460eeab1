package org.diabetestechnology.drh.service.http.pg.service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileProcessingStatus;
import org.diabetestechnology.drh.service.http.pg.constant.ResearchStudyState;
import org.diabetestechnology.drh.service.http.pg.constant.StudyInteractionType;
import org.diabetestechnology.drh.service.http.pg.request.PublicationUpdateRequest;
import org.diabetestechnology.drh.service.http.pg.request.ResearchStudySettingsRequest;
import org.diabetestechnology.drh.service.http.pg.request.StudyRequest;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class ResearchStudyService {

    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final InteractionService interactionService;
    private final MasterService masterService;
    private final DbActivityService activityLogService;

    public ResearchStudyService(@Qualifier("secondaryDsl") DSLContext dsl, UserNameService userNameService,
            PartyService partyService, InteractionService interactionService,
            MasterService masterService, DbActivityService activityLogService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.interactionService = interactionService;
        this.masterService = masterService;
        this.activityLogService = activityLogService;
    }

    @Transactional
    public String saveResearchStudy(StudyRequest request) {
        LOG.info("Saving research study with identifier: {}, tenantId: {}, title: {}, visibility: {}",
                request.researchStudyIdentifier(), request.tenantId(), request.title(), request.visibility());
        String activityData = activityLogService.prepareActivityLogMetadata();

        final var query = dsl
                .select(DSL.field("drh_stateless_research_study.save_research_study({0}, {1}, {2}, {3}, {4}, {5}, {6})",
                        String.class,
                        DSL.val(request.researchStudyIdentifier()),
                        DSL.val(request.tenantId()),
                        DSL.val(request.title()),
                        DSL.val(request.description()),
                        DSL.val(request.createdBy() == null ? "UNKNOWN" : request.createdBy()),
                        DSL.val(request.visibility()),
                        DSL.cast(DSL.val(
                                activityData),
                                JSONB.class)));
        LOG.info("Save Research Study Query: {}", query);

        String studyResponse = query.fetchOneInto(String.class);
        try {// get hub inteaction id as a response of the function

            JsonNode studyResponseJson = objectMapper.readTree(studyResponse);

            if (studyResponseJson.has("study_id")) {
                String studyId = studyResponseJson.get("study_id").asText();
                System.out.println("Study ID: " + studyId);
                String interactionResponse = interactionService.saveHubInteraction(studyId);
                if (interactionResponse != null) {
                    LOG.info("Successfully saved hub interaction for studyId: {}", studyId);
                    interactionService.saveStudyInteraction(studyId, interactionResponse, StudyInteractionType.CREATE,
                            ActionDescription.CREATE_STUDY, null,
                            ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                            JsonUtils.toJson(studyResponseJson), null, 200, "Success", ActionType.CREATE_STUDY,
                            FileProcessingStatus.SUCCESS);
                } else {
                    LOG.info("Failed to save hub interaction for studyId: {}", studyId);
                }
            } else {
                System.out.println("Key 'study_id' does not exist.");
                LOG.info("Failed to save hub interaction for studyId: {}", studyResponseJson);
                String interactionResponse = interactionService.saveHubInteraction(null);
                interactionService.saveStudyInteraction(null, interactionResponse, StudyInteractionType.CREATE,
                        "Failed to Create a Study", null, null, JsonUtils.toJson(request),
                        null, JsonUtils.toJson(studyResponseJson), 0, "Failed", ActionType.CREATE_STUDY,
                        FileProcessingStatus.FAILED);

            }

        } catch (JsonProcessingException e) {
            LOG.error("Failed to process the JSON ", e);

        }
        LOG.info("Save Research Study Response: {}", studyResponse);

        if (studyResponse != null) {
            LOG.info("Successfully saved research study : {}", studyResponse);
        } else {
            LOG.warn("Failed to save research study for identifier: {}", request.researchStudyIdentifier());
        }

        return studyResponse;
    }

    @Transactional

    public JSONB editResearchStudy(String studyId, JSONB studyData) throws JsonProcessingException {
        try {
            final var userId = userNameService.getUserId();
            String userPartyId = partyService.getPartyIdByUserId(userId);
            String activityData = activityLogService.prepareActivityLogMetadata();
            LOG.info("Updating research study with ID: {}, data: {}", studyId, studyData);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.update_research_study_inline({0}, {1}, {2}, {3})",
                    String.class,
                    DSL.val(studyId),
                    DSL.val(studyData),
                    DSL.val(userPartyId),
                    DSL.cast(DSL.val(activityData), JSONB.class)));
            LOG.info("Edit Research Study Query: {}", query);
            // fetch from state
            JSONB editResearchStudyResponse = query.fetchOneInto(JSONB.class);

            LOG.info("Edit Research Study Response: {}", editResearchStudyResponse);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            if (editResearchStudyResponse != null) {
                // fetchto state
                LOG.info("Successfully updated research study with ID: {}", studyId);
                interactionService.saveStudyInteraction(studyId, hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.UPDATE_STUDY,
                        ResearchStudyState.ACTIVE,
                        ResearchStudyState.ACTIVE, studyData.data(),
                        editResearchStudyResponse.data(), null, 200, "Success",
                        ActionType.UPDATE_STUDY, FileProcessingStatus.SUCCESS);
            } else {
                LOG.warn("Failed to update research study with ID: {}", studyId);
                interactionService.saveStudyInteraction(studyId, hubInteractionId, StudyInteractionType.UPDATE,
                        "Failed to Update a Study", null, null, studyData.data(),
                        null, null, 200, "Failed", ActionType.UPDATE_STUDY, FileProcessingStatus.FAILED);
            }
            return editResearchStudyResponse;
        } catch (Exception e) {
            LOG.error("Failed to process the JSON ", e);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            interactionService.saveStudyInteraction(studyId,
                    hubInteractionId, StudyInteractionType.UPDATE,
                    "Failed to Update a Study", null, null, studyData.data(),
                    null, JsonUtils.toJson(e.getMessage()), 0, "Error", ActionType.UPDATE_STUDY,
                    FileProcessingStatus.FAILED);
            return null;
        }
    }

    @Transactional

    public JSONB saveResearchStudySettings(ResearchStudySettingsRequest request) {
        LOG.info("Saving study settings for studyId: {}", request.studyId());
        String hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());

        if (StringUtils.hasText(request.nctNumber())) {
            if (!isValidNctNumber(request.nctNumber())) {
                final var message = "The provided NCT Number is invalid. It must start with 'NCT' followed by exactly 8 digits (e.g., NCT12345678).";
                interactionService.saveStudyInteraction(request
                        .studyId(),
                        hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.UPDATE_STUDY_SETTINGS, null, null, JsonUtils.toJson(
                                request),
                        null,
                        message, 0, "Error", ActionType.UPDATE_STUDY,
                        FileProcessingStatus.FAILED);

                throw new IllegalArgumentException(
                        message);
            }

        }
        JSONB result = null;
        String activityData = activityLogService.prepareActivityLogMetadata();

        final var query = dsl
                .select(DSL.field(
                        "drh_stateless_research_study.save_research_study_settings({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10})",
                        String.class,
                        DSL.val(request.studyId()),
                        DSL.val(request.studyTitle()),
                        DSL.val(request.description()),
                        DSL.val(request.locationId()),
                        DSL.val(request.treatmentModalities()),
                        DSL.val(request.fundingSource()),
                        DSL.val(request.nctNumber()),
                        DSL.val(request.startDate()),
                        DSL.val(request.endDate()),
                        DSL.val(request.userId()),
                        DSL.cast(DSL.val(activityData), JSONB.class)));
        try {
            LOG.info("Saving study settings Query: {}", query);

            result = query.fetchOneInto(JSONB.class);

            LOG.info("Save Research Study Settings Response: {}", result);

            if (result != null) {
                LOG.info("Successfully saved study settings for studyId: {}", request.studyId());
                interactionService.saveStudyInteraction(request
                        .studyId(), hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.UPDATE_STUDY_SETTINGS,
                        ResearchStudyState.ACTIVE,
                        ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                        result.data(), null, 200, "Success",
                        ActionType.UPDATE_STUDY, FileProcessingStatus.SUCCESS);
            } else {
                LOG.warn("Failed to save study settings for studyId: {}", request.studyId());
                interactionService.saveStudyInteraction(request
                        .studyId(), hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.UPDATE_STUDY_SETTINGS,
                        ResearchStudyState.ACTIVE,
                        ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                        null, "Failed to save or update research study settings", 200, "Success",
                        ActionType.UPDATE_STUDY, FileProcessingStatus.FAILED);
            }

        } catch (Exception e) {
            LOG.error("Error while saving study settings for studyId: {}. Error: {}", request.studyId(), e.getMessage(),
                    e);
            interactionService.saveStudyInteraction(request
                    .studyId(), hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_STUDY_SETTINGS,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                    result != null ? JsonUtils.toJson(result.data()) : null, JsonUtils.toJson(e
                            .getMessage()),
                    200, "Success",
                    ActionType.UPDATE_STUDY, FileProcessingStatus.FAILED);

            return JSONB.valueOf(
                    "{\"status\": \"error\",\"error\": \"An error occurred while saving study settings. Please try again later.\"}");
        }

        return result;
    }

    public boolean isValidNctNumber(String nctNumber) {
        if (nctNumber == null || nctNumber.trim().isEmpty()) {
            return false;
        }
        // Regular expression to check for "NCT" followed by exactly 8 digits
        String nctPattern = "NCT\\d{8}";
        return nctNumber.matches(nctPattern);
    }

    @Transactional
    @Async
    public CompletableFuture<JSONB> getResearchStudies(String studyId) {
        LOG.info("Fetching study for studyId: {}", studyId);
        final var query = dsl
                .selectDistinct(DSL.field(
                        "jsonb_build_object(" +
                                "'study_id', study_id, " +
                                "'study_display_id', study_display_id, " +
                                "'title', title, " +
                                "'description', description, " +
                                "'start_date', to_char(start_date, 'MM-DD-YYYY')," +
                                "'end_date', to_char(end_date, 'MM-DD-YYYY')," +
                                "'nct_number', nct_number, " +
                                "'created_by', created_by, " +
                                "'visibility', visibility, " +
                                "'funding_source', funding_source, " +
                                "'study_location', study_location, " +
                                "'treatment_modalities', treatment_modalities, " +
                                "'organization_party_id', organization_party_id," +
                                "'organization_id', organization_id," +
                                "'practitioner_party_id', practitioner_party_id," +
                                "'archive_status', archive_status," +
                                "'rec_status_code', rec_status_code," +
                                "'site_id', site_id" +
                                ")",
                        JSONB.class))
                .from(DSL.table(
                        "(select distinct on (study_id) * " +
                                "from drh_stateless_research_study.research_study_view " +
                                "where study_id = '" + studyId + "')")
                        .as("sub"));
        LOG.info("Get Research Study Query: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        LOG.info("Get Research Study Response: {}", jsonbResult);

        if (jsonbResult == null) {
            return CompletableFuture.completedFuture(JSONB.valueOf("{}")); // Return empty JSONB if the query result is
                                                                           // null
        }
        return CompletableFuture.completedFuture(jsonbResult);

    }

    @Transactional
    public JSONB getMyResearchStudies(String userId) {
        LOG.info("Fetching my studies for userId: {}", userId);
        final var query = dsl
                .select(DSL.field(
                        "drh_stateless_research_study.get_my_studies({0})",
                        String.class,
                        DSL.val(userId)));
        LOG.info("Get My Research Study Query: {}", query);
        JSONB result = query.fetchOneInto(JSONB.class);

        LOG.info("Get My Research Study Response: {}", result);

        if (result == null) {
            return JSONB.valueOf("{}"); // Return empty JSON if the query result is null
        }

        return result;

    }

    @Transactional
    public JSONB getAllResearchStudies(String userId) {
        LOG.info("Fetching all studies for userId: {}", userId);
        final var query = dsl.select(DSL.field(
                "drh_stateless_research_study.get_all_studies({0})",
                String.class,
                DSL.val(userId)));
        LOG.info("Get All Research Study Query: {}", query);
        JSONB result = query.fetchOneInto(JSONB.class);
        LOG.info("Get All Research Study Response: {}", result);

        if (result == null) {
            return JSONB.valueOf("{}"); // Return empty JSON if the query result is null
        }
        return result;
    }

    @Transactional
    public JSONB editResearchStudyArchiveStatus(String studyId, Boolean isArchived) {
        try {

            LOG.info("Updating archive status for studyId: {}", studyId);
            final var userId = userNameService.getUserId();
            final var partyId = partyService.getPartyIdByUserId(userId);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            if (!partyId.equalsIgnoreCase(getStudyCreatedBy(studyId))) {
                LOG.info("You cannot change archive status of a study that you are a part of.");
                LOG.info("Study Created By: {}", getStudyCreatedBy(studyId));
                interactionService.saveStudyInteraction(studyId,
                        hubInteractionId, StudyInteractionType.UPDATE,
                        ActionDescription.UPDATE_ARCHIVE_STATUS,
                        ResearchStudyState.ACTIVE,
                        ResearchStudyState.ACTIVE, null,
                        null, "Permission denied", 0, "Error", ActionType.UPDATE_STUDY,
                        FileProcessingStatus.FAILED);
                throw new RuntimeException("You don't have permission to change archieve status of this Study.");
            }
            String activityData = activityLogService.prepareActivityLogMetadata();
            LOG.info("Fetched practitioner party id: {}", partyId);
            final var editResearchStudyArchiveStatusQuery = dsl.select(DSL.field(
                    "drh_stateless_research_study.update_research_study_archive_status({0}, {1}, {2}, {3})",
                    String.class,
                    DSL.val(studyId),
                    DSL.val(isArchived),
                    DSL.val(partyId),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class)));
            LOG.info("Update Research Study Archive Status Query: {}", editResearchStudyArchiveStatusQuery);
            JSONB editResearchStudyArchiveStatusResponse = editResearchStudyArchiveStatusQuery
                    .fetchOneInto(JSONB.class);

            interactionService.saveStudyInteraction(studyId, hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_ARCHIVE_STATUS,
                    isArchived ? ResearchStudyState.ACTIVE : ResearchStudyState.ARCHIVED,
                    isArchived ? ResearchStudyState.ARCHIVED : ResearchStudyState.UN_ARCHIVED, null,
                    editResearchStudyArchiveStatusResponse.data(), null, 200, "Success",
                    ActionType.UPDATE_STUDY, FileProcessingStatus.SUCCESS);

            LOG.info("Edit Research Study Archive Status Response: {}", editResearchStudyArchiveStatusResponse);
            return editResearchStudyArchiveStatusResponse;
        } catch (Exception e) {
            LOG.error("Error while updating archive status for studyId: {}. Error: {}", studyId, e.getMessage(), e);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            interactionService.saveStudyInteraction(studyId,
                    hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_ARCHIVE_STATUS,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, null,
                    null, JsonUtils.toJson(e.getMessage()), 0, "Error", ActionType.UPDATE_STUDY,
                    FileProcessingStatus.FAILED);

            return null;
        }
    }

    @Transactional
    public JSONB getAllResearchStudyTeam(String studyId) {
        LOG.info("Fetching team for studyId: {}", studyId);
        final var query = dsl.select(DSL.field(

                "jsonb_agg(" +
                        "jsonb_build_object(" +
                        "'name', name, " +
                        "'role', role, " +
                        "'study_id', study_id " +
                        ")" +
                        ")",
                JSONB.class))
                .from("drh_stateless_research_study.existing_team_collabrators_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)));
        LOG.info("Fetching team for studyId: {} , Query: {}", studyId, query);

        JSONB jsonbResult = query
                .fetchOneInto(JSONB.class);

        LOG.info("All Research Study Team Response: {}", jsonbResult);

        if (jsonbResult == null) {
            return JSONB.valueOf("{}"); // Return empty JSON if the query result is null
        }

        return jsonbResult;
    }

    @Transactional
    public JSONB updatePublicationInline(String studyId, PublicationUpdateRequest request, String citationId) {
        LOG.info("Updating publication inline for studyId: {}, citationId: {}", studyId, citationId);
        try {
            ObjectNode data = objectMapper.createObjectNode();
            data.put("publication_title", request.publication_title());
            data.put("publication_date",
                    request.publication_date() != null ? request.publication_date().toString() : null);
            data.put("publication_doi", request.publication_doi());
            data.put("pubmed_id", request.pubmed_id());
            data.put("citation_data_source", request.citation_data_source().name());
            String jsonString = objectMapper.writeValueAsString(data);
            jsonString = filterAllowedKeys(jsonString, objectMapper);
            final var userId = userNameService.getUserId();
            String userPartyId = partyService.getPartyIdByUserId(userId);
            String activityData = activityLogService.prepareActivityLogMetadata();

            // Convert the JSON string into JSONB
            JSONB jsonb = JSONB.valueOf(jsonString);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.update_publication_inline({0}, {1}, {2}, {3}, {4})",
                    String.class,
                    DSL.val(studyId),
                    DSL.val(citationId),
                    DSL.val(jsonb),
                    DSL.val(userPartyId),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class)));
            LOG.info("Publication Inline Update Query : {} ", query);

            JSONB updatePublicationInlineResponse = query.fetchOneInto(JSONB.class);

            LOG.info("Publication Inline Update Response: {}", updatePublicationInlineResponse);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            interactionService.saveStudyInteraction(studyId, hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_STUDY_PUBLICATION,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                    updatePublicationInlineResponse.data(), null, 200, "Success",
                    ActionType.UPDATE_STUDY, FileProcessingStatus.SUCCESS);
            return updatePublicationInlineResponse;
        } catch (Exception e) {
            LOG.error("Error while converting to JSONB or executing the function: {}", e.getMessage(), e);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);
            interactionService.saveStudyInteraction(studyId,
                    hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_STUDY_PUBLICATION,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, JsonUtils.toJson(request),
                    null, JsonUtils.toJson(e.getMessage()), 0, "Error", ActionType.UPDATE_STUDY,
                    FileProcessingStatus.FAILED);

            throw new RuntimeException("Error while processing the updatePublicationInline function", e);
        }

    }

    public JSONB getAllResearchStudyPrincipalInvestigator(String studyId)
            throws InterruptedException, ExecutionException {
        String principalInvestigatorRoleIndex = "Primary-investigator";
        return getInvestigatorOrAuthor(studyId, principalInvestigatorRoleIndex);
    }

    @Transactional
    public JSONB updateVisibility(String researchStudyId, Integer studyVisibilityId) {
        try {
            LOG.info("Updating visibility for studyId: {}", researchStudyId);
            final var userId = userNameService.getUserId();

            String partyId = partyService.getPartyIdByUserId(userId);
            String activityData = activityLogService.prepareActivityLogMetadata();

            LOG.info("Party Id: {}", partyId);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.update_research_study_visibility({0}, {1}, {2}, {3})",
                    JSONB.class,
                    DSL.val(researchStudyId),
                    DSL.val(studyVisibilityId),
                    DSL.val(partyId),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class)));
            LOG.info("UpdateVisibility Query: {}", query);

            JSONB updateVisibilityResponse = query
                    .fetchOneInto(JSONB.class);

            LOG.info("Update Visibility Response: {}", updateVisibilityResponse);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(researchStudyId);
            interactionService.saveStudyInteraction(
                    researchStudyId, hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_STUDY_VISIBILITY + "to "
                            + masterService.getStudyVisibilityById(studyVisibilityId) + ".",
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, null,
                    updateVisibilityResponse.data(), null, 200, "Success",
                    ActionType.UPDATE_STUDY, FileProcessingStatus.SUCCESS);
            return updateVisibilityResponse;
        } catch (Exception e) {
            LOG.error("Error while updating visibility for studyId: {}. Error: {}", researchStudyId, e.getMessage(), e);
            String hubInteractionId = interactionService.getHubIntercationIdOfStudy(researchStudyId);
            interactionService.saveStudyInteraction(
                    researchStudyId,
                    hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.UPDATE_STUDY_VISIBILITY + "to "
                            + masterService.getStudyVisibilityById(studyVisibilityId)
                            + ".",
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, null,
                    null, JsonUtils.toJson(e.getMessage()), 0, "Error", ActionType.UPDATE_STUDY,
                    FileProcessingStatus.FAILED);

            throw new RuntimeException("Error while processing the updateVisibility function", e);
        }
    }

    public JSONB deleteResearchStudy(String studyId) {
        String hubInteractionId = interactionService.getHubIntercationIdOfStudy(studyId);

        LOG.info("Deleting study for studyId: {}", studyId);
        final var userId = userNameService.getUserId();
        final var partyId = partyService.getPartyIdByUserId(userId);
        if (!partyId.equalsIgnoreCase(getStudyCreatedBy(studyId))) {
            LOG.info("You cannot delete a study that you are a part of.");
            LOG.info("Study Created By: {}", getStudyCreatedBy(studyId));

            throw new RuntimeException("You don't have permission to delete this Study.");
        }
        try {
            final var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.delete_research_study({0}, {1})",
                    String.class,
                    DSL.val(studyId),
                    DSL.val(partyId)));
            LOG.info("Deleting study Query : {}", query);

            JSONB deleteResearchStudyResponse = query.fetchOneInto(JSONB.class);
            interactionService.saveStudyInteraction(
                    studyId, hubInteractionId,
                    StudyInteractionType.DELETE,
                    ActionDescription.DELETE_STUDY,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.DELETED, null,
                    deleteResearchStudyResponse.data(), null, 200, "Success",
                    ActionType.DELETE_STUDY, FileProcessingStatus.SUCCESS);
            LOG.info("Delete Research Study Response: {}", deleteResearchStudyResponse);

            return deleteResearchStudyResponse;
        } catch (Exception e) {
            LOG.error("Error while deleting study for studyId: {}. Error: {}", studyId, e.getMessage(), e);

            interactionService.saveStudyInteraction(
                    studyId,
                    hubInteractionId, StudyInteractionType.UPDATE,
                    ActionDescription.DELETE_STUDY,
                    ResearchStudyState.ACTIVE,
                    ResearchStudyState.ACTIVE, null,
                    null, JsonUtils.toJson(e.getMessage()), 0, "Error", ActionType.UPDATE_STUDY,
                    FileProcessingStatus.FAILED);

            throw new RuntimeException("Error while processing the deleteResearchStudy function", e);
        }
    }

    private String getStudyCreatedBy(String studyId) {
        LOG.info("Fetching createdBy for studyId: {}", studyId);
        final var query = dsl.selectDistinct(DSL.field(
                "created_by"))
                .from("drh_stateless_research_study.research_study_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)));
        String practitionerPartyId = query.fetchOneInto(String.class);

        LOG.info("practitionerPartyId: {}", practitionerPartyId);
        // Execute and fetch result
        return practitionerPartyId;
    }

    @Transactional
    public Boolean getResearchStudyArchiveStatus(String studyId) {
        LOG.info("Fetching archive status for studyId: {}", studyId);
        final var query = dsl.selectDistinct(DSL.field(
                "archive_status",
                Boolean.class))
                .from("drh_stateless_research_study.research_study_view")
                .where(DSL.field("study_id").eq(DSL.val(
                        studyId)));
        LOG.info("Get Research Study Archive Status Query: {}", query);

        Boolean isResearchStudyArchived = query
                .fetchOneInto(Boolean.class);

        boolean result = Boolean.TRUE.equals(isResearchStudyArchived);

        LOG.info("Is Research Study Archived: {}", result);
        return result;
    }

    @Transactional
    public String getStudyDisplayId(String studyId) {
        LOG.info("Fetching studyDisplayId for studyId: {}", studyId);
        final var query = dsl.selectDistinct(DSL.field(
                "study_display_id"))
                .from("drh_stateless_research_study.research_study_view")
                .where(DSL.field("study_id").eq(DSL.val(
                        studyId)));
        LOG.info("Get Study Display Id Query: {}", query);

        String studyDisplayId = query
                .fetchOneInto(String.class);

        LOG.info("Study Display Id: {}", studyDisplayId);
        return studyDisplayId;
    }

    @Transactional
    public boolean checkStudyDisplayIdExists(String studyDisplayId) {
        LOG.info("Checking if studyDisplayId exists: {}", studyDisplayId);
        final var query = dsl.selectCount()
                .from("drh_stateful_research_study.research_study")
                .where(
                        DSL.field("study_display_id").eq(studyDisplayId)
                                .and(DSL.field("deleted_at").isNull()));
        LOG.info("Check Study Display Id Exists Query: {}", query);

        boolean exists = query.fetchOne(0, Integer.class) > 0;

        LOG.info("Study exists: {}", exists);
        return exists;
    }

    @Async
    @Transactional
    public JSONB getInvestigatorOrAuthor(String studyId, String roleIndex)
            throws InterruptedException, ExecutionException {
        try {
            LOG.info("Fetching all studies for userId: {}", studyId);
            final var query = dsl.select(DSL.field(
                    "jsonb_agg(" +
                            "jsonb_build_object(" +
                            "'name', name, " +
                            "'role', role, " +
                            "'study_id', study_id " +
                            ")" +
                            ")",
                    JSONB.class))
                    .from("drh_stateless_research_study.existing_team_collabrators_view")
                    .where(DSL.field("study_id").eq(DSL.val(studyId))
                            .and(DSL.lower(DSL.field("role", String.class))
                                    .eq(DSL.lower(DSL.val(roleIndex)))));
            LOG.info("Get All Research Study Query: {}", query);

            JSONB jsonbResult = query
                    .fetchOneInto(JSONB.class);

            LOG.info("Investigator or Author: {}", jsonbResult);

            if (jsonbResult == null) {
                return CompletableFuture.completedFuture(JSONB.valueOf("{}")).get();
            }

            return CompletableFuture.completedFuture(jsonbResult != null ? jsonbResult : JSONB.valueOf("{}")).get();
        } catch (Exception e) {
            LOG.error("Error while fetching investigator or author for studyId: {}. Error: {}", studyId, e.getMessage(),
                    e);
            throw new RuntimeException("Error while fetching investigator or author for studyId: " + studyId, e);
        }
    }

    public JSONB getAllResearchStudyCoInvestigator(String studyId) throws InterruptedException, ExecutionException {
        String coInvestigatorRoleIndex = "Co-Investigator";
        return getInvestigatorOrAuthor(studyId, coInvestigatorRoleIndex);
    }

    public JSONB getAllResearchStudyPrincipalAuthor(String studyId) throws InterruptedException, ExecutionException {
        String principalAuthorRoleIndex = "Principal Author";
        return getInvestigatorOrAuthor(studyId, principalAuthorRoleIndex);
    }

    public JSONB getAllResearchStudyCoAuthor(String studyId) throws InterruptedException, ExecutionException {
        String coAuthorRoleIndex = "Co-Author";
        return getInvestigatorOrAuthor(studyId, coAuthorRoleIndex);

    }

    public JSONB getStudyTeamMembers(String studyId) throws InterruptedException, ExecutionException {
        String studyTeamRoleIndex = "Study Team Member";
        return getInvestigatorOrAuthor(studyId, studyTeamRoleIndex);

    }

    public JSONB getNominatedPrincipalInvestigator(String studyId) throws InterruptedException, ExecutionException {
        String role = "Nominated-Principal Investigator";
        return getInvestigatorOrAuthor(studyId, role);
    }

    @Transactional
    public String getParticipantDisplayId(String participantId) {
        LOG.info("Fetching participant display id for participantId: {}", participantId);
        final var query = dsl.selectDistinct(DSL.field(
                "participant_display_id"))
                .from("drh_stateless_research_study.participant_data_view")
                .where(DSL.field("participant_id").eq(DSL.val(
                        participantId)));
        LOG.info("Get Participant Display Id Query: {}", query);

        final var participantDisplayId = query.fetchOneInto(String.class);

        LOG.info("Participant display id: {}", participantDisplayId);
        return participantDisplayId;
    }

    @Transactional
    public String getStudyOwner(String studyId) {
        LOG.info("Fetching study owner for studyId: {}", studyId);
        final var query = dsl.selectDistinct(DSL.field(
                "created_by",
                String.class))
                .from("drh_stateless_research_study.research_study_view")
                .where(DSL.field("study_id").eq(DSL.val(
                        studyId)));
        LOG.info("Get Research Study owner Query: {}", query);
        String owner = query.fetchOneInto(String.class);

        if (owner != null) {
            LOG.info("Owner of studyId {}: {}", studyId, owner);
        } else {
            LOG.warn("No owner found for studyId: {}", studyId);
        }
        return owner;
    }

    private String filterAllowedKeys(String jsonString, ObjectMapper objectMapper) {
        try {
            ObjectNode node = (ObjectNode) objectMapper.readTree(jsonString);
            Set<String> allowedKeys = Set.of("publication_title", "publication_date", "publication_doi", "pubmed_id",
                    "citation_data_source");
            node.fieldNames().forEachRemaining(key -> {
                if (!allowedKeys.contains(key)) {
                    node.remove(key);
                }
            });
            return objectMapper.writeValueAsString(node);
        } catch (Exception e) {
            LOG.error("Error while filtering allowed keys in JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to filter allowed keys from JSON", e);
        }
    }

    @Transactional
    @Async
    public CompletableFuture<JSONB> getStudyCitations(String studyId) throws InterruptedException, ExecutionException {
        LOG.info("Fetching citations for studyId: {}", studyId);
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'study_id', study_id, " +
                                "'citation_id', citation_id, " +
                                "'publication_title', publication_title, " +
                                "'publication_date', to_char(publication_date, 'MM-DD-YYYY')," +
                                "'publication_doi', publication_doi," +
                                "'pubmed_id', pubmed_id," +
                                "'citation_authors', citation_authors," +
                                "'created_at', created_at," +
                                "'citation_data_source', citation_data_source" +
                                ")" +
                                " ORDER BY created_at DESC" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.research_study_citation_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)));
        LOG.info("Get Research Study Citation Query: {}", query);

        JSONB jsonbResults = query.fetchOne(0, JSONB.class);

        LOG.info("Get Research Study Citation Response: {}", jsonbResults);

        if (jsonbResults == null || jsonbResults.data().isEmpty()) {
            return CompletableFuture.completedFuture(JSONB.jsonb("[]"));
        }
        try {

            return CompletableFuture
                    .completedFuture(jsonbResults);
        } catch (Exception e) {
            LOG.error("Error while processing JSON", e);
            return CompletableFuture.completedFuture(JSONB.jsonb("[]"));
        }
    }

    public Map<String, Object> saveStudyCitation(PublicationUpdateRequest request, String studyId) {
        try {
            final var userId = userNameService.getUserId();
            String userPartyId = partyService.getPartyIdByUserId(userId);
            String activityData = activityLogService.prepareActivityLogMetadata();
            var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.save_study_citation({0}, {1}, cast({2} as date), {3}, {4} ,{5}, {6}, {7})",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(request.publication_title()),
                    DSL.val(request.publication_date()),
                    DSL.val(request.publication_doi()),
                    DSL.val(userPartyId),
                    DSL.val(request.pubmed_id()),
                    DSL.val(request.citation_data_source().name()), DSL.cast(DSL.val(activityData), JSONB.class)));

            LOG.info("Executing SQL Query: {}", query);
            JSONB result = query.fetchOneInto(JSONB.class);
            LOG.info("Study citation saved successfully for studyId: {}", studyId);
            if (result != null) {
                return new ObjectMapper().readValue(result.data(), new TypeReference<Map<String, Object>>() {
                });
            } else {
                return Map.of("status", "error", "message", "No data returned");
            }
        } catch (Exception e) {
            LOG.error("Error saving study citation: {}", e.getMessage(), e);
            return Map.of("status", "error", "message", "Failed to save study citation");
        }
    }

    public Object saveAuthors(String studyId, String[] authorNames, String citationId) {
        try {
            String activityData = activityLogService.prepareActivityLogMetadata();
            var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.save_citation_authors({0}, {1}, {2}, {3})",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(authorNames, String[].class),
                    DSL.val(citationId),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class)));

            LOG.info("Executing SQL Query to save authors: {}", query);
            JSONB result = query.fetchOneInto(JSONB.class);
            LOG.info("Citation authors saved successfully for citationId: {}", citationId);
            return result != null ? result.data() : "{}";

        } catch (Exception e) {
            LOG.error("Error saving citation authors: {}", e.getMessage(), e);
            return JSONB.jsonb("{\"status\": \"error\", \"message\": \"Failed to save citation authors\"}");
        }
    }

    public Boolean isDuplicatePubMedOrDOI(String studyId, String publicationDoi, String pubmedId, String citationId) {
        var query = dsl
                .selectOne()
                .from("drh_stateless_research_study.research_study_citation_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)));
        if (citationId != null) {
            query = query.and(DSL.field("citation_id").notEqual(DSL.val(citationId)));
        }
        if (StringUtils.hasText(publicationDoi) && StringUtils.hasText(pubmedId)) {
            query = query.and(
                    DSL.field("pubmed_id").eq(DSL.val(pubmedId))
                            .or(DSL.field("publication_doi").eq(DSL.val(publicationDoi))));
        } else if (StringUtils.hasText(pubmedId)) {
            query = query.and(DSL.field("pubmed_id").eq(DSL.val(pubmedId)));
        } else if (StringUtils.hasText(publicationDoi)) {
            query = query.and(DSL.field("publication_doi").eq(DSL.val(publicationDoi)));

        } else {
            return false;
        }
        LOG.info("Check for duplicate PubMed or DOI Query: {}", query);
        final boolean exists = dsl
                .fetchExists(query);
        LOG.info("Duplicate PubMed or DOI exists: {}", exists);
        return exists;

    }

    public Map<String, Object> updateStudyCitation(String citationId, PublicationUpdateRequest request,
            String studyId) {
        try {
            final var userId = userNameService.getUserId();
            String userPartyId = partyService.getPartyIdByUserId(userId);

            var query = dsl.select(DSL.field(
                    "drh_stateless_research_study.update_study_citation({0}, {1}, {2}, cast({3} as date), {4}, {5} ,{6})",
                    JSONB.class,
                    DSL.val(studyId),
                    DSL.val(citationId),
                    DSL.val(request.publication_title()),
                    DSL.val(request.publication_date()),
                    DSL.val(request.publication_doi()),
                    DSL.val(userPartyId),
                    DSL.val(request.pubmed_id())));

            LOG.info("Executing SQL Query: {}", query);
            JSONB result = query.fetchOneInto(JSONB.class);
            LOG.info("Study citation saved successfully for studyId: {}", studyId);
            if (result != null) {
                return new ObjectMapper().readValue(result.data(), new TypeReference<Map<String, Object>>() {
                });
            } else {
                return Map.of("status", "error", "message", "No data returned");
            }
        } catch (Exception e) {
            LOG.error("Error saving study citation: {}", e.getMessage(), e);
            return Map.of("status", "error", "message", "Failed to save study citation");
        }
    }

}
