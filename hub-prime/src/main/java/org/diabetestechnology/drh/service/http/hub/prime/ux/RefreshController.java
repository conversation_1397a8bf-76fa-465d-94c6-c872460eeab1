package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
@Hidden
@Tag(name = "DRH Refresh Server API")
public class RefreshController {
    @Autowired
    DataBaseAttachService databaseAttachService;

    @Autowired
    DataAccessService dataAccessService;

    @Hidden
    @GetMapping("/refresh")
    @Operation(summary = "Refresh Activity Server")
    @ResponseBody
    public String refreshActivityAndClentServerDb() {
        databaseAttachService.refreshActivityDb();
        databaseAttachService.refreshClientServerDb();
        return "Success";
    }

    @Hidden
    @GetMapping("/refresh/{dbName}")
    @Operation(summary = "Refresh Study Server")
    @ResponseBody
    public String refreshStudyDb(@PathVariable String dbName) {
        dataAccessService.refreshDatabase(dbName.toLowerCase());
        return "Success";
    }

}
