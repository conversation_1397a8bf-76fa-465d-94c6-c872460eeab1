package org.diabetestechnology.drh.service.http.hub.prime.service.request;

import java.util.Collections;
import java.util.List;

// @formatter:off
public record CohortRequest(
        List<String> studyIds,
        List<String> filters) {
    public CohortRequest {
        studyIds = (studyIds == null) ? Collections.emptyList() : studyIds;
        filters = (filters == null) ? Collections.emptyList() : filters;
    }
}
// @formatter:on
