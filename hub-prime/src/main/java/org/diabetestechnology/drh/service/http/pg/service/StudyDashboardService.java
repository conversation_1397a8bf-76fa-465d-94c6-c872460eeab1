package org.diabetestechnology.drh.service.http.pg.service;

import java.text.DecimalFormat;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class StudyDashboardService {
    private static final Logger LOG = LoggerFactory.getLogger(StudyDashboardService.class);
    private static final String TABLE_NAME = "drh_stateless_research_study.dashboard_metrics_view";
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("0.00");

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;

    public StudyDashboardService(@Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, PartyService partyService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
    }

    @Transactional
    public Integer getTotalParticipants() {
        try {
            LOG.info("Fetching total participants ");
            final var userId = userNameService.getUserId();
            final var createdBy = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);

            var query = dsl
                    .select(DSL.coalesce(DSL.sum(DSL.field("total_participants", Integer.class)), 0))
                    .from(DSL.table(TABLE_NAME))
                    .where(getVisibilityCondition(createdBy, organizationPartyId));

            LOG.info("Executing Query for Fetching total participants {}:", query);

            return Optional.ofNullable(query.fetchOneInto(Integer.class)).orElse(0);
        } catch (Exception e) {
            LOG.error("Error executing query for {}: {}", "total_participants", e.getMessage(), e);
            return 0;
        }
    }

    @Transactional
    public String getFemalePercentage() {
        try {
            LOG.info("Fetching female percentage ");
            final var userId = userNameService.getUserId();
            final var createdBy = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);

            var query = dsl
                    .select(DSL.round(
                            DSL.sum(DSL.field("total_female_percentage", Double.class)
                                    .multiply(DSL.field("total_participants", Double.class)))
                                    .divide(DSL.nullif(DSL.sum(DSL.field("total_participants", Double.class)),
                                            DSL.inline(0.0)).cast(SQLDataType.NUMERIC)),
                            2) // Rounds to 2 decimal places
                            .as("total_female_percentage"))
                    .from(DSL.table(TABLE_NAME))
                    .where(getVisibilityCondition(createdBy, organizationPartyId));

            LOG.info("Executing Query for Fetching female percentage: {}", query);

            Double femalePercentage = query.fetchOneInto(Double.class);
            return DECIMAL_FORMAT.format(Optional.ofNullable(femalePercentage).orElse(0.0)) + "%";
        } catch (Exception e) {
            LOG.error("Error executing query for {}: {}", "female_percentage",
                    e.getMessage(), e);
            return "0.00%";
        }
    }

    @Transactional
    public String getAverageAgeForDashboard() {
        try {
            LOG.info("Fetching average age for dashboard");

            final var userId = userNameService.getUserId();
            final var createdBy = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            var query = dsl
                    .select(
                            DSL.coalesce(DSL.sum(DSL.field("average_age", Double.class)), 0.0),
                            DSL.count(DSL.field("average_age")))
                    .from(DSL.table(TABLE_NAME))
                    .where(getVisibilityCondition(createdBy, organizationPartyId));

            LOG.info("Executing Query: {}", query);
            var result = query.fetchOne();

            if (result == null) {
                return formatWithYears(0.0);
            }
            double totalSum = result.get(0, Double.class);
            int totalCount = result.get(1, Integer.class);
            double finalAverage = (totalCount > 0) ? (totalSum / totalCount) : 0.0;

            return formatWithYears(finalAverage);
        } catch (Exception e) {
            LOG.error("Error computing average age: {}", e.getMessage(), e);
            return "0.0";
        }
    }

    private org.jooq.Condition getVisibilityCondition(String createdBy, String organizationPartyId) {
        return DSL.or(
                DSL.field("visibility_name", String.class).eq("Public"),
                DSL.field("organization_party_id", String.class).eq(organizationPartyId),
                DSL.field("visibility_name", String.class).eq("Private")
                        .and(DSL.field("created_by", String.class).eq(createdBy)))
                .and(DSL.field("deleted_at").isNull())
                .and(DSL.field("rec_status_id", Integer.class).eq(1))
                .and(
                        DSL.field("visibility_name", String.class).ne("Private")
                                .or(DSL.field("created_by", String.class).eq(createdBy)));
    }

    private String formatWithYears(double value) {
        DecimalFormat decimalFormat = new DecimalFormat(value % 1 == 0 ? "0" : "0.0");
        return decimalFormat.format(value) + " Years";
    }

}
