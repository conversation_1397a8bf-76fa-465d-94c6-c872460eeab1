package org.diabetestechnology.drh.service.http.hub.prime;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.Instant;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;

public class LoginLogger {
    private static final Logger LOG = LoggerFactory.getLogger(LoginLogger.class);
    private static final ObjectMapper mapper = new ObjectMapper();

    public static void logSuccess(String username) {
        Map<String, Object> loginEvent = Map.of(
                "event", "login_success",
                "username", username,
                "timestamp", Instant.now().toString());
        try {
            LOG.info(mapper.writeValueAsString(loginEvent));
        } catch (Exception e) {
            LOG.error("Failed to log login event", e);
        }
    }

    public static void anonymousLogin(String username) {
        Map<String, Object> loginEvent = Map.of(
                "event", "skip_login",
                "username", username,
                "timestamp", Instant.now().toString());
        try {
            LOG.info(mapper.writeValueAsString(loginEvent));
        } catch (Exception e) {
            LOG.error("Failed to log login event", e);
        }
    }

    public static void practitionerLogin(String username) {
        LOG.info("Practitioner login detected for user: {}", username);
        Map<String, Object> loginEvent = Map.of(
                "event", "drh_user_login",
                "username", username,
                "timestamp", Instant.now().toString());
        try {
            LOG.info(mapper.writeValueAsString(loginEvent));
        } catch (Exception e) {
            LOG.error("Failed to log login event", e);
        }
    }
}