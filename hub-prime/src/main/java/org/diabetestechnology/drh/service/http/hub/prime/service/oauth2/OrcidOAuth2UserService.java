package org.diabetestechnology.drh.service.http.hub.prime.service.oauth2;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.OauthUsersService;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class OrcidOAuth2UserService extends DefaultOAuth2UserService {
    private static final Logger LOG = LoggerFactory.getLogger(OrcidOAuth2UserService.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private OauthUsersService oauthUsersService;
    @Autowired
    private @Qualifier("secondaryDsl") DSLContext dsl;

    public OrcidOAuth2UserService() {
    }

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        LOG.info("ORCID UserRequest: {} {}", userRequest.getAdditionalParameters().get("orcid"),
                userRequest.getAdditionalParameters().get("name"));

        // Extract access token from the user request
        final var accessToken = userRequest.getAccessToken().getTokenValue();
        LOG.info("ORCID AccessToken: {}", accessToken);
        final var orcid = userRequest.getAdditionalParameters().get("orcid").toString();
        final var name = userRequest.getAdditionalParameters().get("name").toString();
        Boolean exists = dsl
                .select(DSL.value(true))
                .from("drh_stateless_authentication.user_profile_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(orcid)))
                .limit(1) // To ensure we only check if at least one row exists
                .fetchOptional() // Returns Optional<Boolean>
                .isPresent(); // Check if the result is present

        LOG.info("ORCID UserRequest: {} ", userRequest.getAdditionalParameters().values());
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("orcid", orcid);
        if (exists) {
            final var userName = dsl.select(DSL.field("first_name"))
                    .from(DSL.table("drh_stateless_authentication.user_profile_view"))
                    .where(DSL.field("provider_user_id").eq(orcid))
                    .fetchOneInto(String.class);
            attributes.put("name", userName);
            attributes.put("userFullName", userName);
            LOG.info("User Name is : {}", userName);

        } else {
            attributes.put("name", name);
            attributes.put("userFullName", name);

        }
        attributes.put("login", orcid);
        attributes.put("avatar_url", "/user.jpg");
        attributes.put("provider", "ORCID");
        attributes.put("hasAdminMenu", oauthUsersService.hasAdminMenuDB(attributes.get("login").toString(), "ORCiD"));
        String profileUrl = "https://orcid.org/my-orcid?orcid=" + orcid;
        attributes.put("html_url", profileUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);

        LOG.info("orcid : {}", orcid);
        // Create authorities (if needed)
        var authorities = Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        // Fetch user details from ORCID
        RestTemplate restTemplate = new RestTemplate();
        String url = "https://pub.orcid.org/v3.0/" + orcid;
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET,
                new HttpEntity<>(createHeaders(accessToken)), String.class);

        try {
            String responseBody = response.getBody();
            LOG.info("ORCID UserResponse: {}", responseBody);
            JsonNode rootNode = objectMapper.readTree(responseBody);

            // Extract email and institution
            String email = extractEmailFromJson(rootNode);
            String institution = extractInstitutionFromJson(rootNode);

            // Add the extracted attributes to the attributes map
            attributes.put("email", email);
            attributes.put("institution", institution);

        } catch (Exception e) {
            LOG.error("Error while fetching ORCID user details", e);
        }

        // Return the DefaultOAuth2User with the correct attributes
        return new DefaultOAuth2User(
                authorities,
                attributes,
                "orcid");

    }

    public static String extractOrcidFromJson(String json) {
        try {
            JsonNode rootNode = objectMapper.readTree(json);
            JsonNode orcidNode = rootNode.path("orcid-identifier").path("uri");
            if (orcidNode.isTextual()) {
                String orcidUri = orcidNode.asText();
                // Extract the ORCID from the URI
                return orcidUri.substring(orcidUri.lastIndexOf('/') + 1);
            }
        } catch (Exception e) {
            e.printStackTrace(); // Handle the exception properly
        }
        return null;
    }

    public static Map<String, Object> extractDataFromJson(String json) {
        Map<String, Object> attributes = new HashMap<>();
        try {
            JsonNode rootNode = objectMapper.readTree(json);

            // Extract the ORCID identifier
            JsonNode orcidNode = rootNode.path("orcid-identifier").path("uri");
            if (orcidNode.isTextual()) {
                final var orcidUri = orcidNode.asText();
                final var orcid = orcidUri.substring(orcidUri.lastIndexOf('/') + 1);
                attributes.put("orcid", orcid);
            }

            // Extract names
            JsonNode personNode = rootNode.path("person");
            JsonNode nameNode = personNode.path("name");
            JsonNode givenNamesNode = nameNode.path("given-names").path("value");
            JsonNode familyNameNode = nameNode.path("family-name").path("value");

            if (givenNamesNode.isTextual()) {
                attributes.put("name", givenNamesNode.asText() + " " + familyNameNode.asText());
            }
            if (familyNameNode.isTextual()) {
                attributes.put("family_name", familyNameNode.asText());
            }

        } catch (Exception e) {
            e.printStackTrace(); // Handle the exception properly
        }
        return attributes;
    }

    private String extractEmailFromJson(JsonNode rootNode) {
        JsonNode emailNode = rootNode.path("person").path("emails").path("email").get(0).path("email");
        return emailNode.isTextual() ? emailNode.asText() : null;
    }

    private String extractInstitutionFromJson(JsonNode rootNode) {
        JsonNode affiliationsNode = rootNode.path("affiliations");
        if (affiliationsNode.isArray() && affiliationsNode.size() > 0) {
            JsonNode affiliationNode = affiliationsNode.get(0).path("organization");
            JsonNode nameNode = affiliationNode.path("name");
            return nameNode.isTextual() ? nameNode.asText() : null;
        }
        return null;
    }

    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("Accept", "application/json");
        return headers;
    }
}
