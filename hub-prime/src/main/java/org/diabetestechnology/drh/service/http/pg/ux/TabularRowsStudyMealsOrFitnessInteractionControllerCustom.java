package org.diabetestechnology.drh.service.http.pg.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.filter.CustomTabularFilter;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jetbrains.annotations.NotNull;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.JooqRowsSupplier.TypableTable;

import lib.aide.tabular.TabularRowsRequest;
import io.swagger.v3.oas.annotations.tags.Tag;

import static org.jooq.impl.DSL.*;
import org.jooq.*;

@Controller
@Tag(name = "DRH Hub Custom Tabular CGM File Interaction API Endpoints for AG Grid")
@org.springframework.context.annotation.Configuration
public class TabularRowsStudyMealsOrFitnessInteractionControllerCustom {
    static private final Logger LOG = LoggerFactory.getLogger(
            TabularRowsStudyCGMInteractionControllerCustom.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final CustomTabularFilter customTabularFilter;

    @Bean
    public com.fasterxml.jackson.databind.Module jsonbModule() {
        com.fasterxml.jackson.databind.module.SimpleModule module = new SimpleModule();
        module.addSerializer(org.jooq.JSONB.class, new JsonSerializer<org.jooq.JSONB>() {
            @Override
            public void serialize(org.jooq.JSONB value, JsonGenerator gen, SerializerProvider serializers)
                    throws IOException {
                gen.writeRawValue(value.data()); // or gen.writeString(value.data());
            }
        });
        return module;
    }

    public TabularRowsStudyMealsOrFitnessInteractionControllerCustom(final UdiSecondaryDbConfig udiPrimeDbConfig,
            UserNameService userNameService, CustomTabularFilter customTabularFilter,
            PartyService partyService) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.customTabularFilter = customTabularFilter;
    }

    private @NotNull SortField<Object> createSortCondition(
            List<lib.aide.tabular.TabularRowsRequest.SortModel> sortModel,
            TypableTable participantDashboardTable) {
        for (final var sort : sortModel) {
            final var sortField = participantDashboardTable.column(sort.colId());
            if ((sort.sort()).equals("asc")) {
                return field(sortField).asc();
            } else if ((sort.sort()).equals("desc")) {
                return field(sortField).desc();
            } else {
                LOG.info("Not a Valid Sort Field");
            }
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Successful Meals Or Fitness File Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/participant/{fileType}/file/interaction/success/parent/drh_stateless_activity_audit/file_interaction_view.json")
    @ResponseBody
    public Object parentSuccessMealsOrFitnessFileInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String fileType,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
            throws SQLException {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";

        try {

            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();
            var p = typableTable.table().as("p");
            var c = typableTable.table().as("c"); // For child interactions
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;

            Field<String> parentFileInteractionId = DSL.field("p.file_interaction_id", String.class);
            final var userId = userNameService.getUserId();
            final var organizationId = partyService.getOrganizationPartyIdByUser(userId);
            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .selectDistinct(DSL.field("p.study_id"),
                            DSL.field("p.study_display_id"),
                            DSL.field("p.study_title"),
                            DSL.field("p.file_category"),
                            DSL.field("p.organization_name"),
                            DSL.field(
                                    "p.organization_party_id")) // Selecting the specified fields
                    .from(p)

                    .where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and((DSL.field("p.interaction_hierarchy")
                    .eq(DSL.value("null")))
                    .or(DSL.field("p.interaction_hierarchy").isNull()))
                    .and(DSL.field("p.study_id").isNotNull())
                    .and(DSL.field("p.study_display_id").isNotNull())
                    .and(DSL.field("p.interaction_status").notEqual(ActionStatus.FAILED))
                    .and(DSL.field("p.organization_party_id").eq(DSL.value(organizationId)))
                    .and((DSL.field("p.file_category").equalIgnoreCase(DSL.value(FileType.MEALS)))
                            .or((DSL.field("p.file_category").equalIgnoreCase(DSL.value(
                                    FileType.FITNESS)))))
                    .andNotExists(
                            DSL.selectOne()
                                    .from(c)
                                    .where(
                                            DSL.field(
                                                    "trim(both '\"' from jsonb_path_query_first(c.interaction_hierarchy::jsonb, '$[0]')::text)",
                                                    String.class)
                                                    .eq(parentFileInteractionId)
                                                    .and(DSL.field("c.interaction_status").eq(ActionStatus.FAILED))));
            ;
            bindValues.add("null");
            bindValues.add(ActionStatus.FAILED);
            bindValues.add(organizationId);
            bindValues.add(FileType.MEALS);
            bindValues.add(FileType.FITNESS);
            bindValues.add(ActionStatus.FAILED);
            if (!userNameService.isAdmin()) {
                final var currentUserId = userNameService.getCurrentuserPartyId();
                query = query.and(DSL.field("created_by").eq(DSL.value(currentUserId)));
                bindValues.add(currentUserId);

            }
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@org.jetbrains.annotations.NotNull SelectConditionStep<Record6<Object, Object, Object, Object, Object, Object>>) query
                        .orderBy(
                                customTabularFilter.createSortCondition(payload.sortModel(),
                                        typableTable));
            }

            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Successful Meals Or Fitness File Interaction against a specific participant")
    @GetMapping("/api/ux/tabular/jooq/participant/{fileType}/file/interaction/success/sub/drh_stateless_activity_audit/file_interaction_view/participant_id/{participantId}.json")
    @ResponseBody
    public Object subSuccessMealsOrFitnessFileInteraction(
            final @PathVariable String fileType,
            final @PathVariable String participantId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {

            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            var p = typableTable.table().as("p");
            var c = typableTable.table().as("c"); // For child interactions

            Field<String> parentFileInteractionId = DSL.field("p.file_interaction_id", String.class);
            final var query = udiPrimeDbConfig.dsl().selectFrom(p)
                    .where((DSL.field("p.participant_id").eq(DSL.value(
                            participantId)))
                            .and((DSL.field("p.interaction_hierarchy")
                                    .eq(DSL.val("null"))))
                            .and(DSL.field("p.file_category")
                                    .eq(DSL.value(DSL.value(fileType.equalsIgnoreCase("meals") ? FileType.MEALS
                                            : FileType.FITNESS)))))
                    .andNotExists(
                            DSL.selectOne()
                                    .from(c)
                                    .where(
                                            DSL.field(
                                                    "trim(both '\"' from jsonb_path_query_first(c.interaction_hierarchy::jsonb, '$[0]')::text)",
                                                    String.class)
                                                    .eq(parentFileInteractionId)
                                                    .and(DSL.field("c.interaction_status").eq(ActionStatus.FAILED))));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
            final var response = query.fetch().intoMaps();

            return Map.of("rows", response);
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Details of a successful Melas Or Fitness File Interaction")
    @GetMapping("/api/ux/tabular/jooq/participant/meals-or-fitness/file/interaction/success/child/drh_stateless_activity_audit/file_interaction_view/file_interaction_id/{fileInteractionId}.json")
    @ResponseBody
    public Object childSuccessMealsOrFitnessFileInteraction(
            final @PathVariable String fileInteractionId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {

            // Fetch the result using the dynamically determined table and column; if
            // jOOQ-generated types were found, automatic column value mapping will occur
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                    .where((typableTable.column("file_interaction_id").eq(DSL.value(
                            fileInteractionId)))
                            .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                    .eq(DSL.val("\"" + fileInteractionId + "\"")))))
                    .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.SUCCESS)));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
            final var response = query.fetch().intoMaps();

            return Map.of("rows", response);
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Details of a specific Meals Or Fitness File Interaction")
    @GetMapping("/api/ux/tabular/jooq/participant/meals-or-fitness/file/interaction/child/modal/drh_stateless_activity_audit/file_interaction_view/file_interaction_id/{fileInteractionId}.json")
    @ResponseBody
    public Object childParticipantMealsOrFitnessFileInteractionModal(
            final @PathVariable String fileInteractionId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            // Fetch the result using the dynamically determined table and column; if
            // jOOQ-generated types were found, automatic column value mapping will occur
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                    .where(typableTable.column("file_interaction_id").eq(DSL.value(
                            fileInteractionId)));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            final var response = query.fetch().intoMaps();

            return response;
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Failed Meals or Fitness Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/participant/{fileType}/file/interaction/failed/parent/drh_stateless_activity_audit/file_interaction_view.json")
    @ResponseBody
    public Object parentFailedMealsOrFitnessFileInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String fileType,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            TypableTable typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();

            final var userId = userNameService.getUserId();
            final var organizationId = partyService.getOrganizationPartyIdByUser(userId);
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            var query = udiPrimeDbConfig.dsl()
                    .selectDistinct(DSL.field("study_id"),
                            DSL.field("study_display_id"),
                            DSL.field("study_title"),
                            DSL.field("file_category"),
                            DSL.field("organization_name"),
                            DSL.field(
                                    "organization_party_id")) // Selecting the specified fields

                    .from(typableTable.table()).where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and((DSL.field("study_id")
                    .isNotNull()))
                    .and(DSL.field("study_display_id").isNotNull())
                    .and(DSL.field("interaction_status").eq(ActionStatus.FAILED))
                    .and(DSL.field("organization_party_id").eq(DSL.value(organizationId)))
                    .and((DSL.field("file_category").eq(DSL.value(FileType.MEALS)))
                            .or(DSL.field("file_category").eq(DSL.value(FileType.FITNESS))));
            bindValues.add(ActionStatus.FAILED);
            bindValues.add(organizationId);
            bindValues.add(FileType.MEALS);
            bindValues.add(FileType.FITNESS);
            if (!userNameService.isAdmin()) {
                final var currentUserId = userNameService.getCurrentuserPartyId();
                query = query.and(DSL.field("created_by").eq(DSL.value(currentUserId)));
                bindValues.add(currentUserId);

            }
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@org.jetbrains.annotations.NotNull SelectConditionStep<Record6<Object, Object, Object, Object, Object, Object>>) query
                        .orderBy(
                                customTabularFilter.createSortCondition(payload.sortModel(),
                                        typableTable));
            }
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Failed Meals Or Fitness File Interaction against a specific Meals Or Fitness file")
    @GetMapping("/api/ux/tabular/jooq/participant/meals-or-fitness/file/interaction/failed/child/drh_stateless_activity_audit/file_interaction_view/file_interaction_id/{fileInteractionId}.json")
    @ResponseBody
    public Object childFailedMealsOrFitnessInteraction(
            final @PathVariable String fileInteractionId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            // Fetch the result using the dynamically determined table and column; if
            // jOOQ-generated types were found, automatic column value mapping will occur
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                    .where((typableTable.column("file_interaction_id").eq(DSL.value(
                            fileInteractionId)))
                            .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                    .eq(DSL.val("\"" + fileInteractionId + "\"")))))
                    .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.FAILED)));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            final var response = query.fetch().intoMaps();

            return Map.of("rows", response);
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "List of Failed Meals Or Fitness File Interaction against a specific participant")
    @GetMapping("/api/ux/tabular/jooq/participant/{fileType}/file/interaction/failed/sub/drh_stateless_activity_audit/file_interaction_view/participant_id/{participantId}.json")
    @ResponseBody
    public Object subFailedParticipantMealsOrFitnessFileInteraction(

            final @PathVariable String fileType,
            final @PathVariable String participantId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                    .where((typableTable.column("participant_id").eq(DSL.value(
                            participantId)))
                            .and(DSL.field("file_category")
                                    .eq(DSL.value(fileType.equalsIgnoreCase("meals") ? FileType.MEALS
                                            : FileType.FITNESS)))
                            .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.FAILED))));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            final var response = query.fetch().intoMaps();

            return Map.of("rows", response);
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Study details of a failed Meals Or Fitness File Interaction")
    @GetMapping("/api/ux/tabular/jooq/participant/{fileType}/file/interaction/success/first/child/drh_stateless_activity_audit/file_interaction_view/study_id/{studyId}.json")
    @ResponseBody
    public Object firstChildSuccessMealsOrFitnessParticipants(

            final @PathVariable String fileType,
            final @PathVariable String studyId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            var p = typableTable.table().as("p");
            var c = typableTable.table().as("c");
            Field<String> parentFileInteractionId = DSL.field("p.file_interaction_id", String.class);
            final var query = udiPrimeDbConfig.dsl().selectDistinct(
                    DSL.field("p.participant_id").as(
                            "participant_id"),
                    DSL.field("p.participant_display_id").as(
                            "participant_display_id"),
                    DSL.field("p.file_category").as("file_category"))
                    .from(p)
                    .where((DSL
                            .field("p.study_id").eq(DSL.value(
                                    studyId)))
                            .and(DSL.field("p.file_category")
                                    .eq(DSL.value(fileType.equalsIgnoreCase("meals") ? FileType.MEALS
                                            : FileType.FITNESS)))
                            .and(DSL.field("p.interaction_status").eq(DSL.val(ActionStatus.SUCCESS))))
                    .and(DSL.field("interaction_hierarchy")
                            .eq(DSL.value("null")))
                    .or(DSL.field("interaction_hierarchy").isNull())
                    .andNotExists(
                            DSL.selectOne()
                                    .from(c)
                                    .where(
                                            DSL.field(
                                                    "trim(both '\"' from jsonb_path_query_first(c.interaction_hierarchy::jsonb, '$[0]')::text)",
                                                    String.class)
                                                    .eq(parentFileInteractionId)
                                                    .and(DSL.field("c.interaction_status").eq(ActionStatus.FAILED))));
            LOG.info("Get CGM Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get CGM Interaction Details Corresponds to a Query {}:", query);
            LOG.info("Get CGM Interaction Details Corresponds to a Query {}:", query.getSQL());

            var response = query.fetch().intoMaps();
            return Map.of("rows", response);
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "List of Participants of failed Meals Or Fitness File Interaction")
    @GetMapping("/api/ux/tabular/jooq/participant/{fileType}/file/interaction/failed/first/child/drh_stateless_activity_audit/file_interaction_view/study_id/{studyId}.json")
    @ResponseBody
    public Object firstChildFailedMealsOrFitnessParticipants(

            final @PathVariable String fileType,
            final @PathVariable String studyId) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "file_interaction_view";
        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                    masterTableNameOrViewName);
            final var query = udiPrimeDbConfig.dsl().selectDistinct(
                    DSL.field("participant_id"),
                    DSL.field("participant_display_id"),
                    DSL.field("file_category"))
                    .from(typableTable.table())
                    .where((typableTable.column("study_id").eq(DSL.value(
                            studyId)))
                            .and(DSL.field("file_category")
                                    .eq(DSL.value(fileType.equalsIgnoreCase("meals") ? FileType.MEALS
                                            : FileType.FITNESS)))
                            .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.FAILED))));
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            var response = query.fetch().intoMaps();
            return Map.of("rows", response);
        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

}
