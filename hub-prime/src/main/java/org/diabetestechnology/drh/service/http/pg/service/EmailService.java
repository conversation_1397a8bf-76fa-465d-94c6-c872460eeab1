package org.diabetestechnology.drh.service.http.pg.service;

import java.io.UnsupportedEncodingException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;

@Service
public class EmailService {

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    @Value("${ORG_DRH_SERVICE_EMAIL_FROM}")
    private String emailFrom;
    @Value("${ORG_DRH_SERVICE_OTP_VERIFY_URL}")
    private String otpVerifyUrl;

    public EmailService(JavaMailSender mailSender, TemplateEngine templateEngine) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
    }

    public void sendEmail(String toEmail, String name, String otp)
            throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        // multipart=true to support inline images
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        Context context = new Context();
        context.setVariable("name", name);

        context.setVariable("otp", otp);
        context.setVariable("verifyOtp", otpVerifyUrl);

        String htmlContent = templateEngine.process("email-template", context);

        helper.setFrom(new InternetAddress(emailFrom, "Diabetes Research Hub"));
        helper.setTo(toEmail);
        helper.setSubject("Welcome to Diabetes Research Hub!");
        helper.setText(htmlContent, true); // true = HTML

        // Use PNG image for better email client compatibility
        helper.addInline("logoImage", new ClassPathResource("public/diabetes-research-hub-reverse.png"));

        mailSender.send(message);
    }
}
