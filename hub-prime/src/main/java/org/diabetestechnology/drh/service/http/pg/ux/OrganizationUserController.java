package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.request.OrganizationRequest;
import org.diabetestechnology.drh.service.http.pg.service.OrganizationService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.ObjectMapper;

import org.diabetestechnology.drh.service.http.pg.Response;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Hub Organization API Endpoints")
public class OrganizationUserController {

    private final OrganizationService organizationService;

    public OrganizationUserController(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(OrganizationUserController.class);

    @SuppressWarnings("unchecked")
    @PostMapping("/organization")
    @ResponseBody
    @Operation(summary = "Save an organization")
    public Response saveOrganization(@RequestBody OrganizationRequest request) {
        LOG.info("Received request to save an organization: {}", request);
        try {
            JSONB result = organizationService.saveOrganization(request);
            LOG.info("Successfully saved organization: {}", result);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> resultMap = objectMapper.readValue(result.data(), Map.class);
            return Response.builder()
                    .data(Map.of("organizationDetails",
                            result == null ? null : JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message(
                            resultMap.containsKey("message") ? resultMap.get("message").toString() : "Unknown response")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error occurred while creating organization: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to create organization")
                    .errors("Error creating organization: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/organizations/search")
    @ResponseBody
    @Operation(summary = "Search an organization")
    public Response searchOrganization(@RequestParam String request) {
        LOG.info("Search an organization: Organization request: {}", request);
        try {
            JSONB result = organizationService.searchOrganization(request);
            return Response.builder()
                    .data(Map.of("organizationDetails", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully fetched organization details")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch organization details")
                    .errors("Error in reading organization details: " + e.getMessage())
                    .build();
        }
    }
}
