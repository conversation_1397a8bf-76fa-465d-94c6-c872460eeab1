package org.diabetestechnology.drh.service.http.pg.service;

import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLog;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogDetails;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogMap;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class DbActivityService {

    private static final Logger LOG = LoggerFactory.getLogger(DbActivityService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final ObservabilityRequestFilter observabilityRequestFilter;
    private final DSLContext dsl;
    private final MasterService masterService;
    LogMap logMap = new LogMap();

    public DbActivityService(ObservabilityRequestFilter observabilityRequestFilter,
            @Qualifier("secondaryDsl") DSLContext dsl, MasterService masterService) {
        this.observabilityRequestFilter = observabilityRequestFilter;
        this.dsl = dsl;
        this.masterService = masterService;
    }

    public HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();
    }

    public String prepareActivityLogMetadata() {
        ActivityLog activityLog = new ActivityLog();
        HttpServletRequest request = getCurrentRequest();
        final var requestUrl = request.getRequestURI();
        final var sessionId = request.getSession().getId();
        final var sessionUniqueId = null == activityLog.getSessionUniqueId()
                || activityLog.getSessionUniqueId().isEmpty()
                || activityLog.getSessionUniqueId().isBlank()
                        ? observabilityRequestFilter.getUniqueSession(
                                requestUrl,
                                new ContentCachingRequestWrapper(getCurrentRequest()))
                        : activityLog.getSessionUniqueId();
        List<Map<String, Object>> result = dsl.select(
                DSL.field("hierarchy_path"),
                DSL.field("activity_hierarchy"))
                .from("drh_stateful_activity_audit.activity_log")
                .where(DSL.field("session_id").eq(sessionId))
                // .and(DSL.field("session_unique_id").eq(uniqueSessionId))
                .and(DSL.field("activity_level_id").ne(masterService.getMetricActivityLevel(6)))
                .orderBy(DSL.field("created_at").desc())
                .limit(1)
                .fetchMaps();
        Map<String, LogDetails> activityLogMap = logMap.getLogMap();
        LogDetails logDetails = activityLogMap.getOrDefault(requestUrl, null);
        Map<String, Object> heirarchy = result.isEmpty() ? null : result.get(0);
        if (heirarchy != null) {
            if (heirarchy.get("hierarchy_path") != null)
                activityLog.setHierarchyPath((heirarchy.get("hierarchy_path").toString()) + ", "
                        + requestUrl);
            else
                activityLog.setHierarchyPath(requestUrl);
            if (logDetails != null) {
                if (heirarchy.get("activity_hierarchy") != null) {
                    activityLog.setActivityHierarchy(
                            (heirarchy.get("activity_hierarchy").toString()) + ", " +
                                    activityLog.getActivityType());
                } else {
                    activityLog.setActivityHierarchy(activityLog.getActivityType());
                }
            }

        } else {
            if (logDetails != null) {
                activityLog.setHierarchyPath(requestUrl);
                activityLog.setActivityHierarchy(activityLog.getActivityType());
            }
        }

        ObjectNode jsonNode = objectMapper.createObjectNode();
        jsonNode.put("session_unique_id", sessionUniqueId);
        jsonNode.put("request_url", requestUrl);
        jsonNode.put("session_id", sessionId);
        jsonNode.put("activity_hierarchy", activityLog.getActivityHierarchy());
        jsonNode.put("hierarchy_path", activityLog.getHierarchyPath());

        try {
            String activityJson = objectMapper.writeValueAsString(jsonNode);
            LOG.info("Activity Log JSON: {}", activityJson);
            return activityJson;
        } catch (JsonProcessingException e) {
            // TODO Auto-generated catch block
            LOG.error("Failed to convert activity log to JSON: {}", e.getMessage(), e);
            return null;
        }

    }

}
