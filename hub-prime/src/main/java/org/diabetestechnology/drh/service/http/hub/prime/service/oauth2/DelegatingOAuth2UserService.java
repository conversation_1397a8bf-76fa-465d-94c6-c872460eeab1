package org.diabetestechnology.drh.service.http.hub.prime.service.oauth2;

import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;

public class DelegatingOAuth2UserService implements OAuth2UserService<OAuth2UserRequest, OAuth2User> {

    private final OAuth2UserService<OAuth2UserRequest, OAuth2User> githubUserService;
    private final OAuth2UserService<OAuth2UserRequest, OAuth2User> orcidUserService;

    public DelegatingOAuth2UserService(OAuth2UserService<OAuth2UserRequest, OAuth2User> githubUserService,
            OAuth2UserService<OAuth2UserRequest, OAuth2User> orcidUserService) {
        this.githubUserService = githubUserService;
        this.orcidUserService = orcidUserService;
    }

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        final var registrationId = userRequest.getClientRegistration().getRegistrationId();

        switch (registrationId) {
            case "github":
                return githubUserService.loadUser(userRequest);
            case "orcid":
                return orcidUserService.loadUser(userRequest);
            default:
                throw new IllegalArgumentException("Unknown provider: " + registrationId);
        }
    }
}
