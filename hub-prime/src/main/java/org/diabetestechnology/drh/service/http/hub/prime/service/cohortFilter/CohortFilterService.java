package org.diabetestechnology.drh.service.http.hub.prime.service.cohortFilter;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
import org.diabetestechnology.drh.service.http.hub.prime.service.DataBaseAttachService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class CohortFilterService {
    private static final Logger LOG = LoggerFactory.getLogger(CohortFilterService.class.getName());

    @Autowired
    UserNameService userNameService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    final String alias = "client";

    @Autowired
    DataBaseAttachService databaseAttachService;

    @Autowired
    DataAccessService dataAccessService;

    @Autowired
    AuditService auditService;

    public JdbcResponse saveCohortFilter(CohortFilterRequest request) {
        String createdBy = userNameService.getUserId();
        LOG.info("Save Cohort Filter Log for : {}", request.filterName());
        LOG.info("Filter Create Initiated by: {}", createdBy);
        if (createdBy.equalsIgnoreCase("anonymous")) {
            LOG.error("Anonymous users are not permitted to save cohort filters.");
            return responseBuilder(Map.of(), "Error", "cohortFilters",
                    "Anonymous users are not permitted to save cohort filters.");

            // throw new AccessDeniedException("Anonymous users are not permitted to save
            // cohort filters.");
        }
        // Check for duplicates
        if (isDuplicateFilter(request.filterName(), createdBy)) {
            LOG.warn("Duplicate filter entry found for filter_name: {} and created_by: {}", request.filterName(),
                    createdBy);
            return responseBuilder(Map.of(), "Error", "cohortFilters",
                    "A filter with the name " + request.filterName() + " already exists for the user "
                            + createdBy + ". Please choose a different name or update the existing filter.");
        }
        if (request.filterOption().studyIds().isEmpty()) {
            LOG.error("Studies Cannot be empty.");
            return responseBuilder(Map.of(), "Error: Studies Cannot be empty.", "cohortFilters",
                    "Studies Cannot be empty.");
        }

        String sql = "INSERT INTO " + alias
                + ".filter_interaction (filter_name, filter_description, view_mode, created_by, updated_by, created_at,updated_at,filter) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        // String uuid = UUID.randomUUID().toString();
        ZonedDateTime utcNow = ZonedDateTime.now(ZoneOffset.UTC);
        String utcTime = utcNow.toString();
        int maxAttempts = 3;
        int attempt = 0;
        while (attempt < maxAttempts) {
            try {
                databaseAttachService.refreshClientServerDb();
                // Execute the SQL INSERT statement using JdbcTemplate
                jdbcTemplate.update(sql, request.filterName(), request.filterDescription(),
                        request.viewMode(),
                        createdBy, createdBy, utcTime, utcTime, request.filterOption().toString());
                return responseBuilder(Map.of("Filter", request.filterName()), "Success",
                        "cohortFilters", null);
            } catch (Exception e) {
                LOG.error("Error while saving cohort filter", e.getMessage());
                attempt++;
                LOG.error("Attempt {} - Error while saving cohort filter: {}", attempt, e.getMessage());
                // If the maximum attempts have been reached, throw the exception or handle it
                // as needed
                if (attempt >= maxAttempts) {
                    LOG.error("Maximum retry attempts reached. Unable to save cohort filter.");
                    return responseBuilder(Map.of(), "Error", "cohortFilters", e.getMessage());
                }

                // Optional: add a delay between retries (e.g., 1 second)
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    return responseBuilder(Map.of(), "Error", "cohortFilters", ie.getMessage());
                }
            }
        }
        return responseBuilder(Map.of(), "Error", "cohortFilters", "No filter Saved");

    }

    public JdbcResponse getCohortFilter() {
        String userId = userNameService.getUserId();
        LOG.info("Fetching cohort filter for user: {}", userId);
        String sql = "";
        // Define your SQL query to fetch cohort filters created by the user
        sql = "SELECT distinct(filter_interaction_id), view_mode, created_by, updated_by, filter_name, filter_description, filter "
                + "FROM " + alias + ".filter_interaction "
                + "WHERE created_by = ? OR view_mode like 'Public' COLLATE NOCASE "
                + "ORDER BY updated_at DESC";
        int maxAttempts = 3;
        int attempt = 0;
        while (attempt < maxAttempts) {
            try {
                databaseAttachService.refreshClientServerDb();
                // Execute the SQL SELECT statement and map the result to CohortFilterResponse
                List<CohortFilterResponse> cohortFilters = jdbcTemplate.query(sql, ps -> ps.setString(1, userId),
                        (rs, rowNum) -> {
                            try {
                                if (rs == null || rs.getString("filter") == null) {
                                    LOG.info("Raw filter is empty");
                                    return null; // Handle case when filter is null
                                }
                                String filterJson = rs.getString("filter");
                                LOG.info("Raw filter JSON: {}", filterJson); // Log the raw JSON
                                // Convert the input string to JSON
                                String jsonString = convertToJson(filterJson);
                                LOG.info("JSON String : {}", jsonString);
                                System.out.println(jsonString);
                                ObjectMapper objectMapper = new ObjectMapper();
                                JsonNode jsonNode;
                                try {
                                    jsonNode = objectMapper.readTree(jsonString);
                                    System.out.println(jsonNode.toPrettyString());
                                    return new CohortFilterResponse(
                                            rs.getString("view_mode"),
                                            rs.getString("created_by"),
                                            rs.getString("updated_by"),
                                            Integer.parseInt(rs.getString("filter_interaction_id")),
                                            rs.getString("filter_name"),
                                            rs.getString("filter_description"),
                                            jsonNode);
                                } catch (JsonMappingException e) {
                                    LOG.error("Error while parsing filter_interaction: {}", e.getMessage());
                                } catch (JsonProcessingException e) {
                                    LOG.error("Error while parsing filter_interaction: {}", e.getMessage());
                                }

                                return null;
                            } catch (NumberFormatException e) {
                                LOG.error("Error while parsing filter_interaction: {}", e.getMessage());
                                return null;
                            }
                        });
                // Create a response object and populate it with fetched data

                return responseBuilder(cohortFilters != null
                        ? Map.of("cohortFilters", cohortFilters)
                        : Map.of(), "success", "cohortFilters", null);
            } catch (Exception e) {
                LOG.error("Attempt {} , Error while fetching cohort filter:  {}", attempt, e.getMessage());
                attempt++;
                if (attempt >= maxAttempts) {
                    return responseBuilder(Map.of(), "error", "cohortFilters", e.getMessage());
                }
                // Optional: add a delay between retries (e.g., 1 second)
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }
        return dataAccessService.commonResponseBuilder("cohortFilters");

    }

    public static String convertToJson(String input) {
        try {
            // Remove the "FilterOption" prefix and split the parts
            input = input.substring(input.indexOf('[') + 1, input.lastIndexOf(']'));

            String[] parts = input.split(", filters=");
            String studyIdsPart = parts[0].trim();
            String filtersPart = parts[1].trim();
            LOG.info("Study Part : {} \n Filters Part: {}", studyIdsPart, filtersPart);
            // Process studyIds
            studyIdsPart = studyIdsPart.replace("studyIds=[", "").replace("]", "").trim();
            String[] studyIds = studyIdsPart.split(",\\s*");
            Map<String, Map<String, Object>> filters = new HashMap<>();
            if (!filtersPart.equals("{}")) {
                // Process filters
                filtersPart = filtersPart.replace("filters={", "").replace("}", "").trim();

                // Split individual filters by comma, ensuring to handle nested structures
                String[] filterEntries = filtersPart.split(",\\s*(?=\\w+=FilterModel\\[)");

                for (String entry : filterEntries) {
                    String[] keyValue = entry.split("=", 2);
                    String filterKey = keyValue[0].trim();

                    // Ensure filterKey doesn't contain any extraneous characters
                    filterKey = filterKey.replace("{", "").replace("}", "").trim();

                    String filterValue = keyValue[1].replace("FilterModel[", "").replace("]", "").trim();
                    Map<String, Object> filterDetails = new HashMap<>();

                    // Split the filter value by commas while handling nested values
                    String[] filterProps = filterValue.split(",\\s*(?=\\w+=)");

                    for (String prop : filterProps) {
                        String[] propKeyValue = prop.split("=", 2); // Split on the first '='
                        String propKey = propKeyValue[0].trim();
                        String propValue = propKeyValue.length > 1 ? propKeyValue[1].trim() : null;

                        // Handle specific data types
                        if (propValue != null) {
                            if (propValue.startsWith("[")) { // If it's a list
                                propValue = propValue.replace("[", "").replace("]", "").trim();
                                List<String> values = List.of(propValue.split(",\\s*"));
                                filterDetails.put(propKey, values);
                            } else if (propValue.equals("null")) { // Handle null explicitly
                                filterDetails.put(propKey, null);
                            } else {
                                // Clean up the operator to be a single '='
                                if (propKey.equals("operator")) {
                                    propValue = propValue.replace("==", "=");
                                }
                                filterDetails.put(propKey, propValue);
                            }
                        }
                    }
                    filters.put(filterKey, filterDetails);
                }
            }

            // Build the final JSON structure
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("studyIds", List.of(studyIds));
            jsonMap.put("filters", filters.size() == 0 ? new HashMap<>() : filters);

            // Convert to JSON string
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(jsonMap);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    boolean isDuplicateFilter(String filterName, String createdBy) {
        int maxAttempts = 3;
        int attempt = 0;
        while (attempt < maxAttempts) {
            try {
                databaseAttachService.refreshClientServerDb();
                String sql = "SELECT COUNT(*) FROM " + alias
                        + ".filter_interaction WHERE filter_name = ? AND created_by = ?";
                Integer count = jdbcTemplate.queryForObject(
                        sql,
                        Integer.class,
                        filterName,
                        createdBy);
                return count != null && count > 0;
            } catch (Exception e) {
                LOG.error("Attempt {} , Error while fetching duplicate cohort filter:  {}", attempt, e.getMessage());
                attempt++;
                if (attempt >= maxAttempts) {
                    return true;
                }
                // Optional: add a delay between retries (e.g., 1 second)
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }
        return true;

    }

    public JdbcResponse updateCohortFilter(CohortFilterRequest request, int filterId) {
        LOG.info("Updating Cohort Filter for filter_id: {}", filterId);
        try {
            // Check if the filter exists before updating
            if (!filterExists(filterId)) {
                LOG.warn("No filter found with filter_id: {}", filterId);
                // throw new FilterNotFoundException("No filter found with filter_id: " +
                // filterId);
                return responseBuilder(Map.of(), "Error", "cohortFilters",
                        "No filter found to update");

            }

            // Fetch the existing filter
            String fetchSql = "SELECT created_by, updated_by FROM " + alias
                    + ".filter_interaction WHERE filter_interaction_id = ?";
            Map<String, Object> existingFilter = jdbcTemplate.queryForMap(fetchSql, filterId);

            String createdBy = (String) existingFilter.get("created_by");
            String updatedBy = userNameService.getUserId();
            LOG.info("Existing Filter: createdBy: {}, updatedBy: {}", createdBy, updatedBy);

            // Check if createdBy and updatedBy are not the same
            if (!createdBy.equals(updatedBy)) {
                saveCohortFilter(request);
                return responseBuilder(Map.of("Filter", request.filterName()), "Success",
                        "cohortFilters",
                        null);

            } else {
                if (isDuplicateFilterWhileUpdate(request.filterName(), createdBy, filterId)) {
                    LOG.warn("Duplicate filter entry found for filter_name: {} and created_by: {}",
                            request.filterName(),
                            createdBy);
                    return responseBuilder(Map.of(), "Error", "cohortFilters",
                            "A filter with the name " + request.filterName() + " already exists for the user "
                                    + createdBy + ". Please choose a different name or update the existing filter.");
                }
                String sql = "UPDATE " + alias + ".filter_interaction "
                        + "SET filter_name = ?, filter_description = ?, view_mode = ?, updated_by = ?, updated_at = ?, filter = ? "
                        + "WHERE filter_interaction_id = ?";
                LOG.info("Cohort Filter Update query: {}", sql);

                ZonedDateTime utcNow = ZonedDateTime.now(ZoneOffset.UTC);
                String utcTime = utcNow.toString();
                int maxAttempts = 3;
                int attempt = 0;
                while (attempt < maxAttempts) {
                    try {
                        databaseAttachService.refreshClientServerDb();
                        // Execute the SQL UPDATE statement using JdbcTemplate
                        int rowsAffected = jdbcTemplate.update(sql,
                                request.filterName(),
                                request.filterDescription(),
                                request.viewMode(),
                                request.updatedBy(),
                                utcTime,
                                request.filterOption().toString(),
                                filterId); // Use filterId to identify the record to update

                        if (rowsAffected > 0) {
                            LOG.info("Successfully updated cohort filter with filter_id: {}", filterId);
                            Map<String, Object> updatedMap = new HashMap<>(Map.of("filter", request.filterName()));
                            updatedMap.put("filter_id", filterId);
                            return responseBuilder(updatedMap, "Success", "cohortFilters",
                                    null);

                        } else {
                            LOG.warn("No rows were updated for filter_id: {}", filterId);
                            return responseBuilder(Map.of(), "Error", "cohortFilters",
                                    "No rows updated");
                        }
                    } catch (Exception ex) {
                        attempt++;
                        LOG.error("Attempt {} - Error while updating cohort filter: {}", attempt, ex.getMessage());

                        // If the maximum attempts have been reached, throw the exception or handle it
                        // as needed
                        if (attempt >= maxAttempts) {
                            LOG.error("Maximum retry attempts reached. Unable to update cohort filter. {}",
                                    ex.getMessage());
                            return responseBuilder(Map.of(),
                                    "Maximum retry attempts reached. Unable to update cohort filter.", "cohortFilters",
                                    ex.getMessage());
                        }
                        // Optional: add a delay between retries (e.g., 1 second)
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt(); // Restore interrupted status
                            throw new RuntimeException("Retry interrupted", ie);
                        }
                    }
                }
            }
            return responseBuilder(Map.of(), "Error", "cohortFilters", "No rows updated");

        } catch (Exception e) {
            LOG.error("Error while updating cohort filter with filter_id: {}", filterId, e.getMessage());
            return responseBuilder(Map.of(), "Error", "cohortFilters", e.getMessage());
        }

    }

    boolean filterExists(int filterId) {
        int maxAttempts = 3;
        int attempt = 0;
        while (attempt < maxAttempts) {
            try {
                databaseAttachService.refreshClientServerDb();
                String sql = "SELECT COUNT(*) FROM " + alias + ".filter_interaction WHERE filter_interaction_id = ?";
                Integer count = jdbcTemplate.queryForObject(
                        sql,
                        Integer.class,
                        filterId);
                return count != null && count > 0;
            } catch (Exception e) {
                LOG.error("Attempt {} , Error while fetching cohort filter to update:  {}", attempt, e.getMessage());
                attempt++;
                if (attempt >= maxAttempts) {
                    return false;
                }
                // Optional: add a delay between retries (e.g., 1 second)
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }
        return false;
    }

    private JdbcResponse responseBuilder(Map<String, Object> data, String status, String messageKey, Object error) {
        LOG.info("Preparing response for: {}", messageKey);
        return JdbcResponse.builder()
                .data(data)
                .status("success")
                .message(status)
                .errors(data.isEmpty() && status.equalsIgnoreCase("success")
                        ? "success"
                        : error != null ? error.toString() : "")
                .build();
    }

    boolean isDuplicateFilterWhileUpdate(String filterName, String createdBy, int filterId) {
        int maxAttempts = 3;
        int attempt = 0;
        while (attempt < maxAttempts) {
            try {
                databaseAttachService.refreshClientServerDb();
                String sql = "SELECT COUNT(*) FROM " + alias
                        + ".filter_interaction WHERE filter_name = ? AND created_by = ? AND filter_interaction_id <> ?";
                Integer count = jdbcTemplate.queryForObject(
                        sql,
                        Integer.class,
                        filterName,
                        createdBy,
                        filterId);
                return count != null && count > 0;
            } catch (Exception e) {
                LOG.error("Attempt {} , Error while fetching duplicate cohort filter:  {}", attempt, e.getMessage());
                attempt++;
                if (attempt >= maxAttempts) {
                    return true;
                }
                // Optional: add a delay between retries (e.g., 1 second)
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    throw new RuntimeException("Retry interrupted", ie);
                }
            }
        }
        return true;

    }

}
