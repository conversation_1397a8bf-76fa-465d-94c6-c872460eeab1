package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI.AIService;
import org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI.VannaAIRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "DRH Vanna AI Interaction API")
public class VannaAIController {

    private static final Logger LOG = LoggerFactory.getLogger(VannaAIController.class.getName());
    @Autowired
    private AIService aiService;
    @Autowired
    UserNameService userNameService;

    @Operation(summary = "Save Vanna.ai Interaction")
    @PostMapping(value = "/ai/interaction/save.json", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<String> saveAIInteraction(final @RequestBody @Nonnull VannaAIRequest payload,
            HttpServletRequest request) throws UnknownHostException {
        LOG.info("Save AI Interaction with Payload : {}", payload);

        aiService.saveAIInteraction(payload, request);
        return ResponseEntity.ok("Successfully saved AI Interaction Log");

    }

    @Operation(summary = "Get User Specific Vanna.ai Interaction")
    @GetMapping(value = "/ai/interaction/user")
    @ResponseBody
    public List<Map<String, Object>> getAIInteraction(
            HttpServletRequest request) throws UnknownHostException {
        LOG.info("get AI Interaction for user : {}", userNameService.getUserId());
        return aiService.getAIInteraction(request);
    }

    @Operation(summary = "Get all Vanna.ai Interaction")
    @GetMapping(value = "/ai/interaction/all")
    @ResponseBody
    public List<Map<String, Object>> getAllAIInteraction(
            HttpServletRequest request) throws UnknownHostException {
        LOG.info("get all AI Interaction for user");
        return aiService.getAllAIInteraction();

    }

}
