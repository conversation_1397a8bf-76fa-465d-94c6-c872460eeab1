package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Hub Master API Endpoints")
public class MasterController {
    private static final Logger LOG = LoggerFactory.getLogger(MasterController.class);

    @Autowired
    MasterService masterService;

    @GetMapping("/study-visibility")
    @Operation(summary = "Read List of study visibility")
    @ResponseBody
    public Response getStudyVisibility() {
        LOG.info("get study visibility");
        return Response.builder()
                .data(new HashMap<>(Map.of("studyVisibility",
                        masterService.getStudyVisibility())))
                .status("success")
                .message("Successfully read study visibility")
                .errors(null)
                .build();
    }

    @GetMapping("/race-type")
    @Operation(summary = "Read List of race type")
    @ResponseBody
    public Response getRaceType() {
        LOG.info("get race type");
        return Response.builder()
                .data(new HashMap<>(Map.of("raceType",
                        masterService.getRaceType())))
                .status("success")
                .message("Successfully read race type")
                .errors(null)
                .build();
    }

    @GetMapping("/ethnicity-type")
    @Operation(summary = "Read List of ethnicity type")
    @ResponseBody
    public Response getEthnicityType() {
        LOG.info("get ethnicity type");
        return Response.builder()
                .data(new HashMap<>(Map.of("ethnicityType",
                        masterService.getEthnicityType())))
                .status("success")
                .message("Successfully read ethinicity type")
                .errors(null)
                .build();
    }

    @GetMapping("/gender-type")
    @Operation(summary = "Read List of gender type")
    @ResponseBody
    public Response getGenderType() {
        LOG.info("get gender type");
        return Response.builder()
                .data(new HashMap<>(Map.of("genderType",
                        masterService.getGenderType())))
                .status("success")
                .message("Successfully read gender type")
                .errors(null)
                .build();
    }

    @GetMapping("/profile-status-type")
    @Operation(summary = "Read List of profile status type")
    @ResponseBody
    public Response getProfileStatusType() {
        LOG.info("get profile status type");
        return Response.builder()
                .data(new HashMap<>(Map.of("profileStatusType",
                        masterService.getProfileStatusType())))
                .status("success")
                .message("Successfully read profile status type")
                .errors(null)
                .build();
    }

    @GetMapping("/citation-status")
    @Operation(summary = "Read List of citation status")
    @ResponseBody
    public Response getCitationStatus() {
        LOG.info("Get citation status");
        return Response.builder()
                .data(new HashMap<>(Map.of("citationStatus",
                        masterService.getCitationStatus())))
                .status("success")
                .message("Successfully fetched citation status")
                .errors(null)
                .build();
    }

    @GetMapping("/contact-point-system")
    @Operation(summary = "Read List of Contact Point system")
    @ResponseBody
    public Response getContactPointAddressType() {
        LOG.info("Fetching contact system");
        return Response.builder()
                .data(new HashMap<>(Map.of("contactPointsystem",
                        masterService.getContactPointSystem())))
                .status("success")
                .message("Successfully retrieved contact point system")
                .errors(null)
                .build();
    }

    @GetMapping("/investigator-study-roles")
    @Operation(summary = "Read List of investigator study role")
    @ResponseBody
    public Response getInvestigatorStudyRole() {
        LOG.info("Read List of investigator study role");
        return Response.builder()
                .data(Map.of("investigatorStudyRole",
                        masterService.getInvestigatorStudyRole()))
                .status("success")
                .message("Successfully fetched investigator study role")
                .errors(null)
                .build();
    }

    @GetMapping("/lonic-codes")
    @Operation(summary = "Read List of loinc codes")
    @ResponseBody
    public Response getLoincCodes() {
        LOG.info("Get loinc codes");
        return Response.builder()
                .data(Map.of("loincCodes",
                        masterService.getLoincCodes()))
                .status("success")
                .message("Successfully fetched loinc codes")
                .errors(null)
                .build();
    }

    @GetMapping("/metric-definitions")
    @Operation(summary = "Read List of metric definitions")
    @ResponseBody
    public Response getMetricDefinitions() {
        LOG.info("Get metric definitions");
        return Response.builder()
                .data(Map.of("metricDefinitions",
                        masterService.getMetricDefinitions()))
                .status("success")
                .message("Successfully fetched metric definitions")
                .errors(null)
                .build();
    }

    @GetMapping("/organization-types")
    @Operation(summary = "Read List of organization type")
    @ResponseBody
    public Response getOrganizationType() {
        LOG.info("Get organization type");
        return Response.builder()
                .data(Map.of("organizationType",
                        masterService.getOrganizationType()))
                .status("success")
                .message("Successfully fetched organization type")
                .errors(null)
                .build();
    }

    @GetMapping("/research-study-conditions")
    @Operation(summary = "Read List of research study condition")
    @ResponseBody
    public Response getResearchStudyCondition() {
        LOG.info("Get research study condition");
        return Response.builder()
                .data(Map.of("researchStudyCondition",
                        masterService.getResearchStudyCondition()))
                .status("success")
                .message("Successfully fetched research study condition")
                .errors(null)
                .build();
    }

    @GetMapping("/research-study-focus")
    @Operation(summary = "Read List of research study focus")
    @ResponseBody
    public Response getResearchStudyFocus() {
        LOG.info("Get research study focus");
        return Response.builder()
                .data(Map.of("researchStudyFocus",
                        masterService.getResearchStudyFocus()))
                .status("success")
                .message("Successfully fetched research study focus")
                .errors(null)

                .build();
    }

    @GetMapping("/research-study-party-roles")
    @Operation(summary = "Read List of research study party role")
    @ResponseBody
    public Response getResearchStudyPartyRole() {
        LOG.info("Get research study party role");
        return Response.builder()
                .data(Map.of("researchStudyPartyRole",
                        masterService.getResearchStudyPartyRole()))
                .status("success")
                .message("Successfully fetched research study party role")
                .errors(null)
                .build();
    }

    @GetMapping("/research-subject-statuses")
    @Operation(summary = "Read List of all research subject statuses")
    @ResponseBody
    public Response getResearchSubjectStatus() {
        LOG.info("Get research subject statuses");
        return Response.builder()
                .data(Map.of("researchSubjectStatuses",
                        masterService.getResearchSubjectStatus()))
                .status("success")
                .message("Successfully fetched research subject statuses")
                .errors(null)
                .build();
    }

    @GetMapping("/contact-point-address-use")
    @Operation(summary = "Read List of Contact Point Address use")
    @ResponseBody
    public Response getcontactpointaddressuseview() {
        LOG.info("Fetching contact point address use");
        return Response.builder()
                .data(new HashMap<>(Map.of("contactPointAddressuse",
                        masterService.getContactPointAddressuse())))
                .status("success")
                .message("Successfully retrieved contact point address use")
                .errors(null)
                .build();
    }

    @GetMapping("/all-contact-point-use")
    @Operation(summary = "Read List of all Contact Point Use")
    @ResponseBody
    public Response getContactPointUse() {
        LOG.info("Get contact point uses ");
        return Response.builder()
                .data(Map.of("contactpointuses",
                        masterService.getContactpointuse()))
                .status("success")
                .message("Successfully fetched contact point uses")
                .errors(null)
                .build();
    }

    @GetMapping("/study-status")
    @Operation(summary = "Read List of all study statuses")
    @ResponseBody
    public Response getStudyStatus() {
        LOG.info("Get study statuses");
        return Response.builder()
                .data(Map.of("study statuses",
                        masterService.getStudyStatus()))
                .status("success")
                .message("Successfully fetched study statuses ")
                .errors(null)
                .build();
    }

    @GetMapping("/get-data-from-contact-point-use-view")
    @Operation(summary = "Read List of data from contact point use view")
    @ResponseBody
    public Response getdatafromcontactpointuseview() {
        LOG.info("Get data from contact point use view");
        return Response.builder()
                .data(Map.of("data from contact point use",
                        masterService.getcontactpointuseview()))
                .status("success")
                .message("Successfully fetched data from contact point use ")
                .errors(null)
                .build();
    }

    @Operation(summary = "Get Each Metrics Details")
    @GetMapping("/study/each-metrics-details/{metric}")
    @ResponseBody
    public Response getMetricDefinitions(@PathVariable String metric) {
        LOG.info("Get metric definitions for metric: {}", metric);
        try {
            Object metricDefinition = masterService.getMetricDefinitions(metric);
            return Response.builder()
                    .data(Map.of("metricDefinitions",
                            metricDefinition))
                    .status("success")
                    .message("Successfully fetched metric definitions")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching metric definitions for metric: {}", metric, e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch metric definitions")
                    .errors(e.getMessage())
                    .build();
        }
    }
}