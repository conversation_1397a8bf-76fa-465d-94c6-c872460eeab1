package org.diabetestechnology.drh.service.http.hub.prime;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import reactor.core.publisher.Mono;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLog;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLogService;

import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.ActivityMap;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.ActivityType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Component
public class ObservabilityRequestFilter extends OncePerRequestFilter {

    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final Logger LOG = LoggerFactory.getLogger(ObservabilityRequestFilter.class.getName());
    public static String uniqueSession = "";

    @Autowired
    UserNameService userNameService;

    @Autowired
    ActivityLogService activityLogService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        if (shouldExcludeFromLogging(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return; // Skip logging for these requests
        }

        Instant startTime = Instant.now();
        request.setAttribute(START_TIME_ATTRIBUTE, startTime);
        LOG.info("UxReportableObservabilityFilter, preHandle. Request : {}", request.getRequestURI());

        try {
            ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
            // Wrapped response is used add metrics to the response headers after the
            // response is submitted from controllers.
            ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);
            filterChain.doFilter(wrappedRequest, wrappedResponse);
            if (!wrappedResponse.isCommitted()) {
                LOG.info("NOT COMMITTED!!! {}", wrappedRequest.getRequestURI());
                Instant finishTime = Instant.now();
                Duration duration = Duration.between(startTime, finishTime);
                String startTimeText = startTime.toString();
                String finishTimeText = finishTime.toString();
                String durationMsText = String.valueOf(duration.toMillis());
                String durationNsText = String.valueOf(duration.toNanos());
                wrappedResponse.setHeader("X-Observability-Metric-Interaction-Start-Time", startTimeText);
                wrappedResponse.setHeader("X-Observability-Metric-Interaction-Finish-Time", finishTimeText);
                wrappedResponse.setHeader("X-Observability-Metric-Interaction-Duration-Nanosecs", durationNsText);
                wrappedResponse.setHeader("X-Observability-Metric-Interaction-Duration-Millisecs", durationMsText);
                wrappedResponse.copyBodyToResponse(); // Ensure the response body is copied to the actual response
            } else {
                LOG.info("ALREADY COMMITTED!!! {}", wrappedRequest.getRequestURI());
            }
            final var requestBody = new String(wrappedRequest.getContentAsByteArray(),
                    wrappedRequest.getCharacterEncoding());
            final var requestUrl = wrappedRequest.getRequestURI();
            LOG.info("Request URL: {} ", requestUrl);
            ActivityLog activityLog;
            try {
                activityLog = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);
                if (requestBody != null && !requestBody.isEmpty() && !requestBody.equals("{}")) {
                    activityLog.getActivityData().put("requestBody", requestBody);
                }
                activityLogService.saveActivityLog(activityLog);
            } catch (UnsupportedEncodingException e) {
                LOG.error("UnsupportedEncodingException On activity Save", e);
                e.printStackTrace();
            } catch (UnknownHostException e) {
                LOG.error("UnknownHostException On activity Save", e);
                e.printStackTrace();
            } catch (InterruptedException e) {
                LOG.error("InterruptedException On activity Save", e);
                e.printStackTrace();
            }

        } catch (Exception ex) {
            LOG.error("Error in UxReportableObservabilityFilter", ex);
            // Wrap the request to cache the body
            ActivityLog activityLog;
            try {
                activityLog = activityLogService.getAuditDataFromRequestAndResponse("/error");
                activityLogService.saveActivityLog(activityLog);
            } catch (UnsupportedEncodingException e) {
                LOG.error("UnsupportedEncodingException On activity Save", e);
                e.printStackTrace();
            } catch (UnknownHostException e) {
                LOG.error("UnknownHostException On activity Save", e);
                e.printStackTrace();
            } catch (InterruptedException e) {
                LOG.error("InterruptedException On activity Save", e);
                e.printStackTrace();
            }
            filterChain.doFilter(request, response);

        } finally {
            LOG.debug("Duration calculated for {}", request.getRequestURI());
            // Ensure the response body is copied to the actual response
        }
    }

    public String getUniqueSession(String requestUrl, ContentCachingRequestWrapper wrappedRequest) {
        HttpSession session = wrappedRequest.getSession(true);
        if (requestUrl != null && requestUrl.equals("/") && session.isNew()) {
            uniqueSession = UUID.randomUUID().toString();
            session.setAttribute("uniqueSession", uniqueSession);
        } else {
            uniqueSession = (String) session.getAttribute("uniqueSession");

            // If there's no UUID in the session, generate a new one
            if (uniqueSession == null || uniqueSession.isEmpty()) {
                uniqueSession = UUID.randomUUID().toString();
                session.setAttribute("uniqueSession", uniqueSession);
            }
        }
        return uniqueSession;
    }

    private boolean shouldExcludeFromLogging(String requestUri) {
        // You can add more patterns or file extensions here as needed
        return requestUri.endsWith(".css")
                || requestUri.endsWith(".js")
                || requestUri.endsWith(".gif")
                || requestUri.endsWith(".png")
                || requestUri.endsWith(".jpg")
                || requestUri.endsWith(".svg")
                || requestUri.endsWith(".ico")
                || requestUri.equals("/metadata")
                || requestUri.equals("/HNAP1")
                || requestUri.equals("/evox/about")
                || requestUri.startsWith("/nmaplowercheck")
                || requestUri.equals("/hazelcast/rest/cluster")
                || requestUri.equals("/api")
                || requestUri.startsWith("/maven-site")
                || requestUri.equals("/robots.txt")
                || requestUri.equals("/.env")
                || requestUri.startsWith("/refresh")
                || requestUri.startsWith("/refresh/study")
                || requestUri.startsWith("/rest/applinks")
                || requestUri.equals("/activity-log/save.json")
                || requestUri.equals("/");
    }

    public static String extractStudyIdFromMatrics(String url) {
        // Find the last occurrence of '/'
        int lastSlashIndex = url.lastIndexOf('/');

        // Check if the URL ends with '.html'
        if (url.endsWith(".html")) {
            int htmlIndex = url.indexOf(".html", lastSlashIndex);
            // Check if both indices are found
            if (lastSlashIndex != -1 && htmlIndex != -1) {
                // Extract and return the substring between the last '/' and '.html'
                return url.substring(lastSlashIndex + 1, htmlIndex);
            }
        } else {
            // If it does not end with '.html', return the substring after the last '/'
            if (lastSlashIndex != -1) {
                return url.substring(lastSlashIndex + 1);
            }
        }
        return ""; // Return an empty string if not found
    }

    public static String extractCgmFileName(String url) {
        // Find the last occurrence of '/'
        int lastSlashIndex = url.lastIndexOf('/');
        // If it does not end with '.html', return the substring after the last '/'
        if (lastSlashIndex != -1) {
            return url.substring(lastSlashIndex + 1);
        }
        return ""; // Return an empty string if not found
    }

    public void setActivityData(String requestUrl, ActivityLog activityLog, int statusCode,
            ContentCachingResponseWrapper wrappedResponse) {
        Set<String> validDirectUrls = Set.of("/", "/home", "/svm/info", "/cohort/info", "/cohort",
                "/activity-log/info", "/console/project", "/console/health-info",
                "/console/schema", "/docs", "/docs/swagger", "/docs/announcements", "/docs/api/openapi",
                "/docs/api/openapi/swagger-config", "/docs/api/interactive/swagger-ui/index.html",
                "/docs/api/interactive/index.html",
                "/error", "/skip-login", "/svm", "/activity-log", "/profile", "/profile/info");
        Mono.just(requestUrl)
                .filter(validDirectUrls::contains)
                .flatMap(url -> Mono.just(new ActivityMap().getActivityData(url)))
                .filter(Objects::nonNull)
                .doOnNext(activityDetails -> {
                    activityLog.setActivityName(activityDetails.getActivityName());
                    activityLog.setActivityDescription(activityDetails.getActivityDescription());
                    activityLog.setActivityType(activityDetails.getActivityType());
                    activityLog.setActivityLogLevel(activityDetails.getActivityLogLevel());
                })
                .subscribe();

        if (requestUrl.startsWith("/study/")) {
            activityLog.setActivityName(ActivityType.STUDIES);
            activityLog.setActivityType(ActivityType.STUDIES);
            if (wrappedResponse.getStatus() == 302) {
                activityLog.setActivityDescription(
                        "Load Study Details of " + extractStudyIdFromMatrics(requestUrl) + ".");
            } else if (requestUrl.startsWith("/study/detail/")) {
                activityLog.setActivityDescription(
                        "Fetch Study Details of " + extractStudyIdFromMatrics(requestUrl) + ".");
            }
        } else if (requestUrl.startsWith("/svm/cohort/")) {
            activityLog.setActivityName(ActivityType.COHORT);
            activityLog.setActivityType(ActivityType.COHORT);
        } else if (requestUrl.startsWith("/participant")) {
            activityLog.setActivityName(ActivityType.STUDIES_PARTICIPANT);
            activityLog.setActivityType(ActivityType.STUDIES);
        }

        Mono.just(requestUrl)
                .flatMap(url -> {
                    if (url.startsWith("/svm/file") || url.startsWith("/cgm")) {
                        activityLog.setActivityName(ActivityType.STUDIES_CGM);
                        activityLog.setActivityType(ActivityType.STUDIES);

                        return Mono.just(url)
                                .flatMap(innerUrl -> {
                                    if (innerUrl.startsWith("/svm/files/")) {
                                        activityLog.setActivityDescription("Select the Study "
                                                + extractStudyIdFromMatrics(innerUrl) + " to view CGM Files.");
                                    }
                                    if (innerUrl.startsWith("/cgm/field-name/")) {
                                        activityLog.setActivityDescription(
                                                "CGM File " + extractCgmFileName(innerUrl) + " Selected");
                                    } else if (innerUrl.startsWith("/svm/file/view/")) {
                                        activityLog.setActivityDescription(
                                                "Choosed CGM File " + extractCgmFileName(innerUrl) + ".");
                                    } else if (innerUrl.startsWith("/svm/files/list/")) {
                                        activityLog.setActivityDescription("Listed all CGM Files of "
                                                + extractStudyIdFromMatrics(innerUrl) + ".");
                                    }
                                    return Mono.empty(); // Return an empty Mono when the description is set
                                });
                    }
                    return Mono.empty(); // Return an empty Mono if the initial condition is not met
                })
                .subscribe();

        Mono.just(requestUrl)
                .flatMap(url -> {

                    if (url.endsWith("all_study_summary_cached.json")) {
                        activityLog.setActivityDescription("Studies data Loaded to AgGrid.");
                        activityLog.setActivityName(ActivityType.STUDIES);
                        activityLog.setActivityType(ActivityType.STUDIES);
                    } else if (url.endsWith("/participant_dashboard_cached.json")) {
                        activityLog.setActivityDescription("Study participants data Loaded to AgGrid.");
                        activityLog.setActivityName(ActivityType.STUDIES);
                        activityLog.setActivityType(ActivityType.STUDIES);
                    } else if (url.endsWith("all_participant_dashboard_cached.json")) {
                        activityLog.setActivityDescription("All participants data Loaded to AgGrid.");
                        activityLog.setActivityName(ActivityType.COHORT);
                        activityLog.setActivityType(ActivityType.COHORT);
                    } else if (url.endsWith("activity_log.json")) {
                        activityLog.setActivityDescription("Activity Log data Loaded to AgGrid.");
                        activityLog.setActivityName(ActivityType.ACTIVITY_LOG);
                        activityLog.setActivityType(ActivityType.ACTIVITY_LOG);
                    } else if (url.matches(
                            ".*(cgm_table_name_cached|uniform_resource_lab|uniform_resource_investigator|uniform_resource_cgm_tracing|uniform_resource_participant|uniform_resource_publication|uniform_resource_site|uniform_resource_author|uniform_resource_institution|uniform_resource_cgm_file_metadata|uniform_resource_study).*json$")) {
                        activityLog.setActivityDescription("CGM File data Loaded to AgGrid.");
                        activityLog.setActivityName(ActivityType.STUDIES_CGM);
                        activityLog.setActivityType(ActivityType.STUDIES);
                    }

                    if (url.startsWith("/api/ux/tabular/jooq/audit/activity_log/")) {
                        activityLog.setActivityDescription("Checked Detailed Activity Log.");
                        activityLog.setActivityName(ActivityType.ACTIVITY_LOG);
                        activityLog.setActivityType(ActivityType.ACTIVITY_LOG);
                    }
                    if (url.startsWith("/study/each-study-details/")) {
                        activityLog.setActivityDescription(
                                "Fetch Study details of " + extractStudyIdFromMatrics(url) + ".");
                        activityLog.setActivityName(ActivityType.STUDIES);
                        activityLog.setActivityType(ActivityType.STUDIES);
                    }
                    return Mono.empty();
                })
                .subscribe();

        Mono.just(requestUrl)
                .filter(url -> url.endsWith(".html"))
                .flatMap(url -> {
                    if (url.startsWith("/study")) {
                        return Mono.just(url)
                                .flatMap(studyUrl -> {
                                    if (studyUrl.endsWith("allstudy-total-data-points.html")) {
                                        return Mono.just("Fetch Total Data Points of All Studies.");
                                    } else if (studyUrl.endsWith("average_age.html")) {
                                        return Mono.just("Fetch Average age of participants of All Studies.");
                                    } else if (studyUrl.endsWith("percent_female.html")) {
                                        return Mono.just("Fetch Female Percentage All Studies.");
                                    } else if (studyUrl.endsWith("allstudy-total-cgm-wear.html")) {
                                        return Mono.just("Fetch Total CGM Wear of All Studies.");
                                    } else if (studyUrl.endsWith("allstudy-total-cgm-files.html")) {
                                        return Mono.just("Fetch Total CGM Files of All Studies.");
                                    } else if (studyUrl.endsWith("total_number_of_participants.html")) {
                                        return Mono.just("Fetch Total Participants of All Studies.");
                                    } else if (studyUrl.startsWith("/study/each-study-total-participants")) {
                                        return Mono.just("Fetch Total Participants of the Study "
                                                + extractStudyIdFromMatrics(studyUrl) + ".");
                                    } else if (studyUrl.startsWith("/study/allstudy-total-cgm-files")) {
                                        return Mono.just("Fetch Total CGM files of the Study "
                                                + extractStudyIdFromMatrics(studyUrl) + ".");
                                    } else if (studyUrl.startsWith("/study/each-study-percentage-female")) {
                                        return Mono.just("Fetch Total Female percentage of the Study "
                                                + extractStudyIdFromMatrics(studyUrl) + ".");
                                    } else if (studyUrl.startsWith("/study/each-study-average-age")) {
                                        return Mono.just("Fetch Average age of the participants of the Study "
                                                + extractStudyIdFromMatrics(studyUrl) + ".");
                                    } else {
                                        return Mono.empty();
                                    }
                                });
                    } else if (url.startsWith("/svm/cohort/")) {
                        return Mono.just(url)
                                .flatMap(cohortUrl -> {
                                    if (cohortUrl.endsWith("cgm-count.html")) {
                                        return Mono.just("Fetch CGM Count of COHORT.");
                                    } else if (cohortUrl.endsWith("average-age.html")) {
                                        return Mono.just("Fetch Average Age of COHORT.");
                                    } else if (cohortUrl.endsWith("totalParticipant.html")) {
                                        return Mono.just("Fetch Total Participant of COHORT.");
                                    } else if (cohortUrl.endsWith("femalePercentage.html")) {
                                        return Mono.just("Fetch Female Percentage of COHORT.");
                                    } else {
                                        return Mono.empty();
                                    }
                                });
                    }
                    return Mono.empty();
                })
                .doOnNext(activityLog::setActivityDescription)
                .subscribe();
    }
}
