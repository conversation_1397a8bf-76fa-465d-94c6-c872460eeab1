package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.ParticipantMetricsService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Participant Metrics APIs")
public class ParticipantMetricsController {

    private final ParticipantMetricsService participantMetricsService;

    public ParticipantMetricsController(ParticipantMetricsService participantMetricsService) {
        this.participantMetricsService = participantMetricsService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantMetricsController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Operation(summary = "Retrieve Participant Date Time Range")
    @ResponseBody
    @GetMapping("/studies/{studyId}/participants/{participantId}/date-time-range")
    public Response getParticipantDateTimeRange(@PathVariable String studyId, @PathVariable String participantId) {
        try {
            JSONB result = participantMetricsService.getParticipantDateTimeRange(studyId, participantId);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result.data());
                String message = jsonNode.get("message").asText();

                return Response.builder()
                        .data(Map.of("dateTimeRange", JsonUtils.jsonStringToMapOrList(result.data())))
                        .status("success")
                        .message(message)
                        .errors(null)
                        .build();
            } else {
                return Response.builder()
                        .data(Map.of())
                        .status("failure")
                        .message("Retrieving date-time range for participant failed")
                        .errors(null)
                        .build();
            }
        } catch (Exception e) {
            LOG.error("Error while retrieving date-time range for participant: {}", e.getMessage(), e);

            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving date-time range")
                    .errors("Error in fetching date-time range: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/ambulatory-glucose-profile")
    @ResponseBody
    @Operation(summary = "Get Ambulatory Glucose Profile")
    public Response getAmbulatoryGlucoseProfile(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            LOG.info("API: Fetching ambulatory glucose profile for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, startDate, endDate);
            JSONB result = participantMetricsService.getAmbulatoryGlucoseProfile(studyId, participantId, startDate,
                    endDate);

            return Response.builder()
                    .data(Map.of("ambulatoryGlucoseProfile", JsonUtils.jsonStringToMapOrList(result
                            .data())))
                    .status("success")
                    .message("Ambulatory glucose profile retrieved successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving ambulatory glucose profile")
                    .errors("Error retrieving ambulatory glucose profile: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/participant-metrics")
    @ResponseBody
    @Operation(summary = "Get Participant metrics")
    public Response getParticipantMetrics(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            LOG.info("API: Fetching participant metrics for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, startDate, endDate);
            JSONB result = participantMetricsService.getParticipantMetrics(studyId, participantId, startDate,
                    endDate);

            return Response.builder()
                    .data(Map.of("participantMetrics", JsonUtils.jsonStringToMapOrList(result
                            .data())))
                    .status("success")
                    .message("Participant metrics retrieved successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving participant metrics")
                    .errors("Error retrieving participant metrics: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/time-range-stacked-data")
    @ResponseBody
    @Operation(summary = "Get Time Range Stacked Data")
    public Response getTimeRangeStackedData(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            LOG.info("API: Fetching time range stacked data for Study: {}, Participant: {} from {} to {}",
                    studyId, participantId, startDate, endDate);
            JSONB result = participantMetricsService.getTimeRangeStackedData(studyId, participantId, startDate,
                    endDate);

            return Response.builder()
                    .data(Map.of("timeRangeStackedData", JsonUtils.jsonStringToMapOrList(result
                            .data())))
                    .status("success")
                    .message("Time range stacked data retrieved successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving time range stacked data")
                    .errors("Error retrieving time range stacked data: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/advanced-metrics")
    @ResponseBody
    @Operation(summary = "advanced metrics for Participant")
    public Response getAdvancedMetrics(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            LOG.info("API: Fetching advanced metrics for Participant: {} in Study: {} ({} - {})",
                    participantId, studyId, startDate, endDate);

            JSONB result = participantMetricsService.getAdvancedMetrics(studyId, participantId, startDate, endDate);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result.data());
                String status = jsonNode.has("status") ? jsonNode.get("status").asText() : "success";
                String message = jsonNode.has("message") ? jsonNode.get("message").asText()
                        : "Metrics retrieved successfully";
                return Response.builder()
                        .status(status)
                        .message(message)
                        .data(Map.of("metrics", JsonUtils.jsonStringToMapOrList(result
                                .data())))
                        .errors(null)
                        .build();
            } else {
                return Response.builder()
                        .status("failure")
                        .message("Failed to retrieve advanced metrics")
                        .data(Map.of())
                        .errors(null)
                        .build();
            }
        } catch (Exception e) {
            LOG.error("Error while retrieving advanced metrics for participant: {}", e.getMessage(), e);
            return Response.builder()
                    .status("error")
                    .message("Error retrieving advanced metrics")
                    .data(Map.of())
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/daily-glucose-profile")
    @ResponseBody
    @Operation(summary = "Fetch daily glucose profile for Participant")
    public Response getDailyGlucoseProfile(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            LOG.info("API: Fetching daily glucose profile for Participant: {} in Study: {} from {} to {}",
                    participantId, studyId, startDate, endDate);

            JSONB result = participantMetricsService.getDailyGlucoseProfile(studyId, participantId, startDate, endDate);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result.data());
                String status = jsonNode.has("status") ? jsonNode.get("status").asText() : "success";
                String message = jsonNode.has("message") ? jsonNode.get("message").asText()
                        : "Daily glucose profile fetched successfully";
                return Response.builder()
                        .status(status)
                        .message(message)
                        .data(Map.of("glucoseProfile", JsonUtils.jsonStringToMapOrList(result
                                .data())))
                        .errors(null)
                        .build();
            } else {
                return Response.builder()
                        .status("failure")
                        .message("Failed to retrieve daily glucose profile")
                        .data(Map.of())
                        .errors(null)
                        .build();
            }
        } catch (Exception e) {
            LOG.error("Error while retrieving daily glucose profile for participant: {}", e.getMessage(), e);
            return Response.builder()
                    .status("error")
                    .message("Error retrieving daily glucose profile")
                    .data(Map.of())
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/studies/{studyId}/participants/{participantId}/get-glycemic-risk-indicator")
    @ResponseBody
    @Operation(summary = "get glycemic risk indicator for Participant")
    public Response getGlycemicRiskIndicator(
            @PathVariable String studyId,
            @PathVariable String participantId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            LOG.info("Fetching advanced metrics for Participant: {} in Study: {} ({} - {})",
                    studyId, participantId, startDate, endDate);

            JSONB result = participantMetricsService.getGlycemicRiskIndicator(studyId, participantId, startDate,
                    endDate);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result.data());
                String status = jsonNode.has("status") ? jsonNode.get("status").asText() : "success";
                String message = jsonNode.has("message") ? jsonNode.get("message").asText()
                        : "glycemic risk indicator retrieved for participant  successfully";
                return Response.builder()
                        .status(status)
                        .message(message)
                        .data(Map.of("metrics", JsonUtils.jsonStringToMapOrList(result
                                .data())))
                        .errors(null)
                        .build();
            } else {
                return Response.builder()
                        .status("failure")
                        .message("Failed to retrieve glycemic risk indicator for the given participant ")
                        .data(Map.of())
                        .errors(null)
                        .build();
            }
        } catch (Exception e) {
            LOG.error("Error while retrieving glycemic risk indicator: {}", e.getMessage(), e);
            return Response.builder()
                    .status("error")
                    .message("Error retrieving glycemic risk indicator")
                    .data(Map.of())
                    .errors(e.getMessage())
                    .build();
        }
    }
}