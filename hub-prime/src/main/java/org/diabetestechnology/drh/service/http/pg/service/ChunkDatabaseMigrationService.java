package org.diabetestechnology.drh.service.http.pg.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileProcessingStatus;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.constant.FileUploadStatusStatus;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.PostConstruct;

@Service
public class ChunkDatabaseMigrationService {
    private final S3FileUploadService s3FileUploadService;
    private static final Logger LOG = LoggerFactory.getLogger(ChunkDatabaseMigrationService.class);
    private final DSLContext duckDsl;
    private final String postgresDbName = "postgres_study_db";
    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final DbActivityService activityLogService;

    private final InteractionService interactionService;

    private final String tempFileLocation;
    private final ConcurrentHashMap<String, Integer> uploadedChunks = new ConcurrentHashMap<>();
    ObjectMapper mapper = new ObjectMapper();

    public ChunkDatabaseMigrationService(S3FileUploadService s3FileUploadService,
            @Qualifier("duckDsl") DSLContext duckDsl,
            @Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, PartyService partyService,
            @Value("${ORG_DRH_SERVICE_HTTP_TEMPORARY_FILE_PATH:}") String tempFileLocation,
            InteractionService interactionService, DbActivityService activityLogService) {
        this.s3FileUploadService = s3FileUploadService;
        this.duckDsl = duckDsl;
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.tempFileLocation = tempFileLocation;
        this.interactionService = interactionService;
        this.activityLogService = activityLogService;
    }

    @PostConstruct
    public void init() {
        try {
            duckDsl.execute("DROP SCHEMA IF EXISTS " + postgresDbName + " CASCADE;");
            duckDsl.execute("INSTALL postgres;");
            duckDsl.execute("LOAD postgres;");
            duckDsl.execute("INSTALL sqlite;");
            duckDsl.execute("LOAD sqlite;");

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Transactional
    public Map<String, Object> uploadAndSaveDBFile(String originalFileName, DatabaseMigrationRequest request,
            long fileSize, String contentType)
            throws Exception {
        final var sqliteDbName = "sqlite_study_db" + request.studyId();
        final var tempFilePath = tempFileLocation + originalFileName;
        Pair<DSLContext, Connection> sqliteDslPair = createSQLiteDSL(tempFilePath.toString());
        DSLContext sqliteDsl = sqliteDslPair.getLeft();
        Connection conn = sqliteDslPair.getRight();
        LOG.info("Starting Database migration.");
        List<String> hierarchyJsonArray = null;
        final var fileName = FilenameUtils.getBaseName(originalFileName);
        try {
            LOG.info("Database migration started for file: {}", fileName);
            final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            detachSqliteDatabase(sqliteDbName);
            detachPostgresDatabase();
            attachPostgresDatabase();
            attachSqliteDatabase(tempFilePath, sqliteDbName);
            final var distinctDbFileIds = duckDsl.fetchOne(
                    "SELECT DISTINCT db_file_id FROM " + sqliteDbName + ".file_meta_ingest_data")
                    .get("db_file_id", String.class);
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var uploadTimestamp = ZonedDateTime.now(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ISO_INSTANT);
            final var rootFileInteracionResponse = interactionService.saveFileInteraction(hubInteractionId,
                    request.studyId(),
                    null,
                    "Initiated Database file upload.", JsonUtils.toJson(request), null, distinctDbFileIds,
                    null,
                    fileName,
                    contentType, null, FileType.DATABASE, null,
                    null,
                    uploadTimestamp, null, FileProcessingStatus.SUCCESS, 0,
                    null, null, ActionType.DB_FILE_UPLOAD);
            JsonNode rootFileInteracionResponseJson = mapper.readTree(rootFileInteracionResponse.data());
            final var rootFileInteracionId = rootFileInteracionResponseJson.get("file_interaction_id").asText();
            hierarchyJsonArray = Collections.singletonList(rootFileInteracionId.toString());

            LOG.info("Uploading file: {}", fileName);
            LOG.debug("Uploading Database File {} to S3 Bucket.", fileName);
            final var fileURL = s3FileUploadService.uploadDatabaseFileToS3Bucket(tempFileLocation, originalFileName,
                    request);
            LOG.debug("S3 bucket URL after uploading SQLite DB file : {}", fileURL);
            LOG.debug("Starting to copy tables from SQLite to Postgres");
            JSONB dbData = JSONB.valueOf(prepareJson(
                    distinctDbFileIds, fileName, fileURL, uploadTimestamp, fileSize,
                    request.studyId(), userPartyId, request.organizationPartyId()));

            LOG.info("Database metadata : {}", dbData.data());
            if (fileURL == null) {
                LOG.error("Failed to upload SQLite Database File to S3.");
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Database File Upload failed.", JsonUtils.toJson(request), null, distinctDbFileIds, null,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.FAILURE,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0, "Database File Upload failed.",
                        hierarchyJsonArray,
                        ActionType.S3_FILE_UPLOAD);
                LOG.info("Interaction Response: {}", subFileInteractionResponse.data());

                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                throw new IllegalArgumentException("Failed to upload file to S3");
            } else {
                // File upload to S3 Bucket Successfull
                LOG.info("Successfully uploaded Database File to S3 Bucket ");
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Saved Database file for processing.", JsonUtils.toJson(request), null,
                        distinctDbFileIds,
                        fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.SUCCESS, 0,
                        null, hierarchyJsonArray,
                        ActionType.S3_FILE_UPLOAD);
                LOG.info("Interaction Response: {}", subFileInteractionResponse.data());
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
            }
            LOG.info("SQLite Database File uploaded to S3 Completed. URL is : {}", fileURL);
            boolean exist = exists(request.studyId());
            LOG.debug("Database exists status: {}", exist);
            if (exist) {
                LOG.error("Database already exists for study: {}", request.studyId());
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Database already exists.", JsonUtils.toJson(request), null, distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.SKIP_PROCESSING, 0,
                        "Database Already Exists for the Study.",
                        hierarchyJsonArray,
                        ActionType.SUBMIT_FOR_VERIFICATION);
                LOG.info("Interaction Response: {}", subFileInteractionResponse.data());
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status", "Database already exists for study: " + request.studyId());
            }

            LOG.debug("Temporary location to which the database file copied: {}", tempFilePath);

            LOG.debug("File saved to temporary location: {}", tempFilePath);

            final var copyTableResponse = copyTablesFromSqLiteToPostgres(request.studyId(), tempFilePath,
                    distinctDbFileIds, dbData, sqliteDsl, sqliteDbName);
            LOG.info("Copy Table Response: {}", copyTableResponse);
            if (copyTableResponse.equals("The Sqlite Tables does not contain all the required fields")) {
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content.", JsonUtils.toJson(request), null, distinctDbFileIds, null,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "The SQLite tables do not contain all the required fields necessary for copying to the PostgreSQL table.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);

                return Map.of("status",
                        "The SQLite tables do not contain all the required fields necessary for copying to the PostgreSQL table.");
            } else if (copyTableResponse.equals("Study display id mismatch between SQLite and Postgres")) {
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content.", JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE,
                        FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,

                        "There is a mismatch between the Study Display ID in the database file and the Study to which the data is being uploaded.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);

                return Map.of("status",
                        "There is a mismatch between the Study Display ID in the database file and the Study to which the data is being uploaded.");
            } else if (copyTableResponse.equals(
                    "Failed to Copy SQLITE DB File Content to Postgres DB")) {
                final var subFileInteractionResponse = interactionService
                        .saveFileInteraction(hubInteractionId, request.studyId(), null,
                                "Failed to copy data base content.", JsonUtils.toJson(request), null,
                                distinctDbFileIds, fileURL,
                                fileName,
                                contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                                dbData.data(),
                                uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                                "Failed to Copy SQLITE DB File Content to Postgres DB.",
                                hierarchyJsonArray == null ? null
                                        : hierarchyJsonArray,
                                ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status", "Failed to Copy SQLITE DB File Content to Postgres DB.");
            } else if (copyTableResponse
                    .equals("The Sqlite Table for Participant does not contain data for all the required fields")) {
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content.", JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "The Sqlite Table for Participant does not contain data for all the required field.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                LOG.debug("The Sqlite Table for Participant does not contain data for all the required field");
                return Map.of("status",
                        "The Sqlite Table for Participant does not contain data for all the required field");
            } else if (copyTableResponse.equals("The Sqlite Tables does not contain all the required fields")) {
                LOG.debug("The Sqlite Tables does not contain all the required fields");
                final var subFileInteractionResponse = interactionService
                        .saveFileInteraction(hubInteractionId, request.studyId(), null,
                                "Failed to copy data base content.", JsonUtils.toJson(request), null,
                                distinctDbFileIds, fileURL,
                                fileName,
                                contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                                dbData.data(),
                                uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                                "The Sqlite Tables does not contain all the required fields.", hierarchyJsonArray,
                                ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status", "The Sqlite Tables does not contain all the required fields");
            } else if (copyTableResponse.equals(
                    "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields")) {
                LOG.debug(
                        "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields");
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content.", JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status",
                        "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields");
            } else if (copyTableResponse.equals(
                    "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields")) {
                LOG.debug(
                        "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields");
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content because The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields.",
                        JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status",
                        "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields");
            } else if (copyTableResponse.equals(
                    "The Sqlite Table for study_meta_data does not contain data for all the required fields")) {
                LOG.debug(
                        "The Sqlite Table for study_meta_data does not contain data for all the required fields");
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content because The Sqlite Table for study_meta_data does not contain data for all the required fields.",
                        JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "The Sqlite Table for study_meta_data does not contain data for all the required fields.",
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                return Map.of("status",
                        "The Sqlite Table for study_meta_data does not contain data for all the required fields");
            } else if (copyTableResponse.equals("Proceed with Database Migration")) {
                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Verified Database content.", JsonUtils.toJson(request), null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.SUCCESS, 0,
                        null,
                        hierarchyJsonArray,
                        ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
                LOG.debug("Starting to copy tables from SQLite to Postgres");
                JSONB migrationResponse = migrateDatabase(request.studyId(), tempFilePath, distinctDbFileIds,
                        dbData, hubInteractionId, request, fileURL,
                        fileName, contentType, uploadTimestamp,
                        rootFileInteracionId.toString(), hierarchyJsonArray, sqliteDbName);

                JsonNode jsonNode = mapper.readTree(migrationResponse.data());
                String message = jsonNode.get("message").asText();
                String status = jsonNode.get("status").asText();
                if ((status.equals("success"))) {
                    final var mealsOrFitnessDataExist = mealsOrFitnessExists(sqliteDbName);
                    LOG.info("Meals or Fitness data exist: {}", mealsOrFitnessDataExist);
                    startExtractionNew(request.studyId());
                    return Map.of(
                            "status", "Transferring SQLite database contents is in progress.",
                            "mealsOrFitnessDataExist", mealsOrFitnessDataExist);

                } else if (status.equals("failed")) {
                    return Map.of("status", "Failed to Copy SQLITE DB File Content to Postgres DB.");
                } else if (status.equals("error")) {
                    return Map.of("status", message);
                } else {
                    LOG.info("Heirarchy : {}", hierarchyJsonArray);
                    final var subInteractionResponse = interactionService
                            .saveFileInteraction(hubInteractionId, request.studyId(), null,
                                    "Failed to copy data base content.", JsonUtils.toJson(request),
                                    migrationResponse.data(),
                                    distinctDbFileIds, fileURL,
                                    fileName,
                                    contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                                    dbData.data(),
                                    uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                                    message,
                                    hierarchyJsonArray,
                                    ActionType.CONTENT_VERIFICATION);
                    hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subInteractionResponse);
                    return Map.of("status", message);
                }

            } else {
                LOG.info("Failed to Migrate Database.");
                final var subFileInteractionResponse = interactionService
                        .saveFileInteraction(hubInteractionId, request.studyId(), null,
                                "Failed to copy data base content.", JsonUtils.toJson(request), null,
                                distinctDbFileIds, fileURL,
                                fileName,
                                contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                                dbData.data(),
                                uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                                "Failed to Copy Data from Uploaded Database File.",
                                hierarchyJsonArray,
                                ActionType.CONTENT_VERIFICATION);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray,
                        subFileInteractionResponse);
                return Map.of("status", "Failed to Migrate Database.");
            }
        } catch (Exception e) {
            LOG.error("Error while uploading file: {}", e.getMessage(), e);

            final var hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(),
                    null,
                    "Error while uploading file.", JsonUtils.toJson(request), null,
                    null, null,
                    fileName,
                    contentType, null, FileType.DATABASE, FileUploadStatusStatus.ERROR,
                    null,
                    null, null, FileProcessingStatus.FAILED, 0,
                    "Error while uploading file." + e.getMessage(),
                    hierarchyJsonArray,
                    ActionType.DATABASE_MIGRATION);
            throw new IllegalArgumentException(
                    "Failed to migrate database: " + fileName + " Error: " + e.getMessage());
        } finally {
            detachSqliteDatabase(sqliteDbName);
            detachPostgresDatabase();
            s3FileUploadService.deleteTempFile(tempFilePath);
            conn.close();
            LOG.info("Temporary file deleted: {}", tempFilePath);
        }

    }

    private Boolean mealsOrFitnessExists(String sqliteDbName) {
        int mealsAndFitnessRowCount = duckDsl.fetchOne(
                "SELECT DISTINCT COUNT(*) as count FROM " + sqliteDbName + ".participant_meal_fitness_data")
                .get("count", Integer.class);

        LOG.info("Number of meals: {}", mealsAndFitnessRowCount);
        return mealsAndFitnessRowCount > 0;
    }

    private List<String> setHierarchyArray(List<String> hierarchyJsonArray, JSONB subFileInteractionResponse)
            throws JsonMappingException, JsonProcessingException {
        LOG.info("Setting hierarchy for Interaction Response: {}", subFileInteractionResponse.data());

        JsonNode subFileInteractionResponseJson = mapper.readTree(subFileInteractionResponse.data());

        LOG.info("Hierarchy Array: {}", hierarchyJsonArray);

        // Ensure hierarchyJsonArray is mutable
        if (!(hierarchyJsonArray instanceof ArrayList)) {
            hierarchyJsonArray = new ArrayList<>(hierarchyJsonArray);
        }

        hierarchyJsonArray.add(subFileInteractionResponseJson.get("file_interaction_id").asText());

        LOG.info("Hierarchy Array: {}", hierarchyJsonArray);
        return hierarchyJsonArray;
    }

    private void detachSqliteDatabase(String sqliteDbName) {
        if (isDatabaseAttached(sqliteDbName)) {
            LOG.info("Detaching SQLite database");
            duckDsl.execute("DETACH " + sqliteDbName + ";");
            LOG.info("SQLite database detached");
        } else {
            LOG.info("SQLite database " + sqliteDbName + " is not attached. Skipping detachment.");
        }
    }

    private void detachPostgresDatabase() {
        try {
            LOG.info("Checking if Postgres database '{}' is attached", postgresDbName);

            // Check if the database is attached
            String query = "SHOW DATABASES;";
            List<String> attachedDatabases = duckDsl.fetch(query)
                    .getValues(0, String.class);

            if (attachedDatabases.contains(postgresDbName)) {
                LOG.info("Detaching Postgres database '{}'", postgresDbName);
                duckDsl.execute("DETACH DATABASE " + postgresDbName + ";");
            } else {
                LOG.info("Postgres database '{}' is not attached, skipping detach", postgresDbName);
            }
        } catch (Exception e) {
            LOG.error("Error while detaching Postgres database: {}", e.getMessage(), e);
        }
    }

    private boolean isDatabaseAttached(String sqliteDbName) {
        String checkQuery = "PRAGMA database_list;";
        List<Record> attachedDatabases = duckDsl.fetch(checkQuery);

        boolean isDatabaseAttached = false;

        // Check if the database is attached
        for (Record db : attachedDatabases) {
            if (db.get("name").equals(sqliteDbName)) {
                isDatabaseAttached = true;
                break;
            }
        }
        return isDatabaseAttached;
    }

    private void attachPostgresDatabase() {
        try {
            LOG.info("Attaching Postgres database as {}", postgresDbName);
            duckDsl.execute("DROP SCHEMA IF EXISTS " + postgresDbName + " CASCADE;");
            duckDsl.execute(
                    "ATTACH '' AS " + postgresDbName + " (TYPE POSTGRES);");
            LOG.info("PostgreSQL database attached as: {}", postgresDbName);
        } catch (Exception e) {
            LOG.error("Error while attaching Postgres database: {}", e.getMessage(), e);
        }
    }

    private void attachSqliteDatabase(String tempFilePath, String sqliteDbName) {
        LOG.info("Attaching SQLite database: {}", tempFilePath);
        duckDsl.execute("DROP SCHEMA IF EXISTS " + sqliteDbName + " CASCADE;");
        duckDsl.execute(
                " ATTACH '" + tempFilePath + "' AS " + sqliteDbName + " (TYPE SQLITE); ");
        LOG.info("SQLite database attached as: {}", sqliteDbName);
    }

    private String copyTablesFromSqLiteToPostgres(String studyId, String filePath, String distinctDbFileIds,
            JSONB dbData, DSLContext sqliteDsl, String sqliteDbName) {
        LOG.info("Copying tables from SQLite to Postgres");
        try {
            LOG.info("Distinct db_file_ids from SQLITE: {}", distinctDbFileIds);
            final var sqStudyDisplayId = duckDsl.fetchOne(
                    "SELECT DISTINCT study_display_id FROM " + sqliteDbName + ".participant")
                    .get("study_display_id", String.class);
            LOG.info("Study Display Id from SQLITE: {}", sqStudyDisplayId);
            final var pgStudyDisplayId = dsl
                    .selectDistinct(DSL.field("study_display_id", String.class))
                    .from("drh_stateless_research_study.research_study_view")
                    .where(DSL.field("study_id").eq(DSL.value(
                            studyId)))
                    .fetchOneInto(String.class);
            LOG.info("Study Display Id from POSTGRES: {}", pgStudyDisplayId);
            LOG.debug("dbFileId: {}", distinctDbFileIds);
            if (!(sqStudyDisplayId.toString()).equals(pgStudyDisplayId.toString())) {
                LOG.debug("Study display id mismatch between SQLite and Postgres, SQLite: {}, Postgres: {}",
                        sqStudyDisplayId, pgStudyDisplayId);
                return "Study display id mismatch between SQLite and Postgres";
            } else {
                if (!validateRequiredColumns(filePath, sqliteDsl)) {
                    LOG.error("The Sqlite Tables does not contain all the required fields");
                    return "The Sqlite Tables does not contain all the required fields";
                } else if (!validateParticipantData(filePath, sqliteDsl)) {
                    LOG.error("The Sqlite Table for Participant does not contain data for all the required fields");
                    return "The Sqlite Table for Participant does not contain data for all the required fields";
                } else if (!validateFileMetaIngestData(filePath, sqliteDsl)) {
                    LOG.error(
                            "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields");
                    return "The Sqlite Table for file_meta_ingest_data does not contain data for all the required fields";
                } else if (!validateParticipantMealFitnessData(filePath, sqliteDsl)) {
                    LOG.error(
                            "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields");
                    return "The Sqlite Table for participant_meal_fitness_data does not contain data for all the required fields";
                } else if (!validateStudyMetaData(filePath, sqliteDsl)) {
                    LOG.error(
                            "The Sqlite Table for study_meta_data does not contain data for all the required fields");
                    return "The Sqlite Table for study_meta_data does not contain data for all the required fields";
                } else {
                    LOG.info("Database is valid");

                    return "Proceed with Database Migration";

                }
            }

        } catch (Exception e) {
            LOG.error("Error while copying tables from SQLite to Postgres: {}",
                    e.getMessage(), e);
            throw new IllegalArgumentException("Failed to copy tables from SQLite to Postgres: " + e.getMessage());
        }

    }

    private JSONB migrateDatabase(String studyId, String filePath, String distinctDbFileIds,
            JSONB dbData, String hubInteractionId, DatabaseMigrationRequest request, String fileURL,
            String fileName, String contentType, String uploadTimestamp, String rootFileInteracionId,
            List<String> hierarchyJsonArray, String sqliteDbName)
            throws JsonProcessingException {
        try {
            duckDsl.execute("PRAGMA temp_directory='/tmp/';");
            duckDsl.execute("PRAGMA memory_limit='4GB';");
            duckDsl.execute("PRAGMA threads=2;");

            int batchSize = 10;
            String[] tables = { "study_meta_data", "participant", "file_meta_ingest_data",
                    "participant_meal_fitness_data"
            };
            LOG.info("Heirarchy : {}", hierarchyJsonArray);
            final var subFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                    request.studyId(), null,
                    "Starting to copy Database content.", JsonUtils.toJson(request),
                    null,
                    distinctDbFileIds, fileURL,
                    fileName,
                    contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                    dbData.data(),
                    uploadTimestamp, null, FileProcessingStatus.IN_PROGRESS, 0,
                    null,
                    hierarchyJsonArray,
                    ActionType.COPY_DUCK_DB);
            hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subFileInteractionResponse);
            for (String table : tables) {
                int offset = 0;
                int rowsInserted;
                int rowCount = duckDsl.fetchOne(
                        "SELECT DISTINCT COUNT(*) as count FROM " + sqliteDbName + "." + table)
                        .get("count", Integer.class);
                LOG.info("Copying {}, Number of records: {}", table, rowCount);
                if (rowCount >= 1) {
                    do {
                        String sql = "INSERT INTO " + postgresDbName + ".drh_stateful_db_import_migration." + table +
                                " SELECT * FROM " + sqliteDbName + "." + table +
                                " LIMIT " + batchSize + " OFFSET " + offset + ";";

                        LOG.info("Executing batch migration for table: {} (offset: {})", table, offset);
                        rowsInserted = duckDsl.execute(sql);
                        offset += batchSize;
                        LOG.info("Transferred {} rows to {}", rowsInserted, table);
                    } while (rowsInserted == batchSize);
                }
            }

            LOG.info("Tables copied from SQLite to Postgres");

            LOG.info("Fetching db_file_id from Postgres");
            Boolean exists = dsl.select(DSL.field("exists(" +
                    "select 1 from drh_stateful_db_import_migration.participant " +
                    "where db_file_id = {0})", Boolean.class, DSL.val(distinctDbFileIds)))
                    .fetchOneInto(Boolean.class);
            LOG.debug("Migrated Records availablity status from Postgres: {}", exists);
            if (!exists) {
                LOG.error(
                        "Failed to Copy SQLITE DB File Content to Postgres DB");

                LOG.info("Heirarchy : {}", hierarchyJsonArray);
                final var subInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Failed to copy data base content.", JsonUtils.toJson(request),
                        "Failed to Copy SQLITE DB File Content to Postgres DB",
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.FAILURE,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                        "Failed to Copy SQLITE DB File Content to Postgres DB",
                        hierarchyJsonArray,
                        ActionType.COPY_DUCK_DB);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, subInteractionResponse);
                return JSONB.valueOf(
                        "{\"message\": \"Failed to Copy SQLITE DB File Content to Postgres DB\",\"status\":\"failed\"}");
            } else {
                LOG.error("Copied SQLITE DB File Content to Postgres DB Table");

                final var saveFileInteractionResponse = interactionService.saveFileInteraction(hubInteractionId,
                        request.studyId(), null,
                        "Copied data for processing.", JsonUtils.toJson(request),
                        null,
                        distinctDbFileIds, fileURL,
                        fileName,
                        contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                        dbData.data(),
                        uploadTimestamp, null, FileProcessingStatus.SUCCESS, 0,
                        null, hierarchyJsonArray,
                        ActionType.COPY_DUCK_DB);
                hierarchyJsonArray = setHierarchyArray(hierarchyJsonArray, saveFileInteractionResponse);
                JsonNode fileInteractionResponseJson = mapper.readTree(saveFileInteractionResponse.data());
                final var fileFileUploadStatusStatus = fileInteractionResponseJson.get("status").asText();
                if (fileFileUploadStatusStatus.equals("success")) {
                    // final var fileInteractionId =
                    // fileInteractionResponseJson.get("file_interaction_id").asText();
                    LOG.info("Heirarchy : {}", hierarchyJsonArray);

                    LOG.info("Interaction Hierarchy: {}", hierarchyJsonArray);
                    // Convert existing JSONB to Map
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dbDataMap = mapper.readValue(dbData.data(), Map.class);

                    // Add new key-value pair
                    dbDataMap.put("file_interaction_id", rootFileInteracionId);
                    dbDataMap.put("interaction_hierarchy", hierarchyJsonArray);
                    String lastFileInteractionId = hierarchyJsonArray.isEmpty() ? null
                            : hierarchyJsonArray.get(hierarchyJsonArray.size() - 1);
                    LOG.info("Last File interaction Id : {}", lastFileInteractionId);
                    dbDataMap.put("last_file_interaction_id", lastFileInteractionId);
                    String activityData = activityLogService.prepareActivityLogMetadata();

                    // Convert back to JSONB
                    dbData = JSONB.valueOf(mapper.writeValueAsString(dbDataMap));
                    final var query = dsl
                            .select(DSL.field(
                                    "drh_stateless_raw_observation.insert_cgm_raw_db ({0} ,{1})",
                                    String.class,
                                    DSL.val(dbData),
                                    DSL.cast(DSL.val(activityData), JSONB.class)));
                    LOG.info("Save Data from database Query: {}", query);
                    JSONB response = query.fetchOneInto(JSONB.class);
                    LOG.info("Response from database Query: {}", response);

                    JsonNode jsonNode = mapper.readTree(response.data());
                    final var message = jsonNode.get("message").asText();
                    final var status = jsonNode.get("status").asText();

                    if (status.equals("success")) {
                        LOG.info("Response message: {}", message);
                        LOG.info("File copied to Postgres");
                        LOG.info("Successfully copied tables from SQLite to Postgres");

                        LOG.info("Successfully migrated database");
                        LOG.info("Complete Response: {}", response.data());

                        return response;

                    } else {
                        LOG.info("Response message: {}", message);
                        LOG.info("Failed to copy database");
                        LOG.info("Complete Response: {}", response.data());
                        return JSONB.valueOf(
                                "{\"message\": \"Failed to Copy SQLITE DB File Content to Postgres DB\",\"status\":\"failed\"}");

                    }

                }
                return JSONB.valueOf(
                        "{\"message\": \"Failed to Copy SQLITE DB File Content to Postgres DB\",\"status\":\"failed\"}");

            }

        } catch (Exception e) {
            LOG.error("Error while migrating database: {}", e.getMessage(), e);
            interactionService.saveFileInteraction(hubInteractionId, request.studyId(), null,
                    "Failed to copy data base content.", JsonUtils.toJson(request),
                    "{\"error\": \"Failed to migrate database\",\\\"status\\\":\\\"error\\\"}",
                    distinctDbFileIds, fileURL,
                    fileName,
                    contentType, null, FileType.DATABASE, FileUploadStatusStatus.ERROR,
                    dbData.data(),
                    uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                    "Failed to copy data base content.", null, ActionType.DATABASE_MIGRATION);
            return JSONB.valueOf("{\"error\": \"Failed to migrate database\",\\\"status\\\":\\\"error\\\"}");
        }
    }

    private String prepareJson(String fileId, String fileName, String fileURL, String uploadTimestamp, long fileSize,
            String studyId, String userPartyId, String organizationPartyId) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode jsonObject = objectMapper.createObjectNode();
        jsonObject.put("db_file_id", fileId);
        jsonObject.put("file_name", fileName);
        jsonObject.put("file_url", fileURL);
        jsonObject.put("upload_timestamp", uploadTimestamp);
        jsonObject.put("uploaded_by", userPartyId);
        jsonObject.put("file_size", fileSize);
        jsonObject.put("study_id", studyId);
        jsonObject.put("org_party_id", organizationPartyId);
        jsonObject.put("current_user_id", userPartyId);
        return jsonObject.toString();
    }

    private boolean exists(String studyd) {
        return dsl.fetchExists(
                dsl.selectOne().from("drh_stateless_raw_data.cgm_raw_db_view").where("study_id = ?", studyd));
    }

    public Pair<DSLContext, Connection> createSQLiteDSL(String sqliteFilePath) throws Exception {
        String url = "jdbc:sqlite:" + sqliteFilePath;
        LOG.info("SQLite Database URL: {}", url);
        Connection conn = DriverManager.getConnection(url);
        DSLContext dslContext = DSL.using(conn, SQLDialect.SQLITE);
        return Pair.of(dslContext, conn);
    }

    public boolean validateRequiredColumns(String filePath, DSLContext sqliteDsl) throws Exception {
        // Define required columns for both tables
        Map<String, Set<String>> tableColumnsMap = Map.of(
                "file_meta_ingest_data",
                Set.of("file_meta_id", "db_file_id", "participant_display_id", "file_meta_data", "cgm_data"),
                "participant",
                Set.of("db_file_id", "tenant_id", "study_display_id", "participant_display_id", "site_id",
                        "diagnosis_icd", "med_rxnorm", "treatment_modality", "gender", "race_ethnicity", "age",
                        "bmi", "baseline_hba1c", "diabetes_type", "study_arm"),
                "participant_meal_fitness_data",
                Set.of("db_file_id", "tenant_id", "study_display_id", "participant_display_id", "fitness_meal_id",
                        "meal_data", "fitness_data"),
                "study_meta_data",
                Set.of("db_file_id", "study_meta_id", "tenant_id", "study_display_id", "study_name", "start_date",
                        "end_date", "treatment_modalities", "funding_source", "nct_number", "study_description",
                        "investigators", "publications", "authors", "institutions", "labs", "sites", "elaboration"));
        // Validate each table
        for (Map.Entry<String, Set<String>> entry : tableColumnsMap.entrySet()) {
            String tableName = entry.getKey();
            Set<String> requiredColumns = entry.getValue();

            String query = "PRAGMA table_info(" + tableName + ")";
            Result<Record> result = sqliteDsl.fetch(query);
            LOG.info("Record of Table : {} {}", tableName, result);

            // Extract column names
            Set<String> existingColumns = new HashSet<>();
            LOG.info("Reading existing columns of table {}", tableName);
            for (Record record : result) {
                LOG.info("Column : {}.{}", tableName, record.get("name", String.class));
                existingColumns.add(record.get("name", String.class));
            }

            // If any table is missing required columns, return false
            if (!existingColumns.containsAll(requiredColumns)) {
                Set<String> missingColumns = new HashSet<>(requiredColumns);
                missingColumns.removeAll(existingColumns); // Get missing columns

                LOG.warn("Missing required columns: {} of Table: {}", missingColumns, tableName);
                LOG.debug("Table: {}, Existing columns: {}", tableName, existingColumns);
                LOG.debug("Table: {},Required columns: {}", tableName, requiredColumns);
                LOG.debug("Table: {},Missing columns: {}", tableName, missingColumns);
                return false;
            }
        }

        return true; // Both tables have all required columns
    }

    public boolean validateParticipantData(String filePath, DSLContext sqliteDsl) throws Exception {

        // Query to check if any row has NULL or empty values in required columns
        String query = "SELECT COUNT(*) FROM participant " +
                "WHERE participant_display_id IS NULL OR TRIM(participant_display_id) = '' " +
                "OR age IS NULL OR TRIM(age) = '' " +
                "OR gender IS NULL OR TRIM(gender) = ''";
        LOG.info("Query to validate Participant data :", query);
        Integer count = sqliteDsl.fetchOne(query).into(Integer.class);

        if (count > 0) {
            LOG.debug(
                    "{} rows in the participant table have missing mandatory data (participant_display_id, age, gender).",
                    count);
            return false;
        }

        return true;
    }

    public boolean validateParticipantMealFitnessData(String filePath, DSLContext sqliteDsl) throws Exception {

        // Query to check if any row has NULL or empty values in required columns
        String query = "SELECT COUNT(*) FROM participant_meal_fitness_data " +
                "WHERE participant_display_id IS NULL OR TRIM(participant_display_id) = '' " +
                "OR db_file_id IS NULL OR TRIM(db_file_id) = '' " +
                "OR study_display_id IS NULL OR TRIM(study_display_id) = ''" +
                "OR ((meal_data IS NULL OR TRIM(meal_data) = '' OR meal_data = '{}'OR meal_data = '{[]}' OR meal_data = '[]')"
                +
                " AND(fitness_data IS NULL OR TRIM(fitness_data) = '' OR fitness_data = '{}' OR fitness_data = '{[]}' OR fitness_data = '[]'))";
        LOG.info("Query yo validate Participant Meal Fitness Data: {}", query);
        Integer count = sqliteDsl.fetchOne(query).into(Integer.class);

        if (count > 0) {
            LOG.debug(
                    "{} rows in the participant_meal_fitness_data table have missing mandatory data (db_file_id,participant_display_id, meal_data or fitness_data).",
                    count);
            return false;
        }

        return true;
    }

    public boolean validateStudyMetaData(String filePath, DSLContext sqliteDsl) throws Exception {

        // Query to check if any row has NULL or empty values in required columns
        String query = "SELECT COUNT(*) FROM study_meta_data " +
                "WHERE study_name IS NULL OR TRIM(study_name) = '' " +
                "OR db_file_id IS NULL OR TRIM(db_file_id) = '' " +
                "OR study_display_id IS NULL OR TRIM(study_display_id) = ''" +
                "OR study_description IS NULL OR TRIM(study_description) = ''";
        LOG.info("Query yo validate Studt Metadate: {}", query);
        Integer count = sqliteDsl.fetchOne(query).into(Integer.class);

        if (count > 0) {
            LOG.debug(
                    "{} rows in the study_meta_data table have missing mandatory data (db_file_id,study_name, study_display_id, study_description ).",
                    count);
            return false;
        }

        return true;
    }

    public String uploadChunk(MultipartFile file,
            int chunkNumber,
            int totalChunks,
            String fileName) throws IOException {
        LOG.info("Uploading chunk: {} of {} for file: {}", chunkNumber, totalChunks, fileName);
        // Create upload directory if not exists
        Path uploadPath = Paths.get(tempFileLocation);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        // Save chunk
        File chunkFile = new File(tempFileLocation + fileName + ".part" + chunkNumber);
        file.transferTo(chunkFile);

        // Track uploaded chunks
        uploadedChunks.put(fileName, uploadedChunks.getOrDefault(fileName, 0) + 1);

        // Check if all chunks are uploaded
        if (uploadedChunks.get(fileName) == totalChunks) {
            mergeChunks(fileName, totalChunks);
            uploadedChunks.remove(fileName);
            LOG.info("All chunks uploaded for file: {}", fileName);
            return "Upload completed!";
        }

        return "Chunk " + chunkNumber + " uploaded successfully!";
    }

    private void mergeChunks(String fileName, int totalChunks) throws IOException {
        LOG.info("Merging chunks for file: {}", fileName);
        File mergedFile = new File(tempFileLocation + fileName);
        try (FileOutputStream outputStream = new FileOutputStream(mergedFile, true)) {
            for (int i = 1; i <= totalChunks; i++) {
                File chunkFile = new File(tempFileLocation + fileName + ".part" + i);
                Files.copy(chunkFile.toPath(), outputStream);
                chunkFile.delete(); // Remove chunk after merging
            }
        }
    }

    public boolean validateFileMetaIngestData(String filePath, DSLContext sqliteDsl) throws Exception {
        // Query to check if any mandatory field is NULL or empty
        String query = """
                SELECT COUNT(*) FROM file_meta_ingest_data
                WHERE file_meta_id IS NULL OR TRIM(file_meta_id) = ''
                OR db_file_id IS NULL OR TRIM(db_file_id) = ''
                OR participant_display_id IS NULL OR TRIM(participant_display_id) = ''
                OR file_meta_data IS NULL OR TRIM(file_meta_data) = ''
                OR cgm_data IS NULL OR TRIM(cgm_data) = ''
                """;

        Integer count = sqliteDsl.fetchOne(query).into(Integer.class);
        if (count > 0) {
            LOG.debug("{} rows in file_meta_ingest have missing mandatory data.", count);
            return false;
        }

        // Check for empty JSON or JSON arrays in cgm_data
        String cgmQuery = """
                SELECT COUNT(*) FROM file_meta_ingest_data
                WHERE json_valid(cgm_data) = 0
                OR cgm_data = '{}'
                OR cgm_data = '[]'
                OR cgm_data = '[{}]'
                """;

        Integer cgmCount = sqliteDsl.fetchOne(cgmQuery).into(Integer.class);
        if (cgmCount > 0) {
            LOG.debug("{} rows have empty or invalid JSON in cgm_data.", cgmCount);
            return false;
        }

        // Check for empty JSON or JSON arrays in file_meta_data
        String fileMetaQuery = """
                SELECT COUNT(*) FROM file_meta_ingest_data
                WHERE json_valid(file_meta_data) = 0
                OR file_meta_data = '{}'
                OR file_meta_data = '[]'
                OR file_meta_data = '[{}]'
                """;

        Integer fileMetaCount = sqliteDsl.fetchOne(fileMetaQuery).into(Integer.class);
        if (fileMetaCount > 0) {
            LOG.debug("{} rows have empty or invalid JSON in file_meta_data.", fileMetaCount);
            return false;
        }

        // Check if map_field_of_cgm_date and map_field_of_cgm_value exist and are not
        // empty
        String jsonFieldQuery = """
                SELECT COUNT(*) FROM file_meta_ingest_data
                WHERE json_extract(file_meta_data, '$.map_field_of_cgm_date') IS NULL
                OR TRIM(json_extract(file_meta_data, '$.map_field_of_cgm_date')) = ''
                OR json_extract(file_meta_data, '$.map_field_of_cgm_value') IS NULL
                OR TRIM(json_extract(file_meta_data, '$.map_field_of_cgm_value')) = ''
                """;

        Integer jsonFieldCount = sqliteDsl.fetchOne(jsonFieldQuery).into(Integer.class);
        if (jsonFieldCount > 0) {
            LOG.debug("{} rows have missing or empty required JSON fields in file_meta_data.", jsonFieldCount);
            return false;
        }

        return true;
    }

    public boolean isCompletedCgmRowData(String studyId) {
        LOG.info("Check CGM Row Data Copy status");
        final var exists = interactionService.isCompletedCgmRowData(studyId);
        LOG.info("Check if DB File Interaction Exists Query: {}", exists);
        return exists;
    }

    public Map<String, Object> isCompletedDataExtraction(String studyId) {
        LOG.info("Check Data Migration status");
        try {
            Map<String, Object> statusMap = new HashMap<>();
            final var studyMetaDataexists = interactionService
                    .isDbFileInteractionFinishedForAction(studyId, ActionType.STUDY_METADATA_MIGRATION);
            LOG.info("Check if DB File Interaction Completed Study MetaData: {}", studyMetaDataexists);
            statusMap.put("studyMetaData", studyMetaDataexists);
            final var participantDataexists = interactionService
                    .isDbFileInteractionFinishedForAction(studyId, ActionType.PARTICIPANT_MIGRATION);
            LOG.info("Check if DB File Interaction Completed Participant: {}", participantDataexists);
            statusMap.put("participant", participantDataexists);
            final var cgmDataexists = interactionService.isDbFileInteractionFinishedForAction(
                    studyId, ActionType.CGM_MIGRATION);
            LOG.info("Check if DB File Interaction Completed CGM: {}", cgmDataexists);
            statusMap.put("cgm", cgmDataexists);
            final var mealsAndFitnesexists = interactionService.isMealsOrFitnessInteractionExist(studyId);
            LOG.info("Check if DB File Interaction Completed Participant Meals and Fitness Data: {}",
                    mealsAndFitnesexists);
            statusMap.put("mealsAndFitnes", mealsAndFitnesexists);
            return statusMap;
        } catch (Exception e) {
            LOG.error("Error in isCompletedDataExtraction", e);
            Map<String, Object> errorStatus = new HashMap<>();
            errorStatus.put("error", "Failed to read Migration Status");
            return errorStatus;
        }
    }

    @Async("asyncTaskExecutor")
    public CompletableFuture<Void> startExtractionNew(String studyId) {
        LOG.info("Starting Data Extraction for study with Id: {}", studyId);
        startExtraction(studyId);
        return CompletableFuture.completedFuture(null);
    }

    public String startExtraction(String studyId) {
        try {
            LOG.info("interactionService: {}", interactionService);
            LOG.info("interactionService class: {}", interactionService.getClass());
            // LOG.info("dsl: {}", dsl);
            // LOG.info("dsl class: {}", dsl.getClass());
            LOG.info("Thread: {}", Thread.currentThread().getName());
            LOG.debug("Starting Data Extraction for study with Id: {}", studyId);
            // LOG.info("DSLContext is {}", dsl.configuration().dialect());
            String saveDBInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.SAVE_DB_CONTENT, studyId);
            final var dbFileId = interactionService.getDbFileIdOfCompletedCgmRowData(studyId);
            LOG.info("File Interaction Id of Completed CGM Row Data : {}", saveDBInteractionId);
            boolean studyMetaDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.STUDY_METADATA_MIGRATION);
            LOG.info("Check if DB File Interaction Completed Study MetaData: {}", studyMetaDataExists);
            String studyMetaDataInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.STUDY_METADATA_MIGRATION,
                            studyId);
            String status = null;
            if (!studyMetaDataExists) {
                LOG.debug("Starting Study Metadata Extraction");
                final var query = dsl
                        .select(DSL.field(
                                "drh_stateless_db_import_migration.migrate_study_meta_data ({0},{1})",
                                String.class,
                                DSL.val(dbFileId), DSL.val(saveDBInteractionId)));
                LOG.info("Extract Study MetaData from database Query: {}", query);
                JSONB response = query.fetchOneInto(JSONB.class);
                LOG.info("Study Metadata, Query: {} \n Response: {}", query, response);
                JsonNode jsonNode = mapper.readTree(response.data());
                status = jsonNode.get("status").asText();
                studyMetaDataInteractionId = jsonNode.has("file_interaction_id")
                        ? jsonNode.get("file_interaction_id").asText()
                        : null;
                LOG.debug("Study Metadata Extraction Finished");
            }
            studyMetaDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.STUDY_METADATA_MIGRATION);
            LOG.info("Check if DB File Interaction Completed Study MetaData: {}",
                    studyMetaDataExists);
            String participantInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.PARTICIPANT_MIGRATION,
                            studyId);
            if (studyMetaDataExists && participantInteractionId == null
                    || (studyMetaDataInteractionId != null && status != null && status.equalsIgnoreCase("success"))) {
                LOG.debug("Starting Participant Extraction");
                final var query = dsl
                        .select(DSL.field(
                                "drh_stateless_db_import_migration.migrate_all_participants ({0},{1})",
                                String.class,
                                DSL.val(dbFileId), DSL.val(studyMetaDataInteractionId)));
                LOG.info("Extract participant Data from database Query: {}", query);
                JSONB response = query.fetchOneInto(JSONB.class);
                LOG.info("Participant, Query: {} \n Response: {}", query, response);
                JsonNode jsonNode = mapper.readTree(response.data());
                status = jsonNode.get("status").asText();
                participantInteractionId = jsonNode.has("file_interaction_id")
                        ? jsonNode.get("file_interaction_id").asText()
                        : null;
                LOG.debug("Participant Extraction Finished");

            }

            final var participantDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.PARTICIPANT_MIGRATION);
            String participantCgmInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.CGM_MIGRATION,
                            studyId);
            if (participantDataExists && participantCgmInteractionId == null
                    || (participantInteractionId != null && status != null && status.equalsIgnoreCase("success"))) {
                LOG.debug("Starting CGM Extraction");
                final var query = dsl
                        .select(DSL.field(
                                "drh_stateless_db_import_migration.migrate_all_cgm_data ({0},{1})",
                                String.class,
                                DSL.val(dbFileId), DSL.val(participantInteractionId)));
                LOG.info("Extract CGM Data from database Query: {}", query);
                JSONB response = query.fetchOneInto(JSONB.class);
                LOG.info("CGM, Query: {} \n Response: {}", query, response);
                JsonNode jsonNode = mapper.readTree(response.data());
                status = jsonNode.get("status").asText();
                participantCgmInteractionId = jsonNode.has("file_interaction_id")
                        ? jsonNode.get("file_interaction_id").asText()
                        : null;
                LOG.debug("CGM Extraction Finished");
            }
            final var participantCgmDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.CGM_MIGRATION);

            if (participantCgmDataExists) {
                final String sql = "CALL drh_stateless_db_import_migration.load_cgm_partitions(?, ?)";
                LOG.debug("CGM Partition Loading Started. Query: {}", sql);
                dsl.execute(sql, studyId, participantCgmInteractionId);
                LOG.info("Called procedure load_cgm_partitions with studyId: {}", studyId);
                LOG.debug("CGM Partition Loading Finished");
            }
            final var partitionCgmDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.CGM_PARTITION_MIGRATION);
            String partitionCgmInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.CGM_PARTITION_MIGRATION, studyId);
            String participantMealsInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.MEAL_MIGRATION,
                            studyId);
            LOG.info("Fetching count of meals data from table");
            Boolean mealsDataExists = dsl.select(DSL.field("exists(" +
                    "select 1 from drh_stateful_db_import_migration.participant_meal_fitness_data " +
                    "where db_file_id = {0}  and meal_data is not null and meal_data <> '')", Boolean.class,
                    DSL.val(
                            dbFileId)))
                    .fetchOneInto(Boolean.class);
            LOG.info("Meals Data Exists: {}", mealsDataExists);
            if (mealsDataExists && ((partitionCgmDataExists && participantMealsInteractionId == null)
                    || (partitionCgmInteractionId != null && status != null && status.equalsIgnoreCase("success")))) {

                LOG.debug("Starting Meals Extraction");
                final var query = dsl
                        .select(DSL.field(
                                "drh_stateless_db_import_migration.migrate_all_meal_data ({0},{1})",
                                String.class,
                                DSL.val(dbFileId), DSL.val(partitionCgmInteractionId)));
                LOG.info("Extract Meals Data from database Query: {}", query);
                JSONB response = query.fetchOneInto(JSONB.class);
                LOG.info("Meals, Query: {} \n Response: {}", query, response);
                JsonNode jsonNode = mapper.readTree(response.data());
                status = jsonNode.get("status").asText();
                participantMealsInteractionId = jsonNode.has("file_interaction_id")
                        ? jsonNode.get("file_interaction_id").asText()
                        : null;
                LOG.debug("Meals Extraction Finished");

            }
            final var participantMealDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.MEAL_MIGRATION);

            if (participantMealDataExists) {
                final String sql = "CALL drh_stateless_db_import_migration.load_meal_partitions(?, ?)";
                LOG.debug("Meal Partition Loading Started. Query: {}", sql);
                dsl.execute(sql, studyId, participantMealsInteractionId);
                LOG.info("Called procedure load_meal_partitions with studyId: {}", studyId);
                LOG.debug("Meal Partition Loading Finished");
            }
            final var partitionMealDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.MEAL_PARTITION_MIGRATION);
            String partitionMealInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.MEAL_PARTITION_MIGRATION, studyId);
            String participantFitnessInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.FITNESS_MIGRATION,
                            studyId);
            Boolean fitnessDataExists = dsl.select(DSL.field("exists(" +
                    "select 1 from drh_stateful_db_import_migration.participant_meal_fitness_data " +
                    "where db_file_id = {0}  and meal_data is not null and fitness_data <> '')", Boolean.class,
                    DSL.val(
                            dbFileId)))
                    .fetchOneInto(Boolean.class);
            LOG.info("Fitness Data Exists: {}", fitnessDataExists);
            if ((fitnessDataExists && participantFitnessInteractionId == null && status != null
                    && status.equalsIgnoreCase("success"))
                    && ((mealsDataExists && partitionMealDataExists && partitionMealInteractionId != null)
                            || (!mealsDataExists && partitionCgmDataExists && partitionCgmInteractionId != null))) {
                LOG.debug("Starting Fitness Extraction");
                final var query = dsl
                        .select(DSL.field(
                                "drh_stateless_db_import_migration.migrate_all_fitness_data ({0},{1})",
                                String.class,
                                DSL.val(dbFileId), DSL.val(partitionMealInteractionId)));
                LOG.info("Extract Fitness Data from database Query: {}", query);
                JSONB response = query.fetchOneInto(JSONB.class);
                LOG.info("Fitness, Query: {} \n Response: {}", query, response);
                JsonNode jsonNode = mapper.readTree(response.data());
                status = jsonNode.get("status").asText();
                participantFitnessInteractionId = jsonNode.has("file_interaction_id")
                        ? jsonNode.get("file_interaction_id").asText()
                        : null;
                LOG.debug("Fitness Extraction Finished");

            }
            final var participantFitnessDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.FITNESS_MIGRATION);

            if (participantFitnessDataExists) {
                final String sql = "CALL drh_stateless_db_import_migration.load_fitness_partitions(?, ?)";
                LOG.debug("Fitness Partition Loading Started. Query: {}", sql);
                dsl.execute(sql, studyId, participantFitnessInteractionId);
                LOG.info("Called procedure load_fitness_partitions with studyId: {}", studyId);
                LOG.debug("Fitness Partition Loading Finished");
            }
            final var partitionFitnessDataExists = interactionService.isDbFileInteractionFinishedForAction(studyId,
                    ActionType.FITNESS_PARTITION_MIGRATION);
            final var partitionFitnessInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.FITNESS_PARTITION_MIGRATION, studyId);
            if (partitionFitnessDataExists) {
                cleanUpDatabase(dbFileId, partitionFitnessInteractionId);
                LOG.info("Completed Fitness Partition.");
            } else {
                final var cgmPartitionCompleted = interactionService.isDbFileInteractionFinishedForAction(studyId,
                        ActionType.CGM_PARTITION_MIGRATION);
                if (!fitnessDataExists && !mealsDataExists && cgmPartitionCompleted) {
                    LOG.info("No Fitness and Meals Data Exists.");
                    if (partitionCgmInteractionId != null) {
                        cleanUpDatabase(dbFileId, partitionCgmInteractionId);
                    } else {
                        LOG.info("No Cleanup Required.");
                    }
                } else if (mealsDataExists && !fitnessDataExists && partitionMealDataExists) {
                    LOG.info("Meals Data Exists.");
                    if (partitionMealInteractionId != null) {
                        cleanUpDatabase(dbFileId, partitionMealInteractionId);
                    } else {
                        LOG.info("No Cleanup Required.");
                    }
                } else {
                    LOG.info(
                            "Fitness or Meals Data Extraction occurred some issue. So the clean up cannot be proceeded.");
                }

            }
            if (status == null) {
                LOG.info("Extraction not initiated");
                return "Extraction not initiated";
            } else if (status.equalsIgnoreCase("success")) {
                LOG.info("Extraction Completed");
                return "Extraction Completed";
            } else {
                LOG.info("Extraction Failed");
                return "Extraction Failed";
            }
        } catch (Exception e) {
            final var lastInteractionId = interactionService
                    .getSuccessDbFileInteractionIdOfActionType(ActionType.SAVE_DB_CONTENT, studyId);

            extractAndSaveInteraction(lastInteractionId);
            LOG.error("Error in startExtraction: {}", e.getMessage(), e);
            return "Extraction Failed";
        }

    }

    private void cleanUpDatabase(String dbFileId, String lastInteractionId) {
        final var query = dsl
                .select(DSL.field(
                        "drh_stateless_db_import_migration.cleanup_db_upload_entries ({0},{1})",
                        String.class,
                        DSL.val(dbFileId), DSL.val(lastInteractionId)));

        LOG.info("Cleanup DB Upload Entries Query: {}", query);
        query.execute();
        LOG.info("Cleanup DB Upload Entries executed successfully.");
    }

    private void extractAndSaveInteraction(String lastInteractionId) {
        try {
            JSONB lastInteractionResponse = interactionService.getLastInteractionLog(lastInteractionId);
            List<String> hierarchyJsonArray = interactionService
                    .getAndSetInteractionHierarchyFromInteractionLog(lastInteractionResponse, lastInteractionId);
            JsonNode lastFileInteractionResponseJson;

            lastFileInteractionResponseJson = mapper.readTree(lastInteractionResponse.data());
            final var hubInteractionId = lastFileInteractionResponseJson.get("hub_interaction_id").asText();
            final var request = lastFileInteractionResponseJson.get("request").asText();
            final var distinctDbFileIds = lastFileInteractionResponseJson.get("db_file_id").asText();
            final var contentType = lastFileInteractionResponseJson.get("file_content_type").asText();
            final var fileURL = lastFileInteractionResponseJson.get("file_location").asText();
            final var fileName = lastFileInteractionResponseJson.get("file_name").asText();
            final var uploadTimestamp = lastFileInteractionResponseJson.get("file_processing_initiated_at").asText();
            final var studyId = lastFileInteractionResponseJson.get("study_id").asText();
            LOG.info("Extracting and Saving Interaction for Hub Interaction Id: {}", hubInteractionId);
            LOG.info("Extracting and Saving Interaction for Study Id: {}", studyId);
            LOG.info("Extracting and Saving Interaction for Request: {}", request);
            LOG.info("Extracting and Saving Interaction for Distinct DB File Ids: {}", distinctDbFileIds);
            LOG.info("Extracting and Saving Interaction for File URL: {}", fileURL);
            LOG.info("Extracting and Saving Interaction for Original File Name: {}", fileName);
            LOG.info("Extracting and Saving Interaction for Content Type: {}", contentType);
            LOG.info("Hierarchy : {}", hierarchyJsonArray);
            LOG.info("Extracting and Saving Interaction for Upload Timestamp: {}", uploadTimestamp);

            interactionService
                    .saveFileInteraction(hubInteractionId, studyId, null,
                            "Failed to Extract CGM Data.", JsonUtils.toJson(request), null,
                            distinctDbFileIds, fileURL,
                            fileName,
                            contentType, null, FileType.DATABASE, FileUploadStatusStatus.SUCCESS,
                            null,
                            uploadTimestamp, null, FileProcessingStatus.FAILED, 0,
                            "Failed to Extract CGM Data.",
                            hierarchyJsonArray,
                            ActionType.CONTENT_VERIFICATION);

        } catch (JsonProcessingException e) {
            LOG.error("Error in extractAndSaveInteraction", e);
            e.printStackTrace();
        }

    }

    public Boolean isMealsAndMigrationDataStatusAvailable(String studyId) {
        LOG.info("Check Meals and Fitness Data availability status");
        final var mealsAndFitnessDataExists = mealsAndFitnessDataExists(studyId);
        final var mealsAndFitnessInteractionExists = interactionService.isMealsOrFitnessInteractionExist(studyId);
        LOG.info("Check if meals and fitness Data Exists Query: {}", mealsAndFitnessDataExists);
        LOG.info("Check if Meal Interaction Exists Query: {}", mealsAndFitnessInteractionExists);

        if (mealsAndFitnessDataExists || mealsAndFitnessInteractionExists) {
            LOG.info("Meals and Fitness Data Exists");
            return true;
        } else {
            LOG.info("Meals and Fitness Data does not Exists");
            return false;
        }

    }

    private Boolean mealsAndFitnessDataExists(String studyId) {
        final var dbFileId = interactionService.getDbFileIdOfCompletedCgmRowData(studyId);
        Boolean mealsDataExists = dsl.select(DSL.field("exists(" +
                "select 1 from drh_stateful_db_import_migration.participant_meal_fitness_data " +
                "where db_file_id = {0}  and meal_data is not null and meal_data <> '')", Boolean.class,
                DSL.val(
                        dbFileId)))
                .fetchOneInto(Boolean.class);
        return mealsDataExists;
    }

}
