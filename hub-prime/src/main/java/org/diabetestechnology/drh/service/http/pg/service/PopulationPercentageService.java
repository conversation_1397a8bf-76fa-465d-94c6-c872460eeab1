package org.diabetestechnology.drh.service.http.pg.service;

import java.text.DecimalFormat;
import java.util.concurrent.CompletableFuture;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PopulationPercentageService {

    private static final Logger LOG = LoggerFactory.getLogger(PopulationPercentageService.class);
    private static final String TABLE_NAME = "drh_stateless_research_study.population_percentage_metrics_view";

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;

    public PopulationPercentageService(@Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, PartyService partyService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
    }

    @Transactional
    public CompletableFuture<Integer> getTotalParticipantsWithData() {
        final var userId = userNameService.getUserId();
        final var createdBy = partyService.getPartyIdByUserId(userId);
        final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
        final var totalParticipantsWithData = fetchSum("total_participants_with_data",
                "Fetching total participants with data", createdBy,
                organizationPartyId);
        LOG.info("getTotalParticipantsWithData: {}", totalParticipantsWithData);
        return totalParticipantsWithData;
    }

    @Transactional
    public CompletableFuture<Integer> getTotalCgmFileCount() {
        final var userId = userNameService.getUserId();
        final var createdBy = partyService.getPartyIdByUserId(userId);
        final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
        LOG.info("getTotalCgmFileCount: {}", userId);
        final var totalCgmFileCount = fetchSum("total_cgm_files", "Fetching total CGM file count", createdBy,
                organizationPartyId);
        LOG.info("getTotalCgmFileCount: {}", totalCgmFileCount);
        return totalCgmFileCount;
    }

    @Transactional
    public Object getTotalDataPoints() throws Exception {
        final var userId = userNameService.getUserId();
        final var createdBy = partyService.getPartyIdByUserId(userId);
        final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
        LOG.info("getTotalDataPoints: {}", userId);

        Integer dataPoints = fetchSum("data_points", "Fetching total data points", createdBy, organizationPartyId)
                .get();
        LOG.info("getTotalDataPoints: {}", dataPoints);
        final var formatedDataPoint = formatDataPoints(dataPoints);
        LOG.info("getTotalDataPoints: {}", formatedDataPoint);
        return formatedDataPoint;
    }

    @Async
    private CompletableFuture<Integer> fetchSum(String fieldName, String logMessage, String createdBy,
            String organizationPartyId) {
        LOG.info("fetchSum: {}", fieldName);
        return CompletableFuture
                .supplyAsync(() -> {
                    try {
                        LOG.info(logMessage);
                        var query = dsl
                                .select(DSL.coalesce(DSL.sum(DSL.field(fieldName, Integer.class)), 0))
                                .from(DSL.table(TABLE_NAME))
                                .where(DSL.field("visibility_name", String.class).eq("Public")
                                        .or(DSL.field("organization_party_id", String.class)
                                                .eq(organizationPartyId))
                                        .or(DSL.field("visibility_name", String.class).eq("Private")
                                                .and(DSL.field("created_by", String.class).eq(createdBy))))
                                .and(DSL.field("deleted_at").isNull())
                                .and(DSL.field("rec_status_id", Integer.class).eq(1))
                                .and(DSL.field("visibility_name", String.class).ne("Private")
                                        .or(DSL.field("created_by", String.class).eq(createdBy)));

                        LOG.info("Executing Query for {}: {}", logMessage, query);
                        return query.fetchOneInto(Integer.class);
                    } catch (Exception e) {
                        LOG.error("Error executing query for {}: {}", fieldName, e.getMessage(), e);
                        return 0;
                    }
                }).exceptionally(e -> {
                    LOG.error("Error executing query for {}: {}", fieldName, e.getMessage(), e);
                    return 0;
                });
    }

    private String formatDataPoints(Integer dataPoints) {
        LOG.info("formatDataPoints: {}", dataPoints);
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(dataPoints / 1_000_000.0) + " M";
    }

}
