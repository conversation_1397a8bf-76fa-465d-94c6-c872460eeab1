package org.diabetestechnology.drh.service.http.pg.service;

import java.util.Map;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.request.PractitionerRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;

import com.fasterxml.jackson.core.type.TypeReference;

import com.fasterxml.jackson.databind.JsonNode;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class PractitionerService {

    private static final Logger LOG = LoggerFactory.getLogger(PractitionerService.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final AuthUserDetailsService authUserDetailsService;
    private final PartyService partyService;
    private final DbActivityService activityLogService;
    private final GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter;

    public PractitionerService(@Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, AuthUserDetailsService authUserDetailsService,
            PartyService partyService, MasterService masterService, DbActivityService activityLogService,
            GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.authUserDetailsService = authUserDetailsService;
        this.partyService = partyService;
        this.activityLogService = activityLogService;
        this.gitHubUserAuthorizationFilter = gitHubUserAuthorizationFilter;
    }

    @Transactional
    public JSONB createPractitionerProfile(PractitionerRequest request) throws JsonProcessingException {
        final var userId = userNameService.getUserId();
        final var provider = userNameService.getUserProvider();
        String activityData = activityLogService.prepareActivityLogMetadata();
        LOG.info("Initiating practitioner profile creation. UserId: {}, Provider: {}", userId, provider);
        LOG.info("Organization Party Id: {}", request.organizationPartyId());
        LOG.info("OrcidId: {}", request.orcid());

        JSONB result = null;
        String defaultAuthProvider = "GitHub";
        var query = dsl
                .select(DSL.field(
                        "drh_stateless_research_study.create_practitioner_profile({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} )",
                        String.class,
                        DSL.val(request.name()),
                        DSL.val(defaultAuthProvider),
                        DSL.val(request.email()),
                        DSL.val(request.organizationPartyId()),
                        DSL.val(request.orcid() != null && !request.orcid().isEmpty() ? request.orcid()
                                : null),
                        DSL.val(userId),
                        DSL.val(null, String.class),
                        DSL.cast(DSL.val(
                                activityData),
                                JSONB.class)));
        if (provider.equalsIgnoreCase("GitHub")) {
            LOG.info("Creating practitioner profile for GitHub user {}", userId);
            LOG.info("Create Practitioner Query: {}", query);
            result = query
                    .fetchOneInto(JSONB.class);
        } else if (provider.equalsIgnoreCase("Orcid")) {
            String authProvider = "ORCiD";
            LOG.info("Creating practitioner profile for ORCID user {}", userId);
            final var orcidQuery = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.create_practitioner_profile({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} )",
                            String.class,
                            DSL.val(request.name()),
                            DSL.val(authProvider),
                            DSL.val(request.email()),
                            DSL.val(request.organizationPartyId()),
                            DSL.val(request.orcid()),
                            DSL.val(userId),
                            DSL.val(null, String.class),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Create Practitioner Query: {}", orcidQuery);
            result = orcidQuery
                    .fetchOneInto(JSONB.class);
        } else {
            LOG.warn("Unexpected authentication provider: {}. Practitioner profile creation aborted.", provider);
        }
        LOG.info("Result of practitioner profile creation: {}", result);

        if (result != null) {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(result.toString());
            LOG.info("DB Result: {}", jsonNode);
            String resultStatus = jsonNode.path("status").asText("success");
            LOG.info("Result status: {}", resultStatus);
            LOG.info("Refreshing user permissions in the current session context...");
            boolean refreshSuccess = gitHubUserAuthorizationFilter.refreshUserPermissionsInSession();
            if (refreshSuccess) {
                LOG.info("User session permissions successfully refreshed.");
            } else {
                LOG.warn("Failed to refresh user session permissions. No request or authenticated user found.");
            }
            if (resultStatus.equalsIgnoreCase("success")) {
                LOG.info("Successfully created practitioner profile for UserId: {}", userId);
            } else {
                LOG.warn("Failed to create practitioner profile for UserId: {}", userId);
            }
        } else {
            LOG.warn("Failed to create practitioner profile for UserId: {}", userId);
        }

        return result;
    }

    @Transactional
    public Object getUserDetails() {

        try {
            final var userId = userNameService.getUserId();
            if (userId == null || userId.isEmpty() || userId.equalsIgnoreCase("Anonymous")) {
                LOG.warn("No user ID found in the current request context.");
                return "{}";
            }
            LOG.info("Fetching user details for user ID: {}", userId);
            JSONB result = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'practitioner_id', practitioner_id, " +
                                    "'practitioner_party_id', practitioner_party_id, " +
                                    "'practitioner_name', first_name, " +
                                    "'user_account_primary_email', user_account_primary_email, " +
                                    "'provider_user_id', provider_user_id, " +
                                    "'work_emails', work_emails, " +
                                    "'username', username, " +
                                    "'organization_party_id', organization_party_id, " +
                                    "'auth_provider', CASE WHEN auth_provider = 'ORCiD' THEN 'ORCID' ELSE auth_provider END, "
                                    +
                                    "'profile_status_id', profile_status_id, " +
                                    "'profile_status', profile_status" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_authentication.user_profile_view")
                    .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                    .fetchOneInto(JSONB.class);
            if (result == null || result.data() == null) {
                LOG.info("No user details found for user ID: {}", userId);
                return "{}";
            }
            return result.data();
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return "{}";
        }
    }

    @Transactional
    public Boolean isUserExists() {

        try {
            final var userId = userNameService.getUserId();
            if (userId == null || userId.isEmpty() || userId.equalsIgnoreCase("Anonymous")) {
                LOG.warn("No user ID found in the current request context.");
                return false;
            }
            LOG.info("Fetching user details for user ID: {}", userId);
            Boolean exists = dsl
                    .select(DSL.value(true))
                    .from("drh_stateless_authentication.user_profile_view")
                    .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                    .limit(1) // To ensure we only check if at least one row exists
                    .fetchOptional() // Returns Optional<Boolean>
                    .isPresent(); // Check if the result is present
            return exists;
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return false;
        }
    }

    @Transactional
    public String getUserOrganization() {

        try {
            final var userId = userNameService.getUserId();
            if (userId == null || userId.isEmpty() || userId.equalsIgnoreCase("Anonymous")) {
                LOG.warn("No user ID found in the current request context.");
                return "";
            }
            Object principal = userNameService.getUserPrincipal();
            String organizationPartyId = "";
            if (principal instanceof UserDetails) {
                organizationPartyId = dsl
                        .select(DSL.field("organization_party_id"))
                        .from("drh_stateless_authentication.super_admin_view")
                        .where(DSL.field("email").eq(DSL.val(userId)))
                        .limit(1) // To ensure we only check if at least one row exists
                        .fetchOneInto(String.class);
            } else {

                LOG.info("Fetching user details for user ID: {}", userId);
                organizationPartyId = dsl
                        .select(DSL.field("organization_party_id"))
                        .from("drh_stateless_authentication.user_profile_view")
                        .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                        .limit(1) // To ensure we only check if at least one row exists
                        .fetchOneInto(String.class);
            }
            LOG.debug("Fetched organizationPartyId for user {}: {}", userId, organizationPartyId);
            String organizationPartyName = dsl
                    .select(DSL.field("organization_name"))
                    .from("drh_stateless_research_study.organization_party_view")
                    .where(DSL.field("organization_party_id").eq(DSL.val(
                            organizationPartyId)))
                    .limit(1)
                    .fetchOneInto(String.class);

            LOG.debug("Fetched organizationPartyName for user {}: {}", userId, organizationPartyName);
            return organizationPartyName;

        } catch (Exception e) {
            LOG.error("Error fetching user organization details: {}", e.getMessage(), e);
            return "";
        }
    }

    @Transactional
    public JSONB getLoggedInUserDetails() {

        try {
            final var userId = userNameService.getUserId();
            if (userId == null || userId.isEmpty() || userId.equalsIgnoreCase("Anonymous")) {
                LOG.warn("No user ID found in the current request context.");
                return JSONB.valueOf("{}");
            }
            LOG.info("Fetching user details for user ID: {}", userId);
            Boolean isSuperAdminUser = authUserDetailsService.isSuperAdmin();
            if (isSuperAdminUser) {
                final var query = dsl
                        .select(DSL.field(
                                "jsonb_build_object(" +
                                        "'practitioner_party_id', party_id, " +
                                        "'practitioner_name', full_name, " +
                                        "'user_account_primary_email', email, " +
                                        "'provider_user_id', email, " +
                                        "'work_emails', email, " +
                                        "'username', email, " +
                                        "'organization_party_id', organization_party_id, " +
                                        "'auth_provider', 'Email', " +
                                        "'user_roles', jsonb_build_array(" +
                                        "jsonb_build_object(" +
                                        "'role_id', role_id, " +
                                        "'role_name', role_name" +
                                        ")" +
                                        ")" +
                                        ")",
                                JSONB.class))
                        .from("drh_stateless_authentication.super_admin_view")
                        .where(DSL.field("email").eq(DSL.val(userId)));
                // final var query = dsl
                // .select(DSL.field(
                // "jsonb_build_object(" +
                // "'practitioner_id', practitioner_id, " +
                // "'practitioner_party_id', practitioner_party_id, " +
                // "'practitioner_name', first_name, " +
                // "'user_account_primary_email', user_account_primary_email, " +
                // "'provider_user_id', provider_user_id, " +
                // "'work_emails', work_emails, " +
                // "'username', username, " +
                // "'organization_party_id', organization_party_id, " +
                // "'auth_provider', 'Email', "
                // +
                // "'profile_status_id', profile_status_id, " +
                // "'profile_status', profile_status," +
                // "'user_roles', role_name," +
                // "'tenant_id', tenant_id" +
                // ")",
                // JSONB.class))
                // .from("drh_stateless_authentication.user_profile_view_v1")
                // .where(DSL.field("provider_user_id").eq(DSL.val(userId)));
                LOG.info("getLoggedInUserDetails: {}", query);
                JSONB result = query
                        .fetchOneInto(JSONB.class);
                if (result == null || result.data() == null) {
                    LOG.info("No user details found for user ID: {}", userId);
                    return JSONB.valueOf("{}");
                }
                return result;
            }
            JSONB result = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'practitioner_id', user_id, " +
                                    "'practitioner_party_id', party_id, " +
                                    "'practitioner_name', first_name, " +
                                    "'user_account_primary_email', user_account_primary_email, " +
                                    "'provider_user_id', provider_user_id, " +
                                    "'work_emails', work_emails, " +
                                    "'username', username, " +
                                    "'organization_party_id', organization_party_id, " +
                                    "'auth_provider', CASE WHEN auth_provider = 'ORCiD' THEN 'ORCID' ELSE auth_provider END, "
                                    +
                                    "'profile_status_id', profile_status_id, " +
                                    "'profile_status', profile_status," +
                                    "'user_roles', user_roles," +
                                    "'tenant_id', tenant_id" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_authentication.user_list_view")
                    .where(DSL.field("provider_user_id").eq(DSL.val(userId)))
                    .fetchOneInto(JSONB.class);
            if (result == null || result.data() == null) {
                LOG.info("No user details found for user ID: {}", userId);
                return JSONB.valueOf("{}");
            }
            return result;
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return JSONB.valueOf("{}");
        }
    }

    public Map<String, Object> checkEmailUnique(String email) {
        LOG.debug("Entering isUniqueEmail method with email: {}", email);
        try {
            LOG.info("Calling function to check email uniqueness: {}", dsl.select(DSL.field(
                    "drh_stateless_authentication.check_email_unique({0})",
                    JSONB.class,
                    DSL.val(email))));

            JSONB result = dsl.select(DSL.field(
                    "drh_stateless_authentication.check_email_unique({0})",
                    JSONB.class,
                    DSL.val(email)))
                    .fetchOneInto(JSONB.class);

            LOG.info("Email uniqueness check completed for: {}", email);
            LOG.debug("Result of email uniqueness check: {}", result);

            if (result != null && result.data() != null && !result.data().isBlank()) {

                Map<String, Object> responseMap = OBJECT_MAPPER.readValue(result.data(), new TypeReference<>() {
                });

                // Check status in DB response
                String status = responseMap.getOrDefault("status", "").toString();
                if (!"success".equalsIgnoreCase(status)) {
                    LOG.warn("DB function returned failure or unknown status: {}", responseMap);
                    LOG.debug("Exiting checkEmailUnique method with failure response");

                    return Map.of(
                            "status", "error",
                            "message", responseMap.getOrDefault("message", "Email is not unique"),
                            "email", responseMap.get("email"));

                }
                LOG.debug("Exiting checkEmailUnique method with success response");

                return responseMap;
            } else {
                LOG.warn("No result returned from database function for email: {}", email);
                LOG.debug("Exiting checkEmailUnique method with error response - null result");
                return Map.of("status", "error", "message", "Error while fetching data from Database");
            }
        } catch (Exception e) {
            LOG.error("Error while checking email uniqueness for: {}. Exception: {}", email, e.getMessage(), e);
            LOG.debug("Exiting checkEmailUnique method with exception");
            return Map.of("status", "error", "message", "Failed to check email uniqueness");
        }
    }

    public Boolean isUniqueEmail(String email) {
        LOG.debug("Entering checkEmailUnique method with email: {}", email);
        try {
            final var query = dsl.select(DSL.field(
                    "drh_stateless_authentication.check_email_unique({0})",
                    JSONB.class,
                    DSL.val(email)));
            LOG.info("Calling function to check email uniqueness: {}", query);

            JSONB result = query
                    .fetchOneInto(JSONB.class);

            LOG.info("Email uniqueness check completed for: {}", email);
            LOG.debug("Result of email uniqueness check: {}", result);

            if (StringUtils.hasText(result.data())) {
                LOG.debug("Result data is not null or empty");
                Map<String, Object> responseMap = OBJECT_MAPPER.readValue(result.data(), new TypeReference<>() {
                });
                // Check status in DB response
                final var status = responseMap.getOrDefault("status", "").toString();
                if ("success".equalsIgnoreCase(status)) {
                    LOG.debug("DB function returned success status: {}", responseMap);
                    final Boolean isUnique = (Boolean) responseMap.getOrDefault("is_unique", false);
                    LOG.info("Email uniqueness result for {}: {}", email, isUnique);
                    return isUnique;
                } else {
                    LOG.warn("DB function returned failure or unknown status: {}", responseMap);
                    return false;
                }

            } else {
                LOG.warn("No result returned from database function for email: {}", email);
                LOG.debug("Exiting checkEmailUnique method with error response - null result");
                throw new RuntimeException("No result returned from database function");
            }
        } catch (Exception e) {
            LOG.error("Error while checking email uniqueness for: {}. Exception: {}", email, e.getMessage(), e);
            LOG.debug("Exiting checkEmailUnique method with exception");
            throw new RuntimeException("Error while checking email uniqueness", e);
        }
    }

    public String getAvatarUrl() {
        Object principal = userNameService.getUserPrincipal();
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            String avatarUrl = (String) userPrincipal.getAttribute("avatar_url") != null
                    ? (String) userPrincipal.getAttribute("avatar_url")
                    : "/user.jpg";
            return avatarUrl;
        } else {
            return "/user.jpg"; // Default fallback

        }
    }

    public Object updateProfileDetails(JSONB jsonInput) {
        try {
            final String userId = userNameService.getUserId();
            LOG.debug("Retrieved userId from request: {}", userId);

            final String currentUserId = partyService.getPartyIdByUserId(userId);
            LOG.debug("Resolved partyId for userId {}: {}", userId, currentUserId);
            String activityData = activityLogService.prepareActivityLogMetadata();
            final var query = dsl
                    .select(DSL.field(
                            "drh_stateless_authentication.update_profile_details({0}, {1}, {2})",
                            JSONB.class,
                            DSL.val(jsonInput),
                            DSL.val(currentUserId),
                            DSL.cast(DSL.val(
                                    activityData),
                                    JSONB.class)));
            LOG.info("Query to updatePersonDetails: {}", query);
            final var result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Result of updatePersonDetails: {}", result);
            return result;

        } catch (Exception e) {
            LOG.error("Error executing update_profile_details", e);
            throw new RuntimeException("Database function call failed: " + e.getMessage(), e);
        }
    }

    public Object getProfileDetails() {

        final String userId = userNameService.getUserId();
        LOG.debug("Retrieved userId from request: {}", userId);

        final String currentUserId = partyService.getPartyIdByUserId(userId);
        LOG.debug("Resolved partyId for userId {}: {}", userId, currentUserId);

        final var query = dsl
                .select(DSL.field(
                        "jsonb_build_object(" +
                                "'party_id', party_id, " +
                                "'name', name, " +
                                "'birth_date', to_char(birth_date, 'MM-DD-YYYY'), " +
                                "'line1', line1, " +
                                "'line2', line2, " +
                                "'city', city, " +
                                "'state', state, " +
                                "'user_account_primary_email', user_account_primary_email, " +
                                "'country', country, " +
                                "'postal_code', postal_code, " +
                                "'tenant_id', tenant_id, " +
                                "'practitioner_party_id', practitioner_party_id, " +
                                "'organization_name', organization_name, " +
                                "'organization_party_id', organization_party_id, " +
                                "'telecom', telecom" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_authentication.profile_details_view")
                .where(DSL.field("party_id").eq(currentUserId));

        LOG.info("getProfileDetails: Executing query: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            LOG.warn("getProfileDetails: No data returned from database");
            return "{}";
        }
        return jsonbResult.data(); // Return as String
    }

    @Transactional
    public Boolean isUserEmailExists(String email) {

        try {
            final var query = dsl
                    .select(DSL.value(true))
                    .from("drh_stateless_authentication.profile_details_view")
                    .where(DSL.field("user_account_primary_email").eq(DSL.val(email)))
                    .limit(1); // To ensure we only check if at least one row exists
            LOG.info("Executing query to check if user email exists: {}", query);
            Boolean exists = query
                    .fetchOptional() // Returns Optional<Boolean>
                    .isPresent(); // Check if the result is present
            LOG.info("User email exists: {}", exists);
            return exists;
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return false;
        }
    }

    public Object getUserNameByEmail(String email) {
        try {
            return dsl
                    .select(DSL.field("name"))
                    .from("drh_stateless_authentication.profile_details_view")
                    .where(DSL.field("user_account_primary_email").eq(DSL.val(email)))
                    .fetchOneInto(String.class);
        } catch (Exception e) {
            LOG.error("Error fetching user name by email: {}", e.getMessage(), e);
            throw new RuntimeException("Database query failed: " + e.getMessage(), e);
        }
    }

    public JSONB linkExternalAuthProvider(String email, String provider, String providerId) {
        try {
            // Call the database function to link the external auth provider
            ObjectNode jsonNode = OBJECT_MAPPER.createObjectNode();
            jsonNode.put("email", email);
            jsonNode.put("auth_provider", provider);
            jsonNode.put("provider_user_id", providerId);
            String jsonString = OBJECT_MAPPER.writeValueAsString(jsonNode);
            final var query = dsl
                    .select(DSL.field("drh_stateless_authentication.link_external_auth_provider({0})",
                            JSONB.class, DSL.cast(DSL.val(
                                    jsonString),
                                    JSONB.class)));
            LOG.info("Executing query to link external auth provider: {}", query);
            final var result = query
                    .fetchOneInto(JSONB.class);
            LOG.info("Result of linkExternalAuthProvider: {}", result);
            return result;
        } catch (Exception e) {
            LOG.error("Error linking external auth provider: {}", e.getMessage(), e);
            throw new RuntimeException("Database function call failed: " + e.getMessage(), e);
        }
    }

    @Transactional
    public String getEmailByUserId(String userId, String verificationStatus) {
        LOG.info("Fetching email for userId: {}", userId);
        try {
            final var query = dsl
                    .select(DSL.field("email"))
                    .from("drh_stateless_authentication.user_account_verification_log_view")
                    .where(DSL.field("provider_id").eq(DSL.val(userId)))
                    .and(DSL.field("verification_status_id").eq(DSL.val(verificationStatus)))
                    .orderBy(DSL.field("updated_at").desc()) // Replace with actual timestamp column
                    .limit(1);
            LOG.info("Executing query to fetch email: {}", query);
            String email = query
                    .fetchOneInto(String.class);
            LOG.info("Fetched email: {}", email);
            return email;
        } catch (Exception e) {
            LOG.error("Error fetching email for userId {}: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    public Boolean isUserVerified(String userId, String verificationStatus) {
        LOG.info("Checking if user is verified for userId: {}", userId);
        try {
            final var query = dsl
                    .select(DSL.value(true))
                    .from("drh_stateless_authentication.user_account_verification_log_view")
                    .where(DSL.field("provider_id").eq(DSL.val(userId)))
                    .and(DSL.field("verification_status_id").eq(DSL.val(verificationStatus)))
                    .orderBy(DSL.field("updated_at").desc())
                    .limit(1);
            LOG.info("Executing query to check user verification: {}", query);
            Boolean exists = query // To ensure we only check if at least one row exists
                    .fetchOptional() // Returns Optional<Boolean>
                    .isPresent(); // Check if the result is present
            LOG.info("User verification status: {}", exists);
            return exists;
        } catch (Exception e) {
            LOG.error("Error checking user verification for userId {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    // To get the existing provider before linking a new one
    public String getExistingProvider(String email) {
        try {
            final var query = dsl
                    .select(DSL.field("auth_provider"))
                    .from("drh_stateless_authentication.user_profile_view")
                    .where(DSL.field("user_account_primary_email").eq(DSL.val(email)))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1); // To ensure we only check if at least one row exists
            LOG.info("Executing query to check if user email exists: {}", query);
            String provider = query
                    .fetchOneInto(String.class); // Check if the result is present
            LOG.info("User email exists: {}", provider);
            return provider;
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return null;
        }
    }

}
