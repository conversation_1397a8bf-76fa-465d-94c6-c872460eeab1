package org.diabetestechnology.drh.service.http.hub.prime.study;

public class StudyView {
    private String studyName;
    private String startDate;
    private String endDate;
    private String nctNumber;
    private String studyDescription;

    // Constructor (optional)
    public StudyView(String studyName, String startDate, String endDate, String nctNumber, String studyDescription) {
        this.studyName = studyName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.nctNumber = nctNumber;
        this.studyDescription = studyDescription;
    }

    // Getters and Setters
    public String getStudyName() {
        return studyName;
    }

    public void setStudyName(String studyName) {
        this.studyName = studyName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getNctNumber() {
        return nctNumber;
    }

    public void setNctNumber(String nctNumber) {
        this.nctNumber = nctNumber;
    }

    public String getStudyDescription() {
        return studyDescription;
    }

    public void setStudyDescription(String studyDescription) {
        this.studyDescription = studyDescription;
    }
}
