package org.diabetestechnology.drh.udi;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.jooq.impl.DataSourceConnectionProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserContextConnectionProvider extends DataSourceConnectionProvider {
    private static final Logger LOG = LoggerFactory.getLogger(UserContextConnectionProvider.class.getName());
    private static final ThreadLocal<Map<String, String>> contextVariables = ThreadLocal.withInitial(HashMap::new);
    public static final String DB_ROLE = "current_user_roles";
    public static final String USER_ID = "current_user_party_id";
    public static final String TENANT_ID = "current_user_tenant_id";
    public static final String ORG_ID = "current_user_organization_party_id";
    public static final String AUTH_PROVIDER = "auth_provider";
    public static final String AUTHENTICATED_ROLE = "authenticated";
    public static final String ANONYMOUS_ROLE = "anon";
    public static final String GUEST_ROLE = "guest";

    public UserContextConnectionProvider(DataSource dataSource) {
        super(dataSource);
    }

    public static void setVariable(String key, String value) {
        contextVariables.get().put(key, value);
    }

    public static void setRoleVariable(String key, List<String> values) {
        contextVariables.get().put(key, String.join(",", values));
    }

    public static void clear() {
        contextVariables.remove();
    }

    public UserContextConnectionProvider(CustomDataSource dataSource) {
        super(dataSource.dataSource());
    }

    public Connection acquire() {
        var connection = super.acquire();
        var variables = contextVariables.get();

        if (!variables.isEmpty()) {
            try (var stmt = connection.createStatement()) {
                // Set new variables
                for (var entry : variables.entrySet()) {
                    if (entry.getValue() == null) {
                        continue;
                    }
                    // LOG.debug("Setting DB variable {} to {}", entry.getKey(), entry.getValue());
                    stmt.execute("SET drh." + entry.getKey() + " = '" +
                            entry.getValue().replace("'", "''") + "'"); // Escape single quotes
                }
                setDBRoles(variables, stmt);
            } catch (SQLException e) {
                LOG.error("Error setting DB variables", e);
            }
        }
        return connection;

    }

    private void setDBRoles(Map<String, String> variables, Statement stmt) throws SQLException {

        if (!variables.containsKey(AUTH_PROVIDER) || variables.get(AUTH_PROVIDER) == null) {
            // LOG.debug("Setting DB Role {}", ANONYMOUS_ROLE);
            // stmt.execute("SET ROLE " + ANONYMOUS_ROLE);
            stmt.execute("SET ROLE " + ANONYMOUS_ROLE);
        } else if (variables.containsKey(AUTH_PROVIDER) && variables.get(AUTH_PROVIDER) != null) {
            // LOG.debug("Setting DB Role {}", AUTHENTICATED_ROLE);
            stmt.execute("SET ROLE " + AUTHENTICATED_ROLE);
        } else {
            stmt.execute("SET ROLE " + ANONYMOUS_ROLE);
        }
    }

    @Override
    public void release(Connection connection) {
        var variables = contextVariables.get();
        if (!variables.isEmpty()) {
            try (Statement stmt = connection.createStatement()) {
                for (String key : variables.keySet()) {
                    stmt.execute("RESET drh." + key);
                }
                resetDBRoles(stmt);
            } catch (SQLException e) {
                LOG.error("Error resetting DB variables", e);
            }
        }
        super.release(connection);
    }

    private void resetDBRoles(Statement stmt) throws SQLException {
        // LOG.debug("Resetting DB Role");
        stmt.execute("SET ROLE NONE");
    }
}
