/**
 * CustomDataSource is a custom data source configuration class that uses HikariCP for connection pooling
 * and jOOQ for database interaction. It provides a builder pattern for easy configuration and initialization.
 *
 * <p>Example usage:
 * <pre>{@code
 * CustomDataSource dataSource = CustomDataSource.builder()
 *     .jdbcDbPath("********************************")
 *     .driverClassName("com.mysql.cj.jdbc.Driver")
 *     .dbType(SQLDialect.MYSQL)
 *     .build();
 * }</pre>
 *
 * <p>Features:
 * <ul>
 *   <li>Configurable connection pool settings (maximum pool size, minimum idle connections, etc.)</li>
 *   <li>Thread-safe lazy initialization of DSLContext</li>
 *   <li>Integration with Spring's TransactionAwareDataSourceProxy</li>
 * </ul>
 *
 * <p>Builder pattern allows for flexible and readable configuration:
 * <pre>{@code
 * CustomDataSource dataSource = new CustomDataSource(builder -> {
 *     builder.jdbcDbPath("********************************")
 *            .driverClassName("com.mysql.cj.jdbc.Driver")
 *            .dbType(SQLDialect.MYSQL)
 *            .maximumPoolSize(50)
 *            .minimumIdle(5)
 *            .connectionTimeout(20000)
 *            .idleTimeout(300000)
 *            .maxLifetime(1200000)
 *            .leakDetectionThreshold(15000);
 * });
 * }</pre>
 *
 * <p>Note: The builder requires the following mandatory fields to be set:
 * <ul>
 *   <li>jdbcDbPath</li>
 *   <li>driverClassName</li>
 *   <li>dbType</li>
 * </ul>
 *
 * <p>Throws RuntimeException if mandatory fields are not set during validation.
 *
 * @see com.zaxxer.hikari.HikariConfig
 * @see com.zaxxer.hikari.HikariDataSource
 * @see org.jooq.DSLContext
 * @see org.jooq.SQLDialect
 * @see org.jooq.impl.DataSourceConnectionProvider
 * @see org.jooq.impl.DefaultDSLContext
 */
package org.diabetestechnology.drh.udi;

import org.springframework.jdbc.datasource.TransactionAwareDataSourceProxy;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.util.function.Consumer;

import jakarta.annotation.Nonnull;
import javax.sql.DataSource;

import org.jooq.ConnectionProvider;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.jooq.impl.DataSourceConnectionProvider;

public class CustomDataSource {

    private final DataSource dataSource;
    private volatile DSLContext dslContext;
    private final SQLDialect dbType;
    private final ConnectionProvider connectionProvider;

    private CustomDataSource(Builder builder) {
        builder.validate();
        this.dataSource = springTransAwareHikariDataSource(builder);
        this.dbType = builder.dbType;
        this.connectionProvider = connectionProvider(dataSource, builder.connectionProviderClass);
    }

    public CustomDataSource(Consumer<Builder> builderConsumer) {
        this(createAndConfigureBuilder(builderConsumer));
    }

    private static Builder createAndConfigureBuilder(Consumer<Builder> builderConsumer) {
        var builder = new Builder();
        builderConsumer.accept(builder);
        return builder;
    }

    public static Builder builder() {
        return new Builder();
    }

    @Nonnull
    public DSLContext dslContext() {
        var localRef = this.dslContext;
        if (localRef == null) {
            synchronized (this) {
                localRef = this.dslContext;
                if (localRef == null) {
                    localRef = DSL.using(connectionProvider, dbType);
                    this.dslContext = localRef;
                }
            }
        }
        return this.dslContext;
    }

    @Nonnull
    public DataSource dataSource() {
        return this.dataSource;
    }

    private static DataSource springTransAwareHikariDataSource(final Builder builder) {
        final var hikariConfig = new HikariConfig();
        setIfNotNull(hikariConfig::setUsername, builder.username);
        setIfNotNull(hikariConfig::setPassword, builder.password);
        setIfNotNull(hikariConfig::setJdbcUrl, builder.jdbcDbPath);
        setIfNotNull(hikariConfig::setDriverClassName, builder.driverClassName);
        setIfNotNull(hikariConfig::setMaximumPoolSize, builder.maximumPoolSize);
        setIfNotNull(hikariConfig::setMinimumIdle, builder.minimumIdle);
        setIfNotNull(hikariConfig::setConnectionTimeout, builder.connectionTimeout);
        setIfNotNull(hikariConfig::setIdleTimeout, builder.idleTimeout);
        setIfNotNull(hikariConfig::setMaxLifetime, builder.maxLifetime);
        setIfNotNull(hikariConfig::setLeakDetectionThreshold, builder.leakDetectionThreshold);

        return new TransactionAwareDataSourceProxy(new HikariDataSource(hikariConfig));
    }

    private static ConnectionProvider connectionProvider(DataSource dataSource,
            Class<? extends ConnectionProvider> connectionProviderClass) {
        if (connectionProviderClass != null) {
            try {
                return connectionProviderClass.getConstructor(DataSource.class).newInstance(dataSource);
            } catch (Exception e) {
                throw new RuntimeException("Error creating connection provider", e);
            }
        } else {
            return new DataSourceConnectionProvider(dataSource);
        }
    }

    private static <T> void setIfNotNull(Consumer<T> setter, T value) {
        if (value != null) {
            setter.accept(value);
        }
    }

    public static final class Builder {
        private int maximumPoolSize;
        private int minimumIdle;
        private long connectionTimeout;
        private long idleTimeout;
        private long maxLifetime;
        private long leakDetectionThreshold;
        private String jdbcDbPath;
        private String driverClassName;
        private String username;
        private String password;
        private SQLDialect dbType;
        private Class<? extends ConnectionProvider> connectionProviderClass;

        private Builder() {
        }

        public Builder jdbcDbPath(String jdbcDbPath) {
            this.jdbcDbPath = jdbcDbPath;
            return this;
        }

        public Builder driverClassName(String driverClassName) {
            this.driverClassName = driverClassName;
            return this;
        }

        public Builder dbType(SQLDialect dbType) {
            this.dbType = dbType;
            return this;
        }

        public CustomDataSource build() {
            return new CustomDataSource(this);
        }

        private void validate() {
            if (jdbcDbPath == null) {
                throw new RuntimeException("jdbcDbPath must be set");
            }
            if (driverClassName == null) {
                throw new RuntimeException("driverClassName must be set");
            }
            if (dbType == null) {
                throw new RuntimeException("dbType must be set");
            }
        }

        public Builder connectionProviderClass(Class<? extends ConnectionProvider> connectionProviderClass) {
            this.connectionProviderClass = connectionProviderClass;
            return this;
        }

        public Builder maximumPoolSize(int maximumPoolSize) {
            this.maximumPoolSize = maximumPoolSize;
            return this;
        }

        public Builder minimumIdle(int minimumIdle) {
            this.minimumIdle = minimumIdle;
            return this;
        }

        public Builder connectionTimeout(long connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
            return this;
        }

        public Builder idleTimeout(long idleTimeout) {
            this.idleTimeout = idleTimeout;
            return this;
        }

        public Builder maxLifetime(long maxLifetime) {
            this.maxLifetime = maxLifetime;
            return this;
        }

        public Builder leakDetectionThreshold(long leakDetectionThreshold) {
            this.leakDetectionThreshold = leakDetectionThreshold;
            return this;
        }

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }
    }
}