package org.diabetestechnology.drh.udi;

import java.io.IOException;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.pg.service.AuthUserDetailsService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class DatabaseContextVariablesFilter extends OncePerRequestFilter {
    private final PartyService partyService;
    private AuthUserDetailsService authUserDetailsService;

    public DatabaseContextVariablesFilter(PartyService partyService, AuthUserDetailsService authUserDetailsService) {
        this.partyService = partyService;
        this.authUserDetailsService = authUserDetailsService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        try {
            GitHubUserAuthorizationFilter.getAuthenticatedUser().map(user -> user.ghUser().userId())
                    .ifPresent(userId -> {
                        UserContextConnectionProvider.setVariable(
                                UserContextConnectionProvider.USER_ID,
                                partyService.getPartyIdByUserId(userId));
                        UserContextConnectionProvider.setVariable(
                                UserContextConnectionProvider.TENANT_ID, // Replace with actual variable name
                                partyService.getTenantIdByUserId(userId) // Assuming a method exists to get Org
                                                                         // ID
                        );
                        UserContextConnectionProvider.setVariable(
                                UserContextConnectionProvider.ORG_ID, // Replace with actual variable name
                                partyService.getOrganizationPartyIdByUser(userId) // Assuming a method exists to get Org
                                                                                  // ID
                        );
                    });
            GitHubUserAuthorizationFilter.getAuthenticatedUser().map(user -> user.principal())
                    .ifPresentOrElse(principal -> {
                        String authProvider;
                        String login;

                        if (principal instanceof OAuth2User) {
                            OAuth2User oauth2User = (OAuth2User) principal;
                            authProvider = oauth2User.getAttribute("provider");
                            login = oauth2User.getAttribute("login");
                        } else if (principal instanceof UserDetails) {
                            UserDetails userDetails = (UserDetails) principal;
                            authProvider = "Email";
                            login = userDetails.getUsername();
                        } else {
                            authProvider = null;
                            login = null;
                        }

                        UserContextConnectionProvider.setVariable(UserContextConnectionProvider.AUTH_PROVIDER,
                                authProvider);
                        UserContextConnectionProvider.setRoleVariable(UserContextConnectionProvider.DB_ROLE,
                                authUserDetailsService.getRoles(login, authProvider));
                    }, () -> {
                        UserContextConnectionProvider.setVariable(UserContextConnectionProvider.AUTH_PROVIDER,
                                null);
                        UserContextConnectionProvider.setVariable(UserContextConnectionProvider.DB_ROLE,
                                UserContextConnectionProvider.GUEST_ROLE);
                    });

        } finally {
            chain.doFilter(request, response);
            UserContextConnectionProvider.clear();
        }
    }
}
