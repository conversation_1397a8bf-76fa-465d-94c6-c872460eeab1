package org.diabetestechnology.drh.udi;

import javax.sql.DataSource;

import org.jooq.DSLContext;
import org.jooq.SQLDialect;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.zaxxer.hikari.HikariDataSource;

import jakarta.annotation.PreDestroy;

@Configuration
@EnableTransactionManagement
@ConfigurationProperties(prefix = "org.diabetestechnology.udi.secondary.jdbc")
public class UdiSecondaryDbConfig {
    private static final Logger LOG = LoggerFactory.getLogger(UdiSecondaryDbConfig.class.getName());

    @Value("${org.diabetestechnology.udi.secondary.jdbc.url}")
    private String jdbcUrl;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.driverClassName}")
    private String driverClassName;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.username}")
    private String username;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.password}")
    private String password;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.connection.maximum-pool-size}")
    private int maxPoolSize;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.connection.minimum-idle}")
    private int minIdle;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.connection.connection-timeout}")
    private long connectionTimeout;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.connection.idle-timeout}")
    private long idleTimeout;

    @Value("${org.diabetestechnology.udi.secondary.jdbc.connection.max-lifetime}")
    private long maxLifetime;

    @Bean(name = "udiSecondaryDataSource")
    DataSource dataSource() {
        CustomDataSource ds = postgresCustomDataSource();
        return ds.dataSource();
    }

    @Bean(name = "secondaryDsl")
    public DSLContext dsl() {
        return postgresCustomDataSource().dslContext();
    }

    @Bean(name = "postgresCustomDataSource")
    CustomDataSource postgresCustomDataSource() {
        LOG.info("JDBC URL:: {}", jdbcUrl);
        LOG.info("Driver Class Name:: {}", driverClassName);
        return new CustomDataSource(builder -> {
            builder.username(username);
            builder.password(password);
            builder.jdbcDbPath(jdbcUrl);
            builder.driverClassName(driverClassName);
            builder.maximumPoolSize(maxPoolSize);
            builder.minimumIdle(minIdle);
            builder.connectionTimeout(connectionTimeout);
            builder.idleTimeout(idleTimeout);
            builder.maxLifetime(maxLifetime);
            builder.dbType(SQLDialect.POSTGRES);
            builder.connectionProviderClass(UserContextConnectionProvider.class);
        });
    }

    @Bean(name = "drhDuckDbDataSource")
    public CustomDataSource drhDuckDbDataSource() {
        return CustomDataSource.builder()
                .jdbcDbPath("jdbc:duckdb:")
                .driverClassName("org.duckdb.DuckDBDriver")
                .dbType(SQLDialect.DUCKDB)
                .maximumPoolSize(10)
                .build();
    }

    @Bean(name = "duckDsl")
    public DSLContext duckDsl(@Qualifier("drhDuckDbDataSource") CustomDataSource customDataSource) {
        return customDataSource.dslContext();
    }

    @PreDestroy
    public void shutdown() {
        if (dataSource() instanceof HikariDataSource) {
            ((HikariDataSource) dataSource()).close();
        }
    }
}
