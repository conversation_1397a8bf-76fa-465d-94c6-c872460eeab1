org:
  diabetestechnology:
    service:
      baggage:
        user-agent:
          # `enable-sensitive` safe only in sandbox (local)
          enable-sensitive: true
          exposure: true
      http:
        interactions:
          defaultPersistStrategy: "{ \"nature\": \"fs\" }"

management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: actuator,beans,env,openapi,swagger-ui,health,info,metrics
        exclude: env
  health:
    db:
      enabled: true
  endpoint:
    beans:
      enabled: true
    env:
      enabled: true
      show-values: always
    health:
      enabled: true
      show-details: always
    info:
      enabled: true
    metrics:
      enabled: true
    prometheus:
      enabled: true
  info:
    git:
      mode: full
    build:
      enabled: true
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
