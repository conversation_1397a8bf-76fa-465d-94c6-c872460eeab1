
management:
  otlp: 
    logging:     
      export:
        enabled: true     
      endpoint: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_END_POINT:}api/${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}/v1/logs 
      headers:
        Authorization:  Basic ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_AUTHN_TOKEN:}
        stream-name: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_STREAM_NAME:}
        organization: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}
        service:
          name: Diabetes-Research-Hub
    metrics: 
      export: 
        url:  ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_END_POINT:}api/${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}/v1/metrics 
        enabled: true 
        step: 1m # Interval for pushing metrics 
        headers: 
          enabled: true  
          Authorization:  Basic ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_AUTHN_TOKEN:}
          organization: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}
          stream-name: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_STREAM_NAME:}
          service: 
            name: Diabetes-Research-Hub
    tracing: 
      export:
        enabled: true
      endpoint: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_END_POINT:}api/${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}/v1/traces 
      headers: 
        active : true
        enabled: true 
        step: 1m  
        Authorization:  Basic ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_AUTHN_TOKEN:}
        organization: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_ORGANIZATION:}
        stream-name: ${ORG_DRH_SERVICE_HTTP_OPEN_OBSERVE_STREAM_NAME:}
        service: 
          name: Diabetes-Research-Hub

logging:
  level:
    root: INFO
    io:
      opentelemetry: DEBUG
    org:
      springframework:
        security: DEBUG
      hibernate: ERROR
      diabetestechnology:
        drh: DEBUG

spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 204800MB  # Set maximum file size
      max-request-size: 204800MB  # Set maximum request size (sum of all files in a request)
  thymeleaf:
    cache: true
  cache:
   type: simple
  # mvc:
  #  async:
  #   request-timeout: 60000
  profiles:
    active: ${SPRING_PROFILES_ACTIVE}
  # data:
  #   jdbc:
  #     dialect: org.springframework.data.jdbc.repository.dialect.SQLiteDialect
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  application:
    name: diabetestechnology-hub-prime
  jpa:
    properties:
      hibernate:
        # dialect: org.hibernate.dialect.PostgreSQLDialect
        dialect: org.hibernate.dialect.SQLiteDialect
    hibernate:
      ddl-auto: none
    show-sql: true
  mail:
    host: ${ORG_DRH_SERVICE_EMAIL_HOST}
    port: ${ORG_DRH_SERVICE_EMAIL_PORT}
    username: ${ORG_DRH_SERVICE_EMAIL_USERNAME}
    password: ${ORG_DRH_SERVICE_EMAIL_APP_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  security:
    oauth2:
      client:
        registration:
          orcid:
            scope: /authenticate,email
            authorization-grant-type: authorization_code
        provider:
          orcid:
            token-uri: https://orcid.org/oauth/token
            user-info-uri: https://pub.orcid.org/v3.0/{orcid_id}  
            user-name-attribute: orcid  
            authorization-uri: https://orcid.org/oauth/authorize                                 
springdoc:
  api-docs:
    path: /docs/api/openapi
  swagger-ui.path: /docs/api/interactive/index.html
  swagger-ui:
    doc-expansion: none
  show-actuator: true

server:
  servlet:
    session:
      timeout: 129600s   #session timeout for 36hours (1.5*24*60*60)
    context-path: /
  error:
    whitelabel:
      enabled: false

org:
  diabetestechnology:
    # these should be setup in ENV vars, not here
    # orchestrate:
    #   sftp:
    #     account:
    #       orchctlts:
    #         - tenantId: name1
    #           server: sftp.drh.org
    #           port: 22
    #           username: name1
    #           password: password
    #         - tenantId: name2
    #           server: sftp.drh.org
    #           port: 22
    #           username: name2
    #           password: password
    service:
      http:
        hub:
          prime:
            version: @project.version@            
        interactions:
          defaultPersistStrategy: "{ \"nature\": \"fs\" }"
      baggage:
        user-agent:
          # `enable-sensitive` true should only be used in sandbox (local)
          enable-sensitive: false
          # `exposure` should be used only be used in sandbox (local) or devl (shared)
          exposure: false

    udi:
      prime:
        jdbc:
          # the reason `url` and `jdbcUrl` are both supplied is that some poolers
          # like `url` while others (e.g. Hikari, the default) like `jdbcUrl`
          # url: ${${SPRING_PROFILES_ACTIVE}_DRH_UDI_DS_PRIME_JDBC_URL:}
          # jdbcUrl: ${${SPRING_PROFILES_ACTIVE}_DRH_UDI_DS_PRIME_JDBC_URL:}
          # username: ${${SPRING_PROFILES_ACTIVE}_DRH_UDI_DS_PRIME_JDBC_USERNAME:}
          # password: ${${SPRING_PROFILES_ACTIVE}_DRH_UDI_DS_PRIME_JDBC_PASSWORD:}
          # driverClassName: org.postgresql.Driver
          # driverClassName: org.sqlite.JDBC
          # connection:
          #   maximum-pool-size: 30
          #   minimum-idle: 5
          #   connection-timeout: 30000
          #   idle-timeout: 600000
          #   max-lifetime: 1800000
          #   threshold: 20000
          #   read-only: true
        schema:
          base-path: /target/site/schemaSpy/
      secondary:
        jdbc:
          url: jdbc:postgresql://${PGHOST}:${PGPORT}/${PGDATABASE}
          driverClassName: org.postgresql.Driver
          username: ${PGUSER:}
          password: ${PGPASSWORD:}
          connection:
            maximum-pool-size: 100
            minimum-idle: 10
            connection-timeout: 30000 # 5 minutes - idle connections are removed after this time
            idle-timeout: 300000 # 30 seconds to wait for a connection from the pool
            max-lifetime: 1800000 # 30 minutes - connections are refreshed after this time
aws:
  credentials:
    access-key: ${ORG_DRH_SERVICE_HTTP_AWS_S3_CREDENTIALS_ACCESS_KEY}
    secret-key: ${ORG_DRH_SERVICE_HTTP_AWS_S3_CREDENTIALS_SECRET_KEY}
  region: us-east-2
  s3:
    bucket: ${ORG_DRH_SERVICE_HTTP_AWS_S3_BUCKET}