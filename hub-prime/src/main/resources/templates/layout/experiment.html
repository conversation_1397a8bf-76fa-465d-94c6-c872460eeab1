<!DOCTYPE html>
<html class="h-full bg-gray-100" lang="en" xmlns:th="http://www.thymeleaf.org"
    xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">    
    <meta name="shellAideServerSideProfile" content="java-spring-boot-3">
    <meta name="ssrServletContextPath" th:content="@{/}">
    <meta name="sandboxConsoleConf" th:content="${sandboxConsoleConf}">

    <!-- always set this as the first script since it sets up the importMap for subsequent ESM imports -->
    <script th:replace="~{fragments/shell :: script-import-map}"></script>

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,container-queries"></script>
    <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script>
    <title layout:title-pattern="$CONTENT_TITLE">Debug Layout</title>
</head>

<body class="h-full">
    <div class="min-h-full">
        <main>
            <header class="bg-white shadow-sm">
                <div class="mx-auto max-w-9xl px-4 py-4 sm:px-6 lg:px-8">
                    <a th:href="@{/home}" style="padding-right:25px">Application Home</a> <a
                        th:href="@{/experiment/home.html}">Experiments Home</a>
                    <h1 class="text-lg font-semibold leading-6 text-gray-900">
                        <script>document.write(document.querySelector('title')?.innerText ?? `no title tag in head`)</script>
                    </h1>
                </div>
            </header>

            <div class="mx-auto max-w-9xl py-2 sm:px-6 lg:px-8">
                <div class="w-full mx-auto bg-white p-8 rounded-lg shadow-lg">
                    <div layout:fragment="content"></div>
                </div>
            </div>
        </main>
    </div>
</body>

</html>