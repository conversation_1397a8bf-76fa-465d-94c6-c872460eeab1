<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/experiment}">

<head>
    <title>Experiments</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
    <div layout:fragment="content">
        <article class="w-full prose prose-table:table-fixed full-width-children">
            <h3 class="mt-5">Developer Links (TODO: make available only in sandbox)</h3>
            <ul>
                <li><a th:href="@{/experiment/routes.html}">Application Routes</li>
                <li><a th:href="@{/experiment/authorized-user.html}">Authorized User</li>
                <li><a th:href="@{/experiment/aggrid-aide.html}">AG Grid with <code>ModalAide</code>,
                        <code>AGGridAide</code> and <code>AGGridAideBuilder</code></li>
                <li><a th:href="@{/metadata}"><code>/metadata</code> Capabilities Endpoint</a></li>
                <li><a th:href="@{/docs/api/interactive/index.html}">Swagger UI</a></li>
                <li><a th:href="@{/actuator}" href="/actuator">Management Endpoints Catalog</a></li>
            </ul>
        </article>
    </div>
</body>

</html>