<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Health Information',
                breadcrumbs: [
                    {
                        text: "Console",
                        href: "/console/project",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>
</head>

<body>
    <div layout:fragment="content">
        <div class="flex justify-between flex-col p-2">
            <div clas="mb-2"
                th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
            </div>
        </div>
        <iframe th:src="@{https://health.drh.diabetestechnology.org/status-page/ae9c737f-57d3-4245-a34d-d6d42931fed2}"
            class="w-full min-h-screen"></iframe>
    </div>
</body>

</html>