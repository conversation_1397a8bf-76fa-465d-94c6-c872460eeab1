<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <title>Documentation</title>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // Function to get URL parameter by name
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(window.location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // Function to fetch and parse the routines.html file
            function fetchRoutineUrl(routineName) {
                fetch('/maven-site/schemaSpy/info_schema_lifecycle/routines.html')
                    .then(response => response.text())
                    .then(data => {
                        var parser = new DOMParser();
                        var doc = parser.parseFromString(data, 'text/html');

                        // Search for the routine name in the parsed HTML
                        var links = doc.querySelectorAll('a');
                        var found = false;
                        links.forEach(link => {
                            if (link.textContent.includes(routineName)) {
                                found = true;
                                var routineUrl = link.getAttribute('href');
                                setIframeSrc('/maven-site/schemaSpy/info_schema_lifecycle/' + routineUrl);
                            }
                        });

                        if (!found) {
                            console.log('Routine not found, loading default page');
                            setIframeSrc('/maven-site/schemaSpy/index.html');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching routines.html:', error);
                        setIframeSrc('/maven-site/schemaSpy/index.html'); // Fallback if fetch fails
                    });
            }

            // Function to set the iframe source
            function setIframeSrc(url) {
                var iframe = document.getElementById('contentFrame');
                iframe.src = url;
            }

            // Get the 'page' parameter and use it to find the routine
            var param = getUrlParameter('page'); // Replace 'page' with your parameter name
            if (param) {
                fetchRoutineUrl(param + '()');
            } else {
                setIframeSrc('/maven-site/schemaSpy/index.html'); // Default src if no parameter is present
            }
        });
    </script>
</head>

<body>
    <div layout:fragment="content">
        <iframe id="contentFrame" class="w-full min-h-screen"></iframe>
    </div>
</body>

</html>