<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>

    <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Population Percentage',
                breadcrumbs: [
                    {
                        text: "Studies",
                        href: "/summary/studies",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>

    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script type="module" src="/js/cohortcombined.js" th:attr="data-userid=${#authentication.getPrincipal()}"></script>
</head>

<body>
    <div layout:fragment="content">
        <div class="flex justify-between flex-col pb-4">
            <div clas="mb-2"
                th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=${pagesubdescriptiontitle}, pagesubdescription=${pagesubdescription}, pageattributestitle=${pageattributestitle},pageattributes=null, notes=null,font='')}">
            </div>
        </div>
        <!-- Filter Section (Full Width) -->
        <div>
            <div th:replace="~{fragments/cohortCombinedFilter}"></div>
        </div>
    </div>
</body>

</html>