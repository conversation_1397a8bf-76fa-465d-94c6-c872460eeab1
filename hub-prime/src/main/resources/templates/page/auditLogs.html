<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">

    <!-- if JSON Viewer is not already in the layout, add the following -->
    <!-- <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script> -->

    <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
    <style>
        .grid-description {
            font-size: 14px;
            margin: 8px 0px 10px 15px;
        }
    </style>
    <script type="module">
        import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
        import ModalAide from '@presentation/shell/modal-aide.js';

        const schemaName = 'audit';
        const viewName = 'activity_log';

        function formatDate(value) {
            if (!value) return ''; // Handle null or undefined values

            // Create a Date object from the input string (assumed to be in UTC)
            const date = new Date(value);

            // Extract components of the date in local time zone
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
            const day = String(date.getDate()).padStart(2, '0');
            const year = date.getFullYear();
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            // Format the date string
            return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
        }

        document.addEventListener('DOMContentLoaded', function () {
            const modalAide = new ModalAide();
            const agGridInstance = new AGGridAideBuilder()
                .withColumnDefs([
                   
                    
                    {
                        headerName: "ID",
                        field: "session_unique_id",
                        filter: "agTextColumnFilter",
                        rowGroup: true,       // Group rows by this column
                        hide: false,
                        cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
                            modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}/session_unique_id/${value}.json`));
                        }, modalAide)            // Optionally hide the column if you don't want it displayed
                    },
                    { headerName: "Created By", field: "created_by", filter: "agTextColumnFilter" ,
                    rowGroup: true,       // Group rows by this column
                    hide: false,
                    cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
                            modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}/created_by/${value}.json`));
                        }, modalAide)
                    },
                    
                    {
                        headerName: "Activity",
                        field: "activity_name",
                        filter: "agTextColumnFilter",
                        rowGroup: false,       // Group rows by this column
                        hide: false,

                    },
                    {
                        headerName: "Description",
                        field: "activity_description",
                        filter: "agTextColumnFilter",
                        rowGroup: false,       // Group rows by this column
                        hide: false
                    },
                    {
                        headerName: "IP Address",
                        field: "ip_address",
                        filter: "agTextColumnFilter",
                        rowGroup: false,       // Group rows by this column
                        hide: false
                    },
                    // { headerName: "Description", field: "activity_description", filter: "agTextColumnFilter" },
                    
                ])
                
                .withServerSideDatasource(
                    window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}.json`),
                    (data, valueCols) => {
                        return valueCols.map(col => ({
                            headerName: col.displayName,
                            field: col.field
                        }));
                    },
                )
                .withModalAide(modalAide)
                .withGridDivStyles({ height: "750px", width: "100%" })
                .build();

            agGridInstance.init('serverDataGrid');
        });
    </script>    
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
    <div layout:fragment="content">
        <div class="grid-description" id="date-range"> 
        This widget offers a detailed analysis of User Activity. 
    </div>
        <div id="serverDataGrid" class="ag-theme-alpine"></div>
    </div>
</body>

</html>