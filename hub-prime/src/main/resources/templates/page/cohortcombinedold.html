<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>

    <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>

    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script type="module" src="/js/cohortcombined.js" th:attr="data-userid=${#authentication.getPrincipal()}"></script>
</head>

<body>
    <div layout:fragment="content">

        <!-- Filter Section (Full Width) -->
        <div class="w-full flex bg-[#f1f3f5] px-5 py-4 rounded-[5px]">
            <div th:replace="~{fragments/cohortCombinedFilter}"></div>
        </div>

        <!-- Main Content Section (Metrics & DataGrid) -->
        <div class="w-full flex flex-col">
            <div class="w-full">
                <div>
                    <!-- Metrics Section (Responsive) -->
                    <div class="flex flex-wrap justify-center py-4 mb-4 gap-4">
                        <div class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Each metric adjusts its width depending on screen size -->
                            <div
                                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants', null, @{/study/allstudy-detail-metrics/total_number_of_participants.html})}">
                            </div>
                            <div class="w-full"
                                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants', null, @{/study/allstudy-detail-metrics/total_number_of_participants.html})}">
                            </div>
                            <div class="w-full"
                                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total CGM Files', null, @{/study/allstudy-total-cgm-files.html})}">
                            </div>
                            <div class="w-full"
                                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Average Glucose', null, @{/study/allstudy-detail-metrics/percent_female.html})}">
                            </div>
                            <div class="w-full"
                                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Data Points', null, @{/study/allstudy-total-data-points.html})}">
                            </div>
                        </div>
                    </div>

                    <!-- DataGrid Section -->
                    <div>
                        <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>