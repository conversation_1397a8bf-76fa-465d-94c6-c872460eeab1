<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    var studyId = /*[[${studyId}]]*/ "";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: "Files - " + studyId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/summary/studies",
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script type="module">
    import {
      AGGridAide,
      AGGridAideBuilder,
    } from "@presentation/shell/aggrid-aide.js";
    import ModalAide from "@presentation/shell/modal-aide.js";
    var rowCount = 8
    //const viewName = 'uniform_resource_study';
    document.addEventListener("DOMContentLoaded", function () {
      const modalAide = new ModalAide();
      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "File Name",
            field: "file_name",
            filter: "agTextColumnFilter",
            // autoSizeStrategy: { type: "fitGridWidth" },
            // maxWidth: 800,
            // cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
            //     modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${viewName}/study_id/${value}.json`));
            // }, modalAide)
            enableRowGroup: true,
            cellRenderer: function (params) {
              if (params.value) {
                const link = document.createElement("a");
                link.href =
                  "/svm/file/view/" + params.data.study_id + "/" + params.data.file_name + "/" + params.data.table_name;
                link.innerText = params.value;
                return link;
              } else {
                return null;
              }
            },
          },
          {
            headerName: "Study Id",
            field: "study_id",
            enableRowGroup: true,
            // maxWidth: 500,
            filter: "agTextColumnFilter",
            flex: 1,
            cellRenderer: function (params) {
              if (params.value) {
                const link = document.createElement("a");
                link.href =
                  "/svm/files/list/" + params.value;
                link.innerText = params.value;
                return link;
              } else {
                return null;
              }
            },
          },
          {
            headerName: "Table Name",
            field: "table_name",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 1,
          },
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(
            studyId === "all"
              ? `/api/ux/tabular/jooq/total_cgm_file_name_cached.json`
              : `/api/ux/tabular/jooq/` +
              studyId +
              `/cgm_table_name_cached.json`
          ),
          (data, valueCols) => {
            return valueCols.map((col) => ({
              headerName: col.displayName,
              field: col.field,
            }));
          },
          {
            // beforeRequest: async (reqPayload, dataSourceUrl) => {
            //   reqPayload.body = {
            //     startRow: 0,
            //     endRow: 10,
            //   };
            // },
            beforeSuccess: async (serverRespPayload, respMetrics, reqPayload, dataSourceUrl) => {

              // Check if data is present and extract keys from the first data item
              rowCount = serverRespPayload.data.length;
            }
          }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: (parseInt(rowCount) + 1) * 45 + "px", width: "100%" }) //no of rows displayed + 1(for header) multiplied by 45 (height of 1 row)
        .build();

      agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

      agGridInstance.init("serverDataGrid");
    });
  </script>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
  <div layout:fragment="content">
    <div
      th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes = null,font='')}">
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300 mt-3 table-files"></div>
  </div>
</body>

</html>