<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/sundry-multi-level}">

<head>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">
  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script src="https://www.jsviews.com/download/jsrender.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Failed Participant Interaction',
        breadcrumbs: [{ text: "Interactions", href: "/interactions" }],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout");

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>

  <script type="module">
    import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
    import ModalAide from '@presentation/shell/modal-aide.js';

    let modalAide;

    function formatDate(value) {
      if (!value) return '';
      const date = new Date(value);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
    }

    const schemaName = 'drh_stateless_activity_audit';
    const masterTableNameOrViewName = 'study_participant_interaction_view';

    const questionAnswersColumnDefs = [
      { headerName: "Interaction Type", field: "interaction_type", filter: "agTextColumnFilter" },
      { headerName: "Description", field: "description", filter: "agTextColumnFilter" },
      { headerName: "Action", field: "action_type", filter: "agTextColumnFilter" },
      { headerName: "Status", field: "interaction_status", filter: "agTextColumnFilter" },
      {
        headerName: "Path", field: "uri", filter: "agTextColumnFilter",
        cellRenderer: AGGridAide.modalCellRenderer((params) => {
          const participantInteractionId = params.data.participant_interaction_id;
          modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/participant/interaction/child/modal/${schemaName}/${masterTableNameOrViewName}/participant_interaction_id/${participantInteractionId}.json?`));
        }, modalAide)
      },
      {
        headerName: "Request Time", field: "created_at", filter: "agDateColumnFilter", sort: "desc", hide: false,
        valueFormatter: (params) => formatDate(params.value)
      }
    ];

    function getInteractionGridData(params) {
      const participantInteractionId = params.data.participant_interaction_id;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/participant/interaction/failed/child/${schemaName}/${masterTableNameOrViewName}/participant_interaction_id/${participantInteractionId}.json`))
        .then(response => response.json())
        .then(response => params.successCallback(response))
        .catch(error => console.error('Error fetching details data' + error));
    }

    function getParticipantsGridData(params) {
      const studyId = params.data.study_id;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/participant/interaction/failed/sub/${schemaName}/${masterTableNameOrViewName}/study_id/${studyId}.json`))
        .then(response => response.json())
        .then(response => params.successCallback(response))
        .catch(error => console.error('Error fetching participants data' + error));
    }

    document.addEventListener('DOMContentLoaded', function () {
      modalAide = new ModalAide();

      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          { headerName: "Study Display Name", field: "study_display_id", filter: "agTextColumnFilter", cellRenderer: 'agGroupCellRenderer' },
          { headerName: "Title", field: "study_title", filter: "agTextColumnFilter", cellRenderer: 'agGroupCellRenderer' },
          { headerName: "Organization", field: "organization_name", filter: "agTextColumnFilter", cellRenderer: 'agGroupCellRenderer' },
          { headerName: "Created By", field: "created_by_name", filter: "agTextColumnFilter", cellRenderer: 'agGroupCellRenderer' },
          { headerName: "Created At", field: "created_at", sort: "desc", filter: "agDateColumnFilter", valueFormatter: (params) => formatDate(params.value) }
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/participant/interaction/failed/parent/${schemaName}/${masterTableNameOrViewName}.json?`),
          (data, valueCols) => valueCols.map(col => ({ headerName: col.displayName, field: col.field }))
        )
        .withMasterDetail(true)
        .withDetailRowAutoHeight(false)
        .withDetailCellRendererParams({
          detailGridOptions: {
            masterDetail: true,
            columnDefs: [
              { headerName: "Participant ID", field: "participant_id", filter: "agTextColumnFilter", cellRenderer: 'agGroupCellRenderer' },
              { headerName: "Participant Display ID", field: "participant_display_id", filter: "agTextColumnFilter" },
              { headerName: "Enrolled At", field: "created_at", valueFormatter: (params) => formatDate(params.value) }
            ],
            defaultColDef: { flex: 1 },
            detailCellRendererParams: {
              detailGridOptions: {
                columnDefs: questionAnswersColumnDefs,
                defaultColDef: { flex: 1 }
              },
              getDetailRowData: getInteractionGridData
            },
            getRowNodeId: data => data.participant_id
          },
          getDetailRowData: getParticipantsGridData
        })
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        .build();

      agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

      agGridInstance.init('serverDataGrid');
    });
  </script>
</head>

<body>
  <div layout:fragment="content">
    <div class="grid-description" id="date-range">
      <div class="flex justify-between flex-col">
        <div class="mb-2"
          th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=null, pagesubdescription=null, pageattributestitle=null, pageattributes=null, notes=null, font='')}">
        </div>
      </div>
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine pt-4"></div>

    <!-- Modal for JSON view -->
    <div id="interactionDataModal"
      style="display: none; position: fixed; z-index: 1000; top: 10%; left: 10%; width: 80%; height: 80%; background: white; border: 1px solid #ccc; overflow: auto; padding: 1rem;">
      <button onclick="closeModal()" style="float: right;">Close</button>
      <pre id="modalContent" style="white-space: pre-wrap;"></pre>
    </div>
  </div>
</body>

</html>