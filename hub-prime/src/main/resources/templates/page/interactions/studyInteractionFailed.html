<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/sundry-multi-level}">

<head>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">
  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script src="https://www.jsviews.com/download/jsrender.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Failed Study Interaction',
        breadcrumbs: [{ text: "Interactions", href: "/interactions", }
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <script type="module">
    import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
    import ModalAide from '@presentation/shell/modal-aide.js';

    // Move modalAide declaration outside
    let modalAide;

    function formatDate(value) {
      if (!value) return ''; // Handle null or undefined values

      // Create a Date object from the input string (assumed to be in UTC)
      const date = new Date(value);

      // Extract components of the date in local time zone
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
      const day = String(date.getDate()).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      // Format the date string
      return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
    }

    function showActivityData(data) {
      const modal = document.getElementById("activityDataModal");
      const modalContent = document.getElementById("modalContent");

      if (modalContent) {
        modalContent.innerText = data ? data : 'No data available'; // Set the activity data to the modal content
        modal.style.display = "block"; // Show the modal
      } else {
        console.error("Modal content not found");
      }
    }

    // Close the modal
    function closeModal() {
      document.getElementById("activityDataModal").style.display = "none";
    }

    const schemaName = 'drh_stateless_activity_audit';
    const masterTableNameOrViewName = 'study_interaction_view';

    const questionAnswersColumnDefs = [
      { headerName: "Interaction Type", field: "interaction_type", filter: "agTextColumnFilter" },
      { headerName: "Description", field: "description", filter: "agTextColumnFilter" },
      { headerName: "Action", field: "action_type", filter: "agTextColumnFilter" },
      { headerName: "Status", field: "interaction_status", filter: "agTextColumnFilter" },
      {
        headerName: "Path", field: "uri", filter: "agTextColumnFilter",
        cellRenderer: AGGridAide.modalCellRenderer((params) => {
          const studyInteractionId = params.data.study_interaction_id;
          console.log("studyInteractionId: " + studyInteractionId)
          modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/interaction/child/modal/${schemaName}/${masterTableNameOrViewName}/study_interaction_id/${studyInteractionId}.json?`));
        }, modalAide)
      },
      {
        headerName: "Request Time", field: "created_at", filter: "agDateColumnFilter", sort: "desc", hide: false,
        valueFormatter: (params) => formatDate(params.value)
      },
    ];

    function getQuestionAndAnswerGridData(params) {
      const studyInteractionId = params.data.study_interaction_id;
      const createdBy = params.data.created_by;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/interaction/failed/child/${schemaName}/${masterTableNameOrViewName}/study_interaction_id/${studyInteractionId}.json`))
        .then(response => response.json())
        .then(response => {
          params.successCallback(response);
        })
        .catch(error => {
          console.error('Error fetching details data' + error);
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
      modalAide = new ModalAide(); // Initialize modalAide here
      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "Study Display Name",
            field: "study_display_id",
            filter: "agTextColumnFilter",
            rowGroup: false,
            hide: false,
            cellRenderer: 'agGroupCellRenderer', // Enable expand/collapse for master-detail
          },
          {
            headerName: "Title",
            field: "study_title",
            filter: "agTextColumnFilter",
            rowGroup: false,
            hide: false,
            cellRenderer: 'agGroupCellRenderer', // Enable expand/collapse for master-detail
          },
          {
            headerName: "Organization",
            field: "organization_name",
            filter: "agTextColumnFilter",
            rowGroup: false,
            hide: false,
            cellRenderer: 'agGroupCellRenderer', // Enable expand/collapse for master-detail
          },
          {
            headerName: "Description",
            field: "description",
            filter: "agTextColumnFilter",
            rowGroup: false,
            hide: false,
            cellRenderer: 'agGroupCellRenderer', // Enable expand/collapse for master-detail
          },
          {
            headerName: "Created At",
            field: "created_at",
            sort: "desc",
            filter: "agDateColumnFilter",
            hide: false,
            valueFormatter: (params) => formatDate(params.value)
          }
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(`/api/ux/tabular/jooq/study/interaction/failed/parent/${schemaName}/${masterTableNameOrViewName}.json?`),
          (data, valueCols) => {
            return valueCols.map(col => ({
              headerName: col.displayName,
              field: col.field
            }));
          },
        )
        .withMasterDetail(true)
        .withDetailCellRendererParams({
          detailGridOptions: {
            columnDefs: questionAnswersColumnDefs,
            defaultColDef: {
              flex: 1
            }
          },
          getDetailRowData: params => {
            getQuestionAndAnswerGridData(params);
          }
        })
        .withDetailRowAutoHeight(false)
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        .build();

       agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

      agGridInstance.init('serverDataGrid');
    });        
  </script>

</head>

<body>
  <div layout:fragment="content">
    <div class="grid-description" id="date-range">
      <div class="flex justify-between flex-col">
        <div clas="mb-2"
          th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
        </div>
      </div>
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine pt-4"></div>
  </div>
</body>

</html>