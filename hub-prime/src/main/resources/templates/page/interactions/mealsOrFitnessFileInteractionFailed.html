<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/sundry-multi-level}">

<head>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">
  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script src="https://www.jsviews.com/download/jsrender.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Meals Or Fitness File Interaction',
        breadcrumbs: [{ text: "Interactions", href: "/interactions" }],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout");

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>

  <script type="module">
    import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
    import ModalAide from '@presentation/shell/modal-aide.js';

    let modalAide;

    function formatDate(value) {
      if (!value) return '';
      const date = new Date(value);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
    }

    const schemaName = 'drh_stateless_activity_audit';
    const masterTableNameOrViewName = 'file_interaction_view';
    const fileType = 'Meals';

    const interactionDetailCols = [
      { headerName: "Description", field: "description" },
      { headerName: "Action", field: "interaction_action_type" },
      { headerName: "Status", field: "interaction_status" },
      {
        headerName: "Path", field: "uri",
        cellRenderer: AGGridAide.modalCellRenderer((params) => {
          modalAide.viewFetchedJsonValue(
            window.shell.serverSideUrl(`/api/ux/tabular/jooq/participant/meals-or-fitness/file/interaction/child/modal/${schemaName}/${masterTableNameOrViewName}/file_interaction_id/${params.data.file_interaction_id}.json`)
          );
        }, modalAide)
      },
      {
        headerName: "Request Time", field: "created_at",
        valueFormatter: (params) => formatDate(params.value)
      }
    ];

    function getInteractionGridData(params) {
      const fileInteractionId = params.data.file_interaction_id;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/participant/meals-or-fitness/file/interaction/failed/child/${schemaName}/${masterTableNameOrViewName}/file_interaction_id/${fileInteractionId}.json`))
        .then(res => res.json()).then(data => {
          params.successCallback(data.rows || []);
        });
    }

    function getFilesGridData(params) {
      const participantId = params.data.participant_id;
      const fileContentType = params.data.file_category;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/participant/${fileContentType}/file/interaction/failed/sub/${schemaName}/${masterTableNameOrViewName}/participant_id/${participantId}.json`))
        .then(res => res.json()).then(data => {
          params.successCallback(data.rows || []);
        });
    }

    function getParticipantsGridData(params) {
      const studyId = params.data.study_id;
      const fileContentType = params.data.file_category;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/participant/${fileContentType}/file/interaction/failed/first/child/${schemaName}/${masterTableNameOrViewName}/study_id/${studyId}.json`))
        .then(res => res.json()).then(data => {
          params.successCallback(data.rows || []);
        });
    }

    document.addEventListener('DOMContentLoaded', () => {
      modalAide = new ModalAide();

      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          { headerName: "Study Display Name", field: "study_display_id", cellRenderer: 'agGroupCellRenderer', filter: "agTextColumnFilter" },
          { headerName: "Title", field: "study_title", filter: "agTextColumnFilter" },
          { headerName: "Organization", field: "organization_name", filter: "agTextColumnFilter" },
          { headerName: "File Type", field: "file_category", filter: "agTextColumnFilter" }
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(`/api/ux/tabular/jooq/participant/${fileType}/file/interaction/failed/parent/${schemaName}/${masterTableNameOrViewName}.json`),
          (data, valueCols) => valueCols.map(col => ({ headerName: col.displayName, field: col.field }))
        )
        .withMasterDetail(true)
        .withDetailCellRendererParams({
          detailGridOptions: {
            columnDefs: [
              { headerName: "Participant ID", field: "participant_display_id", cellRenderer: 'agGroupCellRenderer' },
              { headerName: "File Type", field: "file_category" }
            ],
            defaultColDef: { flex: 1 },
            masterDetail: true,
            detailCellRendererParams: {
              detailGridOptions: {
                columnDefs: [
                  { headerName: "File Name", field: "file_name", cellRenderer: 'agGroupCellRenderer' },
                  { headerName: "Content Type", field: "file_content_type" },
                  { headerName: "Enrolled At", field: "created_at", valueFormatter: p => formatDate(p.value) }
                ],
                defaultColDef: { flex: 1 },
                masterDetail: true,
                detailCellRendererParams: {
                  detailGridOptions: {
                    columnDefs: interactionDetailCols,
                    defaultColDef: { flex: 1 },
                    rowData: [] // fix: explicitly set empty array to avoid AG Grid error
                  },
                  getDetailRowData: getInteractionGridData
                },
                getDetailRowData: getInteractionGridData
              },
              getDetailRowData: getFilesGridData
            }
          },
          getDetailRowData: getParticipantsGridData
        })
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        .build();
      agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };
      agGridInstance.init("serverDataGrid");
    });
  </script>
</head>

<body>
  <div layout:fragment="content">
    <div id="serverDataGrid" class="ag-theme-alpine pt-4"></div>
    <div id="interactionDataModal"
      style="display: none; position: fixed; z-index: 1000; top: 10%; left: 10%; width: 80%; height: 80%; background: white; border: 1px solid #ccc; overflow: auto; padding: 1rem;">
      <button onclick="document.getElementById('interactionDataModal').style.display='none'"
        style="float: right;">Close</button>
      <pre id="modalContent" style="white-space: pre-wrap;"></pre>
    </div>
  </div>
</body>

</html>