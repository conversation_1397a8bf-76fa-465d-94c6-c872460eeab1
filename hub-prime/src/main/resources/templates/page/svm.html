<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <th:block th:insert="fragments/favicon :: favicon"></th:block>

  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'All Studies',
        breadcrumbs: [
          {
            text: "Studies",
            href: "/summary/studies",
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <script type="module">
    import {
      AGGridAide,
      AGGridAideBuilder,
    } from "@presentation/shell/aggrid-aide.js";
    import ModalAide from "@presentation/shell/modal-aide.js";

    //const viewName = 'uniform_resource_study';
    document.addEventListener("DOMContentLoaded", function () {
      const modalAide = new ModalAide();
      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "Study ID",
            field: "study_id",
            filter: "agTextColumnFilter",
            // cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
            //     modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${viewName}/study_id/${value}.json`));
            // }, modalAide)
            enableRowGroup: true,
            cellRenderer: function (params) {
              if (params.value) {
                const link = document.createElement("a");
                link.href = "/study/" + params.value;
                link.innerText = params.value;
                return link;
              } else {
                return null;
              }
            },
          },
          {
            headerName: "Source",
            field: "study_id",
            enableRowGroup: false,
            maxWidth: 500,
            filter: false,
            sortable: false,
            cellRenderer: function (params) {
              if (params.value) {
                const link = document.createElement("a");
                link.href = "/svm/files/" + params.value;
                link.className = "flex items-center";

                const svgIcon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
                svgIcon.setAttribute("class", "w-5 h-5 mt-2 text-blue-500 hover:text-blue-700");
                svgIcon.setAttribute("fill", "none");
                svgIcon.setAttribute("stroke", "currentColor");
                svgIcon.setAttribute("viewBox", "0 0 24 24");
                svgIcon.setAttribute("stroke-width", "2");
                svgIcon.setAttribute("stroke-linecap", "round");
                svgIcon.setAttribute("stroke-linejoin", "round");

                // Define the file shape
                const path1 = document.createElementNS("http://www.w3.org/2000/svg", "path");
                path1.setAttribute("d", "M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6z");

                // Define the file folded corner
                const path2 = document.createElementNS("http://www.w3.org/2000/svg", "path");
                path2.setAttribute("d", "M14 2v6h6");

                // Define the lines inside the file
                const line1 = document.createElementNS("http://www.w3.org/2000/svg", "line");
                line1.setAttribute("x1", "8");
                line1.setAttribute("y1", "13");
                line1.setAttribute("x2", "16");
                line1.setAttribute("y2", "13");
                line1.setAttribute("stroke", "currentColor");

                const line2 = document.createElementNS("http://www.w3.org/2000/svg", "line");
                line2.setAttribute("x1", "8");
                line2.setAttribute("y1", "17");
                line2.setAttribute("x2", "14");
                line2.setAttribute("y2", "17");
                line2.setAttribute("stroke", "currentColor");

                svgIcon.appendChild(path1);
                svgIcon.appendChild(path2);
                svgIcon.appendChild(line1);
                svgIcon.appendChild(line2);

                link.appendChild(svgIcon);

                return link;
              } else {
                return null;
              }
            },
            flex: 1,
            tooltipValueGetter: function (params) {
              return "View imported CSV sources";
            }
          },
          {
            headerName: "Study Name",
            field: "study_name",
            enableRowGroup: true,
            maxWidth: 500,
            filter: "agTextColumnFilter",
            tooltipValueGetter: (params) => `${params.value}`,
            flex: 1
          },
          {
            headerName: "NCT Number",
            field: "nct_number",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 1
          },
          {
            headerName: "Description",
            field: "study_description",
            maxWidth: 500,
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            tooltipValueGetter: (params) => `${params.value}`,
            flex: 2
          },
          {
            headerName: "From Date",
            field: "start_date",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 1
          },
          {
            headerName: "To Date",
            field: "end_date",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 1
          },
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(
            `/api/ux/tabular/jooq/all_study_summary_cached.json`
          ),
          (data, valueCols) => {
            return valueCols.map((col) => ({
              headerName: col.displayName,
              field: col.field,
            }));
          }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: (9 + 1) * 50 + "px", width: "100%" }) //no of rows displayed + 1(for header) multiplied by 45 (height of 1 row)
        // .withGridOptions({
        //   onRowClicked: function (event) {
        //     // Example: Open a modal with study details
        //     const studyId = event.data.study_id;
        //     const link = document.createElement("a");
        //     link.href = "/study/" + studyId;
        //     link.click()
        //   },
        // })
        .build();

      agGridInstance.init("serverDataGrid");

      // function showLoader(loaderId) {
      //   document.getElementById(loaderId).style.display = "block";
      // }

      function getSvmValues() {
        showLoading("totalParticipantsBottomLoader");
        showLoading("totalFemaleBottomLoader");
        showLoading("totalAverageBottomLoader");
      }

      function assignValues(eleClass, value) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.innerHTML = value;
        });
      }

    });

  </script>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
</head>

<body>
  <div layout:fragment="content">
    <div
      th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=${pagesubdescriptiontitle},pageattributestitle=${pageattributestitle},pageattributes = ${pageattributes}, pagesubdescription=${pagesubdescription}, notes = null,font='font-semibold')}">
    </div>
    <div class="flex justify-center pb-5">
      <div class="w-[100%] flex justify-between grid grid-cols-1 md:grid-cols-2 gap-4 lg:grid-cols-6">
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants', null, @{/study/allstudy-vanity-metrics/total_number_of_participants.html})}">
        </div>
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total CGM Files', '/svm/files/list', @{/study/allstudy-total-cgm-files.html})}">
        </div>
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('% Female', null, @{/study/allstudy-vanity-metrics/percent_female.html})}">
        </div>
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('Average Age', null, @{/study/allstudy-vanity-metrics/average_age.html})}">
        </div>
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('Days of wear', null, @{/study/allstudy-total-cgm-wear.html})}">
        </div>
        <div
          th:replace="~{fragments/metricsWithTitle :: serverTextStat('Data Points', null, @{/study/allstudy-total-data-points.html})}">
        </div>
      </div>
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
  </div>
</body>

</html>