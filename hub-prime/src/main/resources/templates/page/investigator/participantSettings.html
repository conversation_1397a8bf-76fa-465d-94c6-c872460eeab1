<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script th:src="@{/js/utils.js}"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var participantId = /*[[${participantId}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var participantDisplayId = /*[[${participantDisplayId}]]*/ "";
    var studyArchived = false;
    var tab = /*[[${tab}]]*/ "";
    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: participantDisplayId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", async function () {
      window.layout.initActiveRoute();
      if (!isLoggedIn()) {
        showModal();
        //window.location.href = "/studies/all"
      }
      else if (isLoggedIn() && !isProfileCompleted()) {
        showProfilePromptModal();
      }
      else {
        if (studyId) {
          let studyOwner = await getStudyOwner(studyId);

          let archiveStatus = await getStudyArchiveStatus(studyId);
          console.log("archiveStatus", archiveStatus);
          if (archiveStatus == true || studyOwner != getLoggedInUser()) {
            toggleTooltip("update-participant-button", true);

            [...document.getElementsByClassName("participant-setting-fields")].forEach((element) => {
              element.disabled = true;
            });
            studyArchived = true;
            document.querySelector(".archive-alert").classList.remove("hidden");
          }
          else {
            let privilegeListArray = [];
            privilegeListArray =
              JSON.parse(localStorage.getItem("userPrivilegeInfoArray")) || "[]";
            if (privilegeListArray.includes("Edit Participant")) {
              toggleTooltip("update-participant-button", false);

              [...document.getElementsByClassName("participant-setting-fields")].forEach((element) => {
                element.disabled = false;
              });
              studyArchived = false;
              document.querySelector(".archive-alert").classList.add("hidden");
            }
            else {
              toggleTooltip("update-participant-button", true);

              [...document.getElementsByClassName("participant-setting-fields")].forEach((element) => {
                element.disabled = true;
              });
              document.querySelector(".archive-alert").classList.remove("hidden");
            }
          }
        }
      }
      if (document.getElementById("ethnicity_type_id")) {
        getEthnicityList()
          .then((data) => {
            localStorage.setItem('ethnicityList', JSON.stringify(data));
            createOptions("ethnicity_type_id", data);
          })
          .catch((error) => {
            console.error("Error fetching data:", error);
          });
      }
      if (document.getElementById("gender_type_id")) {
        getGenderList()
          .then((data) => {
            localStorage.setItem('genderList', JSON.stringify(data));
            createOptions("gender_type_id", data);
          })
          .catch((error) => {
            console.error("Error fetching data:", error);
          });
      }
      if (document.getElementById("race_type_id")) {
        getRaceList()
          .then((data) => {
            localStorage.setItem('raceList', JSON.stringify(data));
            createOptions("race_type_id", data);
            getParticipantInfo();
          })
          .catch((error) => {
            console.error("Error fetching data:", error);
          });
      }
      [
        "gender_type_id",
        "age"
      ].forEach(id => {
        document.getElementById(id)?.addEventListener("blur", async function () {
          let element = document.getElementById(id);
          if (document.getElementById(id)?.value != "") {
            element.classList.remove("border-[#e23636]");
          }
        });
      });
      document.getElementById("update-participant-button").addEventListener("click", async () => {
        let isValid = true;
        let fieldsToValidate = [];
        fieldsToValidate = [
          "gender_type_id",
          "age"
        ];
        // Validate all fields
        fieldsToValidate.forEach((fieldId) => {
          const isFieldValid = validateField(fieldId);
          if (!isFieldValid) {
            isValid = false;
          }
        });
        const age = DOMPurify.sanitize(document.getElementById("age").value);
        const diagnosisIcd = DOMPurify.sanitize(document.getElementById("diagnosis_icd").value);
        const medRxnorm = DOMPurify.sanitize(document.getElementById("med_rxnorm").value);
        const treatmentModality = DOMPurify.sanitize(document.getElementById("treatment_modality").value);
        const gender = DOMPurify.sanitize(document.getElementById("gender_type_id").value);
        const race = DOMPurify.sanitize(document.getElementById("race_type_id").value);
        const ethnicity = DOMPurify.sanitize(document.getElementById("ethnicity_type_id").value);
        const bmi = DOMPurify.sanitize(document.getElementById("bmi").value);
        const baselineHba1c = DOMPurify.sanitize(document.getElementById("baseline_hba1c").value);
        const diabetesType = DOMPurify.sanitize(document.getElementById("diabetes_type").value);
        const studyArm = DOMPurify.sanitize(document.getElementById("studyArm").value);
        if (isValid) {
          if (age < 0 || age > 150) {
            document.getElementById("age-error").textContent = "Age should be between 0 and 150";
            isValid = false;
            return
          } else {
            document.getElementById("age-error").textContent = "";
          }
        }
        if (isValid) {
          showLoading('updateParticipantLoader');
          try {
            // Send the user's message to the API
            const response = await fetch(`/participant/${participantId}/update`, {
              method: "PUT",
              headers: {


                "Content-Type": "application/json",
              },
              body: JSON.stringify(
                {
                  "studyId": studyId,
                  "orgPartyId": localStorage.getItem("organizationPartyId"),
                  "participantDisplayId": participantDisplayId,
                  "genderId": gender,
                  "age": age,
                  "diagnosisIcd": diagnosisIcd,
                  "medRxnorm": medRxnorm,
                  "treatmentModality": treatmentModality,
                  "raceId": race,
                  "ethnicityId": ethnicity,
                  "bmi": bmi,
                  "baselineHba1c": baselineHba1c,
                  "diabetesType": diabetesType,
                  "studyArm": studyArm
                }


              ),
            });

            if (!response.ok) {
              throw new Error("Failed to fetch response from the API.");
            }


            const data = await response.json();
            if (data.status === "success") {
              showToast("Participant updated successfully!", "success");
              hideLoading('updateParticipantLoader');
              const params = new URLSearchParams(window.location.search);
              const tab = params.get("tab");
              window.location.href = "/participants/info/" + studyId + "/" + participantId + "?tab=" + tab;
            }
            else {
              showToast("Something went wrong!", "error");
              hideLoading('updateParticipantLoader');
            }

          } catch (e) {
            console.error("Error fetching API response:", e);
            hideLoading('updateParticipantLoader');

          }
        }
        else {
          hideLoading('updateParticipantLoader');
        }
      });
    });
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
    function getParticipantInfo() {
      fetchData(
        `/participant/${participantId}`,
        (data, error) => {
          if (!error) {
            const result = data?.data?.participantData;
            result?.participant_age != null ? document.getElementById("age").value = result?.participant_age : "";
            result?.bmi != null ? document.getElementById("bmi").value = result?.bmi : "";
            result?.baseline_hba1c != null ? document.getElementById("baseline_hba1c").value = result?.baseline_hba1c : "";
            result?.diabetes_type != null ? document.getElementById("diabetes_type").value = result?.diabetes_type : "";
            result?.study_arm != null ? document.getElementById("studyArm").value = result?.study_arm : "";
            if (result?.participant_race != null) {
              const raceType = JSON.parse(localStorage.getItem("raceList"));
              const selectedRace = raceType.find(
                (race) => race.display === result?.participant_race
              );
              document.getElementById("race_type_id").value = selectedRace.race_type_id;
            }
            if (result?.participant_ethnicity != null) {
              const ethnicityType = JSON.parse(localStorage.getItem("ethnicityList"));
              const selectedEthnicity = ethnicityType.find(
                (ethnicity) => ethnicity.display === result?.participant_ethnicity
              );
              document.getElementById("ethnicity_type_id").value = selectedEthnicity.ethnicity_type_id;
            }
            result?.treatment_modality != null ? document.getElementById("treatment_modality").value = result?.treatment_modality : "";
            result?.diagnosis_icd != null ? document.getElementById("diagnosis_icd").value = result?.diagnosis_icd : "";
            result?.med_rxnorm != null ? document.getElementById("med_rxnorm").value = result?.med_rxnorm : "";
            if (result?.participant_gender != null) {
              const genderType = JSON.parse(localStorage.getItem("genderList"));
              const selectedGender = genderType.find(
                (gender) => gender.value === result?.participant_gender
              );
              document.getElementById("gender_type_id").value = selectedGender.gender_type_id;
            }
          }
          else {
            console.error("Error fetching participant info:", error);
          }
        }
      );
    }

    function assignValues(eleClass, value) {
      document.querySelectorAll("." + eleClass).forEach((element) => {
        element.classList.remove("hidden");
        element.value = value;
      });
    }

  </script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <div id="new-participant-form"
      class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative w-full max-w-2xl max-h-full">
        <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
        </div>
        <div class="form-ctr pt-2">
          <div class="border rounded dark:border-gray-600">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
              <h3 class="text-base font-semibold">Participant Settings</h3>
            </div>
            <div class="px-10 py-6 ">
              <div class="gap-1">
                <div class="grid grid-cols-3  mb-4">
                  <label for="gender_type_id" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Gender<span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_gender')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <select name="gender_type_id" id="gender_type_id" rows="4"
                      class="block  w-full rounded px-4 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500  participant-setting-fields"
                      placeholder="Select Gender">
                    </select>
                    <span id="gender_type_id-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>


                <div class="grid grid-cols-3  mb-4">
                  <label for="age" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Age<span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_age')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="age" id="age"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Enter Age" />
                    <span id="age-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>

                <div class="grid grid-cols-3  mb-4">
                  <label for="bmi" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      BMI
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_bmi')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="bmi" id="bmi" type="number"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Enter BMI" />
                    <span id="bmi-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="baseline_hba1c" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Baseline HbA1c
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_baseline_hba1c')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="baseline_hba1c" id="baseline_hba1c" type="number"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Enter Baseline HbA1c" />
                    <span id="baseline_hba1c-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="diabetes_type" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Diabetes Type
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_diabetes_type')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="diabetes_type" id="diabetes_type"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Enter Diabatese Type" />
                    <span id="diabetes_type-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="study_arm" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Study Arm
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_study_arm')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <textarea type="text" name="studyArm" id="studyArm"
                      class="block w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder=""></textarea>
                    <span id="study_arm-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="race_type_id" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Race
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_race')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <select name="race_type_id" id="race_type_id" rows="4"
                      class="block  w-full rounded px-4 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Select Race">
                    </select>
                    <span id="race_type_id-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="ethnicity_type_id" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Ethnicity
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_ethnicity')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <select name="ethnicity_type_id" id="ethnicity_type_id" rows="4"
                      class="block  w-full rounded px-4 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="Select Ethnicity">
                    </select>
                    <span id="ethnicity_type_id-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="diagnosis_icd" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Diagnosis ICD
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_diagnosis_icd')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input type="text" name="diagnosis_icd" id="diagnosis_icd"
                      class="block w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="" />
                    <span id="diagnosis_icd-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="med_rxnorm" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Med Rxnorm
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_med_rxnorm')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input type="text" name="med_rxnorm" id="med_rxnorm"
                      class="block w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="" />
                    <span id="med_rxnorm-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="treatment_modality" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Treatment Modalities
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('participant_treatment_modalities')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input type="text" name="treatment_modality" id="treatment_modality"
                      class="block w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 participant-setting-fields"
                      placeholder="" />
                    <span id="treatment_modality-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>

              </div>

            </div>
          </div>
          <div class="my-4 text-right">
            <button type="button" id="update-participant-button" data-tooltip-target="tooltip-update-participant"
              data-privilege-buttons="Edit Participant" data-tooltip-placement="top"
              class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Update Participant
            </button>
            <span id="tooltip-update-participant" role="tooltip"
              class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-left text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
              permissions are limited.</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
          </div>
          <div class="my-4 text-right hidden" id="updateParticipantLoader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              updating participant...
            </button>
          </div>
        </div>



      </div>
    </div>
    <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to update participant.')}">
    </div>
    <div
      th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to update participant.')}">
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
  </div>
</body>

</html>