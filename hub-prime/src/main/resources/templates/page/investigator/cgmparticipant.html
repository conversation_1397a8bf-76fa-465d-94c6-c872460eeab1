<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var participantId = /*[[${participantId}]]*/ "";
    var currentPage = /*[[${currentPage}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var participantDisplayId = /*[[${participantDisplayId}]]*/ "";
    var tab = /*[[${tab}]]*/ "";
  </script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: participantDisplayId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies/dashboard",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      hideLoading("cgmParticipantLoader");
      window.layout.initActiveRoute();
    });
  </script>

  <script type="module">
    import {
      AGGridAide,
      AGGridAideBuilder,
    } from "@presentation/shell/aggrid-aide.js";
    import ModalAide from "@presentation/shell/modal-aide.js";

    //const viewName = 'uniform_resource_study';
    document.addEventListener("DOMContentLoaded", function () {
      const modalAide = new ModalAide();
      const schemaName = "drh_stateless_raw_observation";
      const viewName = "file_device_cgm_observation_view";
      let fileContent = '';
      showLoading("cgmParticipantLoader");
      let gridFilterParams = {
        "study_id": {
          "filterType": "text",
          "type": "equals",
          "filter": studyId
        },
        "participant_sid": {
          "filterType": "text",
          "type": "equals",
          "filter": participantId
        }
      };

      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "FILE NAME",
            field: "file_name",
            filter: "agTextColumnFilter",
            enableRowGroup: true,
            flex: 3,
          },
          {
            headerName: "SOURCE PLATFORM",
            field: "source_platform",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 2,
          },
          // {
          //   headerName: "DEVICE ID",
          //   field: "device_id",
          //   enableRowGroup: true,
          //   filter: "agTextColumnFilter",
          //   flex: 2,
          // },
          {
            headerName: "DEVICE NAME",
            field: "devicename",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 3,
          },
          {
            headerName: "DATE TIME",
            field: "date_time",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 2,
          },
          {
            headerName: "CGM VALUE",
            field: "cgm_value",
            enableRowGroup: true,
            filter: "agTextColumnFilter",
            flex: 1,
          },
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(
            `/api/ux/tabular/jooq/` + schemaName + `/` + viewName + `.json`
          ),
          ((data, valueCols) => {
            return valueCols.map((col) => ({
              headerName: col.displayName,
              field: col.field,
            }));
          }),
          {
            beforeRequest: async (reqPayload, dataSourceUrl) => {
              // Add custom parameters here
              reqPayload.body = {
                ...reqPayload.body,
                "filterModel": {
                  ...reqPayload.body.filterModel, // Preserve AG Grid filters
                  ...gridFilterParams, // Add custom filter
                }
              };
              return reqPayload;
              // return reqPayload;
            },
            beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
              let lastResponseLength = serverRespPayload.data.length;
              // Clear the grid if there's no more data
              if (lastResponseLength === 0) {
                clearGrid();
              }
            },
          }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: (9 + 1) * 50 + "px", width: "100%" })
        .build();
      agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };
      agGridInstance.init("serverDataGrid");
      hideLoading("cgmParticipantLoader");
    });
  </script>
  <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>

  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script th:src="@{/js/utils.js}"></script>
  <script type="module" src="/js/participants.js"
    th:attr="data-studyid=${studyId},data-fileName=${fileName},data-tableName=${tableName}"></script>
</head>

<body>
  <div layout:fragment="content">
    <div th:replace="~{page/investigator/fragments/participant}"></div>
    <ul class="flex flex-wrap border-b border-gray-200 mb-4" id="pageTabs"></ul>
    <div class="flex justify-end mb-4">
      <a th:href="'/mystudies/uploadParticipant/' + ${studyId} + '/' + ${participantId} + '?tab=' +${tab}"
        class="relative">
        <button type="button" id="cgm-upload-container" data-tooltip-target="archive-cgm-upload"
          privilege-action-buttons-links="Upload Cgm Data" data-tooltip-placement="top"
          class="h-27 upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1 mr-1 relative hidden">
          <svg width="18" height="12" class="inline-block mr-1" viewBox="0 0 18 12" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M14.5125 4.53C14.0025 1.9425 11.73 2.38419e-07 9 2.38419e-07C6.8325 2.38419e-07 4.95 1.23 4.0125 3.03C1.755 3.27 0 5.1825 0 7.5C0 9.9825 2.0175 12 4.5 12H14.25C16.32 12 18 10.32 18 8.25C18 6.27 16.4625 4.665 14.5125 4.53ZM14.25 10.5H4.5C2.8425 10.5 1.5 9.1575 1.5 7.5C1.5 5.9625 2.6475 4.68 4.17 4.5225L4.9725 4.44L5.3475 3.7275C6.06 2.355 7.455 1.5 9 1.5C10.965 1.5 12.66 2.895 13.0425 4.8225L13.2675 5.9475L14.415 6.03C15.585 6.105 16.5 7.0875 16.5 8.25C16.5 9.4875 15.4875 10.5 14.25 10.5ZM10.0875 4.5H7.9125V6.75H6L9 9.75L12 6.75H10.0875V4.5Z"
              fill="#59636E" />
          </svg>
          <span class="text-xs pl-0 leading-3 font-semibold">Upload CGM Tracing</span>
        </button>
        <span id="archive-cgm-upload" role="tooltip"
          class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
          permissions are limited.</span>
        <div class="tooltip-arrow" data-popper-arrow></div>
      </a>
    </div>

    <div
      th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=${pagesubdescriptiontitle},pagesubdescription=${pagesubdescription},pageattributestitle=${pageattributestitle},pageattributes=null,notes = null,font='')}">
    </div>
    <span class="loader loader-big left-1/2 transform -translate-x-1/2 absolute z-10 mt-4"
      id="cgmParticipantLoader"></span>
    <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
    <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

          <div class="p-4 md:p-5 text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to add participant data.
            </h3>
            <a href="/">
              <button type="button"
                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                Login
              </button>

            </a>
          </div>
        </div>
      </div>
    </div>
    <div id="profile-prompt-modal" data-modal-backdrop="static" tabindex="-1"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

          <div class="p-4 md:p-5 text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please complete your profile
              information to add participant.
            </h3>
            <a href="/userprofile/info">
              <button type="button"
                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                Complete your profile
              </button>

            </a>
          </div>
        </div>
      </div>
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
  </div>
</body>

</html>