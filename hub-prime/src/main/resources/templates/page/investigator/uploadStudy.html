<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <!-- Include Tom Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

    <!-- Include Tom Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script th:src="@{/js/upload.js}"></script>
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        var currentPage = /*[[${scurrentPage}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        var showDatabaseUploadStatus = /*[[${showDatabaseUploadStatus}]]*/ false;
        var tab = /*[[${tab}]]*/ "";
    </script>
    <script type="module" src="/js/uploadstudy.js" th:attr="data-studyid=${studyId}"></script>
    <script th:inline="javascript" type="module">
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        import * as sh from "@presentation/shell/shell-aide.js";
        const tabs = [
            { text: "Dashboard", href: "/studies/dashboard" },
            { text: "Population Percentage", href: "/studies/population" },
            { text: "All Studies", href: "/studies/all" },
            { text: "My Studies", href: "/studies/mystudies" },
        ];
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: "Upload Study Database",
                breadcrumbs: [
                    {
                        text: "Studies",
                        href: "/studies/dashboard",
                    },
                    {
                        text: tab,
                        href: tabs.find(t => t.text === tab)?.href,
                    },
                    {
                        text: studyDisplayId,
                        href: "/study/info/" + studyId + "?tab=" + tab,
                    },

                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (!isLoggedIn()) {
                showModal();
                //window.location.href = "/studies/all"
            }
            else if (!isProfileCompleted()) {
                showProfilePromptModal();
            }
            fetchData(
                `/research-study?studyId=` + studyId,
                async (responseData, error) => {
                    if (!error) {
                        const result = responseData.data.studyDetails;
                        const elements = document.getElementsByClassName(
                            "title"
                        );
                        for (let el of elements) {
                            el.innerHTML = result.title;
                        }
                        getStudyVisibility()
                            .then((visibilityData) => {
                                visibilityData.forEach((element) => {
                                    const elements = document.getElementsByClassName(
                                        "st-" + element.visibility_name.toLowerCase()
                                    );
                                    if (element.visibility_id == result.visibility) {
                                        for (let el of elements) {
                                            el.classList.remove("hidden");
                                        }

                                        const statusElements =
                                            document.getElementsByClassName("st-status");
                                        for (let el of statusElements) {
                                            el.innerHTML = element.visibility_name;
                                        }
                                    } else {
                                        for (let el of elements) {
                                            el.classList.add("hidden");
                                        }
                                    }
                                });
                            })
                            .catch((error) => {
                                console.error("Error fetching data:", error);
                            });
                        if (result.archive_status == true) {
                            toggleTooltip("upload-files", result.archive_status);
                            [...document.getElementsByClassName("uploadStudyDatabase-field")].forEach((element) => {
                                element.disabled = true;
                            });
                            document.querySelector(".archive-alert").classList.remove("hidden");
                        }
                    } else {
                        console.error("Error fetching study info:", error);
                    }
                });
        });
        // Function to show the modal
        function showProfilePromptModal() {
            const modalEle = document.getElementById("profile-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideProfilePromptModal() {
            const modalEle = document.getElementById("profile-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }


        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }
    </script>

</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
    <div layout:fragment="content">
        <div class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative w-full max-w-2xl max-h-full">
                <div class="relative">
                    <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
                    </div>
                    <div th:replace="~{page/investigator/fragments/dbUploadProgress}">
                    </div>
                    <div th:replace="~{page/investigator/fragments/dbMigrationSuccess}">
                    </div>
                    <div class="flex items-center pt-4 pb-2">
                        <h3 class="text-lg font-semibold text-gray-900 title">
                        </h3>
                        <div class="flex mt-1 ml-2 px-2 py-1 border border-gray-300 rounded-full">
                            <svg class="st-private hidden" width="8.5" height="11" viewBox="0 0 8.5 11" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.875 4H7.3125V2.85714C7.3125 1.28 6.0525 0 4.5 0C2.9475 0 1.6875 1.28 1.6875 2.85714V4H1.125C0.50625 4 0 4.51429 0 5.14286V10.8571C0 11.4857 0.50625 12 1.125 12H7.875C8.49375 12 9 11.4857 9 10.8571V5.14286C9 4.51429 8.49375 4 7.875 4ZM4.5 9.14286C3.88125 9.14286 3.375 8.62857 3.375 8C3.375 7.37143 3.88125 6.85714 4.5 6.85714C5.11875 6.85714 5.625 7.37143 5.625 8C5.625 8.62857 5.11875 9.14286 4.5 9.14286ZM6.24375 4H2.75625V2.85714C2.75625 1.88 3.53813 1.08571 4.5 1.08571C5.46187 1.08571 6.24375 1.88 6.24375 2.85714V4Z"
                                    fill="#383838" />
                            </svg>
                            <svg class="st-public hidden" width="11" height="11" viewBox="0 0 11 11" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M5.5 11C4.73917 11 4.02417 10.8556 3.355 10.5669C2.68583 10.2781 2.10375 9.88625 1.60875 9.39125C1.11375 8.89625 0.721875 8.31417 0.433125 7.645C0.144375 6.97583 0 6.26083 0 5.5C0 4.73917 0.144375 4.02417 0.433125 3.355C0.721875 2.68583 1.11375 2.10375 1.60875 1.60875C2.10375 1.11375 2.68583 0.721875 3.355 0.433125C4.02417 0.144375 4.73917 0 5.5 0C6.26083 0 6.97583 0.144375 7.645 0.433125C8.31417 0.721875 8.89625 1.11375 9.39125 1.60875C9.88625 2.10375 10.2781 2.68583 10.5669 3.355C10.8556 4.02417 11 4.73917 11 5.5C11 6.26083 10.8556 6.97583 10.5669 7.645C10.2781 8.31417 9.88625 8.89625 9.39125 9.39125C8.89625 9.88625 8.31417 10.2781 7.645 10.5669C6.97583 10.8556 6.26083 11 5.5 11ZM5.5 9.9C6.72833 9.9 7.76875 9.47375 8.62125 8.62125C9.47375 7.76875 9.9 6.72833 9.9 5.5C9.9 5.43583 9.89771 5.36938 9.89313 5.30062C9.88854 5.23187 9.88625 5.17458 9.88625 5.12875C9.84042 5.39458 9.71667 5.61458 9.515 5.78875C9.31333 5.96292 9.075 6.05 8.8 6.05H7.7C7.3975 6.05 7.13854 5.94229 6.92313 5.72688C6.70771 5.51146 6.6 5.2525 6.6 4.95V4.4H4.4V3.3C4.4 2.9975 4.50771 2.73854 4.72313 2.52313C4.93854 2.30771 5.1975 2.2 5.5 2.2H6.05C6.05 1.98917 6.10729 1.80354 6.22188 1.64313C6.33646 1.48271 6.47625 1.35208 6.64125 1.25125C6.45792 1.20542 6.27229 1.16875 6.08437 1.14125C5.89646 1.11375 5.70167 1.1 5.5 1.1C4.27167 1.1 3.23125 1.52625 2.37875 2.37875C1.52625 3.23125 1.1 4.27167 1.1 5.5H3.85C4.455 5.5 4.97292 5.71542 5.40375 6.14625C5.83458 6.57708 6.05 7.095 6.05 7.7V8.25H4.4V9.7625C4.58333 9.80833 4.76438 9.84271 4.94313 9.86563C5.12188 9.88854 5.3075 9.9 5.5 9.9Z"
                                    fill="#383838" />
                            </svg>
                            <svg class="st-archived hidden" width="11" height="10" viewBox="0 0 11 10" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M1.375 2.75H0.458333V8.70833C0.458333 9.2125 0.870833 9.625 1.375 9.625H9.16667V8.70833H1.375V2.75Z"
                                    fill="#383838" />
                                <path
                                    d="M9.625 1.83333H6.41667L5.5 0.916667H3.20833C2.70417 0.916667 2.29625 1.32917 2.29625 1.83333L2.29167 6.875C2.29167 7.37917 2.70417 7.79167 3.20833 7.79167H9.625C10.1292 7.79167 10.5417 7.37917 10.5417 6.875V2.75C10.5417 2.24583 10.1292 1.83333 9.625 1.83333Z"
                                    fill="#383838" />
                            </svg>

                            <span class="px-1 leading-3 st-status text-xs text-gray-500"></span>
                        </div>
                    </div>

                    <div class="form-ctr">
                        <div class="border rounded dark:border-gray-600">
                            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
                                <h3 class="text-base font-semibold">Upload Study Database</h3>
                            </div>
                            <div class="px-10 py-6 ">
                                <div class="gap-1 mb-4 ">
                                    <span class="block mb-4 text-sm font-normal text-gray-900 ">
                                        If you have a study database generated through Edge Software, kindly upload
                                        here.
                                    </span>
                                    <div class="grid grid-cols-3">
                                        <label for="uploadStudyDatabase"
                                            class="text-sm font-normal text-gray-900  py-6">
                                            Choose File
                                        </label>
                                        <div class="border rounded dark:border-gray-600 col-span-2" id="drop-zone">
                                            <label for="uploadStudyDatabase" class="px-4 py-5 block">
                                                <div class="flex items-center justify-center">
                                                    <svg width="17" height="13" class="inline-block mt-1 mr-2"
                                                        viewBox="0 0 17 13" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M13.7063 5.05521C13.2246 2.62583 11.0783 0.802029 8.5 0.802029C6.45292 0.802029 4.675 1.95687 3.78958 3.64687C1.6575 3.87221 0 5.66784 0 7.84372C0 10.1745 1.90542 12.0687 4.25 12.0687H13.4583C15.4133 12.0687 17 10.4914 17 8.54789C17 6.68888 15.5479 5.18196 13.7063 5.05521ZM13.4583 10.6604H4.25C2.68458 10.6604 1.41667 9.39994 1.41667 7.84372C1.41667 6.40017 2.50042 5.19604 3.93833 5.04817L4.69625 4.97071L5.05042 4.30175C5.72333 3.01312 7.04083 2.21037 8.5 2.21037C10.3558 2.21037 11.9567 3.52012 12.3179 5.32984L12.5304 6.38609L13.6142 6.46355C14.7192 6.53397 15.5833 7.45643 15.5833 8.54789C15.5833 9.70977 14.6271 10.6604 13.4583 10.6604ZM9.52708 5.02704H7.47292V7.13955H5.66667L8.5 9.95623L11.3333 7.13955H9.52708V5.02704Z"
                                                            fill="#737373" />
                                                    </svg>
                                                    <span class="text-sm text-[#737373]">Drop files here or
                                                        browse</span>
                                                </div>
                                            </label>
                                            <input type="file" name="uploadStudyDatabase" id="uploadStudyDatabase"
                                                accept=".sqlite,.db"
                                                class="hidden h-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm  focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 uploadStudyDatabase-field"
                                                placeholder="Drop files here or browse" />
                                            <input type="hidden" name="studyId" id="studyId" th:value="${studyId}" />
                                            <div id="upload-ctr" class="mt-4"></div>
                                        </div>


                                        <div id="upload-list" class="mt-4 text-sm font-semibold hidden">Uploaded Files
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                        <!-- Add this inside your HTML file -->
                        <div id="uploadProgressContainer"
                            class="hidden w-full bg-gray-200 rounded-full dark:bg-gray-700 mt-2">
                            <div id="uploadProgress"
                                class="bg-gray-800 text-xs font-medium text-blue-100 text-center p-0.5 leading-none rounded-full"
                                style="width: 0%"> 0%</div>

                        </div>
                        <div class="my-4 text-right relative">
                            <button type="button" id="upload-files" data-tooltip-target="archive-upload-study"
                                data-privilege-buttons="Upload Study Db" data-tooltip-placement="top"
                                class=" text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
                                Upload
                            </button>
                            <span id="archive-upload-study" role="tooltip"
                                class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs text-left font-normal text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                                permissions are limited</span>
                            <div class="tooltip-arrow" data-popper-arrow></div>
                            <div class="col-span-2 mb-4 mt-3  hidden" id="uploadDatabaseLoader">
                                <button disabled type="button"
                                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                    <svg aria-hidden="true" role="status"
                                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                            fill="currentColor" />
                                        <path
                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                            fill="#1C64F2" />
                                    </svg>
                                    Uploading Study Database...
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <span class="loader loader-big justify-self-center absolute z-10" id="uploadStudyLoader"></span>
        <div class="justify-self-center  md:w-full lg:w-5/6">
            <div id="migration-workflow" class="mt-4 mb-8 hidden">
                <h3 class="text-lg font-semibold my-4 text-center">Study Database Migration Workflow</h3>
                <div class="ml-4 place-items-center">
                    <ol class="flex items-center w-full">
                        <li
                            class="flex w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:inline-block dark:after:border-gray-700">
                            <span id="studyMetaData-svg-ctr"
                                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 dark:bg-gray-700 shrink-0">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                    viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 6.03v13m0-13c-2.819-.831-4.715-1.076-8.029-1.023A.99.99 0 0 0 3 6v11c0 .563.466 1.014 1.03 1.007 3.122-.043 5.018.212 7.97 1.023m0-13c2.819-.831 4.715-1.076 8.029-1.023A.99.99 0 0 1 21 6v11c0 .563-.466 1.014-1.03 1.007-3.122-.043-5.018.212-7.97 1.023" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight text-sm ml-4">Study Metadata Migration</h3>
                        </li>
                        <li
                            class="flex w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:inline-block dark:after:border-gray-700">
                            <span id="participant-svg-ctr"
                                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 dark:bg-gray-700 shrink-0">
                                <svg class="w-4 h-4 text-gray-500 lg:w-5 lg:h-5 dark:text-gray-100" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                    <path
                                        d="M18 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2ZM6.5 3a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5ZM3.014 13.021l.157-.625A3.427 3.427 0 0 1 6.5 9.571a3.426 3.426 0 0 1 3.322 2.805l.159.622-6.967.023ZM16 12h-3a1 1 0 0 1 0-2h3a1 1 0 0 1 0 2Zm0-3h-3a1 1 0 1 1 0-2h3a1 1 0 1 1 0 2Zm0-3h-3a1 1 0 1 1 0-2h3a1 1 0 1 1 0 2Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight text-sm ml-4">Participant Migration</h3>
                        </li>

                        <li class="flex items-center w-full " id="cgm-ctr-final">
                            <span id="cgm-svg-ctr-final"
                                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 dark:bg-gray-700 shrink-0">
                                <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
                                    <path
                                        d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2Zm-3 14H5a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2Zm0-4H5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2Zm0-5H5a1 1 0 0 1 0-2h2V2h4v2h2a1 1 0 1 1 0 2Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight text-sm ml-4"
                                title="Continuous Glucose Monitoring Data Migration">CGM Data Migration</h3>
                        </li>
                        <li id="cgm-ctr-middle"
                            class="hidden flex w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:inline-block dark:after:border-gray-700">
                            <span id="cgm-svg-ctr-middle"
                                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 dark:bg-gray-700 shrink-0">
                                <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
                                    <path
                                        d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2Zm-3 14H5a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2Zm0-4H5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2Zm0-5H5a1 1 0 0 1 0-2h2V2h4v2h2a1 1 0 1 1 0 2Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight text-sm  ml-4"
                                title="Continuous Glucose Monitoring Data Migration">CGM Data Migration</h3>
                        </li>
                        <li class="flex items-center w-full hidden" id="mealsAndFitnes-ctr-final">
                            <span id="mealsAndFitnes-svg-ctr"
                                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 dark:bg-gray-700 shrink-0">
                                <svg class="w-4 h-4 text-gray-500 lg:w-5 lg:h-5 dark:text-gray-100" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
                                    <path
                                        d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2ZM7 2h4v3H7V2Zm5.7 8.289-3.975 3.857a1 1 0 0 1-1.393 0L5.3 12.182a1.002 1.002 0 1 1 1.4-1.436l1.328 1.289 3.28-3.181a1 1 0 1 1 1.392 1.435Z" />
                                </svg>
                            </span>
                            <h3 class="font-medium leading-tight text-sm ml-4">Meal and Fitness Data Migration</h3>
                        </li>
                    </ol>
                </div>
            </div>
            <div id="serverDataGrid" class=" ag-theme-alpine rounded border border-slate-300 hidden"></div>
        </div>


        <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to upload database.')}">
        </div>
        <div
            th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to upload database.')}">
        </div>
        <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;" class="text-xs">
        </div>
    </div>

    </div>
</body>

</html>