<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
    integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
    crossorigin="anonymous"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:src="@{/js/utils.js}"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var participantDisplayId = /*[[${participantDisplayId}]]*/ "";
    var participantId = /*[[${participantId}]]*/ "";
    var tab = /*[[${tab}]]*/ "";
    var startDate = moment().subtract(7, "days");
    var endDate = moment();
  </script>
  <style>
    .dadaterangepicker.show-calendar {
      left: 55px !important;
    }
  </style>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: participantDisplayId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
      if (isLoggedIn()) {
        toggleTooltip("edit-participant-button", false);
        toggleTooltip("cgm-upload-container", false);
      }
      else {
        toggleTooltip("edit-participant-button", true);
        toggleTooltip("cgm-upload-container", true);

      }
      if (!isLoggedIn() || !isProfileCompleted()) {
        //showModal();
        //window.location.href = "/studies/all"
      }
      else if (isLoggedIn() && !isProfileCompleted()) {
        //showProfilePromptModal();
      }

    });
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
  </script>
  <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
  <link th:href="@{/css/style.css}" rel="stylesheet" />

  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script type="module" src="/js/participants.js"
    th:attr="data-studyid=${studyId},data-fileName=${fileName},data-tableName=${tableName}"></script>
</head>

<body>
  <div layout:fragment="content">
    <div th:replace="~{page/investigator/fragments/participant}"></div>
    <ul class="flex flex-wrap border-b border-gray-200 mb-4" id="pageTabs"></ul>
    <div style="display: flex">
      <div class="py-2">
        <h2 class="text-md font-semibold text-gray-700 mb-2">
          Filter by Date Range
        </h2>
        <div class="flex space-x-2 mt-2">
          <input type="text" name="reportrange" class="min-w-52" />
        </div>
      </div>
    </div>
    <div>
      <div class="py-2">
        <div id="allmetrics">
          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/glucose-statistics}"></div>
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/time-in-range-details}"></div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/agp}"></div>
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/daily-glucose-profile}"></div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div class="">
              <div th:replace="~{fragments/glycemia-risk-index}"></div>
            </div>
            <div class="">
              <div th:replace="~{fragments/all-metrics-data}"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdn.jsdelivr.net/npm/@observablehq/plot@0.6"></script>
    <script>
      const url = "https://api.example.com/data";

      let timeMetrics = {
        timeBelowRangeLow: 0,
        timeBelowRangeVeryLow: 0,
        timeInRange: 0,
        timeAboveRangeVeryHigh: 0,
        timeAboveRangeHigh: 0,
      };
      let allMetricsData = {
        liabilityCdata: 0,
        hypoglycemicEpisodes: 0,
        euglycemicEpisodes: 0,
        hyperglycemicEpisodes: 0,
      };

      function initLoader() {
        document.querySelectorAll(".agp-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".tir-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".gri-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".dgp-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        showLoader("percentageTimeCgmActive");
        showLoader("daysCGMWorn");
        showLoader("meanGlucose");
        showLoader("GMI");
        showLoader("coefficientOfVariants");
        showLoader("liabilityIndexCdata");
        showLoader("hypoglycemicEpisodesCdata");
        showLoader("euglycemicEpisodesCdata");
        showLoader("hyperglycemicEpisodesCdata");
        showLoader("meanAmplitudeCdata");
        showLoader("mValueCdata");
        showLoader("averageDailyRiskRangeCdata");
        showLoader("jIndexCdata");
        showLoader("lowBloodGlucoseIndexCdata");
        showLoader("gradeCdata");
        showLoader("meanOfDailyDifferencesCdata");
        showLoader("congaCdata");
      }
      initLoader();
      getDateRanges();

      function getDateRanges() {
        fetchData(
          `/studies/${studyId}/participants/${participantId}/date-time-range`,
          (res, error) => {
            if (!error) {
              console.log("Date Range Result:", res);

              const result = res?.data?.dateTimeRange;

              console.log("Date Range Result:", result);
              if (
                result?.data?.min_start_date &&
                result?.data?.max_end_date
              ) {
                startDate = result?.data?.min_start_date;
                endDate = result?.data?.max_end_date;
                assignDates(startDate, endDate);
                // cb(moment(startDate), moment(endDate));
                initializeDatePicker(moment(startDate), moment(endDate));
                getParticipantData();
              } else {
                var defaultStartDate = moment().subtract(29, "days");
                var defaultEndDate = moment();
                assignDates(defaultStartDate, defaultEndDate);
                initializeDatePicker(defaultStartDate, defaultEndDate);
                const barChart = document.querySelector('stacked-bar-chart');
                document.querySelectorAll(".tir-loader").forEach((loader) => {
                  loader.classList.add("hidden");
                });
                barChart.noDataFound = true;
                const agpChart = document.querySelector('agp-chart');
                agpChart.noDataFound = true;
                document
                  .querySelectorAll(".agp-chart-element-no-data-error")
                  .forEach((elem) => {
                    elem.classList.add("hidden");
                  });
                const dgpChart = document.querySelector('dgp-chart');
                document
                  .querySelectorAll(".dgp-chart-element-no-data-error")
                  .forEach((elem) => {
                    elem.classList.add("hidden");
                  });
                dgpChart.noDataFound = true;
                const griChart = document.querySelector('gri-chart');
                document.querySelectorAll(".gri-chart-element-no-data-error").forEach((elem) => {
                  elem.classList.add("hidden");
                  griChart.noDataFound = true;
                });
                const glucoseMetrics = ["percentageTimeCgmActive", "daysCGMWorn", "meanGlucose", "GMI", "coefficientOfVariants", "liabilityIndexCdata", "timeInTightRangeCdata", "hypoglycemicEpisodesCdata", "highBloodGlucoseIndexCdata", "euglycemicEpisodesCdata", "hyperglycemicEpisodesCdata", "meanAmplitudeCdata", "mValueCdata", "jIndexCdata", "lowBloodGlucoseIndexCdata", "averageDailyRiskRangeCdata", "gradeCdata", "meanOfDailyDifferencesCdata", "congaCdata"];
                glucoseMetrics.forEach((metric) => {
                  assignValues(
                    metric,
                    nodataSvg
                  );
                  hideItem(metric + "Unit");
                });
                document.querySelector('.datatable').classList.add("hidden");
              }
            } else {
              console.error("Error fetching filter date info:", error);
            }
          }
        );
      }

      function assignDates(date1, date2) {
        assignValues("startDate", moment(startDate).format("MMMM DD, YYYY"));
        assignValues("endDate", moment(endDate).format("MMMM DD, YYYY"));
        const diffInDays = moment(date2).diff(moment(date1), "days") + 1;
        const hoursPassedToday = moment().diff(
          moment().startOf("day"),
          "hours"
        );
        if (startDate === endDate) {
          assignValues("dateDiff", hoursPassedToday + " hours");
        } else {
          assignValues("dateDiff", diffInDays + " days");
        }
      }
      function assignValues(eleClass, value) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.innerHTML = value;
        });
      }

      function getParticipantData() {
        initLoader();
        getMetrics(startDate, endDate);
        getTimeRanges(startDate, endDate);
        getGRI(startDate, endDate);
        // getLiabilityIndex();
        getAllMetricsData(startDate, endDate);
        getGraphData(startDate, endDate);
        getDailyGlucoseProfile(startDate, endDate);
      }

      function showLoader(item) {
        let loaderId = item + "Loader";
        let unitId = item + "Unit";

        if (document.getElementById(loaderId))
          document.getElementById(loaderId).classList.remove("hidden");

        //hideItem(unitId)
      }

      function hideLoader(item) {
        let loaderId = item + "Loader";
        let unitId = item + "Unit";

        if (document.getElementById(loaderId))
          document.getElementById(loaderId).classList.add("hidden");
      }

      function showItem(eleClass) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.classList.remove("hidden");
        });
      }
      function hideItem(eleClass) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.classList.add("hidden");
        });
      }



      function cb(start, end) {
        startDate = start;
        endDate = end;
        assignDates(startDate, endDate);
        getParticipantData();
      }

      function initializeDatePicker(startDate, endDate) {
        $('input[name="reportrange"]').daterangepicker(
          {
            opens: "right",
            startDate: startDate,
            endDate: endDate,
            ranges: {
              Today: [moment(), moment()],
              "Last 1 day": [moment().subtract(1, "days"), moment()],
              "Last 7 days": [moment().subtract(7, "days"), moment()],
              "Last 14 days": [moment().subtract(14, "days"), moment()],
              "Last 30 Days": [moment().subtract(30, "days"), moment()],
              "Last 90 Days": [moment().subtract(90, "days"), moment()],
            },
            cb,
          },
          function (start, end, label) {
            startDate = start.format("YYYY-MM-DD");
            endDate = end.format("YYYY-MM-DD");
            cb(startDate, endDate);
          }
        );
      }

      function formatDateToMatch(date) {
        return date.toISOString().slice(0, 19);
      }

      function clearSVG(selector, componentId) {
        const component = document.querySelector(componentId); // or '#myComponent'
        const shadowRoot = component.shadowRoot;
        const svg = shadowRoot.querySelector(selector);
        if (svg) {
          while (svg.firstChild) {
            svg.removeChild(svg.firstChild);
          }
        }
      }
    </script>
    <!-- Inline JavaScript -->
    <script th:inline="javascript">
      function showHideCalculationText(object) {
        const calculationTextTitle = document.getElementById(textTitle + 'Data');
        var textTitle = object.getAttribute('data-name');
        // Show/hide the calculation container
        const calculations = document.getElementById(textTitle);
        calculations.classList.toggle("hidden");
        if (calculations && !calculations.classList.contains("hidden")) {
          fetch(`/study/each-metrics-details/${textTitle}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
              }
              return response.json(); // Parse the JSON response
            }).then(dataResponse => {
              // Dynamically add calculation text
              const calculationText = document.getElementById(textTitle + 'Data');
              calculationText.innerHTML = ''; // Clear existing content

              const textParagraph = document.createElement('p');
              textParagraph.className = '';
              const metricInfoRaw = dataResponse?.data?.metricDefinitions[0]?.metric_info;
              const metricInfoParsed = JSON.parse(metricInfoRaw);
              textParagraph.textContent = metricInfoParsed?.description;
              calculationText.appendChild(textParagraph);

            });
        }
      }
      function hideCalculationText(object) {
        var textTitle = object.getAttribute('data-name');
        // Show/hide the calculation container
        const calculations = document.getElementById(textTitle);
        calculations.classList.add("hidden");
      }
    </script>
    <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

          <div class="p-4 md:p-5 text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to add participant data.
            </h3>
            <a href="/">
              <button type="button"
                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                Login
              </button>

            </a>
          </div>
        </div>
      </div>
    </div>
    <div id="profile-prompt-modal" data-modal-backdrop="static" tabindex="-1"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

          <div class="p-4 md:p-5 text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please complete your profile
              information to get participant Info.
            </h3>
            <a href="/userprofile/info">
              <button type="button"
                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                Complete your profile
              </button>

            </a>
          </div>
        </div>
      </div>
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
  </div>
</body>

</html>