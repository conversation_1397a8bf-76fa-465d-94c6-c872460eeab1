<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <!-- Include Tom Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

    <!-- Include Tom Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
    <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        var tab = /*[[${tab}]]*/ "";
        var count = /*[[${count}]]*/ "";
        var citation_identifier = /*[[${citation_identifier}]]*/ "";
        // Now you can use studyId in your JavaScript code
        document.addEventListener("DOMContentLoaded", function () {
            var uploadStudyLink = document.getElementById('uploadStudyLink');
            uploadStudyLink.href = '/uploadStudy/' + studyId + "?tab=" + tab;
            var settingsStudyLink = document.getElementById('settingsLink');
            settingsStudyLink.href = '/settings/' + studyId + "?tab=" + tab;
            var addParticipantLink = document.getElementById('addParticipant');
            addParticipantLink.href = '/participants/add/' + studyId + "?tab=" + tab;
        });
    </script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        var studyId = /*[[${studyId}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        var tab = /*[[${tab}]]*/ "";
        const tabs = [
            { text: "Dashboard", href: "/studies/dashboard" },
            { text: "Population Percentage", href: "/studies/population" },
            { text: "All Studies", href: "/studies/all" },
            { text: "My Studies", href: "/studies/mystudies" },
        ];
        var count = /*[[${count}]]*/ "";
        var citation_identifier = /*[[${citation_identifier}]]*/ "";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: studyDisplayId,
                breadcrumbs: [
                    {
                        text: "Studies",
                        href: "/studies",
                    },
                    {
                        text: tab,
                        href: tabs.find(t => t.text === tab)?.href,
                    }
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (isLoggedIn()) {
                toggleTooltip("addParticipant-btn", false);
                toggleTooltip("upload-study-btn", false);
                toggleTooltip("settings-study-btn", false);
            }
            // else {
            //     toggleTooltip("addParticipant-btn", true);
            //     toggleTooltip("upload-study-btn", true);
            //     toggleTooltip("settings-study-btn", true);
            // }
            const datepickerStart = document.getElementById('start_dateInput');
            const datepicker1 = new Datepicker(datepickerStart, {
                format: 'mm-dd-yyyy', // Customize the date format
                autohide: true,       // Automatically close the calendar after date selection
            });
            const datepickerEnd = document.getElementById('end_dateInput');
            const datepicker2 = new Datepicker(datepickerEnd, {
                format: 'mm-dd-yyyy', // Customize the date format
                autohide: true,       // Automatically close the calendar after date selection
            });
            // const datepickerPub = document.getElementById('publication_dateInput');
            // const datepicker3 = new Datepicker(datepickerPub, {
            //     format: 'mm-dd-yyyy', // Customize the date format
            //     autohide: true,       // Automatically close the calendar after date selection
            // });
            const datepickerpu = document.getElementById('publicationDate');
            const datepicker4 = new Datepicker(datepickerpu, {
                format: 'mm-dd-yyyy', // Customize the date format
                autohide: true,       // Automatically close the calendar after date selection
            });


            const today = new Date();
            const formattedDate = today.toISOString();
            if (document.getElementById('currentDate')) {
                document.getElementById('currentDate').value = formattedDate;
            }
        });
    </script>

    <script type="module">
        import {
            AGGridAide,
            AGGridAideBuilder,
        } from "@presentation/shell/aggrid-aide.js";
        import ModalAide from "@presentation/shell/modal-aide.js";

        const schemaName = "drh_stateless_research_study";
        const viewName = "study_participant_dashboard_metrics_view";
        let fileContent = '';

        let gridFilterParams = {
            "study_id": {
                "filterType": "text",
                "type": "equals",
                "filter": studyId
            }
        };
        document.addEventListener("DOMContentLoaded", async function () {
            const modalAide = new ModalAide();
            const agGridInstance = new AGGridAideBuilder()
                .withColumnDefs([
                    {
                        headerName: "Participant ID",
                        field: "participant_display_id",
                        filter: "agTextColumnFilter",
                        minWidth: 200,
                        cellRenderer: function (params) {
                            if (params.value) {
                                const link = document.createElement("a");
                                const participantId = params.data.participant_id;
                                link.href = "/participants/info/" + studyId + "/" + participantId + "?tab=" + tab;
                                link.innerText = params.value;
                                return link;
                            } else {
                                return null;
                            }
                        },
                    },
                    {
                        headerName: "Gender",
                        field: "gender",
                        filter: "agTextColumnFilter",
                        minWidth: 150,
                    },
                    { headerName: "Age", field: "age", filter: "agTextColumnFilter" },
                    {
                        headerName: "Study Arm",
                        field: "Study Arm",
                        filter: "agTextColumnFilter",
                        minWidth: 150,
                    },
                    {
                        headerName: "Baseline HbA1c",
                        field: "Baseline HbA1C",
                        filter: "agTextColumnFilter",
                        minWidth: 200,
                        valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "TIR", field: "tir", filter: "agTextColumnFilter", minWidth: 150, valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "TAR(VH)",
                        field: "tar_vh",
                        minWidth: 150,
                        filter: "agTextColumnFilter", valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "TAR(H)",
                        field: "tar_h",
                        minWidth: 150,
                        filter: "agTextColumnFilter", valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "TBR(L)",
                        field: "tbr_l",
                        minWidth: 150,
                        filter: "agTextColumnFilter", valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "TBR(VL)",
                        field: "tbr_vl",
                        minWidth: 150,
                        filter: "agTextColumnFilter", valueFormatter: (params) => {
                            // Check if the value is a number and append "%" symbol
                            return params.value != null ? `${params.value}%` : '';
                        }
                    },
                    {
                        headerName: "GMI", field: "gmi", minWidth: 150, filter: "agTextColumnFilter"
                        // , valueFormatter: (params) => {
                        //   // Check if the value is a number and append "%" symbol
                        //   return params.value != null ? `${params.value}mg/dL` : '';
                        // }
                    },
                    {
                        headerName: "%GV",
                        field: "percent_gv",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                    },
                    {
                        headerName: "GRI",
                        field: "gri",
                        minWidth: 150,
                        filter: "agTextColumnFilter"
                    },
                    {
                        headerName: "Days Of Wear",
                        field: "days_of_wear",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                    },
                    {
                        headerName: "Data Start Date",
                        field: "data_start_date",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                    },
                    {
                        headerName: "Data End Date",
                        field: "data_end_date",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                    },
                    {
                        headerName: "CGM Devices",
                        field: "cgm_devices",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`
                    },
                    {
                        headerName: "CGM Files",
                        field: "cgm_files",
                        minWidth: 150,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`
                    },
                ])
                .withServerSideDatasource(
                    window.shell.serverSideUrl(
                        `/api/ux/tabular/jooq/` + schemaName + `/` + viewName + `.json`
                    ),
                    ((data, valueCols) => {
                        return valueCols.map((col) => ({
                            headerName: col.displayName,
                            field: col.field,
                        }));
                    }),
                    {
                        beforeRequest: async (reqPayload, dataSourceUrl) => {
                            // Add custom parameters here
                            reqPayload.body = {
                                ...reqPayload.body,
                                "filterModel": {
                                    ...reqPayload.body.filterModel, // Preserve AG Grid filters
                                    ...gridFilterParams, // Add custom filter
                                },
                            };
                            return reqPayload;

                        },
                        beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
                            let lastResponseLength = serverRespPayload.data.length; console.log(lastResponseLength);
                            // Clear the grid if there's no more data
                            if (lastResponseLength === 0) {
                                clearGrid();
                            }
                        },
                    },
                )
                .withModalAide(modalAide)
                .withGridDivStyles({ height: "750px", width: "100%" })
                .build();
            agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

            agGridInstance.init("serverDataGrid");
            //   getUserPrivileges()
            //     .then((data) => {
            //         let permissionsList = userViewEditPermissions('STUDY');
            //     })
            //     .catch((error) => {
            //         console.error("Error fetching data:", error);
            //     });


            if (studyId != '') {
                let archiveStatus = await getStudyArchiveStatus(studyId);
                console.log("archiveStatus", archiveStatus);
                if (archiveStatus == true) {
                    toggleTooltip("addParticipant-btn", true);
                    toggleTooltip("upload-study-btn", true);
                    studyArchived = true;
                }
                else {
                    toggleTooltip("addParticipant-btn", false);
                    toggleTooltip("upload-study-btn", false);
                    studyArchived = false;
                }
                await loadPublications(studyId);
                await loadPrimaryInvestigator(studyId);
                await loadNominatedPrincipalInvestigator(studyId);
                await loadCoInvestigator(studyId);
                await loadStudyTeamDetails(studyId);
                await loadInvestigatorSource();
                await loadSourceAuthors();
                await loadStudyDetails(studyId);
            }

        });

    </script>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/study.js}"></script>
    <script th:src="@{/js/utils.js}"></script>
    <script th:src="@{/js/publication.js}"></script>

</head>

<body>
    <div layout:fragment="content">
        <div class="grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 gap-">
            <div class="col-span-1 md:col-span-9 lg:col-span-9">
                <div class="flex">
                    <h5 class="text-l title"></h5>
                    <div>
                        <div class="flex mt-1 ml-2 px-2 py-1 border border-gray-300 rounded-full">
                            <svg class="st-private hidden" width="9" height="12" viewBox="0 0 9 12" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.875 4H7.3125V2.85714C7.3125 1.28 6.0525 0 4.5 0C2.9475 0 1.6875 1.28 1.6875 2.85714V4H1.125C0.50625 4 0 4.51429 0 5.14286V10.8571C0 11.4857 0.50625 12 1.125 12H7.875C8.49375 12 9 11.4857 9 10.8571V5.14286C9 4.51429 8.49375 4 7.875 4ZM4.5 9.14286C3.88125 9.14286 3.375 8.62857 3.375 8C3.375 7.37143 3.88125 6.85714 4.5 6.85714C5.11875 6.85714 5.625 7.37143 5.625 8C5.625 8.62857 5.11875 9.14286 4.5 9.14286ZM6.24375 4H2.75625V2.85714C2.75625 1.88 3.53813 1.08571 4.5 1.08571C5.46187 1.08571 6.24375 1.88 6.24375 2.85714V4Z"
                                    fill="#383838" />
                            </svg>
                            <svg class="st-public hidden" width="11" height="11" viewBox="0 0 11 11" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M5.5 11C4.73917 11 4.02417 10.8556 3.355 10.5669C2.68583 10.2781 2.10375 9.88625 1.60875 9.39125C1.11375 8.89625 0.721875 8.31417 0.433125 7.645C0.144375 6.97583 0 6.26083 0 5.5C0 4.73917 0.144375 4.02417 0.433125 3.355C0.721875 2.68583 1.11375 2.10375 1.60875 1.60875C2.10375 1.11375 2.68583 0.721875 3.355 0.433125C4.02417 0.144375 4.73917 0 5.5 0C6.26083 0 6.97583 0.144375 7.645 0.433125C8.31417 0.721875 8.89625 1.11375 9.39125 1.60875C9.88625 2.10375 10.2781 2.68583 10.5669 3.355C10.8556 4.02417 11 4.73917 11 5.5C11 6.26083 10.8556 6.97583 10.5669 7.645C10.2781 8.31417 9.88625 8.89625 9.39125 9.39125C8.89625 9.88625 8.31417 10.2781 7.645 10.5669C6.97583 10.8556 6.26083 11 5.5 11ZM5.5 9.9C6.72833 9.9 7.76875 9.47375 8.62125 8.62125C9.47375 7.76875 9.9 6.72833 9.9 5.5C9.9 5.43583 9.89771 5.36938 9.89313 5.30062C9.88854 5.23187 9.88625 5.17458 9.88625 5.12875C9.84042 5.39458 9.71667 5.61458 9.515 5.78875C9.31333 5.96292 9.075 6.05 8.8 6.05H7.7C7.3975 6.05 7.13854 5.94229 6.92313 5.72688C6.70771 5.51146 6.6 5.2525 6.6 4.95V4.4H4.4V3.3C4.4 2.9975 4.50771 2.73854 4.72313 2.52313C4.93854 2.30771 5.1975 2.2 5.5 2.2H6.05C6.05 1.98917 6.10729 1.80354 6.22188 1.64313C6.33646 1.48271 6.47625 1.35208 6.64125 1.25125C6.45792 1.20542 6.27229 1.16875 6.08437 1.14125C5.89646 1.11375 5.70167 1.1 5.5 1.1C4.27167 1.1 3.23125 1.52625 2.37875 2.37875C1.52625 3.23125 1.1 4.27167 1.1 5.5H3.85C4.455 5.5 4.97292 5.71542 5.40375 6.14625C5.83458 6.57708 6.05 7.095 6.05 7.7V8.25H4.4V9.7625C4.58333 9.80833 4.76438 9.84271 4.94313 9.86563C5.12188 9.88854 5.3075 9.9 5.5 9.9Z"
                                    fill="#383838" />
                            </svg>
                            <svg class="st-internal hidden" width="11" height="10" viewBox="0 0 11 10" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M1.375 2.75H0.458333V8.70833C0.458333 9.2125 0.870833 9.625 1.375 9.625H9.16667V8.70833H1.375V2.75Z"
                                    fill="#383838" />
                                <path
                                    d="M9.625 1.83333H6.41667L5.5 0.916667H3.20833C2.70417 0.916667 2.29625 1.32917 2.29625 1.83333L2.29167 6.875C2.29167 7.37917 2.70417 7.79167 3.20833 7.79167H9.625C10.1292 7.79167 10.5417 7.37917 10.5417 6.875V2.75C10.5417 2.24583 10.1292 1.83333 9.625 1.83333Z"
                                    fill="#383838" />
                            </svg>

                            <span class="px-1 leading-3 st-status text-xs"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-1 md:col-span-3 lg:col-span-3 md:text-right mt-2 md:mt-0">
                <a id="uploadStudyLink" href="#">

                    <button type="button" id="upload-study-btn" data-tooltip-target="tooltip-upload-study"
                        privilege-action-buttons-links="Upload Study Db" data-tooltip-placement="top"
                        class="hidden upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1 mr-1">
                        <svg width="18" height="12" class="inline-block mr-1" viewBox="0 0 18 12" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M14.5125 4.53C14.0025 1.9425 11.73 2.38419e-07 9 2.38419e-07C6.8325 2.38419e-07 4.95 1.23 4.0125 3.03C1.755 3.27 0 5.1825 0 7.5C0 9.9825 2.0175 12 4.5 12H14.25C16.32 12 18 10.32 18 8.25C18 6.27 16.4625 4.665 14.5125 4.53ZM14.25 10.5H4.5C2.8425 10.5 1.5 9.1575 1.5 7.5C1.5 5.9625 2.6475 4.68 4.17 4.5225L4.9725 4.44L5.3475 3.7275C6.06 2.355 7.455 1.5 9 1.5C10.965 1.5 12.66 2.895 13.0425 4.8225L13.2675 5.9475L14.415 6.03C15.585 6.105 16.5 7.0875 16.5 8.25C16.5 9.4875 15.4875 10.5 14.25 10.5ZM10.0875 4.5H7.9125V6.75H6L9 9.75L12 6.75H10.0875V4.5Z"
                                fill="#59636E" />
                        </svg>
                        <span class="text-xs pl-0 leading-3 font-semibold">Upload Study Database</span>
                    </button>
                    <span id="tooltip-upload-study" role="tooltip"
                        class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                        permissions are limited.</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </a>
                <a id="settingsLink" href="#">
                    <button type="button" id="settings-study-btn" data-tooltip-target="tooltip-settings-study"
                        data-tooltip-placement="top" privilege-action-buttons-links="Study Settings"
                        class="hidden upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1">
                        <svg width="14" height="16" class="inline-block mr-1" viewBox="0 0 14 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12.3552 8.70506C12.3852 8.48006 12.4002 8.24756 12.4002 8.00006C12.4002 7.76006 12.3852 7.52006 12.3477 7.29506L13.8702 6.11006C14.0052 6.00506 14.0427 5.80256 13.9602 5.65256L12.5202 3.16256C12.4302 2.99756 12.2427 2.94506 12.0777 2.99756L10.2852 3.71756C9.91022 3.43256 9.51272 3.19256 9.07022 3.01256L8.80022 1.10756C8.77022 0.92756 8.62022 0.80006 8.44022 0.80006H5.56022C5.38022 0.80006 5.23772 0.92756 5.20772 1.10756L4.93772 3.01256C4.49522 3.19256 4.09022 3.44006 3.72272 3.71756L1.93022 2.99756C1.76522 2.93756 1.57772 2.99756 1.48772 3.16256L0.0552156 5.65256C-0.0347844 5.81006 -0.00478458 6.00506 0.145216 6.11006L1.66772 7.29506C1.63022 7.52006 1.60022 7.76756 1.60022 8.00006C1.60022 8.23256 1.61522 8.48006 1.65272 8.70506L0.130216 9.89006C-0.00478446 9.99506 -0.0422845 10.1976 0.0402155 10.3476L1.48022 12.8376C1.57022 13.0026 1.75772 13.0551 1.92272 13.0026L3.71522 12.2826C4.09022 12.5676 4.48772 12.8076 4.93022 12.9876L5.20022 14.8926C5.23772 15.0726 5.38022 15.2001 5.56022 15.2001H8.44022C8.62022 15.2001 8.77022 15.0726 8.79272 14.8926L9.06272 12.9876C9.50522 12.8076 9.91022 12.5676 10.2777 12.2826L12.0702 13.0026C12.2352 13.0626 12.4227 13.0026 12.5127 12.8376L13.9527 10.3476C14.0427 10.1826 14.0052 9.99506 13.8627 9.89006L12.3552 8.70506ZM7.00022 10.7001C5.51522 10.7001 4.30022 9.48506 4.30022 8.00006C4.30022 6.51506 5.51522 5.30006 7.00022 5.30006C8.48522 5.30006 9.70022 6.51506 9.70022 8.00006C9.70022 9.48506 8.48522 10.7001 7.00022 10.7001Z"
                                fill="#59636E" />
                        </svg>
                        <span class="text-xs pl-0 leading-3 font-semibold">Settings</span>
                    </button>
                    <span id="tooltip-settings-study" role="tooltip"
                        class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                        permissions are limited.</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 gap-">
            <div class="col-span-1 md:col-span-9 lg:col-span-10">
                <article class="grid grid-cols-1 md:grid-cols-12 gap-0 md:gap-5">
                    <div class="col-span-1 md:col-span-12 lg:col-span-12 pb-4">
                        <div clas="mb-2"
                            th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=${pagesubdescriptiontitle},pagesubdescription=${pagesubdescription},pageattributestitle=${pageattributestitle}, pageattributes=${pageattributes}, notes=null,font='font-semibold')}">
                        </div>
                    </div>

                </article>
                <div class="grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 gap-">
                    <div class="col-span-1 md:col-span-12 lg:col-span-12">
                        <div>
                            <div
                                class=" grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 justify-between w-full px-4 border border-gray-200 bg-gray-50 rounded">

                                <div
                                    class="col-span-1 md:col-span-2 lg:col-span-2 pl-3 md:pl-0 py-3 pr-3 relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('textarea','title','Study Name','','Add Study Name...','basic','')}">
                                    </div>
                                </div>
                                <div class="col-span-1 md:col-span-2 lg:col-span-2 p-3 md:border-l relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','start_date','Start Date','','Add Start Date...','basic','')}">
                                    </div>
                                </div>
                                <div class="col-span-1 md:col-span-2 lg:col-span-2 p-3 md:border-l relative study-item">

                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','end_date','End Date','','Add End Date...','basic','')}">
                                    </div>
                                </div>
                                <div class="col-span-1 md:col-span-2 lg:col-span-2 p-3 md:border-l relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','nct_number','NCT Number','','Add NCT Number...','basic','')}">
                                    </div>
                                </div>
                                <div class="col-span-1 md:col-span-2 lg:col-span-2 p-3 md:border-l relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','study_location','Location','','Add Study Location...','advanced','')}">
                                    </div>
                                </div>
                                <div class="col-span-1 md:col-span-2 lg:col-span-2 p-3 md:border-l relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','treatment_modality','Treatment Modalities','','Add Treatment Modalities...','advanced','')}">
                                    </div>
                                </div>


                            </div>
                            <div
                                class="  grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 justify-between w-full px-4 border border-gray-200 bg-gray-50 rounded  mt-4">
                                <div
                                    class="col-span-1 md:col-span-3 lg:col-span-3 pl-3 md:pl-0 py-3 pr-3 relative study-item">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('text','funding_source','Funding Source','','Add Funding Source...','advanced','')}">
                                    </div>
                                </div>
                                <div
                                    class="col-span-1 md:col-span-9 lg:col-span-9 p-3  md:border-l relative study-item ">
                                    <div
                                        th:replace="~{page/investigator/fragments/editField :: editField('textarea','description','Study Description','','No Description Provided...','basic','')}">
                                    </div>
                                </div>
                            </div>
                            <div class="w-full py-2 mt-4 mb-4 flex justify-between items-center">
                                <div class="text-left">
                                    <p class="text-black-500 font-bold text-xl"></p>
                                </div>
                                <div class="text-right ">
                                    <button privilege-action-buttons-links="Edit Study Inline" id="addPublication-btn"
                                        data-tooltip-target="tooltip-publications-study" data-tooltip-placement="top"
                                        class="hidden upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1 mr-1"
                                        onclick="showDrawerPublish()">
                                        <svg width="13" height="13" class="inline-block" viewBox="0 0 13 13" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.5 0.25C3.05 0.25 0.25 3.05 0.25 6.5C0.25 9.95 3.05 12.75 6.5 12.75C9.95 12.75 12.75 9.95 12.75 6.5C12.75 3.05 9.95 0.25 6.5 0.25ZM9.625 7.125H7.125V9.625H5.875V7.125H3.375V5.875H5.875V3.375H7.125V5.875H9.625V7.125Z"
                                                fill="#59636E" />
                                        </svg>
                                        <span class="text-xs pl-0 leading-3 font-semibold">Add Publications</span>
                                    </button>
                                    <span id="tooltip-publications-study" role="tooltip"
                                        class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                                        permissions are limited.</span>
                                    <div class="tooltip-arrow" data-popper-arrow></div>
                                </div>
                            </div>
                            <div id="publicationContainer" class="mt-4">
                                <div th:fragment="publicationFragment(count,citation_identifier)">
                                    <div th:if="${count!= '' && count!= null}"
                                        class=" grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 justify-between w-full px-4 border border-gray-200 bg-gray-50 rounded  mt-4 publication-items">
                                        <div
                                            class="col-span-1 md:col-span-2 lg:col-span-2   py-3 pr-3 relative study-item">
                                            <div
                                                th:replace="~{page/investigator/fragments/editField :: editField('text','publication_medid','Pubmed Id','','Add Pubmed Id...','publication',${citation_identifier})}">
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-1 md:col-span-2 lg:col-span-2 p-3 border-l pr-3 relative study-item">
                                            <div
                                                th:replace="~{page/investigator/fragments/editField :: editField('text','publication_doi','DOI','','Add DOI...','publication',${citation_identifier})}">
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-1 md:col-span-3 lg:col-span-3 p-3 border-l pr-3 relative study-item">
                                            <div
                                                th:replace="~{page/investigator/fragments/editField :: editField('textarea','publication_title','Publication Title','','Add Publication Title...','publication',${citation_identifier})}">
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-1 md:col-span-2 lg:col-span-2 p-3 border-l pr-3 relative study-item">
                                            <div
                                                th:replace="~{page/investigator/fragments/editField :: editField('text','publication_date','Publication Date','','Add Publication Date...','publication',${citation_identifier})}">
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-2 md:col-span-3 lg:col-span-3 p-3 border-l pr-3 relative study-item">
                                            <div
                                                th:replace="~{page/investigator/fragments/editField :: editField('dropdown','teamOfAuthors','Team of Authors','','Add Team of Authors...','author',${citation_identifier})}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-2 mb-4 mt-3  hidden" id="publicationDetailsLoader">
                                <button disabled type="button"
                                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                    <svg aria-hidden="true" role="status"
                                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                            fill="currentColor" />
                                        <path
                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                            fill="#1C64F2" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-span-1 md:col-span-3 lg:col-span-2">
                <div class="p-2 ml-6 mt-3">
                    <div class="py-2 relative study-item">
                        <div
                            th:replace="~{page/investigator/fragments/editField :: editField('dropdown','primaryInvestigator',' Study Principal Investigator','','Enter Study Principal Investigator...','investigator','')}">
                        </div>
                    </div>
                    <div class="py-2 relative study-item">
                        <div
                            th:replace="~{page/investigator/fragments/editField :: editField('dropdown','nominatedPrincipalInvestigator','Nominated Principal Investigator','','Enter Nominated Principal Investigator...','investigator','')}">
                        </div>
                    </div>
                    <div class="py-2 relative study-item">
                        <div
                            th:replace="~{page/investigator/fragments/editField :: editField('dropdown','coinvestigatorsList',' Co-Investigators','','Enter Co-Investigators...','investigator','')}">
                        </div>
                    </div>
                    <div class="py-2 relative study-item">
                        <div
                            th:replace="~{page/investigator/fragments/editField :: editField('dropdown','investigatorsList',' Study Team','','Enter Study Team...','investigator','')}">
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="w-full py-2 mt-4 mb-4 flex justify-between items-center">
            <div class="text-left">
                <p class="text-black-500 font-bold text-xl"></p>
            </div>
            <div class="text-right ">
                <a href="#" id="addParticipant">
                    <button type="button" id="addParticipant-btn" data-tooltip-target="tooltip-archive-participant"
                        privilege-action-buttons-links="Add Participant" data-tooltip-placement="top"
                        class="hidden upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1 mr-1">
                        <svg width="13" height="13" class="inline-block" viewBox="0 0 13 13" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M6.5 0.25C3.05 0.25 0.25 3.05 0.25 6.5C0.25 9.95 3.05 12.75 6.5 12.75C9.95 12.75 12.75 9.95 12.75 6.5C12.75 3.05 9.95 0.25 6.5 0.25ZM9.625 7.125H7.125V9.625H5.875V7.125H3.375V5.875H5.875V3.375H7.125V5.875H9.625V7.125Z"
                                fill="#59636E" />
                        </svg>
                        <span class="text-xs pl-0 leading-3 font-semibold">Add Participants</span>
                    </button>
                    <span id="tooltip-archive-participant" role="tooltip"
                        class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-left text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                        permissions are limited.</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </a>

            </div>
        </div>
        <div id="serverDataGrid" class=" ag-theme-alpine rounded border border-slate-300"></div>
        <div id="participant-container" class="hidden border border-[C6CED5]-800 rounded-md">
            <div class="flex flex-col items-center text-center md:py-20  lg:py-20 sm:py-5  md:px-80  lg:px-80 sm:px-5">
                <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24"
                    data-view-component="true" class="octicon octicon-person-add color-fg-muted mb-2">
                    <path
                        d="M4 9.5a5 5 0 1 1 7.916 4.062 7.973 7.973 0 0 1 5.018 *********** 0 1 1-1.499.044 6.469 6.469 0 0 0-12.932 0 .75.75 0 0 1-1.499-.044 7.972 7.972 0 0 1 5.059-7.181A4.994 4.994 0 0 1 4 9.5ZM9 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm10.25-5a.75.75 0 0 1 .75.75V4h2.25a.75.75 0 0 1 0 1.5H20v2.25a.75.75 0 0 1-1.5 0V5.5h-2.25a.75.75 0 0 1 0-1.5h2.25V1.75a.75.75 0 0 1 .75-.75Z">
                    </path>
                </svg>

                <h2 class="font-semibold text-2xl pt-4">This study currently has no participants enrolled.</h2>
                <p class="text-sm font-normal p-2">Add participants to this study either by<a
                        class="underline text-blue-600" th:href="'/participants/add/' + ${studyId}+'?tab='+ ${tab}"> csv
                        upload</a> or
                    <a class="underline text-blue-600"
                        th:href="'/participants/add/' + ${studyId}+'?tab='+ ${tab}">manual addition</a> .
                </p>
            </div>
        </div>
        <!-- drawer component -->
        <div id="drawer-publication"
            class="fixed top-0 right-0 z-40 w-[480px] h-screen p-8 overflow-y-auto transition-transform translate-x-full bg-white dark:bg-gray-800"
            tabindex="-1" aria-labelledby="drawer-right-label">
            <h5 id="drawer-label"
                class="inline-flex items-center mb-6 text-base font-semibold text-gray-500 uppercase dark:text-gray-400">
                <svg class="w-4 h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                </svg>Publication Details
            </h5>
            <button type="button" id="close-publish-drawn"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center dark:hover:bg-gray-600 dark:hover:text-white">
                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                </svg>
                <span class="sr-only">Close menu</span>
            </button>
            <!-- <form class="mb-6"> -->
            <div class="mb-6 p-4 border rounded dark:border-gray-600">
                <div class="grid gap-1 mb-4 grid-cols-2">
                    <div class="col-span-2 mb-2 input-container" id="publication_pubmed-container">
                        <label for="publicationPubmed" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                            <div class="flex flex-row">
                                Pubmed Id
                                <div
                                    th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('pubmedIdDrawer')}">
                                </div>
                            </div>
                        </label>
                        <input type="text" name="publicationPubmed" id="publicationPubmed"
                            class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                            placeholder="Pubmed Id" />
                        <span id="publicationPubmed-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                        <div class="text-end my-1 hidden" id="fetch-pubmed-btn-ctr">
                            <button id="fetch-pubmed-btn" type="button"
                                class="text-white bg-[#03A9F4]  focus:ring-4 focus:outline-none font-normal  rounded  inline-flex items-center px-2 py-0.5 text-center  text-xs ">Get
                                Details</button>
                        </div>
                        <input type="hidden" id="publicationId" name="publicationId" value="" />

                    </div>
                    <div class="col-span-2 mb-4 mt-3  hidden" id="publicationPubmedLoader">
                        <button disabled type="button"
                            class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                            <svg aria-hidden="true" role="status"
                                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor" />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="#1C64F2" />
                            </svg>
                        </button>
                    </div>
                    <div class="col-span-2 mb-2 input-container" id="publication_doi-container">
                        <label for="publicationDoi" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                            <div class="flex flex-row">
                                DOI
                                <div
                                    th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationdoiDrawer')}">
                                </div>
                            </div>
                        </label>
                        <input type="text" name="publicationDoi" id="publicationDoi"
                            class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                            placeholder="DOI" />
                        <span id="publicationDoi-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                        <div class="text-end my-1 hidden" id="fetch-doi-btn-ctr">
                            <button id="fetch-doi-btn" type="button"
                                class="text-white bg-[#03A9F4]  focus:ring-4 focus:outline-none font-normal  rounded  inline-flex items-center px-2 py-0.5 text-center  text-xs ">Get
                                Details</button>
                        </div>
                    </div>
                    <div class="col-span-2 mb-4 mt-3  hidden" id="publicationDoiLoader">
                        <button disabled type="button"
                            class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                            <svg aria-hidden="true" role="status"
                                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor" />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="#1C64F2" />
                            </svg>
                        </button>
                    </div>
                    <div class="col-span-2 mb-2">
                        <label for="publicationTitle" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                            <div class="flex flex-row">
                                Publication Title
                                <div
                                    th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationTitleDrawer')}">
                                </div>
                            </div>
                        </label>
                        <input type="text" name="publicationTitle" id="publicationTitle"
                            class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                            placeholder="Publication Title" />
                        <span id="publicationTitle-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                    <div class="col-span-2 mb-2">
                        <label for="publicationDate" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                            <div class="flex flex-row">
                                Publication Date
                                <div
                                    th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationdateDrawer')}">
                                </div>
                            </div>
                        </label>
                        <input type="text" name="publicationDate" id="publicationDate"
                            class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  datepicker-input"
                            placeholder="Publication Date" />
                        <span id="publicationDate-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                    <div class="col-span-2 ">
                        <label for="authorTeam" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                            <div class="flex flex-row">
                                Team Of Authors
                                <div
                                    th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('authorteamDrawer')}">
                                </div>
                            </div>
                        </label>
                        <select name="authorTeam" id="authorTeam"
                            class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  teamOfAuthors"></select>
                        <span id="authorTeam-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                    </div>



                </div>


            </div>
            <div id="data-fetch-message" class="hidden text-green-700"></div>
            <div id="data-fetch-alert" class="hidden text-red-600"></div>

            <div class="flex justify-end pt-3">
                <button type="submit" id="update-publish-btn"
                    class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                    Update
                </button>
            </div>

            <div class="col-span-2 mb-4 -mt-3 hidden" id="publishLoader">
                <button disabled type="button"
                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                    <svg aria-hidden="true" role="status"
                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                        fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="#1C64F2" />
                    </svg>
                    Update study...
                </button>
            </div>
        </div>



        <!-- Loader Overlay -->
        <div id="loaderOverlay"
            class="hidden fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
            <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-gray-300">
            </div>
        </div>
        <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
    </div>
</body>

</html>