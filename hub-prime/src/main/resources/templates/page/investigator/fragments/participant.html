<script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
<div class="grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 gap-">
    <div class="col-span-1 md:col-span-7 lg:col-span-7">
        <div class="flex">
            <h5 class="text-l participantId"></h5>
        </div>
    </div>
    <div class="col-span-1 md:col-span-5 lg:col-span-5 justify-self-end mt-2 md:mt-0 relative">
        <button type="button" id="edit-participant-button" data-tooltip-target="archive-edit-participant"
            privilege-action-buttons-links="Edit Participant" data-tooltip-placement="top"
            class="upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-2 py-0 pb-1 relative hidden">
            <svg width="14" height="16" class="inline-block" viewBox="0 0 14 16" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M12.3552 8.70506C12.3852 8.48006 12.4002 8.24756 12.4002 8.00006C12.4002 7.76006 12.3852 7.52006 12.3477 7.29506L13.8702 6.11006C14.0052 6.00506 14.0427 5.80256 13.9602 5.65256L12.5202 3.16256C12.4302 2.99756 12.2427 2.94506 12.0777 2.99756L10.2852 3.71756C9.91022 3.43256 9.51272 3.19256 9.07022 3.01256L8.80022 1.10756C8.77022 0.92756 8.62022 0.80006 8.44022 0.80006H5.56022C5.38022 0.80006 5.23772 0.92756 5.20772 1.10756L4.93772 3.01256C4.49522 3.19256 4.09022 3.44006 3.72272 3.71756L1.93022 2.99756C1.76522 2.93756 1.57772 2.99756 1.48772 3.16256L0.0552156 5.65256C-0.0347844 5.81006 -0.00478458 6.00506 0.145216 6.11006L1.66772 7.29506C1.63022 7.52006 1.60022 7.76756 1.60022 8.00006C1.60022 8.23256 1.61522 8.48006 1.65272 8.70506L0.130216 9.89006C-0.00478446 9.99506 -0.0422845 10.1976 0.0402155 10.3476L1.48022 12.8376C1.57022 13.0026 1.75772 13.0551 1.92272 13.0026L3.71522 12.2826C4.09022 12.5676 4.48772 12.8076 4.93022 12.9876L5.20022 14.8926C5.23772 15.0726 5.38022 15.2001 5.56022 15.2001H8.44022C8.62022 15.2001 8.77022 15.0726 8.79272 14.8926L9.06272 12.9876C9.50522 12.8076 9.91022 12.5676 10.2777 12.2826L12.0702 13.0026C12.2352 13.0626 12.4227 13.0026 12.5127 12.8376L13.9527 10.3476C14.0427 10.1826 14.0052 9.99506 13.8627 9.89006L12.3552 8.70506ZM7.00022 10.7001C5.51522 10.7001 4.30022 9.48506 4.30022 8.00006C4.30022 6.51506 5.51522 5.30006 7.00022 5.30006C8.48522 5.30006 9.70022 6.51506 9.70022 8.00006C9.70022 9.48506 8.48522 10.7001 7.00022 10.7001Z"
                    fill="#59636E" />
            </svg>
            <span class="text-xs pl-0 leading-3 ">Edit Participant</span>
        </button>
        <span id="archive-edit-participant" role="tooltip"
            class="tooltip-txt hidden absolute z-50 text-left invisible inline-block px-2 py-1 text-xs font-normal text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 w-48 dark:bg-white border">Edit
            permissions are limited.</span>
        <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
</div>
<form id="participantUpdateForm">
    <div class="py-4">
        <div
            class=" grid grid-cols-1 md:grid-cols-7 lg:grid-cols-7 gap-4 justify-between w-full px-4 border border-gray-200 bg-gray-50 rounded">

            <div class="col-span-1 md:col-span-1 lg:col-span-1 pl-3 md:pl-0 py-3">
                <p class="text-black-500 font-bold text-sm">Participant ID</p>
                <span class="loader loader-small" id="participantIDLoader"></span>
                <p class="text-black-500 font-normal text-sm mt-1 participantId"></p>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('dropdown','gender_type_id','Gender','','Add Gender...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('number','age','Age','','Add Age...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('number','bmi','BMI','','Add BMI...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('number','baseline_hba1c','Baseline HbA1c','','Add Baseline HbA1c...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-2 lg:col-span-2 px-5 py-3 pr-3 md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('textarea','study_arm','Study Arm','','Add Study Arm...','','')}">
                </div>
            </div>

        </div>
        <div
            class="grid grid-cols-1 md:grid-cols-7 lg:grid-cols-7 gap-4 justify-between w-full px-4 border border-gray-200 bg-gray-50 rounded mt-4">
            <div class="col-span-1 md:col-span-1 lg:col-span-1 pl-3 md:pl-0 py-3 relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('text','diabetes_type','Diabetes Type','','Add Diabetes Type...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('text','diagnosis_icd','Diagnosis ICD','','Add Diagnosis ICD...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('text','med_rxnorm','Med Rxnorm','','Add Med Rxnorm...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('dropdown','race_type_id','Race','','Add Race...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-1 lg:col-span-1 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('dropdown','ethnicity_type_id','Ethnicity','','Add Baseline HbA1c...','','')}">
                </div>
            </div>
            <div class="col-span-1 md:col-span-2 lg:col-span-2 px-5 py-3 pr-3  md:border-l relative participant-item">
                <div
                    th:replace="~{page/investigator/fragments/editField :: editField('text','treatment_modality','Treatment Modalities','','Add Treatment Modalities...','','')}">
                </div>
            </div>

        </div>

    </div>
</form>