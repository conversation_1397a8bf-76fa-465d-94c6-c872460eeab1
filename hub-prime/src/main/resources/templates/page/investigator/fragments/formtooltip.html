<div th:fragment="formtooltip(id)">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
        th:attr="data-tooltip-target=${id}" stroke="currentColor" class="size-4 ml-1 text-gray-600 field-label-info">
        <path stroke-linecap="round" stroke-linejoin="round"
            d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
    </svg>
    <div th:id="${id}" role="tooltip" data-tooltip-placement= "left"
        class="absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black transition-opacity duration-300 bg-gray-100 rounded-lg w-48 shadow-xs opacity-0 tooltip dark:bg-white border">
        <span></span>
        <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
</div>