<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script th:src="@{/js/profile.js}"></script>
    <script th:src="@{/js/organization.js}"></script>
    <!-- Include Tom Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

    <!-- Include Tom Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <script th:inline="javascript" type="module">
        var verificationEmail = /*[[${verificationEmail}]]*/ "";
        document.getElementById("userEmail").value = verificationEmail;

        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Create User Profile',
                breadcrumbs: [
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (!isLoggedIn()) {
                showModal();
            }
        });

        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }
    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

    <div layout:fragment="content">

        <div id="new-study-modal"
            class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative w-full max-w-5xl max-h-full">
                <!-- Modal content -->
                <div class="relative bg-white">
                    <!-- Modal header -->
                    <div class="flex items-center justify-between px-1 pt-4 pb-2">
                        <h3 class="text-lg font-semibold text-gray-900 ">
                            Create User Profile
                        </h3>
                    </div>
                    <!-- Modal body -->

                    <div class="p-1 md:p-1" sec:authorize="isAuthenticated()">
                        <div class=" px-8 py-6 border rounded dark:border-gray-600">
                            <div class="gap-1">
                                <label for="participant"
                                    class="block mb-4 text-base font-normal text-gray-900  text-center">
                                    Thank you for logging in using <span
                                        th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
                                        th:href="${isOAuth2 ? #authentication.getPrincipal().attributes['html_url']:'#'}"
                                        th:text="${isOAuth2 ? (#authentication.getPrincipal().attributes['provider'] == 'ORCiD' ? 'ORCID' : #authentication.getPrincipal().attributes['provider']):'Email'}">GitHub</span>,
                                    In order to
                                    submit data ,
                                    please
                                    complete your profile.
                                </label>
                                <div class="grid grid-cols-12  mb-4">
                                    <label for="orcidID" id="orcidID-label"
                                        class="col-span-4 mb-2 text-sm font-normal text-gray-900 ">
                                        ORCID ID (Please provide your ORCID ID if you have it, and we will fetch details
                                        from ORCID)
                                    </label>
                                    <div class="col-span-8">
                                        <input name="orcidID" id="orcidID" rows="4"
                                            class="block  w-full text-sm font-normal rounded text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            placeholder="Enter your ORCID ID" />
                                        <span id="orcidID-error"
                                            class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-12  mb-4 -mt-3 hidden" id="orcid-loader">
                                    <button disabled type="button"
                                        class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                        <svg aria-hidden="true" role="status"
                                            class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                fill="currentColor" />
                                            <path
                                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                fill="#1C64F2" />
                                        </svg>
                                        Fetching user details...
                                    </button>
                                </div>
                                <div class="grid grid-cols-12  mb-4">
                                    <label for="userName" id="userName-label"
                                        class="col-span-4 mb-2 text-sm font-normal text-gray-900 ">
                                        Name (Please enter your full name)<span class="text-red-500">*</span>
                                    </label>
                                    <div class="col-span-8">
                                        <input name="userName" id="userName" rows="4"
                                            class="block  w-full text-sm font-normal rounded text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            placeholder="Enter your name" required />
                                        <span id="userName-error"
                                            class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-12  mb-4">
                                    <label for="userEmail" id="userEmail-label"
                                        class="col-span-4 mb-2 text-sm font-normal rounded text-gray-900 ">
                                        Email (Please enter Your email address)<span class="text-red-500">*</span>
                                    </label>

                                    <div class="col-span-8">
                                        <input type="text" name="userEmail" id="userEmail"
                                            class="rounded border border-gray-300 text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                                            placeholder="Enter your email ID" required readonly />
                                        <span id="userEmail-error"
                                            class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-12  mb-4 -mt-3 hidden" id="userEmail-loader">
                                    <button disabled type="button"
                                        class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                        <svg aria-hidden="true" role="status"
                                            class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                fill="currentColor" />
                                            <path
                                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                fill="#1C64F2" />
                                        </svg>
                                        Checking if Email Exists...
                                    </button>
                                </div>
                                <div class="grid grid-cols-12">
                                    <label for="userInstitution" id="userInstitution-label"
                                        class=" col-span-4 mb-2 text-sm font-normal rounded text-gray-900 dark:text-white">
                                        Institution/Organization (Please select your Organization from the existing
                                        list)<span class="text-red-500">*</span>
                                    </label>

                                    <div class="col-span-8" id="select-repo-inline">
                                        <select name="userInstitution" id="userInstitution"
                                            class="text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                                            placeholder="Search Institution"></select>
                                        <span id="institution-error"
                                            class="text-sm font-normal mt-2 mb-2 text-red-500 hidden"></span>
                                    </div>
                                    <!-- <div class="col-span-3">
                                        <button id="add-new-org" type="button"
                                            class="text-white bg-[#1b76be] ml-2 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-normal rounded text-sm inline-flex items-center px-4 py-2 text-center self-stretch">Add
                                            New Organization</button>
                                    </div> -->
                                </div>
                                <div class="grid grid-cols-12">

                                    <label
                                        class="col-start-5 col-span-8 mb-2 text-sm font-normal rounded text-gray-900 ">

                                        If the organization is not found in the list, search it from Research
                                        Organization Registry (ROR)
                                        by clicking
                                        <a id="add-new-org" type="button" href="#"
                                            class="text-[#1b76be] font-normal rounded text-sm inline-flex items-center text-center self-stretch">Add
                                            New Organization</a>.

                                    </label>

                                </div>

                                <div class="grid grid-cols-12 hidden" id="new-institution-ctr">
                                    <label for="userNewInstitution" id="userNewInstitution-label"
                                        class=" col-span-4 mb-2 text-sm font-normal rounded text-gray-900 ">
                                        Institution/Organization (Please add a new Organization)<span
                                            class="text-red-500">*</span>
                                    </label>

                                    <div class="col-span-8" id="select-repo-inline">
                                        <select name="userNewInstitution" id="userNewInstitution"
                                            class="text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                                            placeholder="Search Institution"></select>
                                        <span id="institution-error"
                                            class="text-sm font-normal mt-2 mb-2 text-red-500 hidden"></span>
                                    </div>

                                </div>
                                <div class="grid grid-cols-12 hidden" id="new-institution-label-ctr">
                                    <label
                                        class="col-start-5 col-span-8 mb-2 text-sm font-normal rounded text-gray-900 ">
                                        If you still can't find the organization, please send an email to
                                        <EMAIL> with the organization details.
                                    </label>
                                </div>

                                <div class="grid grid-cols-12 hidden " id="organization-loader">
                                    <button disabled type="button"
                                        class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                        <svg aria-hidden="true" role="status"
                                            class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                fill="currentColor" />
                                            <path
                                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                fill="#1C64F2" />
                                        </svg>
                                        Creating organization...
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end pt-3">
                            <button type="submit" id="save-profile"
                                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm inline-flex items-center px-4 py-2 text-center">
                                Complete User Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
        </div>


</body>



</html>