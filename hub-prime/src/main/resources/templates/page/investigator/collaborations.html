<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <!-- Include Tom Select CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Include Tom Select JS -->
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script th:src="@{/js/utils.js}"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var tab = /*[[${tab}]]*/ "";
    var studyArchived = false;
    var principalInvestigatorList; //PI tomselect
    var studyTeamMemberList; //Study Team tomselect
    var coInvestigatorList; // CoInvestigator tomselect
    var nominatedPrincipalInvestigatorList; //NPI tomselect

    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";

    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: "Collaboration and Teams",
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies/dashboard",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },

        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", async function () {
      window.layout.initActiveRoute();
      if (!isLoggedIn()) {
        showModal();
        //window.location.href = "/studies/all"
      }
      else if (!isProfileCompleted()) {
        showProfilePromptModal();
      }
      else {
        if (studyId) {
          let studyOwner = await getStudyOwner(studyId);

          let archiveStatus = await getStudyArchiveStatus(studyId);
          console.log("archiveStatus", archiveStatus);
          if (archiveStatus == true || studyOwner != getLoggedInUser()) {
            toggleTooltip("update-collab-study", true);
            document.querySelector(".archive-alert").classList.remove("hidden");
            studyArchived = true;
            principalInvestigatorList.disable();
            nominatedPrincipalInvestigatorList.disable();
            studyTeamMemberList.disable();
            coInvestigatorList.disable();
          }
          else {
            toggleTooltip("update-collab-study", false);
            document.querySelector(".archive-alert").classList.add("hidden");
            studyArchived = false;
            principalInvestigatorList.enable();
            nominatedPrincipalInvestigatorList.enable();
            studyTeamMemberList.enable();
            coInvestigatorList.enable();
          }
        }
      }
    });
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
  </script>
  <script>
    let defaultInvestigators = [];
    let defaultAuthors = [];
    let principalInvestigators = [];
    let primaryAuthors = [];
    let teamAuthors = [];


    let principalInvestigatorCollaboration; //PI
    let nominatedPrincipalInvestigatorCollaboration;//NPI
    let studyTeamMemberCollaboration = []; // Study team members
    let coInvestigatorsCollaboration = []; // Co-Investigators
    let authorTeamCollaboration = []; // Author Team 
    let primaryAuthorCollaboration;

    document.addEventListener("DOMContentLoaded", async function () {

      await fetchData(
        `/investigator`,
        async (responseData, error) => {
          if (!error) {
            result = responseData.data.investigators;
            defaultInvestigators = result;
            initStudyPI(defaultInvestigators, '');
            initNPI(defaultInvestigators, '');
            initCoInvestigators(defaultInvestigators, []);
            initStudyTeamMember(defaultInvestigators, []);
          }
          else {
            console.error("Error fetching investigator info:", error);
          }
        });


      getprincipalInvestigators()
        .then(async (principalInvestigators) => {
          console.log(principalInvestigators[0]?.name)
          await initStudyPI(defaultInvestigators, principalInvestigators[0]?.name);
        });
      getnominatedPrincipalInvestigators()
        .then(async (npInvestigators) => {
          await initNPI(defaultInvestigators, npInvestigators[0]?.name);
        });
      getStudyTeamMember()
        .then(async (studyTeamMembers) => {
          console.log(studyTeamMembers)
          await initStudyTeamMember(defaultInvestigators, studyTeamMembers);
        });
      getcoInvestigators()
        .then(async (coInvestigators) => {
          await initCoInvestigators(defaultInvestigators, coInvestigators);
        });


      document.getElementById("update-collab-study").addEventListener("click", async () => {
        showLoading('update-collab-loader');

        let isValid = true;
        const principalInvestigator = principalInvestigatorCollaboration;//PI
        const nominatedPrincipalInvestigator = nominatedPrincipalInvestigatorCollaboration;//NPI
        const studyTeamMember = studyTeamMemberCollaboration;// Study Team Member
        const coInvestigators = coInvestigatorsCollaboration;// Co-Investigators
        const authorTeam = authorTeamCollaboration;

        // Check if at least one input is present
        if (principalInvestigator || nominatedPrincipalInvestigator || (coInvestigators.length > 0) || (studyTeamMember.length > 0)) {
          isValid = true;
        } else {
          isValid = false;
          errorMessage = "At least one of the following is required: Principal Investigator, Nominated Principal Investigator, Co-Investigators, Study Team Members or Author Team member.";
          document.getElementById('studyTeamMember-error').textContent = errorMessage;
        }
        if (isValid) {
          const data = {
            "studyId": studyId,
            "coInvestigatorsName": coInvestigators, // Co-Investigators
            "principalInvestigatorName": principalInvestigator, //PI
            "nominatedPrincipalInvestigatorName": nominatedPrincipalInvestigator, //NPI
            // "coAuthorsName": authorTeam, //Author Team
            "studyTeamNames": studyTeamMember, // Study Team Member
          };
          postData(`/collaboration-team`, data, (res) => {
            hideLoading('update-collab-loader');

            if (res && res.status === "success") {
              showToast("Study settings saved successfully!", "success");
              window.location = "/study/info/" + studyId + "?tab=" + tab;
            } else {
              //   const errorMessage = res?.errors || "Please try again later.";
              showToast("Something went wrong!", "error");
            }
          });
        }
        hideLoading('update-collab-loader');

      });


    });
    function getnominatedPrincipalInvestigators() {
      return new Promise((resolve, reject) => {
        fetchRawData(`/research-study/nominated-principal-investigator?studyId=` + studyId, (res) => {
          try {
            const npiInvestigators = res.data.studyTeam;
            resolve(npiInvestigators); // Resolve the promise with the fetched data
          } catch (error) {
            reject(error); // Reject the promise in case of an error
          }
        });
      });
    }

    function getStudyTeamMember() {
      return new Promise((resolve, reject) => {
        fetchRawData(`/research-study/study-team-members?studyId=` + studyId, (res) => {
          try {
            const coInvestigators = res.data.studyTeam;
            resolve(coInvestigators); // Resolve the promise with the fetched data
          } catch (error) {
            reject(error); // Reject the promise in case of an error
          }
        });
      });
    }
    function getprincipalInvestigators() {
      return new Promise((resolve, reject) => {
        fetchRawData(`/research-study/principal-investigator?studyId=` + studyId, (res) => {
          try {
            const principalInvestigators = res.data.studyTeam;
            resolve(principalInvestigators); // Resolve the promise with the fetched data
          } catch (error) {
            reject(error); // Reject the promise in case of an error
          }
        });
      });
    }
    function getcoInvestigators() {
      return new Promise((resolve, reject) => {
        fetchRawData(`/research-study/co-investigator?studyId=` + studyId, (res) => {
          try {
            const coInvestigators = res.data.studyTeam;
            resolve(coInvestigators); // Resolve the promise with the fetched data
          } catch (error) {
            reject(error); // Reject the promise in case of an error
          }
        });
      });
    }

    function initStudyPI(defaultInvestigators, studyPIValue) {
      if (principalInvestigatorList instanceof TomSelect) {
        principalInvestigatorList.destroy();  // Only call destroy if it's a valid TomSelect instance
      }
      console.log("initStudy PI", defaultInvestigators, studyPIValue);
      principalInvestigatorList = new TomSelect("#studyPI", {
        plugins: ["remove_button"],
        create: true,
        maxItems: 1,
        valueField: 'investigator_name',
        labelField: 'investigator_name',
        searchField: 'investigator_name',
        options: defaultInvestigators,
        createFilter: function (input) { return input.length >= parseInt(2, 10); },
        onInitialize: function () {
          this.setValue(studyPIValue);
        },
        onChange: function (value) {
          principalInvestigatorCollaboration = value;
        },
        onItemRemove: function (value, $item) {

        }
      });
      if (studyArchived == true) {
        principalInvestigatorList.disable();
      }
      else {
        principalInvestigatorList.enable();
      }

    }

    function initNPI(defaultInvestigators, NPIValue) {
      if (nominatedPrincipalInvestigatorList instanceof TomSelect) {
        nominatedPrincipalInvestigatorList.destroy();  // Only call destroy if it's a valid TomSelect instance
      }
      nominatedPrincipalInvestigatorList = new TomSelect("#studyNPI", {
        plugins: ["remove_button"],
        create: true,
        maxItems: 1,
        valueField: 'investigator_name',
        labelField: 'investigator_name',
        searchField: 'investigator_name',
        options: defaultInvestigators,
        createFilter: function (input) { return input.length >= parseInt(2, 10); },
        onInitialize: function () {
          this.setValue(NPIValue);
        },
        onChange: function (value) {
          nominatedPrincipalInvestigatorCollaboration = value;
        },
        onItemRemove: function (value, $item) {

        }
      });

      if (studyArchived == true) {
        nominatedPrincipalInvestigatorList.disable();
      }
      else {
        nominatedPrincipalInvestigatorList.enable();
      }
    }

    function initStudyTeamMember(defaultInvestigators, studyTeamMembers) {
      if (studyTeamMemberList instanceof TomSelect) {
        studyTeamMemberList.destroy();  // Only call destroy if it's a valid TomSelect instance
      }

      studyTeamMemberList = new TomSelect("#studyTeamMember", {
        plugins: ["remove_button"],
        create: true,
        maxItems: null,
        valueField: 'investigator_name',
        labelField: 'investigator_name',
        searchField: 'investigator_name',
        options: defaultInvestigators,
        createFilter: function (input) { return input.length >= parseInt(2, 10); },
        onInitialize: function () {
          let teamListSelected = [];
          if (Object.keys(studyTeamMembers).length != 0) {
            studyTeamMembers?.forEach((team) => {
              teamListSelected.push(team.name);
            });
            this.setValue(teamListSelected);
          }
        },
        onChange: function (value) {
          studyTeamMemberCollaboration = value;
          //handleStudyTeamMemberSelection(value)
        },
        onItemRemove: function (value, $item) {

        }
      });
      if (studyArchived == true) {
        studyTeamMemberList.disable();
      }
      else {
        studyTeamMemberList.enable();
      }
    }

    function initCoInvestigators(defaultInvestigators, coInvestigators) {
      if (coInvestigatorList instanceof TomSelect) {
        coInvestigatorList.destroy();  // Only call destroy if it's a valid TomSelect instance
      }
      coInvestigatorList = new TomSelect("#coInvestigators", {
        plugins: ["remove_button"],
        create: true,
        maxItems: null,
        valueField: 'investigator_name',
        labelField: 'investigator_name',
        searchField: 'investigator_name',
        options: defaultInvestigators,
        createFilter: function (input) { return input.length >= parseInt(2, 10); },
        onInitialize: function () {
          let teamListSelected = [];
          if (Object.keys(coInvestigators).length != 0) {
            coInvestigators?.forEach((team) => {
              teamListSelected.push(team.name);
            });
            this.setValue(teamListSelected);
          }
        },
        onChange: function (value) {
          coInvestigatorsCollaboration = value;
          //handlecoInvestigatorSelection(value)
        },
        onItemRemove: function (value, $item) {

        }
      });
      if (studyArchived == true) {
        coInvestigatorList.disable();
      }
      else {
        coInvestigatorList.enable();
      }
    }


  </script>
  <script type="module" src="/js/settings.js" th:attr="data-studyid=${studyId}"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <ul class="flex flex-wrap border-b border-gray-200" id="pageTabs"></ul>
    <div class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full  pt-8">

      <div class="relative w-full max-w-2xl max-h-full">
        <div th:replace="~{page/investigator/fragments/archiveInfoAlert}"></div>
        <div class="form-ctr pt-2">
          <div class="border rounded dark:border-gray-600">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
              <h3 class="text-base font-semibold">Collaboration and Teams</h3>
            </div>
            <div class="px-10 py-6 ">
              <div class="gap-1">
                <div class="grid gap-4">
                  <div class="py-2 mb-4 rounded-t border-b border-gray-200">
                    <h3 class="text-base font-semibold">Investigators</h3>
                  </div>
                  <div class="grid grid-cols-3  mb-4">
                    <label for="studyPI" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        Principal Investigator
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_principal_investigator')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <select name="studyPI" id="studyPI" title="Principal Investigator"
                        class="box-border h-9 w-full  ml-[-1px] bg-[#ffffff] text-sm collaboration-field"
                        data-live-search="true">
                      </select>
                      <span id="studyPI-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div>
                  <div class="grid grid-cols-3  mb-4">
                    <label for="studyNPI" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        <div class="w-36">Nominated Principal Investigator</div>
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_nominated_pricipal_investigator')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <select name="studyNPI" id="studyNPI" title="Nominated Principal Investigator"
                        class="box-border h-9 w-full  ml-[-1px] bg-[#ffffff] text-sm collaboration-field"
                        data-live-search="true">
                      </select>
                      <span id="studyNPI-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div>
                  <div class="grid grid-cols-3  mb-4">
                    <label for="coInvestigators" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        Co-Investigators
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_coInvestigator')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <select name="coInvestigators" id="coInvestigators" title="coInvestigators"
                        class="box-border  w-full  ml-[-1px] bg-[#ffffff] text-sm collaboration-field"
                        data-live-search="true">
                      </select>
                      <span id="coInvestigators-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div>
                  <div class="grid grid-cols-3  mb-4">
                    <label for="studyTeamMember" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        Study Team Members
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_study_team_members')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <select name="studyTeamMember" id="studyTeamMember" multiple
                        class="box-border  w-full ml-[-1px] bg-[#ffffff] text-sm collaboration-field"
                        data-live-search="true">
                      </select>
                      <span id="studyTeamMember-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div>

                  <!-- <div class="py-2 mb-4 rounded-t border-b border-gray-200">
                    <h3 class="text-base font-semibold">Authors</h3>
                  </div>
                  <div class="grid grid-cols-3  mb-4 hidden">
                    <label for="principalAuthor" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        Principal Author
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_principal_author')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <div class="flex flex-row">
                        <span class="block text-xs mb-2 font-normal text-gray-500">Add publication details to update
                          authors</span>
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_add_publication_details')}">
                        </div>
                      </div>
                      <select name="principalAuthor" id="principalAuthor" title="Principal Author"
                        class="box-border h-9 w-full  bg-[#ffffff] text-sm collaboration-field" data-live-search="true">
                      </select>
                      <span id="principalAuthor-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div> 
                  <div class="grid grid-cols-3  mb-4">
                    <label for="authorTeam" class="text-sm font-normal text-gray-900  py-2">
                      <div class="flex flex-row">
                        Author Team
                        <div
                          th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('collab_author_team')}">
                        </div>
                      </div>
                    </label>
                    <div class="col-span-2">
                      <select name="authorTeam" id="authorTeam" multiple
                        class="box-border  w-full  ml-[-1px] bg-[#ffffff] text-sm collaboration-field"
                        data-live-search="true">
                      </select>
                      <span id="authorTeam-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                    </div>
                  </div> -->

                </div>
              </div>
            </div>
          </div>
          <div class="my-4 text-right">

            <button type="button" id="update-collab-study" data-tooltip-target="update-collab-study-tooltip"
              data-tooltip-trigger="hover" data-privilege-buttons="Collaboration and Teams" data-tooltip-placement="top"
              class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Update Study
            </button>


            <div id="update-collab-study-tooltip" role="tooltip"
              class="tooltip-txt absolute z-50 invisible hidden inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">
              Edit
              permissions are limited.</div>
            <div class="tooltip-arrow" data-popper-arrow></div>
          </div>
          <div class="my-4 text-right hidden" id="update-collab-loader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              Updating Collaborations...
            </button>
          </div>
        </div>
      </div>
    </div>

    <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to update study.')}">
    </div>
    <div
      th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to update study.')}">
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
  </div>
</body>

</html>