<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Edit Profile</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>

    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>

    <style>
        /* Remove spinner arrows for number inputs */
        input[type="number"].no-spinner::-webkit-outer-spin-button,
        input[type="number"].no-spinner::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"].no-spinner {
            -moz-appearance: textfield;
        }
    </style>

    <script th:src="@{/js/profile.js}"></script>

    <script th:inline="javascript" type="module">

        var verificationEmail = /*[[${verificationEmail}]]*/ "";
        console.log("verificationEmail", verificationEmail);
        let nextPage = "";
        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Verify OTP',
                breadcrumbs: [
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };
        const inputs = document.querySelectorAll(".otp-box");
        const form = document.getElementById("otp-form");
        const errorMsg = document.getElementById("userOtp-error");

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (!isLoggedIn()) {
                showModal();
            }

            inputs.forEach((input, index) => {
                input.classList.add("w-12", "h-12", "border", "rounded", "text-center", "text-lg", "focus:outline-none", "focus:ring-2", "focus:ring-blue-500");

                input.addEventListener("input", (e) => {
                    let val = e.target.value;

                    // Only keep the first digit and ensure it's a number
                    if (val.length > 1 || /\D/.test(val)) {
                        val = val.replace(/\D/g, '').slice(0, 1);
                        e.target.value = val;
                    }

                    if (val && index < inputs.length - 1) {
                        console.log("Moving focus to next input", val, index, inputs[index + 1]);
                        inputs[index + 1].focus();
                    }
                });

                input.addEventListener("keydown", (e) => {
                    if (e.key === "Backspace" && !e.target.value && index > 0) {
                        inputs[index - 1].focus();
                    }
                });

                input.addEventListener("paste", (e) => {
                    e.preventDefault(); // disable paste
                });
            });
            document.getElementById("verify-otp").addEventListener("click", async function () {
                await verifyOtp();
            });

            document.getElementById("resend-email").addEventListener("click", async function () {
                await resendEmail();
            });

            document.getElementById("navigate-to-nextpage").addEventListener("click", async function () {
                window.location.href = nextPage || "/home";
            });

        });

        async function verifyOtp() {


            const userOtp = Array.from(inputs).map(input => input.value.trim()).join("");
            if (userOtp.length < 6 || /\D/.test(userOtp)) {
                errorMsg.innerText = "Please enter a valid 6-digit OTP.";
                errorMsg.classList.remove("hidden");
            } else {
                errorMsg.classList.add("hidden");
                // ✅ Proceed with OTP validation
                console.log("OTP submitted:", userOtp);
                try {
                    // Send the user's message to the API
                    showLoading("verifyOtp-Loader");

                    // Prepare data for submission
                    const otpParams = {
                        "otp": userOtp,
                        "email": verificationEmail
                    }
                    const response = await fetch('/verify-otp', {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify(otpParams),
                    });

                    if (!response.ok) {
                        throw new Error("Failed to fetch response from the API.");
                    }

                    const data = await response.json();
                    console.log(data);
                    if (data.status == "success") {
                        hideLoading("verifyOtp-Loader");
                        localStorage.setItem("emailVerified", "TRUE");
                        setTimeout(function () {
                            let linkingMessage = "The OTP has been verified successfully. ";
                            let modalTitle = "";
                            let existingProvider = data.data.response.existingProvider;
                            let currentProvider = data.data.response.currentProvider;
                            if (data.data.response.responsePage == "createProfile") {
                                modalTitle = "Registration Successful";
                                linkingMessage += "To get started, please complete your profile by entering your details on the profile screen.";
                                nextPage = "/userprofile/info";
                            }
                            else if (data.data.response.responsePage == "home") {
                                modalTitle = "Authentication Successful";
                                linkingMessage += "You're now logged in! Click below to continue to your profile.";
                                nextPage = "/home";
                            }
                            else if (data.data.response.responsePage == "accountLinking") {
                                modalTitle = "Account Linking Confirmation";
                                linkingMessage += "Your email <span class='font-semibold'>" + verificationEmail + "</span> is already registered in DRH via <span class='font-semibold'>" + existingProvider.toLowerCase() == 'orcid' ? existingProvider.toUpperCase() : existingProvider + "</span>. We’ve successfully linked your current login from <span class='font-semibold'>" + currentProvider.toLowerCase() == 'orcid' ? currentProvider.toUpperCase() : currentProvider + "</span> to the existing profile.";
                                nextPage = "/home";
                            }
                            document.getElementById("account-modal-title").innerHTML = modalTitle;
                            document.getElementById("linking-message").innerHTML = linkingMessage;
                            showMsgModal();

                        }, 2000); // 2 seconds delay
                    }
                    else {
                        hideLoading("verifyOtp-Loader");

                        showToast(data.message, "error");
                        errorMsg.innerText = data.message;
                        errorMsg.classList.remove("hidden");
                    }
                } catch (e) {
                    console.error("Error fetching API response:", e);
                }
            }



        }


        async function resendEmail() {

            errorMsg.classList.add("hidden");
            const userEmail = verificationEmail; // Use the email from the server-side variable
            if (!userEmail) {
                console.error("No email address available for verification.");
                return;
            }
            showLoading("resendEmail-Loader");

            try {
                // Send the user's message to the API

                // Prepare data for submission
                const verifyParams = {
                    "email": userEmail
                }

                console.log("Sending verification email with params:", verifyParams);
                const response = await fetch('/email', {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(verifyParams),
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch response from the API.");
                }

                const data = await response.json();
                if (data.status === "success") {
                    hideLoading("resendEmail-Loader");
                    showToast(data.message, "success");
                    console.log("Verification email sent successfully:", data);
                    inputs.forEach(input => input.value = "");
                }
            } catch (e) {
                hideLoading("sendEmail-Loader");
                console.error("Error fetching API response:", e);
            }

        }




        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }

        // Function to show the modal
        function showMsgModal() {
            const msgModalEle = document.getElementById("account-message-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const msgModal = new Modal(msgModalEle, options)
            msgModal.show();
            return false;
        }

        // Function to hide the modal
        function hideMsgModal() {
            const msgModalEle = document.getElementById("account-message-modal");
            const msgModal = new Modal(msgModalEle)
            msgModal.hide();
        }

    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

    <div layout:fragment="content">


        <div id="verify-otp-modal"
            class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full ">
            <div class="relative w-full max-h-full border border-gray-200">
                <!-- Modal content -->


                <div class="flex items-center justify-center p-8" sec:authorize="isAuthenticated()">
                    <div class="bg-gray-50 p-8 rounded-lg w-full max-w-md ">
                        <div class="flex flex-col items-center p-6">
                            <!-- Envelope Icon -->
                            <svg class="w-16 h-16 mb-4 text-gray-700" fill="none" stroke="currentColor" stroke-width="2"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M16 12l-4 4m0 0l-4-4m4 4V8m-9 4a9 9 0 1118 0 9 9 0 01-18 0z" />
                            </svg>

                            <h2 class="text-2xl font-semibold mb-2">Verify OTP</h2>
                            <p class="text-gray-600 text-center mb-6">Please enter the 6-digit code that was sent to
                                your email address <span th:text="${verificationEmail}"></span>.</p>

                            <!-- OTP Input -->
                            <div class="flex space-x-2 mb-4">
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp1" id="userOtp1"
                                    class="otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp2" id="userOtp2"
                                    class="otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp3" id="userOtp3"
                                    class="otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp4" id="userOtp4"
                                    class="otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp5" id="userOtp5"
                                    class=" otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <input type="number" inputmode="numeric" maxlength="1" name="userOtp6" id="userOtp6"
                                    class="otp-box no-spinner  w-12 h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                            </div>
                            <p class="text-center text-red-600 text-sm my-2 hidden" id="userOtp-error"></p>

                            <!-- Verify Button -->
                            <button id="verify-otp" class=" w-full bg-[#1F883D] focus:ring-4 focus:outline-none focus:ring-red-300
                                dark:focus:ring-red-800 text-white py-2 rounded">Verify</button>
                            <div class="col-span-2 mb-4 mt-3  hidden" id="verifyOtp-Loader">
                                <button disabled type="button"
                                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                    <svg aria-hidden="true" role="status"
                                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                            fill="currentColor" />
                                        <path
                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                            fill="#1C64F2" />
                                    </svg>
                                    Verifying OTP...
                                </button>
                            </div>
                            <!-- Resend Link -->
                            <p class="text-gray-600 mt-4 text-sm">
                                Didn’t receive the code?
                                <button id="resend-email"
                                    class="text-blue-700 font-medium hover:underline">Resend</button>
                            <div class="col-span-2 mb-4 mt-3  hidden" id="resendEmail-Loader">
                                <button disabled type="button"
                                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                    <svg aria-hidden="true" role="status"
                                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                            fill="currentColor" />
                                        <path
                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                            fill="#1C64F2" />
                                    </svg>
                                    Sending Email...
                                </button>
                            </div>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                        <div class="p-4 md:p-5 text-center">
                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to
                                proceed with OTP verification.
                            </h3>
                            <a href="/">
                                <button type="button"
                                    class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                                    Login
                                </button>

                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div id="account-message-modal" data-modal-backdrop="static" tabindex="-1"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-2xl max-h-full bg-white rounded-lg">

                    <div class="w-full bg-white  p-6 text-center" sec:authorize="isAuthenticated()">
                        <div class="flex justify-center mb-4">
                            <div class="text-green-600 text-3xl">✅</div>
                        </div>
                        <h2 class="text-xl font-semibold mb-2" id="account-modal-title">Account Linking Confirmation
                        </h2>
                        <p class="text-gray-700 my-4" id="linking-message">
                        </p>
                        <button id="navigate-to-nextpage"
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 rounded">
                            Continue
                        </button>
                    </div>
                </div>
            </div>
            <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
        </div>



</body>



</html>