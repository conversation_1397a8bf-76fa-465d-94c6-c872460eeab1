<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <!-- Include Tom Select CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
  <!-- Include Tom Select JS -->
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:src="@{/js/utils.js}"></script>
  <script th:src="@{/js/publication.js}"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var tab = /*[[${tab}]]*/ "";
    var citationDataSource = 'Manual'
    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    var studyArchived = false;
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    import * as sh from "@presentation/shell/shell-aide.js";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: "Publication Settings",
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies/dashboard",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },

        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", async function () {
      window.layout.initActiveRoute();

      // Show login/profile modal if needed
      if (!isLoggedIn()) {
        showModal();
        return;
      }
      if (!isProfileCompleted()) {
        showProfilePromptModal();
        return;
      }

      // Archive/owner logic
      if (studyId) {
        try {
          const [studyOwner, archiveStatus] = await Promise.all([
            getStudyOwner(studyId),
            getStudyArchiveStatus(studyId)
          ]);
          const isArchivedOrNotOwner = archiveStatus === true || studyOwner !== getLoggedInUser();
          toggleTooltip("add-new-publication-btn", isArchivedOrNotOwner);
          toggleTooltip("update-publication-settings", isArchivedOrNotOwner);
          document.querySelector(".archive-alert")?.classList.toggle("hidden", !isArchivedOrNotOwner);
          if (isArchivedOrNotOwner) {
            [...document.getElementsByClassName("publication-settings-field")].forEach((element) => {
              element.disabled = true;
            });
          }
          studyArchived = isArchivedOrNotOwner;
        } catch (e) {
          console.error("Error fetching study owner/archive status", e);
        }
      }

      await loadSourceAuthors();
      await loadPublicationDetails();

      // Datepicker initialization
      const datepickerPub = document.getElementById('publicationDate');
      if (datepickerPub) {
        new Datepicker(datepickerPub, {
          format: 'mm-dd-yyyy',
          autohide: true,
        });
      }
    });

    function getStudySettings() {
      console.log("getStudySettings");
      fetchRawData(`/research-study?studyId=${studyId}`, (res) => {
        try {
          const data = res.data.studyDetails;
          const setValue = (id, value) => {
            const el = document.getElementById(id);
            if (el && value != null) el.value = value;
          };
          setValue("publicationDoi", data.publicationDoi);
          setValue("publicationPubmed", data.publicationPubmed);
          setValue("publicationTitle", data.publicationTitle);
          setValue("publicationDate", data.publicationDate);
          setValue("author_team", data.author_team);
        } catch (error) {
          console.error("Error parsing data:", error);
        }
      });
    }
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }

    async function showAddPublicationModal() {
      const modalEle = document.getElementById("add-publication-modal");
      const options = {
        backdrop: 'static',
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options);
      modal.show();

      // Reset modal fields and errors
      const fields = [
        { id: "publicationTitle", value: "" },
        { id: "publicationDate", value: "" },
        { id: "publicationPubmed", value: "" },
        { id: "publicationDoi", value: "" },
        { id: "publicationId", value: "" }
      ];
      fields.forEach(f => {
        const el = document.getElementById(f.id);
        if (el) el.value = f.value;
      });

      const errorFields = [
        "publicationDoi-error",
        "publicationPubmed-error",
        "publicationTitle-error",
        "data-fetch-message",
        "data-fetch-alert"
      ];
      errorFields.forEach(id => {
        const el = document.getElementById(id);
        if (el) el.innerHTML = "";
      });

      ["publicationPubmed", "publicationDoi"].forEach(id => {
        document.getElementById(id)?.classList.remove("border-[#e23636]");
      });

      document.getElementById("publication-modal-title").innerText = "Add New Publication";
      document.getElementById("update-publication-settings").textContent = "Add Publication";
      document.getElementById("update-publication-settings").disabled = false;

      initAuthorsTeam(sourceAuthors, []);
      disablePubmedDataFetching();
      disableDOIDataFetching();
      publicationDetailsLoad = false;
      authorTeamCollaboration = [];
      return false;
    }

    function hideAddPublicationModal() {
      const modalEle = document.getElementById("add-publication-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    document.addEventListener("DOMContentLoaded", async function () {

      document
        .getElementById("publicationTitle")
        ?.addEventListener("blur", async function () {
          let element = document.getElementById("publicationTitle");
          if (document.getElementById("publicationTitle")?.value != "") {
            element.classList.remove("border-[#e23636]");
          }
        });


      document.getElementById("close-publication-btn").addEventListener("click", function () {
        hideAddPublicationModal();
      });

      document.getElementById("update-publication-settings").addEventListener("click", async () => {
        const pubmedId = DOMPurify.sanitize(document.getElementById("publicationPubmed").value).trim();
        const publicationDoi = DOMPurify.sanitize(document.getElementById("publicationDoi").value).trim();
        const publicationTitle = DOMPurify.sanitize(document.getElementById("publicationTitle").value).trim();
        const publicationDate = DOMPurify.sanitize(document.getElementById("publicationDate").value).trim();
        const authorTeam = authorTeamCollaboration;

        const publicationId = DOMPurify.sanitize(document.getElementById("publicationId").value) || "";

        let isValid = true;

        // Reset error messages
        document.getElementById("publicationTitle-error").textContent = "";
        document.getElementById("publicationPubmed-error").textContent = "";
        document.getElementById("publicationDoi-error").textContent = "";

        if (!publicationTitle) {
          document.getElementById("publicationTitle-error").textContent = "Please fill Publication Title";
          return;
        }

        if (pubmedId) {
          if (!isValidPubmed(pubmedId)) {
            document.getElementById("publicationPubmed-error").textContent = "Invalid format. Use only digits, up to 8 characters.";
            return;
          }
          const pubmedExists = await pubmedIdExists();
          if (pubmedExists) {
            document.getElementById("publicationPubmed-error").textContent = "This Pubmed Id already exists";
            disablePubmedDataFetching();
            return;
          }
        }

        if (publicationDoi) {
          if (!/^10\.\d{4,9}\/\S+$/.test(publicationDoi)) {
            document.getElementById("publicationDoi-error").textContent = "Invalid format. Start with '10.', followed by 4 to 9 digits, a '/' separator, and a non-empty suffix";
            return;
          }
          const doiExists = await pubmedIdExists();
          if (doiExists) {
            document.getElementById("publicationDoi-error").textContent = "This Publication DOI already exists";
            disableDOIDataFetching();
            return;
          }
        }

        showLoading('update-publication-loader');

        const data = {
          "publication_title": publicationTitle,
          "publication_date": publicationDate || null,
          "publication_doi": publicationDoi,
          "pubmed_id": pubmedId,
          "collaboration_team": {
            "studyId": studyId,
            "coAuthorsName": authorTeam
          },
          "citation_data_source": citationDataSource
        };

        const handleResponse = async (res, successMsg) => {
          hideLoading('update-publication-loader');
          if (res && res.status === "success") {
            await loadSourceAuthors();
            showToast(successMsg, "success");
            loadPublicationDetails();
          } else {
            showToast("Something went wrong!", "error");
          }
        };

        if (publicationId) {
          updateData(`/research-study/publication-author?citationID=${publicationId}`, data, res =>
            handleResponse(res, "Publication settings updated successfully!")
          );
        } else {
          postData(`/research-study/citations`, data, res =>
            handleResponse(res, "Publication settings saved successfully!")
          );
        }

        hideAddPublicationModal();
      });
    });

    function loadPublicationForm(publicationId) {
      showAddPublicationModal();
      const selectedPublication = publicationList.find(
        pub => pub.citation_id == publicationId
      );
      if (!selectedPublication) return;

      document.getElementById("publication-modal-title").innerText = "Edit Publication";
      document.getElementById("update-publication-settings").textContent = "Update Publication";

      const {
        publication_title,
        publication_date,
        pubmed_id,
        publication_doi,
        citation_authors,
        citation_data_source
      } = selectedPublication;

      citationDataSource = citation_data_source || 'Manual';
      document.getElementById("publicationTitle").value = publication_title;
      document.getElementById("publicationDate").value = publication_date;
      document.getElementById("publicationPubmed").value = pubmed_id;
      document.getElementById("publicationDoi").value = publication_doi;
      document.getElementById("publicationId").value = publicationId;

      if (pubmed_id || publication_doi) {
        publicationDetailsLoad = true;
        pubMedIdValue = pubmed_id;
        doiValue = publication_doi;
      }

      authorTeamCollaboration = citation_authors;
      initAuthorsTeam(sourceAuthors, authorTeamCollaboration);
    }

    async function loadSourceAuthors() {
      fetchData(`/authors`, async (responseData, error) => {
        if (!error) {
          let result = responseData.data.Authors;
          sourceAuthors = result;
          initAuthorsTeam(sourceAuthors, []);

        } else {
          console.error("Error fetching investigator info:", error);
        }
      });
    }
    function showPubmedIdLoader() {
      document.getElementById("publicationPubmedLoader").classList.remove("hidden");
    }

    function hidePubmedIdLoader() {
      document.getElementById("publicationPubmedLoader").classList.add("hidden");
    }


    function getCoAuthors() {
      return new Promise((resolve, reject) => {
        fetchRawData(`/research-study/co-author?studyId=` + studyId, (res) => {
          try {
            const teamAuthors = res.data.studyTeam;
            resolve(teamAuthors); // Resolve the promise with the fetched data
          } catch (error) {
            reject(error); // Reject the promise in case of an error
          }
        });
      });
    }


    function loadPublicationDetails() {
      const targetElement = document.getElementById("publication-list");
      const addPublicationButton = document.getElementById("add-new-publication-btn");
      if (addPublicationButton) attachAddPublicationEvent(addPublicationButton);

      const url = `/research-study/citations?studyId=${studyId}`;
      fetch("/publicationInfo")
        .then(response => {
          if (!response.ok) throw new Error("Network response was not ok " + response.statusText);
          return response.text();
        })
        .then(fragment => {
          targetElement.innerHTML = "";
          const parser = new DOMParser();
          const doc = parser.parseFromString(fragment, "text/html");
          const fragmentEle = doc.body.firstElementChild;

          fetchData(url, (data, error) => {
            hideLoading("publicationLoader");
            if (error) {
              noPublicationsToDisplay();
              return;
            }

            publicationList = data?.data?.citations || [];
            if (publicationList.length === 0) {
              noPublicationsToDisplay();
            } else {
              publicationList.forEach((publication, index) => {
                const publicationHTML = createPublicationItem(fragmentEle, publication);
                if (index === publicationList.length - 1) {
                  publicationHTML.classList.remove("border-b", "border-[#C6CED5]-800");
                }
                targetElement.appendChild(publicationHTML);
              });
              attachEditEventToPublicationItems(publicationList, loadPublicationForm);
              document.getElementById("add-new-publication-btn").classList.remove("hidden");
            }
            document.getElementById("publications-container").classList.remove("hidden");
          });
        })
        .catch(error => {
          console.error("There was a problem with the fetch operation:", error);
          hideLoading("publicationLoader");
          noPublicationsToDisplay();
        });
    }

    function attachEditEventToPublicationItems(
      publicationList,
      loadPublicationForm
    ) {
      const publicationItems = document.querySelectorAll(
        ".publication-edit"
      );
      publicationItems.forEach((item, index) => {
        item.addEventListener("click", () => {
          const publicationId = publicationList[index].citation_id;
          loadPublicationForm(publicationId);
        });
      });
    }

    function noPublicationsToDisplay() {
      const targetElement = document.getElementById("publication-list");
      targetElement.innerHTML = "";
      const headerBox = document.createElement("div");
      headerBox.className =
        "flex flex-col items-center text-center md:py-20  lg:py-20 sm:py-5  md:px-80  lg:px-80 sm:px-5";
      targetElement.appendChild(headerBox);

      const svg = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "svg"
      );
      svg.setAttribute("width", "24");
      svg.setAttribute("height", "24");
      svg.setAttribute("viewBox", "0 0 24 28"); // Adjusted the viewBox for appropriate scaling
      svg.setAttribute("fill", "none");
      svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");

      // Create the path element
      const path = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "path"
      );
      path.setAttribute(
        "d",
        "M21.4737 22.909H7.57895C6.88421 22.909 6.28968 22.66 5.79537 22.162C5.30021 21.663 5.05263 21.0635 5.05263 20.3636V2.54546C5.05263 1.84545 5.30021 1.246 5.79537 0.74709C6.28968 0.24903 6.88421 0 7.57895 0H15.3789C15.7158 0 16.0371 0.0636364 16.3427 0.190909C16.6476 0.318182 16.9158 0.498484 17.1474 0.731818L22.2737 6.90454C22.5053 7.13788 22.6842 7.40812 22.8105 7.71527C22.9368 8.02327 23 8.34697 23 8.68636V20.3636C23 21.0635 22.7526 21.663 22.2574 22.162C21.7632 22.66 21.1684 22.909 20.4737 22.909ZM2.52632 28C1.83158 28 1.23684 27.751 0.742105 27.253C0.247368 26.755 0 26.1565 0 25.4545V8.9091C0 8.54848 0.121263 8.246 0.36579 8.00163C0.608421 7.75812 0.905263 7.63636 1.26316 7.63636C1.62105 7.63636 1.91789 7.75812 2.16184 8.00163C2.40421 8.246 2.52632 8.54848 2.52632 8.9091V25.4545H15.1579C15.5158 25.4545 15.8126 25.5763 16.0566 25.819C16.299 26.0626 16.4211 26.3659 16.4211 26.7273C16.4211 27.0879 16.299 27.39 16.0566 27.6336C15.8126 27.8769 15.5158 28 15.1579 28H2.52632ZM16.4211 8.9091H21.4737L15.1579 2.54546V7.63636C15.1579 7.99697 15.2789 8.29945 15.5206 8.54382C15.7626 8.78818 16.0632 8.9091 16.4211 8.9091Z"
      );
      path.setAttribute("fill", "#737373");

      // Append the path to the SVG
      svg.appendChild(path);

      // Append the SVG to the document (e.g., body or another element)
      headerBox.appendChild(svg);
      const headingText = document.createElement("h2");
      headingText.className = "font-semibold text-xl pt-4";
      headingText.textContent = "No Publications Found";
      headerBox.appendChild(headingText);
      const descriptionData = document.createElement("p");
      descriptionData.className = "text-sm font-normal p-2";
      descriptionData.textContent =
        "It looks like there are no publications listed under diabetic research studies yet. Start building the knowledge base by adding your first publication.";
      headerBox.appendChild(descriptionData);

      document.getElementById("publications-container").classList.remove("hidden");
    }

    function attachAddPublicationEvent(buttonElement) {
      buttonElement.addEventListener("click", () => {
        showAddPublicationModal();
      });
    }

    function createPublicationItem(fragmentEle, publication) {
      let element = fragmentEle.cloneNode(true);
      element.id = "publication-" + publication.citation_id;
      element.classList.add("border-b", "border-[#C6CED5]-800");
      element.querySelector(".publication-title").textContent =
        publication.publication_title;
      element.querySelector(".publication-date").textContent =
        publication.publication_date;
      element.querySelector(".publication-doi").textContent =
        publication.publication_doi;
      element.querySelector(".publication-pubmed").textContent =
        publication.pubmed_id;
      element.querySelector(".publication-authors").textContent =
        publication.citation_authors.length > 0 ? publication.citation_authors.toString() : "";
      element.querySelector(".publication-edit").setAttribute("data-id", publication.citation_id);

      if (studyArchived == true) {
        element.querySelector('.pub-edit-field')?.classList.add("hidden");
      }
      else {
        element.querySelector('.pub-edit-field')?.classList.remove("hidden");
      }
      return element;
    }

  </script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script type="module" src="/js/settings.js" th:attr="data-studyid=${studyId}"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <ul class="flex flex-wrap border-b border-gray-200" id="pageTabs"></ul>
    <div class=" justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full pt-8">
      <div class="relative w-full max-h-full">
        <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
        </div>


      </div>
    </div>
    <span class="loader loader-big justify-self-center" id="publicationLoader" style="display: block;"></span>

    <div class="" id="publications-container">
      <div id="publications-header" class="flex flex-row justify-between items-center px-1 py-2 ">
        <h6 class="text-md font-medium grow">Publications</h6>
        <button id="add-new-publication-btn" privilege-action-buttons-links="Edit Publications"
          data-tooltip-target="archive-publication-study"
          class="hidden upload-icon text-[#3E3E3E] border border-gray-200 bg-gray-50 focus:outline-none focus:ring-0font-medium rounded px-3 py-0 pb-1 mr-1">
          <svg width="13" height="13" class="inline-block" viewBox="0 0 13 13" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M6.5 0.25C3.05 0.25 0.25 3.05 0.25 6.5C0.25 9.95 3.05 12.75 6.5 12.75C9.95 12.75 12.75 9.95 12.75 6.5C12.75 3.05 9.95 0.25 6.5 0.25ZM9.625 7.125H7.125V9.625H5.875V7.125H3.375V5.875H5.875V3.375H7.125V5.875H9.625V7.125Z"
              fill="#59636E" />
          </svg>
          <span class="text-xs pl-0 leading-3 font-semibold">Add Publication</span>
        </button>
        <span id="archive-publication-study" role="tooltip"
          class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs text-left font-normal text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
          permissions are limited</span>
        <div class="tooltip-arrow" data-popper-arrow></div>
      </div>


      <div id="publication-list">
        <div class="flex flex-col items-center text-center md:py-20  lg:py-20 sm:py-5  md:px-80  lg:px-80 sm:px-5">
          <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
          </svg>
          <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">No Publications Found</h3>
        </div>
      </div>

    </div>
    <!-- Main modal -->
    <div id="add-publication-modal" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
          <!-- Modal header -->
          <div
            class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white" id="publication-modal-title">
              Add New Publication
            </h3>
            <button type="button" id="close-publication-btn"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close modal</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5 space-y-4">
            <div class="form-ctr pt-2 " id="publicationFormCtr">
              <div class="border rounded dark:border-gray-600">

                <div class="px-10 py-6 ">
                  <div class="gap-1">
                    <div class="grid grid-cols-3  mb-4">
                      <label for="publicationPubmed" class="text-sm font-normal text-gray-900  py-2">
                        <div class="flex flex-row">
                          Pubmed Id
                          <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('pubmedIdDrawer')}">
                          </div>
                        </div>
                      </label>
                      <div class="col-span-2">
                        <input name="publicationPubmed" id="publicationPubmed" rows="4"
                          class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 publication-settings-field"
                          placeholder="Enter Pubmed Id" />
                        <span id="publicationPubmed-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                        <div class="text-end my-1 hidden" id="fetch-pubmed-btn-ctr">
                          <button id="fetch-pubmed-btn" type="button"
                            class="text-white bg-[#03A9F4]  focus:ring-4 focus:outline-none font-normal  rounded  inline-flex items-center px-2 py-0.5 text-center  text-xs ">Get
                            Details</button>
                        </div>
                      </div>
                    </div>
                    <div class="grid grid-cols-3  mb-4  hidden" id="publicationPubmedLoader">
                      <button disabled type="button"
                        class="col-start-2 col-span-2 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                        <svg aria-hidden="true" role="status"
                          class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                          viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="#1C64F2" />
                        </svg>
                      </button>
                    </div>
                    <div class="grid grid-cols-3  mb-4">
                      <label for="publicationDoi" class="text-sm font-normal text-gray-900  py-2">
                        <div class="flex flex-row">
                          DOI
                          <div
                            th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationdoiDrawer')}">
                          </div>
                        </div>
                      </label>
                      <div class="col-span-2">
                        <input name="publicationDoi" id="publicationDoi" rows="4"
                          class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 publication-settings-field"
                          placeholder="Enter DOI" />
                        <span id="publicationDoi-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                        <div class="text-end my-1 hidden" id="fetch-doi-btn-ctr">
                          <button id="fetch-doi-btn" type="button"
                            class="text-white bg-[#03A9F4]  focus:ring-4 focus:outline-none font-normal  rounded  inline-flex items-center px-2 py-0.5 text-center  text-xs ">Get
                            Details</button>
                        </div>
                      </div>
                    </div>
                    <div class="grid grid-cols-3  mb-4  hidden" id="publicationDoiLoader">
                      <button disabled type="button"
                        class="col-start-2 col-span-2 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                        <svg aria-hidden="true" role="status"
                          class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                          viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="#1C64F2" />
                        </svg>
                      </button>
                    </div>
                    <div class="grid grid-cols-3  mb-4">
                      <label for="publicationTitle" class="text-sm font-normal text-gray-900  py-2">
                        <div class="flex flex-row">
                          Publication Title
                          <div
                            th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationTitleDrawer')}">
                          </div>
                        </div>
                      </label>
                      <div class="col-span-2">
                        <input name="publicationTitle" id="publicationTitle" rows="4"
                          class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 publication-settings-field"
                          placeholder="Enter Publication Title" />
                        <span id="publicationTitle-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                      </div>
                    </div>
                    <div class="grid grid-cols-3  mb-4">
                      <label for="publicationDate" class="text-sm font-normal text-gray-900  py-2">
                        <div class="flex flex-row">
                          Publication Date
                          <div
                            th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('publicationdateDrawer')}">
                          </div>
                        </div>
                      </label>
                      <div class="col-span-2">
                        <input name="publicationDate" id="publicationDate" type="text"
                          class="border border-gray-300 text-gray-900 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 publication-settings-field"
                          placeholder="MM-DD-YYYY" />
                        <span id="publicationDate-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                      </div>
                    </div>

                    <div class="grid grid-cols-3  mb-4">
                      <label for="authorTeam" class="text-sm font-normal text-gray-900  py-2">
                        <div class="flex flex-row">
                          Team of Authors
                          <div
                            th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('authorteamDrawer')}">
                          </div>
                        </div>
                      </label>
                      <div class="col-span-2">
                        <select name="authorTeam" id="authorTeam" multiple
                          class="box-border  w-full  ml-[-1px] bg-[#ffffff] text-sm publication-settings-field"
                          data-live-search="true">
                        </select>
                        <span id="authorTeam-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="data-fetch-message" class="hidden text-green-700"></div>
              <div id="data-fetch-alert" class="hidden text-red-600"></div>
              <div class="my-4 text-right relative">
                <input type="hidden" id="publicationId" name="publicationId" value="" />
                <button type="button" id="update-publication-settings" data-privilege-buttons="Edit Publications"
                  data-tooltip-target="archive-publication-settings-tooltip" data-tooltip-placement="top"
                  class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
                  Update Publications
                </button>
                <span id="archive-publication-settings-tooltip" role="tooltip"
                  class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-left text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                  permissions are limited.</span>
                <div class="tooltip-arrow" data-popper-arrow></div>
              </div>
              <div class="my-4 text-right hidden" id="update-publication-loader">
                <button disabled type="button"
                  class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                  <svg aria-hidden="true" role="status"
                    class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                      fill="currentColor" />
                    <path
                      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                      fill="#1C64F2" />
                  </svg>
                  Updating Publications...
                </button>
              </div>
            </div>
          </div>
          <!-- Modal footer -->

        </div>
      </div>
    </div>


    <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to update publications.')}">
    </div>
    <div
      th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to update publications.')}">
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
</body>

</html>