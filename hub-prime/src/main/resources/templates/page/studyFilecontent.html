<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />

  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var fileName = /*[[${fileName}]]*/ "";
    var tableName = /*[[${tableName}]]*/ "";

    // Now you can use studyId in your JavaScript code
  </script>

  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";

    var studyId = /*[[${studyId}]]*/ "";
    var fileName = /*[[${fileName}]]*/ "";
    var tableName = /*[[${tableName}]]*/ "";

    // Now you can use studyId in your JavaScript code

    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: fileName,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/summary/studies",
          },
          {
            text: studyId,
            href: "/study/detail/" + studyId,
          },
          {
            text: "CGM Files",
            href: "/svm/cgm/files/list/" + studyId,
          },

        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>

  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script type="module">
    import {
      AGGridAide,
      AGGridAideBuilder,
    } from "@presentation/shell/aggrid-aide.js";
    import ModalAide from "@presentation/shell/modal-aide.js";
    let columnDefs = [];
    document.addEventListener("DOMContentLoaded", function () {

      async function fetchColumnDefs() {
        try {
          let response = await fetch(`/cgm/field-name/${studyId}/${tableName}`, {
            method: "GET",
          });

          if (!response.ok) {
            throw new Error("Network response was not ok " + response.statusText);
          }

          let data = await response.json();

          if (data.errors && data.errors !== "") {
            // Handle errors if needed
          } else {
            let columnDefs = Object.values(data).map((val) => ({
              headerName: val.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase()), // Header names with spaces and capitalized
              field: val,
              filter: "agTextColumnFilter",
              enableRowGroup: true,
            }));

            return columnDefs;
          }
        } catch (error) {
          console.log(error);
          // Handle errors if needed
        }
      }

      // Usage
      fetchColumnDefs().then(dynamicColumnDefs => {
        const modalAide = new ModalAide();
        const agGridInstance = new AGGridAideBuilder()
          .withColumnDefs(dynamicColumnDefs)
          .withServerSideDatasource(
            window.shell.serverSideUrl(
              `/api/ux/tabular/jooq/` + studyId + `/` + tableName + `.json`

            ),
            (data, valueCols) => {
              return valueCols.map((col) => ({
                headerName: col.displayName,
                field: col.field,
              }));
            }
          )
          .withModalAide(modalAide)
          .withGridDivStyles({ height: (10 + 1) * 45 + "px", width: "100%" }) //no of rows displayed + 1(for header) multiplied by 45 (height of 1 row)
          .build();
        agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

        agGridInstance.init("serverDataGrid");
      });


      // function showLoader(loaderId) {
      //   document.getElementById(loaderId).style.display = "block";
      // }

      function getSvmValues() {
        showLoading("totalParticipantsBottomLoader");
        showLoading("totalFemaleBottomLoader");
        showLoading("totalAverageBottomLoader");
      }

      function assignValues(eleClass, value) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.innerHTML = value;
        });
      }
    });
  </script>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
  <div layout:fragment="content">
    <div
      th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null, notes = null,font='')}">
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
  </div>
</body>

</html>