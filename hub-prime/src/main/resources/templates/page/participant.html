<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
    integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
    crossorigin="anonymous"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>

  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
  <script th:src="@{/js/utils.js}"></script>

  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var participantId = /*[[${participantId}]]*/ "";
    var startDate = moment().subtract(7, "days");
    var endDate = moment();
    // Now you can use studyId in your JavaScript code
  </script>

  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";

    var studyId = /*[[${studyId}]]*/ "";
    var participantId = /*[[${participantId}]]*/ "";
    // Now you can use studyId in your JavaScript code

    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: participantId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/summary/studies",
          },
          {
            text: studyId,
            href: "/study/detail/" + studyId,
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
  <div layout:fragment="content">
    <div
      th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null, notes=null,font='')}">
    </div>
    <div style="display: flex">

      <div class="py-2">
        <h2 class="text-md font-semibold text-gray-700 mb-2">
          Filter by Date Range
        </h2>
        <div class="flex space-x-2 mt-2">
          <input type="text" name="reportrange" class="min-w-52" />
        </div>
      </div>
    </div>

    <div th:replace="~{fragments/patientdetails}"></div>
    <div>
      <div class="py-2">
        <div id="allmetrics">
          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/glucose-statistics}"></div>
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/time-in-range-details}"></div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/agp}"></div>
            <div class="col-span-1 md:col-span-2" th:replace="~{fragments/daily-glucose-profile}"></div>
          </div>

          <!-- testing starts -->
          <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div class="">
              <div th:replace="~{fragments/glycemia-risk-index}"></div>
            </div>
            <div class="">
              <div th:replace="~{fragments/all-metrics-data}"></div>
            </div>
          </div>
          <!-- testing ends -->

        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdn.jsdelivr.net/npm/@observablehq/plot@0.6"></script>
    <script>
      const url = "https://api.example.com/data";

      let timeMetrics = {
        timeBelowRangeLow: 0,
        timeBelowRangeVeryLow: 0,
        timeInRange: 0,
        timeAboveRangeVeryHigh: 0,
        timeAboveRangeHigh: 0,
      };
      let allMetricsData = {
        liabilityCdata: 0,
        hypoglycemicEpisodes: 0,
        euglycemicEpisodes: 0,
        hyperglycemicEpisodes: 0,
      };

      function initLoader() {
        document.querySelectorAll(".agp-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".tir-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".gri-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        document.querySelectorAll(".dgp-loader").forEach((loader) => {
          loader.classList.remove("hidden");
        });
        showLoader("percentageTimeCgmActive");
        showLoader("daysCGMWorn");
        showLoader("meanGlucose");
        showLoader("GMI");
        showLoader("coefficientOfVariants");
        showLoader("liabilityIndexCdata");
        showLoader("hypoglycemicEpisodesCdata");
        showLoader("euglycemicEpisodesCdata");
        showLoader("hyperglycemicEpisodesCdata");
        showLoader("meanAmplitudeCdata");
        showLoader("mValueCdata");
        showLoader("averageDailyRiskRangeCdata");
        showLoader("jIndexCdata");
        showLoader("lowBloodGlucoseIndexCdata");
        showLoader("gradeCdata");
        showLoader("meanOfDailyDifferencesCdata");
        showLoader("congaCdata");
      }
      initLoader();
      getParticipantInfo();
      getDateRanges();

      function getParticipantInfo() {
        fetchData(
          `/participant-info/${studyId}/${participantId}`,
          (data, error) => {
            if(!error){
            const result = data.data.participantInfo;

            assignValues("participantId", result.participant_id);
            assignValues("age", result.age);
            assignValues("bmi", result.bmi);
            assignValues("baselineHba1c", result.baseline_hba1c);
            assignValues("diabateseType", result.diabetes_type);
            assignValues("studyArm", result.study_arm);
            }
            else {
              console.error("Error fetching participant info:", error);
            }
          }
        );
      }

      function getDateRanges() {
        fetchData(
          `/participant-cgm-dates/${studyId}/${participantId}`,
          (data, error) => {
            if(!error){
            const result = data.data.participantCGMDates;
            if (
              result.participant_cgm_start_date &&
              result.participant_cgm_end_date
            ) {
              startDate = result.participant_cgm_start_date;
              endDate = result.participant_cgm_end_date;
              assignDates(startDate, endDate);
              // cb(moment(startDate), moment(endDate));
              initializeDatePicker(moment(startDate), moment(endDate));
              getParticipantData();
            } else {
              var defaultStartDate = moment().subtract(29, "days");
              var defaultEndDate = moment();
              assignDates(defaultStartDate, defaultEndDate);
              initializeDatePicker(defaultStartDate, defaultEndDate);
            }
          } else {
              console.error("Error fetching participant info:", error);
            }
          }
        );
      }

      function assignDates(date1, date2) {
        assignValues("startDate", moment(startDate).format("MMMM DD, YYYY"));
        assignValues("endDate", moment(endDate).format("MMMM DD, YYYY"));
        const diffInDays = moment(date2).diff(moment(date1), "days") + 1;
        const hoursPassedToday = moment().diff(
          moment().startOf("day"),
          "hours"
        );
        if (startDate === endDate) {
          assignValues("dateDiff", hoursPassedToday + " hours");
        } else {
          assignValues("dateDiff", diffInDays + " days");
        }
      }

      function getParticipantData() {
        initLoader();
        getMetrics();
        getTimeRanges();
        getGRI();
        getLiabilityIndex();
        getAllMetricsData();
        getGraphData();
        getDailyGlucoseProfile();
      }

      function showLoader(item) {
        let loaderId = item + "Loader";
        let unitId = item + "Unit";

        if (document.getElementById(loaderId))
          document.getElementById(loaderId).classList.remove("hidden");

        //hideItem(unitId)
      }

      function hideLoader(item) {
        let loaderId = item + "Loader";
        let unitId = item + "Unit";

        if (document.getElementById(loaderId))
          document.getElementById(loaderId).classList.add("hidden");
      }

      function showItem(eleClass) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.classList.remove("hidden");
        });
      }
      function hideItem(eleClass) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.classList.add("hidden");
        });
      }

      function assignValues(eleClass, value) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.classList.remove("hidden")
          element.innerHTML = value;

        });
      }

      function cb(start, end) {
        startDate = start;
        endDate = end;
        assignDates(startDate, endDate);
        getParticipantData();
      }

      function initializeDatePicker(startDate, endDate) {
        $('input[name="reportrange"]').daterangepicker(
          {
            opens: "left",
            startDate: startDate,
            endDate: endDate,
            ranges: {
              Today: [moment(), moment()],
              "Last 1 day": [moment().subtract(1, "days"), moment()],
              "Last 7 days": [moment().subtract(7, "days"), moment()],
              "Last 14 days": [moment().subtract(14, "days"), moment()],
              "Last 30 Days": [moment().subtract(30, "days"), moment()],
              "Last 90 Days": [moment().subtract(90, "days"), moment()],
            },
            cb,
          },
          function (start, end, label) {
            startDate = start.format("YYYY-MM-DD");
            endDate = end.format("YYYY-MM-DD");
            cb(startDate, endDate);
          }
        );
      }

      function formatDateToMatch(date) {
        return date.toISOString().slice(0, 19);
      }

      function clearSVG(selector) {
        var svg = document.querySelector(selector); // Replace with the correct selector for your SVG
        if (svg) {
          while (svg.firstChild) {
            svg.removeChild(svg.firstChild);
          }
        }
      }
    </script>
  </div>
</body>

</html>