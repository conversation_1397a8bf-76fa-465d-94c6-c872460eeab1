<style>
    .line {
        fill: none;
        stroke: lightgrey;
        stroke-width: 1px;
    }

    .highlight-area {
        fill: lightgreen;
        opacity: 1;
    }

    .highlight-line {
        fill: none;
        stroke: green;
        stroke-width: 1px;
    }

    .highlight-glucose-h-line {
        fill: none;
        stroke: orange;
        stroke-width: 1px;
    }

    .highlight-glucose-l-line {
        fill: none;
        stroke: red;
        stroke-width: 1px;
    }

    .reference-line {
        stroke: black;
        stroke-width: 1px;
    }

    .vertical-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }

    .day-label {
        font-size: 10px;
        fill: #000;
    }

    .day-label-top {
        font-size: 12px;
        text-anchor: middle;
        fill: #000;
    }

    .axis path,
    .axis line {
        fill: none;
        shape-rendering: crispEdges;
    }

    .mg-dl-label {
        font-size: 14px;
        font-weight: bold;
        text-anchor: middle;
        fill: #000;
        transform: rotate(-90deg);
        transform-origin: left center;
    }

    .horizontal-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }
</style>

<div class="w-full border border-gray-400 rounded relative">
    <div class="flex p-2" style="background-color: #E3E3E2; border-bottom: 1px solid #E3E3E2;">
        <p class="text-black font-sans text-base font-bold">
            DAILY GLUCOSE PROFILE
        </p>
        <div class="ml-auto">
            <div th:replace="~{fragments/calculatorText :: calculatorText('daily_glucose_profile')}"></div>
        </div>
    </div>
    <div class="p-2">
        <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('daily_glucose_profile')}"></div>
    </div>
    <div class="dgp-loader hidden"></div>
    <!-- <p class="dgp-chart-element-error hidden text-md font-bold text-center py-6"></p> -->
    <div class="dgp-chart-element-error hidden">
        <div
            th:replace="~{fragments/chart-errors :: chartError('/chart-skeletons/dgp-skel-gr.png','35rem','dgp-reload')}">
        </div>
    </div>
    <div class="dgp-chart-element-no-data-error hidden">
        <div
            th:replace="~{fragments/chart-errors :: chartNoDataError('/chart-skeletons/dgp-skel-gr.png','35rem','dgp-reload')}">
        </div>
    </div>
    <div class="p-5">
        <!-- <div id="daily-gp1">
            <svg id="dgp-wk1"></svg>
        </div>
        <div id="daily-gp2" class="mt-4">
            <svg id="dgp-wk2"></svg>
        </div> -->
        <dgp-chart></dgp-chart>
        <p class="py-2 px-4 text-gray-800 font-normal text-xs hidden" id="dgp-note"><b>NOTE:</b> The Daily Glucose
            Profile
            plots the glucose levels of the last 14 days.</p>
    </div>

</div>
<script type="module" src="/js/wc/d3/dgp-chart.js"></script>
<script>
    async function getDailyGlucoseProfile(startDateInput, endDateInput, retries = 3, delay = 5000) {
        try {
            clearSVG("#dgp-wk1", "dgp-chart");
            clearSVG("#dgp-wk2", "dgp-chart");


            // await new Promise((resolve) => setTimeout(resolve, 3000)); // Add a 3-second delay

            const response = await fetch(
                `/studies/${studyId}/participants/${participantId}/daily-glucose-profile?startDate=` + startDateInput + `&&endDate=` + endDateInput
            );

            // Check if the request was successful
            if (!response.ok) {
                throw new Error(
                    "Network response was not ok " + response.statusText
                );
            }
            const result = await response.json();
            const dgpChart = document.querySelector('dgp-chart');
            const resultData = result.data.glucoseProfile.data;
            if (resultData.length > 0) {

                document.querySelectorAll("svg#dgp-wk1").forEach((elem) => {
                    elem.classList.remove('hidden');
                });
                document.querySelectorAll("svg#dgp-wk2").forEach((elem) => {
                    elem.classList.remove('hidden');
                });
                document
                    .querySelectorAll(".dgp-chart-element-error")
                    .forEach((elem) => {
                        elem.classList.add("hidden");
                    });
                document
                    .querySelectorAll(".dgp-chart-element-no-data-error")
                    .forEach((elem) => {
                        elem.classList.add("hidden");
                    });
                document.getElementById("dgp-note").classList.remove("hidden");

                dgpChart.result = resultData;
                dgpChart.noDataFound = false;

            } else {
                dgpChart.noDataFound = true;
                document.getElementById("dgp-note").classList.add("hidden");
            }
            document.querySelectorAll(".dgp-loader").forEach((loader) => {
                loader.classList.add("hidden");
            });

            //console.log(jsonData);
        } catch (error) {
            console.error("Fetch error:", error);
            if (retries > 0) {
                console.log(`Retrying... Attempts left: ${retries}`);
                setTimeout(
                    () => getDailyGlucoseProfile(startDateInput, endDateInput, - 1, delay),
                    delay
                );
            } else {
                console.error("Max retries reached. Could not fetch data.");
                // Hide the loader in case of error
                document.querySelectorAll(".dgp-loader").forEach((loader) => {
                    loader.classList.add("hidden");
                });

            }
        }
    }


    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById("dgp-reload").addEventListener("click", function (event) {
            document.querySelectorAll(".dgp-loader").forEach((loader) => {
                loader.classList.remove("hidden");
            });
            document.querySelectorAll(".dgp-chart-element-error").forEach((loader) => {
                loader.classList.add("hidden");
            });
            document.querySelectorAll(".dgp-chart-element-no-data-error").forEach((loader) => {
                loader.classList.add("hidden");
            });
            getDailyGlucoseProfile(startDateInput, endDateInput);
        });
    });

</script>