<!-- thymeleaf-fragment.html -->
<div th:fragment="titleWithCount(title, cdata, value ,suffix)"
    class="flex m-2 pt-4 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
    <p class="font-sans text-sm font-normal" th:text="${title}">
        Title Placeholder
    </p>
    <p class="font-sans text-sm font-normal">
        <span th:text="${cdata}" th:attr="column=${value}"></span> <span class="text-xs" th:text="${suffix}"></span>
    </p>
</div>