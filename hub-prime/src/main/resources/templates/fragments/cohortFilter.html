<!-- Include Tom Select CSS -->
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

<!-- Include Tom Select JS -->
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>

<style>
  .ts-control {
    border: none;
  }
</style>

</script>
<div class="w-full flex flex-col md:flex-row">
  <!-- Form container -->
  <div class="hidden cursor-pointer" id="expand-ctr"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
      viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-8 mr-1.5 mt-0.5 p-2 border bg-gray-200">
      <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
    </svg>
  </div>
  <div class="w-60 mr-5  filter-card" id="filter-list-ctr">
    <div id="saved-filter" class="flex cursor-pointer mb-2"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="size-8 mr-1.5 mt-0.5 p-2 border bg-gray-200">
        <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
      </svg>
      <div class="font-medium text-sm ml-1 mt-2">Saved Filters</div>
    </div>

    <ul class="list-none h-[130px] overflow-auto pr-2.5" id="filter-list">
    </ul>
  </div>

  <div class="p-0 w-full">
    <!-- Filter Section: Studies and Gender -->
    <div class="flex flex-col md:flex-row">
      <!-- Studies -->
      <div class="mr-6 w-full md:w-auto">
        <div class="flex">
          <div
            class="border rounded-[3px] rounded-br-none rounded-tr-none border-solid border-[#DDDDDD] text-sm content-center px-2">
            <div class="flex">
              <div th:replace="~{fragments/icons :: studies()}"></div>
              <label class="self-center">Studies</label>
            </div>
          </div>
          <select name="Studies_to_include" id="studiesToInclude" title="Studies to include" multiple
            class="box-border h-9 w-full md:w-auto border rounded-[3px] rounded-bl-none rounded-tl-none border-solid border-[#DDDDDD] ml-[-1px] bg-[#ffffff] text-sm"
            data-live-search="true">
            <option value="DCLP1">DCLP1</option>
            <option value="DCLP3">DCLP3</option>
            <option value="DSS1">DSS1</option>
            <option value="NTLT">NTLT</option>
            <option value="CTR3">CTR3</option>
            <option value="DFA">DFA</option>
            <option value="IEOGC">IEOGC</option>
            <option value="RTCCGM">RTCCGM</option>
            <option value="WAD1">WAD1</option>
          </select>
        </div>
      </div>
      <!-- Gender -->
      <div class="mr-6 w-full md:w-auto mt-4 md:mt-0">
        <div class="flex">
          <div
            class="border rounded-[3px] rounded-br-none rounded-tr-none border-solid border-[#DDDDDD] text-sm content-center px-2">
            <div class="flex">
              <div th:replace="~{fragments/icons :: gender()}"></div>
              <label class="self-center">Gender</label>
            </div>
          </div>
          <select id="gender" title="Gender"
            class="box-border h-9 w-full md:w-auto border rounded-[3px] rounded-bl-none rounded-tl-none border-solid border-[#DDDDDD] ml-[-1px] bg-[#ffffff] text-sm">
            <option value="">Select</option>
            <option value="M">Male</option>
            <option value="F">Female</option>
            <option value="O">Others</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Accordion Filter Section -->
    <div class="box-border w-full h-13 bg-white border border-solid border-grey-600 shadow-md rounded-md my-4 p-2 px-2">
      <div class="flex flex-col md:flex-row justify-between">
        <div class="flex flex-col md:flex-row">
          <!-- Filter Label -->
          <div id="filter-button"
            class="border bg-[#f8fafd] rounded-[3px] rounded-br-none rounded-tr-none border-solid border-[#DDDDDD] text-sm content-center px-2 mb-2 md:mb-0  cursor-pointer">
            <div class="flex">
              <div th:replace="~{fragments/icons :: filter()}"></div>
              <label class="self-center">Filter</label>
            </div>
          </div>
          <!-- Filter Category Select -->
          <select id="filter-category"
            class="h-9 w-full md:w-auto rounded-[3px] rounded-bl-none rounded-tl-none border-solid border-[#DDDDDD] ml-[-1px] bg-[#ffffff] text-sm focus:shadow-none mb-2 md:mb-0">
            <option value="" title="">Select Category</option>
            <option value="age" title="Age">Age</option>
            <option value="tir" title="Time in Range (TIR)">Time in Range (TIR)</option>
            <option value="tbr" title="Time Below Range (TBR)">Time Below Range (TBR)</option>
            <option value="tar" title="Time Above Range (TAR)">Time Above Range (TAR)</option>
            <option value="wear_time_percentage" title="% Wear Time">% Wear Time</option>
            <option value="gri" title="Glycemic Risk Index (GRI)">Glycemic Risk Index (GRI)</option>
          </select>
          <div id="applied-filters" class="flex flex-wrap ml-2"></div>
        </div>
      </div>
      <div id="filter_manadatory" class="hidden">
        <p class="pt-1 pl-1 text-red-700 text-xs">Please select a filter category before adding.</p>
      </div>
      <div id="dynamic-filters" class="flex flex-col"></div>
    </div>

    <!-- Apply and Clear Buttons -->
    <div class="mt-4 flex flex-col md:flex-row">
      <!-- <button
        class="bg-[#378AEA] text-white py-1 px-4 hover:bg-blue-600 w-full md:w-[145px] h-8 gap-0 rounded-[3px] mr-0 md:mr-3 mb-4 md:mb-0"
        id="apply-filter">
        Apply <span class="text-base font-semibold text-white opacity-[0.7]" id="filter-count">(0)</span> Filters
      </button> -->



      <button id="show-filter-modal" data-modal-target="save-modal" data-modal-toggle="save-modal"
        class="mr-2 block text-blue-600 bg-blue-100 hover:bg-blue-200 hover:text-white focus:ring-0 focus:outline-none focus:ring-blue-300 font-medium rounded-[3px] border border-blue-300 text-base px-5 py-1 h-8 text-center dark:bg-blue-400 dark:hover:bg-blue-400 dark:focus:ring-blue-800 hover:bg-blue-500"
        type="button">
        Save Filter
      </button>
      <div id="tooltip-animation" role="tooltip"
        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 tooltip">
        You must be logged in to save filters.
        <div class="tooltip-arrow" data-popper-arrow></div>
      </div>
      <button id="clear-all"
        class="bg-gray-400 text-white py-1 px-4 hover:bg-gray-300  w-full md:w-[100px] h-8 gap-0 rounded-[3px] mr-0 md:mr-3 mb-4 md:mb-0">
        Clear All
      </button>
    </div>
  </div>
</div>