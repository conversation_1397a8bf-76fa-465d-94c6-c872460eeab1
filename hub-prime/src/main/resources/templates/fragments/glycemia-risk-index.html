<div>
  <script>
    function showHideCalculation() {
      const calculations = document.getElementById("calculation-container");
      const isCalculationsHidden = calculations.classList.contains("hidden");
      if (isCalculationsHidden) {
        document
          .getElementById("calculation-container")
          .classList.remove("hidden");
      } else {
        document
          .getElementById("calculation-container")
          .classList.add("hidden");
      }
    }
  </script>
  <div class="w-full border border-gray-400 rounded">
    <div class="flex p-2 relative justify-between" style="background-color: #e3e3e2; border-bottom: 1px solid #e3e3e2">
      <p class="text-black font-sans text-base font-bold">
        Glycemia Risk Index
      </p>

      <div th:onclick="showHideCalculation()" class="cursor-pointer">
        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
          <path d="M17 5H7V7H17V5Z" fill="currentColor" />
          <path d="M7 9H9V11H7V9Z" fill="currentColor" />
          <path d="M9 13H7V15H9V13Z" fill="currentColor" />
          <path d="M7 17H9V19H7V17Z" fill="currentColor" />
          <path d="M13 9H11V11H13V9Z" fill="currentColor" />
          <path d="M11 13H13V15H11V13Z" fill="currentColor" />
          <path d="M13 17H11V19H13V17Z" fill="currentColor" />
          <path d="M15 9H17V11H15V9Z" fill="currentColor" />
          <path d="M17 13H15V19H17V13Z" fill="currentColor" />
          <path clip-rule="evenodd"
            d="M3 3C3 1.89543 3.89543 1 5 1H19C20.1046 1 21 1.89543 21 3V21C21 22.1046 20.1046 23 19 23H5C3.89543 23 3 22.1046 3 21V3ZM5 3H19V21H5V3Z"
            fill="currentColor" fill-rule="evenodd" />
        </svg>
      </div>
      <div id="calculation-container"
        class="hidden absolute bg-white z-[10] right-2.5 top-[55px] shadow-sm rounded-[3px] border border-solid border-[#dbdbdb]">
        <div class="flex justify-between px-2 py-2 items-center bg-[#e5e7eb]">
          <p class="font-semibold">Calculations</p>
          <div class="cursor-pointer" th:onclick="showHideCalculation()">
            <svg viewBox="0 0 32 32" width="20" height="20" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <style>
                  .cls-1 {
                    fill: none;
                    stroke: #000;
                    stroke-linecap: round;
                    stroke-linejoin: round;
                    stroke-width: 2px;
                  }
                </style>
              </defs>
              <title />
              <g id="cross">
                <line class="cls-1" x1="7" x2="25" y1="7" y2="25" />
                <line class="cls-1" x1="7" x2="25" y1="25" y2="7" />
              </g>
            </svg>
          </div>
        </div>
        <div class="text-xs px-3 py-2 flex flex-col gap-1">
          <p>Hypoglycemia Component = VLow + (0.8 × Low)</p>
          <p>Hyperglycemia Component = VHigh + (0.5 × High)</p>
          <p>
            GRI = (3.0 × Hypoglycemia Component) + (1.6 × Hyperglycemia
            Component)
          </p>
          <p class="font-normal font-sans text-sm">Equivalently,</p>
          <p>GRI = (3.0 × VLow) + (2.4 × Low) + (1.6 × VHigh) + (0.8 × High)</p>
        </div>
      </div>
    </div>
    <div th:replace="~{fragments/GRIchart}"></div>

    <div class="overflow-auto p-5">
      <table class="datatable w-full mb-2 border table-fixed">
        <thead>
          <tr class="bg-gray-200">
            <th class="py-2 text-center">TIR</th>
            <th class="py-2 text-center">TAR(VH)</th>
            <th class="py-2 text-center">TAR(H)</th>
            <th class="py-2 text-center">TBR(L)</th>
            <th class="py-2 text-center">TBR(VL)</th>
            <th class="py-2 text-center">TITR</th>
            <th class="py-2 text-center">GRI</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="TIR py-2 text-center"></td>
            <td class="TAR_VH py-2 text-center"></td>
            <td class="TAR_H py-2 text-center"></td>
            <td class="TBR_L py-2 text-center"></td>
            <td class="TBR_VL py-2 text-center"></td>
            <td class="timeInTightRangeCdata py-2 text-center"></td>
            <td class="GRI py-2 text-center"></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="float-right p-4 hidden">
    <button th:onclick="${isCalculationShown} ? hideCalculation() : showCalculation()"
      th:text="${isCalculationShown ? 'Hide Calculation' : 'Show Calculation'}"></button>
  </div>
</div>
<script type="module" src="/js/wc/d3/gri-chart.js"></script>
<script>
  function getGRI(startDateInput, endDateInput) {
    fetchData(
      `/studies/${studyId}/participants/${participantId}/get-glycemic-risk-indicator?startDate=` + startDateInput + `&&endDate=` + endDateInput,
      (resdata, error) => {
        document.querySelectorAll(".gri-loader").forEach((loader) => {
          loader.classList.add("hidden");
        });
        console.log(resdata);
        var errorStatus = false;

        if (!error && resdata.data && resdata.data.metrics) {
          const result = resdata.data.metrics.data;
          if (result != null) {
            assignValues("TIR", result.time_in_range_percentage);
            assignValues("TAR_VH", result.time_above_VH_percentage);
            assignValues("TAR_H", result.time_above_H_percentage);
            assignValues("TBR_L", result.time_below_low_percentage);
            assignValues("TBR_VL", result.time_below_VL_percentage);
            assignValues("GRI", result.GRI);
            createGRIChart(result, false);
          }
          else {
            assignValues("TIR", '<span title="No data found in %s">⚠️</span>');
            assignValues("TAR_VH", '<span title="No data found in %s">⚠️</span>');
            assignValues("TAR_H", '<span title="No data found in %s">⚠️</span>');
            assignValues("TBR_L", '<span title="No data found in %s">⚠️</span>');
            assignValues("TBR_VL", '<span title="No data found in %s">⚠️</span>');
            assignValues("GRI", '<span title="No data found in %s">⚠️</span>');
            createGRIChart([], false);
          }

        } else {
          assignValues("TIR", '<span title="No data found in %s">⚠️</span>');
          assignValues("TAR_VH", '<span title="No data found in %s">⚠️</span>');
          assignValues("TAR_H", '<span title="No data found in %s">⚠️</span>');
          assignValues("TBR_L", '<span title="No data found in %s">⚠️</span>');
          assignValues("TBR_VL", '<span title="No data found in %s">⚠️</span>');
          assignValues("GRI", '<span title="No data found in %s">⚠️</span>');
          createGRIChart([], true);

        }
      }
    );
  }

  function createGRIChart(griData = [], error) {
    const width = 150;
    const height = 150;
    const margin = { top: 20, right: 0, bottom: 10, left: 30 };
    let noDataFound = false;
    const griChart = document.querySelector('gri-chart');

    clearSVG(".gri-chart", "gri-chart");
    document.querySelector('gri-chart').classList.remove("hidden");
    if (error == true) {
      document.querySelectorAll(".gri-chart-element-error").forEach((elem) => {
        // elem.innerHTML = "<p>No data available during this duration</p>";
        elem.classList.remove("hidden");
      });
      document.querySelectorAll(".gri-chart-element-no-data-error").forEach((elem) => {
        elem.classList.add("hidden");
      });
      document.querySelector('.datatable').classList.add("hidden");
      document.querySelector('gri-chart').classList.add("hidden");
    } else if (parseFloat(griData.GRI) === 0 || isNaN(griData.GRI) || griData === null) {
      document.querySelectorAll(".gri-chart").forEach((elem) => {
        elem.classList.add("hidden");
      });

      griChart.noDataFound = true;

      document.querySelector('.datatable').classList.add("hidden");
    } else {
      document.querySelectorAll(".gri-chart-element-no-data-error").forEach((elem) => {
        elem.classList.add("hidden");
      });
      document.querySelectorAll(".gri-chart-element-error").forEach((elem) => {
        // elem.innerHTML = "<p>No data available during this duration</p>";
        elem.classList.add("hidden");
      });
      document.querySelector('.datatable').classList.remove("hidden");
      document.querySelectorAll(".gri-chart").forEach((elem) => {
        elem.classList.remove("hidden");
      });
      griChart.noDataFound = false;
      griChart.data = griData;

    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    document.getElementById("gri-reload").addEventListener("click", function (event) {
      document.querySelectorAll(".gri-loader").forEach((loader) => {
        loader.classList.remove("hidden");
      });
      document.querySelector('.datatable').classList.remove("hidden");
      document.querySelectorAll(".gri-chart-element-error").forEach((loader) => {
        loader.classList.add("hidden");
      });
      document.querySelectorAll(".gri-chart-element-no-data-error").forEach((loader) => {
        loader.classList.add("hidden");
      });
      getGRI();
    });
  });
</script>