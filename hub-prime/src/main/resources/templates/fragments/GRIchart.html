<div class="relative">
  <script src="https://d3js.org/d3.v6.min.js"></script>
  <style>
    svg {
      display: block;
      margin: auto;
    }
  </style>
  <div class="gri-loader"></div>
  <!-- <p class="gri-chart-element-error hidden text-md font-bold text-center py-6"></p> -->
  <div class="gri-chart-element-error hidden">
    <div th:replace="~{fragments/chart-errors :: chartError('/chart-skeletons/gri-skel-gr.png','45rem','gri-reload')}">
    </div>
  </div>
  <div class="gri-chart-element-no-data-error hidden">
    <div
      th:replace="~{fragments/chart-errors :: chartNoDataError('/chart-skeletons/gri-skel-gr.png','45rem','gri-reload')}">
    </div>
  </div>
  <!-- <svg class="gri-chart hidden"></svg> -->
  <gri-chart></gri-chart>
</div>