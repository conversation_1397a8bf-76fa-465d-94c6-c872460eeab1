<!-- Include Tom Select CSS -->
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

<!-- Include Tom Select JS -->
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>

<style>
  .ts-control {
    border: none;
  }
</style>

</script>
<div class="w-full flex flex-col md:flex-row">
  <!-- Form container -->
  <div class="hidden cursor-pointer" id="expand-ctr"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
      viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-8 mr-1.5 mt-0.5 p-2 border bg-gray-200">
      <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
    </svg>
  </div>


  <div class="p-0 w-full">
    <!-- Accordion Filter Section -->
    <div class="box-border w-full bg-white border border-gray-200 shadow-sm rounded mb-2 p-2">

      <!-- starts -->
      <article class="grid grid-cols-1 sm:grid-cols-12 md:grid-cols-12 gap-2 lg:gap-4">
        <div class="col-span-12 md:col-span-12 lg:col-span-4 xl:col-span-2" id="search-study-left">
          <!-- Filter Label and Custom Select -->
          <div class="flex flex-row items-start">
            <!-- Dropdown Button and Options -->
            <div class="relative w-full">

              <div>
                <h2 class="flex flex-row items-start">
                  <div class="flex items-center border bg-blue-50 rounded-l border-blue-300 px-2 py-2
                  cursor-pointer" id="filter-toggle">
                    <div th:replace="~{fragments/icons :: filter()}"></div>
                  </div>
                  <button type="button" id="filter-header-button"
                    class="flex items-center justify-between w-full px-3 py-2 font-medium rtl:text-right text-blue-500 border border-blue-200 focus:ring-0 dark:border-blue-700 dark:text-blue-400 bg-blue-100 hover:bg-blue-100 focus:bg-blue-100 dark:hover:bg-blue-800 gap-3 rounded-none rounded-r"
                    data-accordion-target="#accordion-collapse-body-1" aria-expanded="false"
                    aria-controls="accordion-collapse-body-1">
                    <span>Select Category</span>

                  </button>
                </h2>
                <div id="accordion-collapse-body-1" aria-labelledby="accordion-collapse-heading-1" class="">
                  <div class="p-3 border border-gray-200 dark:border-gray-700 dark:bg-gray-900">
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Age</span>
                      <input type="checkbox" id="age_check" value="age" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Time in Range (TIR)</span>
                      <input type="checkbox" id="tir_check" value="tir" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Time Below Range (TBR)</span>
                      <input type="checkbox" id="tbr_check" value="tbr" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Time Above Range (TAR)</span>
                      <input type="checkbox" id="tar_check" value="tar" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">% Wear Time</span>
                      <input type="checkbox" id="wear_time_check" value="wear_time" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Glycemic Risk Index (GRI)</span>
                      <input type="checkbox" id="gri_check" value="gri" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Gender</span>
                      <input type="checkbox" id="gender_check" value="gender" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Glucose Management Indicator (GMI)</span>
                      <input type="checkbox" id="gmi_check" value="gmi" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">Days of Wear</span>
                      <input type="checkbox" id="days_wear_check" value="days_wear" class="ml-auto filter-cat-opt">
                    </div>
                    <div class="grid grid-cols-12 gap-x-2 gap-y-1 pb-2.5">
                      <span class="text-sm text-gray-700 col-span-11">HbA1c</span><input type="checkbox"
                        id="hba1c_check" value="hba1c" class="ml-auto filter-cat-opt">
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
        <div class="col-span-12 md:col-span-12 lg:col-span-8 xl:col-span-10" id="search-study-right">
          <div class="mb-4 hiiden" id="filter-container">
            <aside class="mb-1">
              <!-- Applied Filters and Error Message Section -->
              <div id="applied-filters" class="flex flex-wrap mr-2 mb-1 content-baseline"></div>
              <div id="filter_manadatory" class="hidden mt-2 text-red-700 text-xs">Please select a filter category
                before
                adding.</div>
            </aside>
            <aside>
              <div id="dynamic-filters" class="flex flex-col"></div>
            </aside>
            <aside> <!-- Apply and Clear Buttons -->
              <div class="mt-0 flex flex-col md:flex-row">
                <!-- <button id="show-filter-modal" data-modal-target="save-modal" data-modal-toggle="save-modal"
                class="mr-2 block text-blue-600 bg-blue-100 hover:bg-blue-200 hover:text-white focus:ring-0 focus:outline-none focus:ring-blue-300 font-medium rounded-[3px] border border-blue-300 text-base px-5 py-1 h-8 text-center dark:bg-blue-400 dark:hover:bg-blue-400 dark:focus:ring-blue-800 hover:bg-blue-500"
                type="button">
                Save Filter
              </button> -->

                <button id="clear-all"
                  class="bg-gray-400 text-white py-1 px-4 hover:bg-gray-300  w-full md:w-[100px] h-8 gap-0 rounded-[3px] mr-0 md:mr-3 mb-4 md:mb-0">
                  Clear All
                </button>
              </div>
            </aside>
          </div>
          <div class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Each metric adjusts its width depending on screen size -->
            <div class="w-full"
              th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants With Data', null, @{/research-study/population/total-participants-with-data.html})}">
            </div>
            <div class="w-full"
              th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total CGM Files',  null, @{/research-study/population/total-cgm-file-count.html})}">
            </div>

            <div class="w-full"
              th:replace="~{fragments/metricsWithTitle :: serverTextStat('Data Points', null, @{/research-study/population/data-points.html})}">
            </div>
          </div>
          <div class="mt-4">
            <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
          </div>
        </div>

      </article>

      <!-- ends -->

    </div>
  </div>


</div>