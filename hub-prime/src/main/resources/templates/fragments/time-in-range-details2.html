<div class="border border-gray-400 rounded">
  <div class="flex p-2 bg-[#E3E3E2] border-b-[1px] border-solid border-[#E3E3E2]">
    <p class="text-black font-sans text-base font-bold">Goals for Type 1 and Type 2 Diabetes</p>
  </div>
  <div class="pl-4 pr-4 pt-2">
    <div class="tir-ctr">
      <div class="tir-loader hidden"></div>
      <p class="tir-chart-element-error hidden text-md font-bold text-center py-6"></p>
      <div class="chartContainer">
        <svg id="tir-chart" class="m-0"></svg>
      </div>
      <!-- <div>
        <div
          class="flex pt-4 mb-4 h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
          <p class="font-sans text-s m font-normal">
            Time in Tight Range
          </p>
          <p class="font-sans text-sm font-normal">
            <span class="timeInTightRangeCdata"></span><span class="text-xs timeInTightRangeCdataUnit">mg/dL</span>
          </p>
        </div>
      </div> -->
    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
<script src="https://cdn.jsdelivr.net/npm/@observablehq/plot@0.6"></script>
<script>

  createChart(false)

  function createChart(invalidData) {
    //clearSVG("#tir-chart");
    document.querySelectorAll(".chartContainer").forEach((elem) => {
      elem.classList.remove("hidden")
    });
    document
      .querySelectorAll(".tir-chart-element-error")
      .forEach((elem) => {
        elem.classList.add("hidden");
      });
    document.querySelectorAll(".tir-loader").forEach((loader) => {
      loader.classList.add("hidden");
    });

    // Data
    const chartHeight = 450;
    const chartWidth = 700;

    // const chartdata = [
    //   {
    //     category: "Very Low",
    //     value: timeMetrics.timeBelowRangeVeryLow,
    //     goal: "<1%",
    //     color: "#F0F0F0",
    //   },
    //   {
    //     category: "Low",
    //     value: timeMetrics.timeBelowRangeLow,
    //     goal: "<4%",
    //     color: "#F0F0F0",
    //   },
    //   {
    //     category: "Target",
    //     value: timeMetrics.timeInRange,
    //     goal: "≥70%",
    //     color: "#F0F0F0",
    //   },
    //   {
    //     category: "High",
    //     value: timeMetrics.timeAboveRangeHigh,
    //     goal: "<25%",
    //     color: "#F0F0F0",
    //   },
    //   {
    //     category: "Very High",
    //     value: timeMetrics.timeAboveRangeVeryHigh,
    //     goal: "<5%",
    //     color: "#F0F0F0",
    //   },
    // ];

    // console.log(chartdata)

    const chartdata = [
      { category: "Very Low", value: 5, goal: "<1%", color: "#F0F0F0" },
      { category: "Low", value: 10, goal: "<4%", color: "#F0F0F0" },
      { category: "Target", value: 72, goal: "≥70%", color: "#F0F0F0" },
      { category: "High", value: 5, goal: "<25%", color: "#F0F0F0" },
      { category: "Very High", value: 8, goal: "<5%", color: "#F0F0F0" },
    ];

    // Normalize data to ensure total is 100
    const total = chartdata.reduce((acc, curr) => acc + curr.value, 0);
    if (total >= 98 && invalidData === false) {
      // Dimensions and margins
      const margin = { top: 80, right: 0, bottom: 60, left: 0 };
      const width = chartWidth - margin.left - margin.right;
      const height = chartHeight - margin.top - margin.bottom;

      // Create the SVG container
      const svg = d3
        .selectAll(".chartContainer")
        .select("svg#tir-chart")
        .attr("width", chartWidth)
        .attr("height", chartHeight)
        .append("g")
        .attr(
          "transform",
          `translate(-${margin.left + 200},${margin.top})`
        );

      // Create the x scale
      const x = d3
        .scaleBand()
        .domain(["Goals for Type 1 and Type 2 Diabetes"])
        .range([0, width])
        .padding(0.8);

      // Create the y scale
      const y = d3.scaleLinear().domain([0, 100]).range([height, 0]);

      // Create the stacked bars
      let y0 = 0;
      chartdata.forEach((d) => {
        let barHeight = d.value > 0 ? d.value + 2 : 2;
        let color = d.value > 0 ? d.color : "#d3d3d3";
        svg
          .append("rect")
          .attr("x", x("Goals for Type 1 and Type 2 Diabetes"))
          .attr("y", y(y0 + barHeight))
          .attr("width", x.bandwidth())
          .attr("height", height - y(barHeight))
          .attr("fill", color)
          .attr("stroke", "white");
        y0 += barHeight;
      });

      // Add y-axis without lines or text
      const yAxis = d3.axisLeft(y).tickSize(0).tickValues([]);
      const yAxisGroup = svg.append("g").call(yAxis);
      yAxisGroup.selectAll("line").remove();
      yAxisGroup.selectAll("path").remove();

      let y1 = 0;
      let highEndPoint, veryHighEndPoint;
      let lowEndPoint, veryLowEndPoint;
      let targetValue = 0;
      chartdata.forEach((d, i) => {
        let barValue = d.value > 0 ? d.value : 2;
        let lineY = y1 + barValue / 2;
        let textY = y1 + barValue / 2 + 5;
        let lineLength = 130;
        targetValue = chartdata.find(
          (d) => d.category === "Target"
        ).value;

        if (d.category === "Very High") {
          lineY = y1 + barValue / 2 + 7;
          textY = lineY + 4;
          lineLength = 25;
          veryHighEndPoint = {
            x:
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              120 +
              lineLength,
            y: y(lineY),
          };
        }
        if (d.category === "High") {
          lineY = y1 + barValue / 2 + 5;
          textY = lineY - 4;
          lineLength = 25;
          highEndPoint = {
            x:
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              120 +
              lineLength,
            y: y(lineY),
          };
        }
        if (d.category === "Low") {
          lineY = y1 + barValue / 2 + 1;
          textY = lineY + 4;
          //textY = (targetValue + d.value) < 6 ? y1 + barValue - barValue - 3 : y1 + barValue - barValue + 5;
          lineLength = 25;
          lowEndPoint = {
            x:
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              120 +
              lineLength,
            y: y(lineY),
          };
        }
        if (d.category === "Very Low") {
          lineY = y1 + barValue / 2;
          textY = lineY - 4;
          lineLength = 25;
          veryLowEndPoint = {
            x:
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              120 +
              lineLength,
            y: y(lineY),
          };
        }

        // svg.append("text")
        //   .attr("x", x('Goals for Type 1 and Type 2 Diabetes') + x.bandwidth() + 10)
        //   .attr("y", y(textY))
        //   .attr("text-anchor", "start")
        //   .attr("dy", ".50em")
        //   .style("font-weight", "bold")
        //   .text(d.category !== "Target" ? `${d.category}: ${d.value}%` : `${d.category}`);

        svg
          .append("text")
          .attr(
            "x",
            x("Goals for Type 1 and Type 2 Diabetes") + x.bandwidth() + 10
          )
          .attr("y", y(textY))
          .attr("text-anchor", "start")
          .attr("dy", ".50em")
          .style("font-weight", "bold")
          .style("font-size", "12px")
          .append("tspan")
          .attr(
            "x",
            x("Goals for Type 1 and Type 2 Diabetes") + x.bandwidth() + 10
          )
          .text(d.category)
          .append("tspan")
          .attr("x", function () {
            // Adjust this value to space out the category and value
            return (
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              10 +
              130
            ); // Adjust the offset as needed
          })
          .attr("text-anchor", "end")
          .text(d.category !== "Target" ? `${d.value}%` : "");

        if (d.category === "Very High") {
          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              10
            )
            .attr("y", y(textY + 4))
            .text("Goal: <5%")
            .style("font-size", "12px")
            .style("fill", "#7A7A7B");
        }

        if (d.category === "Very Low") {
          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              10
            )
            .attr("y", y(textY - 10))
            .text("Goal: <1%")
            .style("font-size", "12px")
            .style("fill", "#7A7A7B");

          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              90
            )
            .attr("y", y(textY - 10))
            .attr("text-anchor", "start")
            .text("Each 1% time in range = about 15 minutes")
            .style("font-size", "12px")
            .style("fill", "#7A7A7B");
        }
        const highValue = chartdata.find(
          (d) => d.category === "High"
        ).value;
        if (d.category === "Target") {
          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              154
            )
            .attr("y", y(textY))
            .attr("text-anchor", "start")
            .attr("dy", ".50em")
            .style("font-size", "15px")
            .style("font-weight", "bold")
            .text(`${d.value}%`);

          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              202
            )
            .attr("y", y(textY - 3))
            .text("Goal: ≥70%")
            .style("font-size", "12px")
            .style("fill", "#7A7A7B");

          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() +
              90
            )
            .attr("y", y(textY - 12))
            .attr("text-anchor", "start")
            .text("Each 5% increase is clinically beneficial")
            .style("font-size", "12px")
            .style("fill", "#7A7A7B");

          if (highValue > 0) {
            svg
              .append("text")
              .attr(
                "x",
                x("Goals for Type 1 and Type 2 Diabetes") +
                x.bandwidth() -
                100
              )
              .attr("y", y(y1 + d.value))
              .attr("text-anchor", "start")
              .text("180")
              .style("font-weight", "bold")
              .style("font-size", "12px");
          }
        }

        if (d.category === "High" && highValue > 0) {
          svg
            .append("text")
            .attr(
              "x",
              x("Goals for Type 1 and Type 2 Diabetes") +
              x.bandwidth() -
              100
            )
            .attr("y", y(y1 + d.value))
            .attr("text-anchor", "start")
            .style("font-weight", "bold")
            .text("250")
            .style("font-size", "12px");
        }
        svg
          .append("line")
          .attr(
            "x1",
            x("Goals for Type 1 and Type 2 Diabetes") + x.bandwidth() + 10
          )
          .attr("y1", y(lineY))
          .attr(
            "x2",
            x("Goals for Type 1 and Type 2 Diabetes") +
            x.bandwidth() +
            120 +
            lineLength
          )
          .attr("y2", y(lineY))
          .attr("width", 200)
          .attr("stroke", "black")
          .attr("stroke-width", 1)
          .attr("stroke-linecap", "round");

        y1 += barValue;
      });

      // Add the line connecting "High" and "Very High"
      if (highEndPoint && veryHighEndPoint) {
        const midX = (highEndPoint.x + veryHighEndPoint.x) / 2;
        const midY = (highEndPoint.y + veryHighEndPoint.y) / 2;

        // Find values for "High" and "Very High"
        const highValue = chartdata.find(
          (d) => d.category === "High"
        ).value;
        const veryHighValue = chartdata.find(
          (d) => d.category === "Very High"
        ).value;

        // Sum of "High" and "Very High" values
        const sumHighAndVeryHigh = highValue + veryHighValue;

        // Add text above the horizontal line
        svg
          .append("text")
          .attr(
            "x",
            (x("Goals for Type 1 and Type 2 Diabetes") +
              chartWidth -
              margin.right) /
            2 +
            50
          )
          .attr("y", midY - 5)
          .style("font-size", "15px")
          .style("font-weight", "bold")
          .text(`${sumHighAndVeryHigh}%`);

        svg
          .append("text")
          .attr(
            "x",
            (x("Goals for Type 1 and Type 2 Diabetes") +
              chartWidth -
              margin.right) /
            2 +
            90
          )
          .attr("y", midY - 5)
          .style("font-size", "12px")
          .text("Goal: <25%")
          .style("fill", "#7A7A7B");

        // Draw the line connecting "High" and "Very High"
        svg
          .append("line")
          .attr("x1", highEndPoint.x)
          .attr("y1", highEndPoint.y)
          .attr("x2", veryHighEndPoint.x)
          .attr("y2", veryHighEndPoint.y)
          .attr("stroke", "black")
          .attr("stroke-width", 1)
          .attr("stroke-linecap", "round");

        // Draw the horizontal line from the midpoint
        svg
          .append("line")
          .attr("x1", highEndPoint.x)
          .attr("y1", midY)
          .attr("x2", chartWidth - margin.right - 40)
          .attr("y2", midY)
          .attr("stroke", "black")
          .attr("stroke-width", 1)
          .attr("stroke-linecap", "round");
      }

      if (lowEndPoint && veryLowEndPoint) {
        const midX = (lowEndPoint.x + veryLowEndPoint.x) / 2;
        const midY = (lowEndPoint.y + veryLowEndPoint.y) / 2;

        // Find values for "Low" and "Very Low"
        const lowValue = chartdata.find(
          (d) => d.category === "Low"
        ).value;
        const veryLowValue = chartdata.find(
          (d) => d.category === "Very Low"
        ).value;

        // Sum of "Low" and "Very Low" values
        const sumLowAndVeryLow = lowValue + veryLowValue;

        svg
          .append("text")
          .attr(
            "x",
            (x("Goals for Type 1 and Type 2 Diabetes") +
              chartWidth -
              margin.right) /
            2 +
            50
          )
          .attr("y", midY - 5)
          .style("font-size", "15px")
          .style("font-weight", "bold")
          .text(`${sumLowAndVeryLow}%`);

        svg
          .append("text")
          .attr(
            "x",
            (x("Goals for Type 1 and Type 2 Diabetes") +
              chartWidth -
              margin.right) /
            2 +
            90
          )
          .attr("y", midY - 5)
          .style("font-size", "12px")
          .text("Goal: <4%")
          .style("fill", "#7A7A7B");

        svg
          .append("line")
          .attr("x1", lowEndPoint.x)
          .attr("y1", lowEndPoint.y)
          .attr("x2", veryLowEndPoint.x)
          .attr("y2", veryLowEndPoint.y)
          .attr("stroke", "black")
          .attr("stroke-width", 1)
          .attr("stroke-linecap", "round");

        // Draw the horizontal line from the midpoint
        svg
          .append("line")
          .attr("x1", lowEndPoint.x)
          .attr("y1", midY)
          .attr("x2", chartWidth - margin.right - 40)
          .attr("y2", midY)
          .attr("stroke", "black")
          .attr("stroke-width", 1)
          .attr("stroke-linecap", "round");
      }
    } else {
      // Add text to indicate that the sum is less than 100
      document.querySelectorAll(".chartContainer").forEach((elem) => {
        elem.classList.add("hidden")
      });
      document
        .querySelectorAll(".tir-chart-element-error")
        .forEach((elem) => {
          elem.innerHTML =
            "<p>No data available during this duration</p>";
          elem.classList.remove("hidden");
        });
      // d3.selectAll(".chartContainer")
      //   .select("svg#tir-chart")
      //   .attr("width", chartWidth)
      //   .attr("height", chartHeight)
      //   .append("text")
      //   .attr("x", chartWidth / 2)
      //   .attr("y", chartHeight / 2)
      //   .attr("text-anchor", "middle")
      //   .attr("dy", ".35em")
      //   .style("font-size", "20px")
      //   .style("font-weight", "bold")
      //   .text(
      //     invalidData
      //       ? "No data available during this duration"
      //       : "Sum of time time range values is less than 100"
      //   );
    }
  }

</script>