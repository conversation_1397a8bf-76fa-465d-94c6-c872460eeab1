<div th:fragment="introduction(pagedescription,pagesubdescriptiontitle, pagesubdescription,pageattributestitle, pageattributes, notes,font)" class="w-full mb-2">
  <div>
    <p class="mb-2">
      <span th:each="item : ${pageDescription}" class="mt-2 text-sm" th:text="${item}"></span>
  </p>
    <p>
      <span th:each="item : ${pagesubdescriptiontitle}" class="mt-2 text-sm mb-2 block font-semibold" th:text="${item}"></span>
      <ul class="list-disc ml-5 mb-4 pl-3">
        <li th:each="subDesc : ${pagesubdescription}" class="mb-1 text-sm" th:text="${subDesc}"></li>
    </ul>
    <p>
      <span th:each="item : ${pageattributestitle}" th:classappend="${'mt-2 text-sm mb-2 block '+font}" th:text="${item}"></span>
    <ul class="list-disc ml-5 mb-4 pl-3">
      <li  th:each="subDesc : ${pageattributes}" class="mb-1 text-sm" th:text="${subDesc}"></li>
  </ul>
</p>
</div>

  <div th:if="${notes != null}" class="flex items-top p-4 mt-4 text-sm text-yellow-800 rounded-lg bg-yellow-50"
    role="alert">
    <svg class="flex-shrink-0 inline w-4 h-4 me-3 ml-0 mt-[3px]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
      fill="currentColor" viewBox="0 0 20 20">
      <path
        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
    </svg>
    <span class="sr-only">Info</span>
    <div>
      <span class="font-medium uppercase">Note</span>
      <ul th:each="item : ${notes}">
        <li class="list-disc ml-4">
          <p class="text-sm text-[#6b7280]" th:text="${item}"></p>
        </li>
      </ul>
    </div>
  </div>
</div>