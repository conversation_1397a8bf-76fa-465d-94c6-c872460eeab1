<!-- src/main/resources/templates/fragments.html -->
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<body>
    <div th:fragment="textStat(title, data)">
        <div
            class="overflow-hidden rounded-lg bg-white px-3 py-3 shadow sm:p-6 min-w-36 w-auto grid gap-2 grid-cols-1 border-b-4 border-b-indigo-400">
            <dt class="truncate text-sm font-medium text-gray-500" th:text="${title}">Metrics Title</dt>
            <dd class="truncate mt-1 text-3xl font-semibold tracking-tight text-indigo-600" th:text="${data}">0%</dd>
        </div>
    </div>

    <div th:fragment="serverTextStat(title, link, hxGet)">
        <div class="overflow-hidden rounded-lg bg-white px-3 py-3 shadow sm:p-6 min-w-36 w-auto grid gap-2 grid-cols-1"
            th:classappend="${link != null} ? 'border-b-4 border-b-blue-600' : ''">
            <dt class="truncate text-sm font-medium text-gray-500" th:text="${title}">Metrics Title</dt>
            <a th:if="${link != null}" th:href="${link}" th:attr="hx-get=${hxGet}" hx-trigger="load" hx-swap="innerHTML"
                class="mt-1 text-3xl font-semibold tracking-tight text-blue-600 hover:cursor-pointer hover:no-underline"><img
                    src="/fidget-spinner-64x64-ani.gif"></img></a>
            <dd th:if="${link == null}" th:attr="hx-get=${hxGet}" hx-trigger="load" hx-swap="innerHTML"
                class="text-3xl font-semibold tracking-tight text-gray-900"><img src="/fidget-spinner-64x64-ani.gif"
                    class="w-10"></img></dd>
        </div>
    </div>
</body>

</html>