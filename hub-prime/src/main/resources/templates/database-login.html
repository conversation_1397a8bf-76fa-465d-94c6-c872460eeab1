<!DOCTYPE html>
<html class="h-full bg-gray-100" lang="en" xmlns:th="http://www.thymeleaf.org">


<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,container-queries"></script>
    <script>
        localStorage.clear();
        sessionStorage.clear();
    </script>

    <!-- <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
        }

        input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 0.75rem;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        button:hover {
            background-color: #0056b3;
        }

        .error-message {
            color: #dc3545;
            margin-bottom: 1rem;
        }

        .oauth-links {
            margin-top: 1rem;
            text-align: center;
        }

        .oauth-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 0.5rem;
        }
    </style> -->
</head>

<body class="h-full">
    <div class="min-h-full flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="py-10 px-10 max-w-lg w-full space-y-8 border-solid border-slate-300 bg-slate-50 border rounded-md">
            <div>
                <img class="mx-auto h-32 w-auto" src="/diabetes-research-hub.svg" alt="Diabetes Research Hub">
                <h2 class="text-center text-3xl font-bold text-gray-900">Sign in to <br />Diabetes Research Hub</h2>
            </div>
            <div class="login-container">
                <div th:if="${param.error}" class="error-message">
                    Invalid username or password.
                </div>

                <form th:action="@{/superadmin/login}" method="post" class="text-center mt-4 space-y-6">
                    <div  class="grid grid-cols-4  mb-4 form-group">
                        <label for="username" class="text-sm font-normal text-gray-900  py-2">
                            <div class="flex flex-row">
                                Username <span class="text-red-500">*</span></div></label>
                                <div class="col-span-3">
                        <input type="text" id="username" name="username" required autofocus class="block w-full rounded px-1.5 py-2 text-sm font-normal text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        </div>
                    </div>

                    <div  class="grid grid-cols-4  mb-4 form-group">
                        <label for="password" class="text-sm font-normal text-gray-900  py-2">
                            <div class="flex flex-row">Password<span class="text-red-500">*</span>
                            </div></label>
                            <div class="col-span-3">
                        <input type="password" id="password" name="password" required   class="block w-full rounded px-1.5 py-2 text-sm font-normal text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            </div>
                    </div>

                    <div class="py-5">
                        <button type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-blue-600 text-m font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-indigo-500">
                            Login
                        </button>
                        </p>
                    </div>
                </form>
        </div>
    </div>
</body>

</html>