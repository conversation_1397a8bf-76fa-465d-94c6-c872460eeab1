body, html {
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    }

.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
/*.table-files .ag-center-cols-container {width:100%!important; max-width: 100%!important;}*/
/* --- media query starts -- */ 
.active-filter {color:#2563eb!important; background: #dbeafe!important;}
.input-mandatory {color:#a91919!important; border: 1px solid #a91919!important;}

.text-xsm {
    font-size: 13px!important;
    line-height: 15px!important;
}

/* ----------------- */
@media (max-width: 1199px) {
#allmetrics .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
} 
}

/* styles.css */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 2s linear infinite;
    display: none;
    /* Initially hidden */
}

.loaderBottom {
    border: 4px solid #ffffff;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 2s linear infinite;
    display: none;
    /* Initially hidden */
}

.loader-small {
    width: 20px;
    height: 20px;
    border-width: 3px;
}

.loader-big {
    width: 40px;
    height: 40px;
    border-width: 3px;
}

div#serverDataGrid a {
    color: rgb(59 130 246) !important;
}
td:last-child {
    border: none;
}
thead tr {
    text-align: left;
}

.toast {
    display: inline-block;
    min-width: 200px;
    text-align: center;
    position: relative;
    animation: slideIn 0.5s ease;
  }
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* .tooltip-btn:hover + .tooltip-txt {
    display: block;
    } */

