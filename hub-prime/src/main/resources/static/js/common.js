let tooltipTexts = {};
let userPrivilegeInfoArray = [];
let menuListArray = [];
document.addEventListener("DOMContentLoaded", function () {
  checkUserExistence();
  getUserName();
  setupLogoutButton();
  loadTooltipTexts();
  const pageTabs = document.getElementById("pageTabs");
  pageTabs?.querySelectorAll("li").forEach((listitem) => {
    listitem.className = "mr-2 hidden";
    const anchor = listitem.querySelector("a");
    const textContent = anchor?.textContent.trim();
    listitem.setAttribute("data-privilege-tabs", textContent);
  });
  hideUnauthorizedTabs();
});

function setupLogoutButton() {
  document
    .getElementById("logout-button")
    ?.addEventListener("click", function () {
      const keysToRemove = [
        "userDetails",
        "practitionerPartyId",
        "organizationPartyId",
        "profileStatus",
        "userRoles",
        "userRole",
        "userPrivilegeInfoArray",
        "menuListArray",
        "tenant_id",
        "emailVerified",
      ];
      keysToRemove.forEach((key) => localStorage.removeItem(key));
      window.location.href = "/logout";
    });
}

function checkUserExistence() {
  fetch(`/practitioners/existence`)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok " + response.statusText);
      }
      return response.json();
    })
    .then((response) => {
      if (response.error) {
        clearUserDetails();
      } else {
        handleUserDetails(response.data.userDetails);
        let parsedData = response.data.userDetails || "{}";
        if (Object.keys(parsedData).length > 0) {
          let privilegeList = JSON.parse(
            localStorage.getItem("userPrivilegeInfoArray") || "[]"
          );
          if (privilegeList.length == 0) {
            getUserPrivileges()
              .then((data) => { })
              .catch((error) => {
                console.error("Error fetching data:", error);
              });
          } else {
            hideUnauthorizedMenus();
            hideUnauthorizedActions();
            disableUnauthorizedButtons();
          }
        } else {
          localStorage.setItem("userRole", "guest");
          let guestPrivilegeList = JSON.parse(
            localStorage.getItem("guestPrivilegeInfoArray") || "[]"
          );
          if (guestPrivilegeList.length == 0) {
            getGuestPrivileges()
              .then((data) => { })
              .catch((error) => {
                console.error("Error fetching data:", error);
              });
          } else {
            hideUnauthorizedMenus();
            hideUnauthorizedActions();
            disableUnauthorizedButtons();
          }
        }
      }
    })
    .catch((error) => {
      console.error("There was a problem with the fetch operation:", error);
    });
}

function clearUserDetails() {
  [
    "userDetails",
    "practitionerPartyId",
    "organizationPartyId",
    "profileStatus",
    "userRoles",
    "userRole",
    "userPrivilegeInfoArray",
    "menuListArray",
    "emailVerified",
  ].forEach((key) => localStorage.removeItem(key));
}

function handleUserDetails(userDetails) {
  let parsedData = userDetails || "{}";
  if (Object.keys(parsedData).length > 0) {
    let {
      organization_party_id,
      practitioner_party_id,
      profile_status,
      user_account_primary_email,
      auth_provider,
      user_roles,
      tenant_id
    } = parsedData;
    if (practitioner_party_id || organization_party_id) {
      localStorage.setItem("userDetails", JSON.stringify(userDetails));
      localStorage.setItem("practitionerPartyId", practitioner_party_id);
      localStorage.setItem("organizationPartyId", organization_party_id);
      localStorage.setItem("profileStatus", profile_status);
      localStorage.setItem("tenant_id", tenant_id);
      if (user_roles && user_roles.length > 0) {
        localStorage.setItem("userRoles", JSON.stringify(user_roles));
        localStorage.setItem("userRole", user_roles[0]?.role_name);
      }

      if (profile_status !== "COMPLETE") {
        document
          .getElementById("profile-link-ctr")
          ?.classList?.remove("hidden");
      }
      loadUserProfile();
    }
  } else {
    clearUserDetails();
  }
}

function getLoggedInUser() {
  let loggedInUserId = localStorage.getItem("practitionerPartyId") || "";
  return loggedInUserId;
}
function loadUserProfile() {
  let userDetails = localStorage.getItem("userDetails")
    ? JSON.parse(localStorage.getItem("userDetails"))
    : {};
  if (userDetails?.practitioner_name && document.getElementById("userName")) {
    document.getElementById("userName").value = userDetails.practitioner_name;
  }
  if (
    userDetails?.user_account_primary_email &&
    document.getElementById("userEmail")
  ) {
    document.getElementById("userEmail").value =
      userDetails.user_account_primary_email;
  }
  if (userDetails?.orcidID && document.getElementById("orcidID")) {
    document.getElementById("orcidID").value = userDetails.orcidID;
  }
  if (
    userDetails?.organization_party_id &&
    document.getElementById("userInstitution")
  ) {
    document.getElementById("userInstitution").value =
      userDetails.organization_party_id;
  }
}
function isLoggedIn() {
  const scriptTag = document.querySelector(
    'script[id="authenticate-check-js"]'
  );
  return scriptTag
    ? scriptTag.getAttribute("data-authenticate-status") === "true"
    : false;
}

function isProfileCompleted() {
  return localStorage.getItem("profileStatus") === "COMPLETE";
}

function isEmailVerified() {
  return localStorage.getItem("emailVerified") === "TRUE";
}


async function getStudyArchiveStatus(studyId) {
  return new Promise((resolve, reject) => {
    fetchRawData(
      "/research-study/archive-status?studyId=" + studyId,
      (res, error) => {
        if (error) {
          reject(error);
        } else {
          try {
            const data = res?.data?.archiveStatus;
            resolve(data);
          } catch (error) {
            reject(error);
          }
        }
      }
    );
  });
}

async function getStudyOwner(studyId) {
  return new Promise((resolve, reject) => {
    fetchRawData(
      "/research-study/study-owner?studyId=" + studyId,
      (res, error) => {
        if (error) {
          reject(error);
        } else {
          try {
            const data = res?.data?.studyOwner;
            resolve(data);
          } catch (error) {
            reject(error);
          }
        }
      }
    );
  });
}

async function getList(endpoint) {
  return new Promise((resolve, reject) => {
    fetchRawData(endpoint, (res, error) => {
      if (error) {
        reject(error);
      } else {
        try {
          //const data = JSON.parse(res.data[Object.keys(res.data)[0]]);
          const data = res.data[Object.keys(res.data)[0]];
          resolve(data);
        } catch (error) {
          reject(error);
        }
      }
    });
  });
}

async function getGenderList() {
  return getList(`/gender-type`);
}

async function getEthnicityList() {
  return getList(`/ethnicity-type`);
}

async function getRaceList() {
  return getList(`/race-type`);
}

function createOptions(eleId, options) {
  const selectBox = document.getElementById(eleId);
  selectBox.innerHTML = "";
  const defaultOption = document.createElement("option");
  defaultOption.textContent = "Select";
  defaultOption.value = "";
  selectBox.appendChild(defaultOption);

  options.forEach((member) => {
    const option = document.createElement("option");
    if (
      eleId == "gender" ||
      eleId == "gender_type_id" ||
      eleId == "gender_type_idDropdown"
    ) {
      option.textContent = member.value;
      option.value = member.gender_type_id;
    } else if (
      eleId == "race" ||
      eleId == "race_type_id" ||
      eleId == "race_type_idDropdown"
    ) {
      option.textContent = member.display;
      option.value = member.race_type_id;
    } else if (
      eleId == "ethnicity" ||
      eleId == "ethnicity_type_id" ||
      eleId == "ethnicity_type_idDropdown"
    ) {
      option.textContent = member.display;
      option.value = member.ethnicity_type_id;
    }
    selectBox.appendChild(option);
  });
}

function showLoading(loaderId) {
  const loader = document.getElementById(loaderId);
  if (loader) loader.style.display = "block";
}

function hideLoading(loaderId) {
  const loader = document.getElementById(loaderId);
  if (loader) loader.style.display = "none";
}

function clearGrid() {
  document.querySelector(".ag-body").innerHTML =
    '<div class="text-3xl text-[gray] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">No data available</div>';
}

function toggleTooltip(elementId, condition) {
  const element = document.getElementById(elementId);
  if (element) {
    const tooltipName = element.getAttribute("data-tooltip-target");
    const tooltipElement = document.getElementById(tooltipName);
    if (condition) {
      if (element) {
        element.disabled = true;
        tooltipElement?.classList.remove("hidden");
      }
    } else {
      if (element) {
        element.disabled = false;
        tooltipElement?.classList.add("hidden");
      }
    }
  }
}

async function loadTooltipTexts() {
  const response = await fetch("/tooltiptexts.json");
  tooltipTexts = await response.json();
  initializeTooltips();
}

function initializeTooltips() {
  const tooltipElements = document.querySelectorAll("[data-tooltip-target]");

  tooltipElements.forEach((element) => {
    const tooltipId = element.getAttribute("data-tooltip-target");
    const tooltip = document.getElementById(tooltipId);
    if (tooltip) {
      const tooltipText = tooltipTexts[tooltipId];
      if (tooltipText) {
        tooltip.querySelector("span").textContent = tooltipText;
      }

      element.addEventListener("mouseenter", () => {
        tooltip.classList.remove("invisible", "opacity-0");
        tooltip.classList.add("visible", "opacity-100");
      });

      element.addEventListener("mouseleave", () => {
        tooltip.classList.remove("visible", "opacity-100");
        tooltip.classList.add("invisible", "opacity-0");
      });
    }
  });
}

async function getUserPrivileges() {
  return new Promise((resolve, reject) => {
    let practitionerPartyId = localStorage.getItem("practitionerPartyId") || "";
    fetchRawData(
      `/permissions/by-roles?userPartyId=${practitionerPartyId}`,
      (res, error) => {
        if (error) {
          reject(error);
        } else {
          try {
            const data = res.data.response;
            for (const [category, permissions] of Object.entries(
              data.userPermissions
            )) {
              menuListArray.push(category);
              permissions.forEach((permission) => {
                userPrivilegeInfoArray.push(permission);
              });
            }
            ["guestPrivilegeInfoArray", "menuGuestListArray"].forEach((key) =>
              localStorage.removeItem(key)
            );
            localStorage.setItem(
              "userPrivilegeInfoArray",
              JSON.stringify(userPrivilegeInfoArray)
            );
            localStorage.setItem(
              "menuListArray",
              JSON.stringify(menuListArray)
            );
            hideUnauthorizedMenus();
            hideUnauthorizedActions();
            resolve(data);
          } catch (error) {
            reject(error);
          }
        }
      }
    );
  });
}
async function getGuestPrivileges() {
  return new Promise((resolve, reject) => {
    fetchRawData(`/permissions/guest-role`, (res, error) => {
      if (error) {
        reject(error);
      } else {
        try {
          const data = res.data.permissions;
          const guestPrivilegeInfoArray = [];
          const guestMenuListArray = [];
          for (const [category, permissions] of Object.entries(data)) {
            guestMenuListArray.push(category);
            permissions.forEach((permission) => {
              guestPrivilegeInfoArray.push(permission);
            });
          }
          ["userPrivilegeInfoArray", "menuListArray"].forEach((key) =>
            localStorage.removeItem(key)
          );
          localStorage.setItem(
            "guestPrivilegeInfoArray",
            JSON.stringify(guestPrivilegeInfoArray)
          );
          localStorage.setItem(
            "menuGuestListArray",
            JSON.stringify(guestMenuListArray)
          );
          hideUnauthorizedMenus();
          hideUnauthorizedActions();
          hideUnauthorizedTabs();
          resolve(data);
        } catch (error) {
          reject(error);
        }
      }
    });
  });
}
function hideUnauthorizedMenus() {
  let menuListArray = [];
  let userRole = localStorage.getItem("userRole") || "";
  if (userRole == "guest") {
    menuListArray = JSON.parse(
      localStorage.getItem("menuGuestListArray") || "[]"
    );
  } else {
    menuListArray = JSON.parse(localStorage.getItem("menuListArray") || "[]");
  }
  document.querySelectorAll("[data-privilege]").forEach((item) => {
    const requiredPrivilege = item.getAttribute("data-privilege").toUpperCase();
    if (menuListArray.includes(requiredPrivilege)) {
      item.classList.remove("hidden");
    }
  });
}
function hideUnauthorizedActions() {
  let privilegeListArray = [];
  let userRole = localStorage.getItem("userRole") || "";
  if (userRole == "guest") {
    privilegeListArray = JSON.parse(
      localStorage.getItem("guestPrivilegeInfoArray") || "[]"
    );
  } else {
    privilegeListArray = JSON.parse(
      localStorage.getItem("userPrivilegeInfoArray") || "[]"
    );
  }
  document
    .querySelectorAll("[privilege-action-buttons-links]")
    .forEach((item) => {
      const requiredPrivilege = item.getAttribute(
        "privilege-action-buttons-links"
      );
      if (privilegeListArray.includes(requiredPrivilege)) {
        item.classList.remove("hidden");
      }
    });
}

function disableUnauthorizedButtons() {
  let privilegeListArray = [];
  let userRole = localStorage.getItem("userRole") || "";
  if (userRole == "guest") {
    privilegeListArray = JSON.parse(
      localStorage.getItem("guestPrivilegeInfoArray") || "[]"
    );
  } else {
    privilegeListArray = JSON.parse(
      localStorage.getItem("userPrivilegeInfoArray") || "[]"
    );
  }
  document.querySelectorAll("[data-privilege-buttons]").forEach((item) => {
    const requiredPrivilege = item.getAttribute("data-privilege-buttons");
    if (!privilegeListArray.includes(requiredPrivilege)) {
      item.disabled = true;
    }
  });
}
function hideUnauthorizedTabs() {
  let privilegeListArray = [];
  let userRole = localStorage.getItem("userRole") || "";
  if (userRole == "guest") {
    privilegeListArray = JSON.parse(
      localStorage.getItem("guestPrivilegeInfoArray") || "[]"
    );
  } else {
    privilegeListArray =
      JSON.parse(localStorage.getItem("userPrivilegeInfoArray")) || "[]";
  }
  document.querySelectorAll("[data-privilege-tabs]").forEach((item) => {
    const requiredPrivilege = item.getAttribute("data-privilege-tabs");
    if (privilegeListArray.includes(requiredPrivilege)) {
      item.classList.remove("hidden");
    }
  });
}

async function pubmedIdExists() {
  const pubmedId = document.getElementById("publicationPubmed").value || null;
  const publicationDoi =
    document.getElementById("publicationDoi").value || null;
  const publicationId = document.getElementById("publicationId").value || null;
  return new Promise((resolve, reject) => {
    fetchData(
      `/research-study/citations/exist?pubmedId=${pubmedId}&studyId=${studyId}&publicationDoi=${publicationDoi}&citationId=${publicationId}`,
      (data, error) => {
        if (!error) {
          const result = data?.data?.existCitation || false;
          resolve(result);
        } else {
          reject(error);
        }
      }
    );
  });
}
function convertToUTCFormat(dateInput) {
  const inputDateValue = new Date(dateInput);
  const utcDate = new Date(
    Date.UTC(
      inputDateValue.getFullYear(),
      inputDateValue.getMonth(),
      inputDateValue.getDate()
    )
  );
  return utcDate;
}

function getUserName() {
  fetch(`/user-name`)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok " + response.statusText);
      }
      return response.json();
    })
    .then((response) => {
      if (response.data) {
        console.log("User Name:", response.data.userName);

        document.querySelectorAll(".userFullName").forEach((element) => {
          if (element) {
            element.textContent = response.data.userName;
          }
        }
        );
      }
    })
    .catch((error) => {
      console.error("There was a problem with the fetch operation:", error);
    });
}
