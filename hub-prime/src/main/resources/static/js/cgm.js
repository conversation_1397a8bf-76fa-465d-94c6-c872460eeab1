let devicenames = [];
document.addEventListener("DOMContentLoaded", async function () {
  let cgmfile = "";
  let lastUploadedFile = "";
  localStorage.removeItem("cgmData");
  const params = new URLSearchParams(window.location.search);
  const tab = params.get("tab");
  const selectElementSource = document.getElementById(
    "sourcePlatform",
  );

  let dataSources = [];
  try {

    await fetchData(
      `/cgm/source-platform`,
      async (dataJson, error) => {
        console.log("responseData", dataJson);
        if (!error) {
          if (dataJson.status == "success") {
            dataSources = dataJson.data.sourcePlatforms;
            console.log("Data sources:", dataSources);
            dataSources.forEach((source) => {
              const option = document.createElement("option");
              option.value = source;
              option.text = source;
              selectElementSource.appendChild(option);
            });
            console.log("Data sources:", dataSources);
          } else {
            console.error("Failed to fetch data sources");
            return;
          }
        }
        else {
          console.error("Error fetching participant info:", error);
        }
      }
    );

    // Fetch participant data
    const participantId = document.getElementById("participantId").value;
    const responseP = await fetch(
      `/participant/${participantId}`,
    );

    if (responseP.ok) {
      const dataJsonP = await responseP.json();
      if (dataJsonP.status === "success") {
        const participant_display_id =
          dataJsonP.data.participantData.participant_display_id;
        document.getElementById("patientID").value =
          participant_display_id;
      } else {
        console.error("Failed to fetch participant data");
      }
    } else {
      console.error("Failed to fetch participant data");
    }
  } catch (error) {
    console.error("Error:", error);
  }
  document.getElementById("sourcePlatform").addEventListener(
    "change",
    async function (e) {
      e.preventDefault();
      const sourcePlatform = e.target.value;
      console.log("sourcePlatform", sourcePlatform);
      if (sourcePlatform === "Select") {
        return;
      }
      else {
        try {
          await fetchData(
            `/cgm/cgm-devices?manufacturer=${sourcePlatform}`,
            async (dataJson, error) => {
              console.log("responseData", dataJson);
              if (!error) {
                if (dataJson.status == "success") {
                  devicenames = dataJson.data.cgmDevices;
                  console.log("Data sources:", devicenames);
                  let selectElementDevice = document.getElementById(
                    "deviceName",
                  );
                  selectElementDevice.innerHTML = ""; // Clear any existing options
                  const defaultoption = document.createElement("option");
                  defaultoption.value = "";
                  defaultoption.text = "Select";
                  selectElementDevice.appendChild(defaultoption);

                  devicenames.forEach((device) => {
                    const option = document.createElement("option");
                    option.value = device.id;
                    option.text = device.device_name;
                    selectElementDevice.appendChild(option);
                  });
                  console.log("Data sources:", devicenames);
                } else {
                  console.error("Failed to fetch data sources");
                  return;
                }
              }
              else {
                console.error("Error fetching participant info:", error);
              }
            }
          );
        } catch (error) {
          console.error("Error:", error);
        }
      }
    },
  );


  ["patientID", "sourcePlatform", "deviceName", "cgmDateMapping", "cgmValueMapping"
  ].forEach(id => {
    document.getElementById(id)?.addEventListener("blur", async function () {
      let element = document.getElementById(id);
      if (document.getElementById(id)?.value != "") {
        element.classList.remove("border-[#e23636]");
      }
    });
  });


  document.getElementById("uploadCgmDataForm").addEventListener(
    "submit",
    async function (e) {
      e.preventDefault();
      showLoading('submitCgmLoader');
      let isValid = true;
      let fieldsToValidate = [];
      fieldsToValidate = [
        "patientID", "deviceName", "sourcePlatform", "cgmDateMapping", "cgmValueMapping"
      ];
      // Validate all fields
      fieldsToValidate.forEach((fieldId) => {
        const isFieldValid = validateField(fieldId);
        if (!isFieldValid) {
          isValid = false;
        }
      });
      const fileInput = document.getElementById("CgmUpload");
      const fileBorder = document.getElementById("cgmUploadBorder");
      let errorClass = "border-[#e23636]";
      if (!cgmfile || cgmfile == "") {
        isValid = false;
        fileBorder.classList.add(errorClass);
      }
      else {
        fileBorder.classList.remove(errorClass);
      }
      if (isValid) {
        if (!localStorage.getItem("cgmData")) {
          showToast(
            "Please upload CGM tracing before submitting",
            "error",
          );
          hideLoading('submitCgmLoader');
          return;
        } else {
          console.log("cgmData", localStorage.getItem("cgmData"));
          try {
            const cgmData = JSON.parse(localStorage.getItem("cgmData"));
            console.log("cgmData", cgmData);
            const response = await fetch("/study-participant/cgm/data", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                fileName: cgmData.fileName,
                cgmRawDataJson: cgmData.cgmRawDataJson,
                uploadTimestamp: cgmData.uploadTimestamp,
                fileSize: cgmData.fileSize,
                status: "Pending",
                fileMetadata: {
                  deviceId: document.getElementById("deviceName")
                    .value,
                  sourcePlatform: selectElementSource.value,
                  fileName: cgmData.fileName,
                  fileFormat: cgmData.fileFormat,
                  fileUploadDate: cgmData.uploadTimestamp,
                  mapFieldOfCgmDate:
                    document.getElementById("cgmDateMapping")
                      .value,
                  mapFieldOfCgmValue:
                    document.getElementById("cgmValueMapping")
                      .value,
                  interactionHierarchy: cgmData.interactionHierarchy,
                  lastInteractionId: cgmData.lastInteractionId,
                },
                fileType: cgmData.fileType,
                studyId: document.getElementById("studyId")
                  .value,
                orgPartyId: localStorage.getItem(
                  "organizationPartyId",
                ),
                cgmRawDataByteArray: cgmData.cgmRawDataByteArray,
                participantId:
                  document.getElementById("participantId")
                    .value,
                cgmDataXml: cgmData.cgmDataXml,
                fileFormat: cgmData.fileFormat,
                fileUrl: cgmData.fileUrl,
              }),
            });

            const dataJson = await response.json();

            if (response.ok && dataJson.status === "success") {
              showToast("CGM data saved successfully!", "success");
              lastUploadedFile = "";
              localStorage.removeItem("cgmData");
              const studyId =
                document.getElementById("studyId").value;
              const participantId =
                document.getElementById("participantId").value;
              setTimeout(() => {
                window.location.href =
                  `/participants/cgmdata/${studyId}/${participantId}?tab=${tab}`;
              }, 2000);
            } else {
              showToast("Failed to save CGM data", "error");
            }
          } catch (error) {
            console.error("Error:", error);
            showToast(
              "An error occurred while saving CGM data",
              "error",
            );
          }
          hideLoading('submitCgmLoader');
        }
      }
      hideLoading('submitCgmLoader');
    },
  );

  const dropZone = document.getElementById("drop-zone");

  // Handle drag over
  dropZone.addEventListener("dragover", (e) => {
    e.preventDefault();
    dropZone.classList.add("drag-over");
  });

  // Handle drag leave
  dropZone.addEventListener("dragleave", () => {
    dropZone.classList.remove("drag-over");
  });

  // Handle file drop
  dropZone.addEventListener("drop", async (e) => {
    console.log("droppp")
    e.preventDefault();
    dropZone.classList.remove("drag-over");
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      cgmfile = files[0];
      let fileContent = await handleFileDrop('CgmUpload', files[0]);
      console.log("fileContent111", fileContent);
      cgmfile = fileContent;
      handleCgmFileData(cgmfile)
    }
  });

  document.getElementById("CgmUpload").addEventListener("change", async function () {
    cgmfile = document.getElementById("CgmUpload").files[0];
    if (cgmfile) {
      lastUploadedFile = cgmfile;
    }
    let fileContent = await handleFileUpload('CgmUpload', lastUploadedFile);
    cgmfile = fileContent;
    handleCgmFileData(cgmfile)
  })

  document.addEventListener("fileRemoved", function (event) {
    const { inputName } = event.detail;

    if (inputName === "CgmUpload") {
      cgmfile = ""; // Reset the dbfile variable
      lastUploadedFile = ""; // Reset the last uploaded file
      console.log("File removed for input:", inputName);
    }
  });

});

async function handleCgmFileData(cgmDataFile) {
  if (cgmDataFile && cgmDataFile != '') {
    let element = document.getElementById("cgm-upload-status");
    element.classList.remove("text-red-500", "text-green-500");
    element.textContent = "";
    let participantId = document.getElementById("participantId").value;
    try {
      console.log("changed file");
      localStorage.removeItem("cgmData");

      showCgmUploadLoader();
      // Create a FormData object
      const formData = new FormData();
      formData.append(
        "file",
        cgmDataFile
      );
      formData.append("studyId", studyId);
      formData.append("participantId", participantId);
      formData.append(
        "organizationPartyId",
        localStorage.getItem("organizationPartyId"),
      );
      let url = "/study-participant/cgm/file";
      // Send the request
      const response = await fetch(url, {
        method: "POST",
        body: formData, // Pass FormData directly
      });
      // Parse response as JSON
      const data = await response.json();
      document.getElementById("cgmUploadBorder").classList.remove("border-[#e23636]");
      if (data.status === "error") {
        element.classList.add("text-red-500");
        element.textContent = data.message;
      } else {
        localStorage.setItem("cgmData", JSON.stringify(data.data));
        const selectElementDate = document.getElementById(
          "cgmDateMapping",
        );
        const selectElementValue = document.getElementById(
          "cgmValueMapping",
        );
        selectElementDate.innerHTML = ""; // Clear any existing options
        selectElementValue.innerHTML = ""; // Clear any existing options
        const defOption1 = document.createElement("option");
        defOption1.value = "";
        defOption1.text = "Select";
        selectElementDate.appendChild(defOption1);
        const defOption2 = document.createElement("option");
        defOption2.value = "";
        defOption2.text = "Select";
        selectElementValue.appendChild(defOption2);
        data.data.fleHeaders.forEach((header) => {
          const optionDate = document.createElement("option");
          optionDate.value = header;
          optionDate.text = header;
          selectElementDate.appendChild(optionDate);

          const optionValue = document.createElement("option");
          optionValue.value = header;
          optionValue.text = header;
          selectElementValue.appendChild(optionValue);
        });
        element.classList.add("text-green-500");
        element.textContent = "CGM Tracing File uploaded successfully!";
      }
      console.log("upload data", data);
      hideCgmUploadLoader();
    } catch (error) {
      hideCgmUploadLoader();
      console.error("Error:", error);
    }
  }
}

function showCgmUploadLoader() {
  document.getElementById("uploadCgmLoader").classList.remove(
    "hidden",
  );
}

function hideCgmUploadLoader() {
  document.getElementById("uploadCgmLoader").classList.add("hidden");
}
// Function to initialize/reinitialize TomSelect
function initDeviceSelect() {

  // Initialize or reinitialize TomSelect
  devicenames = new TomSelect("#deviceName", {
    plugins: ["remove_button"],
    create: false,
    maxItems: 1,
    valueField: 'practitioner_party_id',
    labelField: 'investigator_name',
    searchField: 'investigator_name',
    options: defaultInvestigators,
    createFilter: function (input) { return input.length >= parseInt(2, 10); },
    onInitialize: function () {
      //the onInitialize callback is invoked once the control is completely initialized.
      this.setValue(devicenames);
    },
    onChange: function (value) {
      handleDeviceSelection(value)
    },
    onItemRemove: function (value, $item) {

    }
  });
}

function handleDeviceSelection(device_name) {
  console.log("device_name", device_name);
  document.getElementById("deviceName").value = device_name;
}