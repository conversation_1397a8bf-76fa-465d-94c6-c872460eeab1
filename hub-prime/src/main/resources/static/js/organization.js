let newOrganizations = [];
document.addEventListener("DOMContentLoaded", function () {
  if (document.getElementById('tenantId')) {
    const selectControl = initInstitution('tenantId');
  }
});
function initInstitution(fieldString) {
  console.log("init ins")
  return new TomSelect("#" + fieldString, {
    valueField: "organization_party_id",
    labelField: "organization_name",
    searchField: "organization_name",
    options: [],
    create: false,
    maxItems: 1,
    maxOptions: 10,
    // minimum query length
    shouldLoad: function (query) {
      return query.length > 1;
    },
    // fetch remote data
    load: function (query, callback) {
      var url = "/organizations/search?request=" + encodeURIComponent(query);
      fetch(url)
        .then((response) => response.json())
        .then((json) => {
          const organizations = json.data.organizationDetails;
          callback(organizations);
        })
        .catch(() => {
          callback();
        });
    },
  });
}
function initNewInstitution() {
  console.log("init ins")
  return new TomSelect("#userNewInstitution", {
    valueField: "name",
    labelField: "name",
    searchField: ["name", "alias"],
    options: [],
    create: false,
    maxItems: 1,
    maxOptions: 10,
    // minimum query length
    shouldLoad: function (query) {
      return query.length > 1;
    },
    // fetch remote data
    load: function (query, callback) {
      var url = "/ror/institution/search?query=" + encodeURIComponent(query);
      fetch(url)
        .then((response) => response.json())
        .then((json) => {
          newOrganizations = [...newOrganizations, ...json];
          callback(json);
        })
        .catch(() => {
          callback();
        });
    },
    onItemAdd: function (value) {
      console.log("value", value);
      console.log(newOrganizations);
      let selectedOrgArray = newOrganizations.filter((org) => org.name == value);

      if (selectedOrgArray.length > 0) {
        showOrgLoader();
        saveOrganization(selectedOrgArray);
      }
      //this.setValue(value)

    },
  });
}
function addOrganization(organizationName) {
  return new Promise((resolve, reject) => {
    fetch(`/ror/institution/search?query=` + organizationName)
      .then((response) => {
        if (!response.ok) {
          // If the response status is not OK, reject the promise
          reject(new Error("Failed to fetch organization details"));
        }
        return response.json();
      })
      .then((json) => {
        console.log(json);
        resolve(json); // Resolve the promise with the JSON response
      })
      .catch((error) => {
        console.error("Error fetching organization details:", error);
        reject(error); // Reject the promise with the error
      });
  });
}

function saveOrganization(orgDetailsArray) {
  console.log(orgDetailsArray);
  if (orgDetailsArray.length > 0) {
    let orgDetails = orgDetailsArray[0];

    const organizationName = orgDetails?.name;
    const organizationAlias = orgDetails?.alias;
    const organizationIdentifierSystemValue = orgDetails?.identifier_system_value;
    const organizationTypeCode = orgDetails?.type_code;
    const organizationTypeDisplay = orgDetails?.type_display;
    const organizationCity = orgDetails?.city;
    const organizationState = orgDetails?.state;
    const organizationLocation = orgDetails?.locations;
    const organizationCountry = orgDetails?.country;
    const organizationWebsiteUrl = orgDetails?.website_url;

    let isValid = true;
    if (isValid) {

      const organizationParams = {
        "name": organizationName,
        "alias": organizationAlias,
        "identifierSystemValue": organizationIdentifierSystemValue,
        "typeCode": organizationTypeCode,
        "typeDisplay": organizationTypeDisplay,
        "city": organizationCity,
        "state": organizationState,
        "country": organizationCountry,
        "websiteUrl": organizationWebsiteUrl,
        "createdBy": localStorage.getItem("practitionerPartyId"),
        "latitude": organizationLocation.lat,
        "longitude": organizationLocation.lng
      };

      console.log(organizationParams);
      postData(`/organization`, organizationParams, (res) => {
        console.log(res);
        if (res && res.status == "success") {
          console.log(res.data.organizationDetails)
          let organizationDetails = res.data.organizationDetails;
          let parsedData = organizationDetails || {};
          console.log(parsedData);
          let organizationPartyId = parsedData.organization_party_id;
          if (parsedData.organization_id && parsedData.organization_id != "" && parsedData.organization_party_id) {
            localStorage.setItem("organizationId", parsedData.organization_id);
            localStorage.setItem("organizationPartyId", organizationPartyId);
            localStorage.setItem("savedOrganizationPartyId", organizationPartyId);
          }
          else if (parsedData.organizationPartyId && parsedData.organizationPartyId != "") {
            localStorage.setItem("organizationPartyId", parsedData.organizationPartyId);
            localStorage.setItem("savedOrganizationPartyId", parsedData.organizationPartyId);
          }
          else {
            showToast(res.message, "error");
          }
          console.log("successss")
        } else {
          console.log("errorr")
        }
        hideOrgLoader();

      });
      // window.location = "/home";
    }
  }

  //window.location = "/home";

}

function handleUserInstitutionChange(value) {
  console.log(value);
  if (value) {
    console.log("value", value)
  }
}