import {
    AGGridAide,
    AGGridAideBuilder,
} from "@presentation/shell/aggrid-aide.js";
import ModalAide from "@presentation/shell/modal-aide.js";

let mealsfitnessfile = "";
let uploadCompleted = false;
let lastUploadedFile = "";
let fileType = "Meals";
//const viewName = 'uniform_resource_study';
document.addEventListener("DOMContentLoaded", async function () {

    const scriptTag = document.querySelector('script[src="/js/mealsfitness.js"]');
    if (scriptTag) {
        fileType = scriptTag.getAttribute('data-fileType') || "Meals";
        studyId = scriptTag.getAttribute('data-studyid');
    }

    setupDragAndDrop();
    setupEventListeners();

    document.getElementById("patientID").value = participantDisplayId;
    if (fileType == "Meals") {
        initMealsGrid();
    }
    else {
        initFitnessGrid();
    }
});
function setupDragAndDrop() {
    const dropZone = document.getElementById("drop-zone");

    dropZone.addEventListener("dragover", (e) => {
        e.preventDefault();
        dropZone.classList.add("drag-over");
    });

    dropZone.addEventListener("dragleave", () => {
        dropZone.classList.remove("drag-over");
    });

    dropZone.addEventListener("drop", async (e) => {
        e.preventDefault();
        dropZone.classList.remove("drag-over");
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            mealsfitnessfile = files[0];
            let fileContent = await handleFileDrop('uploadMealsFitnessData', files[0]);
            console.log("fileContent", fileContent);
            mealsfitnessfile = fileContent;
        }
    });
}

function setupEventListeners() {
    document.getElementById("uploadMealsFitnessData").addEventListener("change", async function () {
        mealsfitnessfile = document.getElementById("uploadMealsFitnessData").files[0];
        if (mealsfitnessfile) {
            lastUploadedFile = mealsfitnessfile;
        }
        let fileContent = await handleFileUpload('uploadMealsFitnessData', lastUploadedFile);
        mealsfitnessfile = fileContent;
    });

    document.getElementById("upload-files").addEventListener("click", async function (e) {
        e.preventDefault();
        if (!uploadCompleted) {
            mealsfitnessfile = await getUploadedFile("uploadMealsFitnessData");
        }
        if (mealsfitnessfile && mealsfitnessfile !== "") {
            await submitFile(mealsfitnessfile);
        } else {
            showToast("Please upload a " + fileType.toLowerCase() + " file", "error");
        }
    });

    document.addEventListener("fileRemoved", function (event) {
        const { inputName } = event.detail;

        if (inputName === "uploadMealsFitnessData") {
            mealsfitnessfile = ""; // Reset the mealsfitnessfile variable
            lastUploadedFile = ""; // Reset the last uploaded file
            console.log("File removed for input:", inputName);
        }
    });

}


async function submitFile(participantmealsfitnessfile) {
    let studyId = document.getElementById("studyId").value;
    showparticipantMealFitnessLoader();

    try {
        const formData = new FormData();
        formData.append("file", participantmealsfitnessfile);
        formData.append("studyId", studyId);
        formData.append("participantId", participantId);
        formData.append("fileType", fileType);
        formData.append("organizationPartyId", localStorage.getItem("organizationPartyId"));
        let url = "/study-participant/meals-fitness/file";

        const response = await fetch(url, {
            method: 'POST',
            body: formData,
        });

        const data = await response.json();

        console.log(data)
        hideparticipantMealFitnessLoader();

        if (data.status === "error") {
            showToast(data.message, data.status);
        } else {
            handleUploadResponse(data, fileType);
        }
    } catch (error) {
        hideparticipantMealFitnessLoader();
        console.log(error);
    }
}
function showparticipantMealFitnessLoader() {
    document.getElementById("participantMealFitnessLoader").classList.remove("hidden");
}

function hideparticipantMealFitnessLoader() {
    document.getElementById("participantMealFitnessLoader").classList.add("hidden");
}

function handleUploadResponse(data, fileType) {
    const parsedResult = data.data.result;
    if (parsedResult.status === "failure") {
        showToast(parsedResult.message, "error");
    } else {
        uploadCompleted = true;
        lastUploadedFile = "";
        document.getElementById("upload-filename").remove();
        document.getElementById("uploadMealsFitnessData").value = "";
        document.getElementById("upload-list").classList.add("hidden");
        showToast(parsedResult.message, "success");
        clearMealFitnessGrid();
        if (fileType == "Meals") {
            initMealsGrid();
        }
        else {
            initFitnessGrid();
        }
    }
    mealsfitnessfile = "";
}
function clearMealFitnessGrid() {
    const gridDiv = document.getElementById("serverDataGrid");
    if (gridDiv) {
        gridDiv.innerHTML = "";
    }
}
function initMealsGrid() {
    const modalAide = new ModalAide();
    const schemaName = "drh_stateless_raw_observation";
    const viewName = "file_device_meal_observation_view";

    let fileContent = '';
    showLoading("cgmParticipantLoader");
    let gridFilterParams = {
        "participant_id": {
            "filterType": "text",
            "type": "equals",
            "filter": participantId
        }
    };

    const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
            {
                headerName: "MEAL TIME",
                field: "occurrence_time",
                filter: "agTextColumnFilter",
                enableRowGroup: true,
                flex: 3,
            },
            {
                headerName: "CALORIES",
                field: "value_quantity",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 2,
            },
            {
                headerName: "MEAL TYPE",
                field: "meal_type",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 3,
            },
        ])
        .withServerSideDatasource(
            window.shell.serverSideUrl(
                `/api/ux/tabular/jooq/` + schemaName + `/` + viewName + `.json`
            ),
            ((data, valueCols) => {
                return valueCols.map((col) => ({
                    headerName: col.displayName,
                    field: col.field,
                }));
            }),
            {
                beforeRequest: async (reqPayload, dataSourceUrl) => {
                    // Add custom parameters here
                    reqPayload.body = {
                        ...reqPayload.body,
                        "filterModel": {
                            ...reqPayload.body.filterModel, // Preserve AG Grid filters
                            ...gridFilterParams, // Add custom filter
                        }
                    };
                    return reqPayload;
                    // return reqPayload;
                },
                beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
                    let lastResponseLength = serverRespPayload.data.length;
                    // Clear the grid if there's no more data
                    if (lastResponseLength === 0) {
                        clearGrid();
                    }
                },
            }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: (9 + 1) * 50 + "px", width: "100%" })
        .build();
    agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };
    agGridInstance.init("serverDataGrid");
    hideLoading("cgmParticipantLoader");
}
function initFitnessGrid() {
    const modalAide = new ModalAide();
    const schemaName = "drh_stateless_raw_observation";
    const viewName = "file_device_fitness_observation_tab_view";
    let fileContent = '';
    showLoading("cgmParticipantLoader");
    let gridFilterParams = {
        "participant_id": {
            "filterType": "text",
            "type": "equals",
            "filter": participantId
        }
    };

    const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
            {
                headerName: "DATE",
                field: "effective_datetime",
                filter: "agTextColumnFilter",
                enableRowGroup: true,
                flex: 3,
            },
            {
                headerName: "STEPS",
                field: "steps",
                filter: "agTextColumnFilter",
                enableRowGroup: true,
                flex: 3,
            },
            {
                headerName: "EXERCISE MINUTES",
                field: "exercise_minutes",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 2,
            },
            {
                headerName: "CALORIES BURNED",
                field: "calories_burned",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 3,
            },
            {
                headerName: "DISTANCE",
                field: "distance",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 3,
            },
            {
                headerName: "HEART RATE",
                field: "heart_rate",
                enableRowGroup: true,
                filter: "agTextColumnFilter",
                flex: 3,
            },

        ])
        .withServerSideDatasource(
            window.shell.serverSideUrl(
                `/api/ux/tabular/jooq/` + schemaName + `/` + viewName + `.json`
            ),
            ((data, valueCols) => {
                return valueCols.map((col) => ({
                    headerName: col.displayName,
                    field: col.field,
                }));
            }),
            {
                beforeRequest: async (reqPayload, dataSourceUrl) => {
                    // Add custom parameters here
                    reqPayload.body = {
                        ...reqPayload.body,
                        "filterModel": {
                            ...reqPayload.body.filterModel, // Preserve AG Grid filters
                            ...gridFilterParams, // Add custom filter
                        }
                    };
                    return reqPayload;
                    // return reqPayload;
                },
                beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
                    let lastResponseLength = serverRespPayload.data.length;
                    // Clear the grid if there's no more data
                    if (lastResponseLength === 0) {
                        clearGrid();
                    }
                },
            }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: (9 + 1) * 50 + "px", width: "100%" })
        .build();
    agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };
    agGridInstance.init("serverDataGrid");
    hideLoading("cgmParticipantLoader");
}