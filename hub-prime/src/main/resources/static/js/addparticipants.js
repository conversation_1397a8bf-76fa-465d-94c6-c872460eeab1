let participantfile = "";
let uploadCompleted = false;
let lastUploadedFile = "";
let gridFilterParams = {
    "study_id": {
        "filterType": "text",
        "type": "equals",
        "filter": studyId
    }
};

import {
    AGGridAide,
    AGGridAideBuilder,
} from "@presentation/shell/aggrid-aide.js";
import ModalAide from "@presentation/shell/modal-aide.js";

document.addEventListener("DOMContentLoaded", async function () {
    initParticipantGrid();
    await populateDropdowns();
    setupDragAndDrop();
    setupEventListeners();
});

async function populateDropdowns() {
    const genderList = await getGenderList();
    createOptions('gender', genderList);
    const ethnicityList = await getEthnicityList();
    createOptions('ethnicity', ethnicityList);
    const raceList = await getRaceList();
    createOptions('race', raceList);
}

function setupDragAndDrop() {
    const dropZone = document.getElementById("drop-zone");

    dropZone.addEventListener("dragover", (e) => {
        e.preventDefault();
        dropZone.classList.add("drag-over");
    });

    dropZone.addEventListener("dragleave", () => {
        dropZone.classList.remove("drag-over");
    });

    dropZone.addEventListener("drop", async (e) => {
        e.preventDefault();
        dropZone.classList.remove("drag-over");
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            participantfile = files[0];
            let fileContent = await handleFileDrop('uploadParticipantData', files[0]);
            console.log("fileContent", fileContent);
            participantfile = fileContent;
        }
    });
}

function setupEventListeners() {
    document.getElementById("uploadParticipantData").addEventListener("change", async function () {
        participantfile = document.getElementById("uploadParticipantData").files[0];
        if (participantfile) {
            lastUploadedFile = participantfile;
        }
        let fileContent = await handleFileUpload('uploadParticipantData', lastUploadedFile);
        participantfile = fileContent;
    });

    document.getElementById("upload-files").addEventListener("click", async function (e) {
        e.preventDefault();
        if (!uploadCompleted) {
            participantfile = await getUploadedFile("uploadParticipantData");
        }
        if (participantfile && participantfile !== "") {
            await submitFile(participantfile);
        } else {
            showToast("Please upload a participant file", "error");
        }
    });

    document.addEventListener("fileRemoved", function (event) {
        const { inputName } = event.detail;

        if (inputName === "uploadParticipantData") {
            participantfile = ""; // Reset the participantfile variable
            lastUploadedFile = ""; // Reset the last uploaded file
            console.log("File removed for input:", inputName);
        }
    });

    ["gender", "age"].forEach(id => {
        document.getElementById(id)?.addEventListener("blur", function () {
            let element = document.getElementById(id);
            if (element.value) {
                element.classList.remove("border-[#e23636]");
            }
        });
    });

    document.getElementById("participantName")?.addEventListener("blur", async function () {
        let element = document.getElementById("participantName");
        let errElement = document.getElementById("participantName-error");
        element.classList.remove("border-[#e23636]");
        let displayId = element.value;
        const validField = await isValidParticipantDisplayId(displayId);
        if (validField) {
            showParticipantDisplayLoader();
            const exists = await checkIdExistence(displayId);
            hideParticipantDisplayLoader();
            errElement.textContent = exists ? "This ID is already taken. Please choose another one." : "";
        } else {
            if (displayId) {
                element.classList.add("border-[#e23636]");
            }
        }
    });

    document.getElementById("add-participant").addEventListener("click", async () => {
        let isValid = true;
        let fieldsToValidate = [];
        fieldsToValidate = [
            "gender",
            "participantName",
            "age"
        ];
        // Validate all fields
        fieldsToValidate.forEach((fieldId) => {
            const isFieldValid = validateField(fieldId);
            if (!isFieldValid) {
                isValid = false;
            }
        });
        const age = DOMPurify.sanitize(document.getElementById("age").value);
        const diagnosisIcd = DOMPurify.sanitize(document.getElementById("diagnosis_icd").value);
        const medRxnorm = DOMPurify.sanitize(document.getElementById("med_rxnorm").value);
        const treatmentModality = DOMPurify.sanitize(document.getElementById("treatment_modalities").value);
        const gender = DOMPurify.sanitize(document.getElementById("gender").value);
        const race = DOMPurify.sanitize(document.getElementById("race").value);
        const ethnicity = DOMPurify.sanitize(document.getElementById("ethnicity").value);
        const bmi = DOMPurify.sanitize(document.getElementById("bmi").value);
        const baselineHba1c = DOMPurify.sanitize(document.getElementById("baseline_hba1c").value);
        const diabetesType = DOMPurify.sanitize(document.getElementById("diabetes_type").value);
        const studyArm = DOMPurify.sanitize(document.getElementById("study_arm").value);
        let element = document.getElementById("participantName");
        let errorElement = document.getElementById("participantName-error");
        if (isValid) {
            const validField = await isValidParticipantDisplayId(DOMPurify.sanitize(element.value));
            if (validField) {
                showParticipantDisplayLoader();
                const exists = await checkIdExistence(DOMPurify.sanitize(element.value));
                if (exists) {
                    errorElement.textContent = "This ID is already taken. Please choose another one.";
                    isValid = false;
                    hideParticipantDisplayLoader();
                    return
                }
                else {
                    errorElement.textContent = "";
                    isValid = true;
                    hideParticipantDisplayLoader();
                }
            }
            else {
                errorElement.textContent =
                    "Invalid format. Use only letters,digits and hyphen, 3 to 11 characters.";
                isValid = false;
                return
            }
            if (age < 0 || age > 150) {
                document.getElementById("age-error").textContent = "Age should be between 0 and 150";
                isValid = false;
                return
            } else {
                document.getElementById("age-error").textContent = "";
            }
        }
        if (isValid) {
            showLoading('addParticipantLoader');
            const data = {
                "participantDisplayId": DOMPurify.sanitize(element.value),
                "studyId": studyId,
                "orgPartyId": localStorage.getItem("organizationPartyId"),
                "age": age,
                "diagnosisIcd": diagnosisIcd,
                "medRxnorm": medRxnorm,
                "treatmentModality": treatmentModality,
                "genderId": gender,
                "raceId": race,
                "ethnicityId": ethnicity,
                "bmi": bmi,
                "baselineHba1c": baselineHba1c,
                "diabetesType": diabetesType,
                "studyArm": studyArm
            };
            postData(`/participant`, data, (res) => {
                if (res && res.status === "success") {
                    hideLoading('addParticipantLoader');
                    showToast("Participant added successfully!", "success");
                    document.querySelectorAll('#add-participant-container input').forEach(field => {
                        if (field.type === 'number') {
                            field.value = '0';
                        } else {
                            field.value = '';
                        }
                    });
                    clearParticipantGrid();
                    initParticipantGrid();
                    resetParticipantForm()  // Reset the form
                } else if (res.status && res.status === "error") {
                    hideLoading('addParticipantLoader');
                    showToast(res.message, "error");
                }
                else {
                    hideLoading('addParticipantLoader');
                    showToast("Issue in adding Participant", "error");
                }
            });
        }
        else {
            hideLoading('addParticipantLoader');
        }
    });
}

async function submitFile(participantDatafile) {
    let studyId = DOMPurify.sanitize(document.getElementById("studyId").value);
    showParticipantUploadLoader();

    try {
        const formData = new FormData();
        formData.append("file", participantDatafile);
        formData.append("studyId", studyId);
        formData.append("orgPartyId", localStorage.getItem("organizationPartyId"));
        let url = "/participant/file/upload";

        const response = await fetch(url, {
            method: 'POST',
            body: formData,
        });

        const data = await response.json();
        hideParticipantUploadLoader();

        if (data.status === "error") {
            showToast(data.message, data.status);
        } else {
            handleUploadResponse(data);
        }
    } catch (error) {
        hideParticipantUploadLoader();
        console.log(error);
    }
}

function handleUploadResponse(data) {
    const parsedResult = data.data.result;
    if (Object.keys(data.data).length === 0) {
        showToast("You have uploaded an empty file", "error");
    } else if (parsedResult.status === "failure") {
        showToast(parsedResult.message, "error");
    } else if (parsedResult?.failure_participants?.length > 0) {
        let errorArray = parsedResult.failure_participants.map(participant => participant.error_message + "- " + participant.participant_display_id);
        let errorString = errorArray.join("<br/>");
        showToast(errorString, "error");
    } else {
        uploadCompleted = true;
        clearParticipantGrid();
        initParticipantGrid();
        lastUploadedFile = "";
        document.getElementById("upload-filename").remove();
        document.getElementById("uploadParticipantData").value = "";
        document.getElementById("upload-list").classList.add("hidden");
        showToast("Added participants successfully", "success");
    }
    participantfile = "";
}

function initParticipantGrid() {
    const schemaName = "drh_stateless_research_study";
    const viewName = "participant_data_view";
    const params = new URLSearchParams(window.location.search);
    const tab = params.get("tab");
    const modalAide = new ModalAide();
    const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
            {
                headerName: "Participant ID",
                field: "participant_display_id",
                filter: "agTextColumnFilter",
                cellRenderer: function (params) {
                    if (params.value) {
                        const link = document.createElement("a");
                        link.href = "/participants/info/" + studyId + "/" + params.data.participant_id + "?tab=" + tab;
                        link.innerText = params.value;
                        return link;
                    } else {
                        return null;
                    }
                },
            },
            {
                headerName: "Gender",
                field: "participant_gender",
                filter: "agTextColumnFilter",
            },
            { headerName: "Age", field: "participant_age", filter: "agTextColumnFilter" },
            {
                headerName: "Race",
                field: "participant_race",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Ethnicity",
                field: "participant_ethnicity",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Study Arm",
                field: "study_arm",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Diabetes Type",
                field: "diabetes_type",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Baseline HbA1c",
                field: "baseline_hba1c",
                filter: "agTextColumnFilter",
                valueFormatter: (params) => {
                    return params.value != null ? `${params.value}%` : '';
                }
            },
        ])
        .withServerSideDatasource(
            window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}.json`),
            (data, valueCols) => valueCols.map((col) => ({
                headerName: col.displayName,
                field: col.field,
            })),
            {
                beforeRequest: async (reqPayload) => {
                    reqPayload.body = {
                        ...reqPayload.body,
                        "filterModel": {
                            ...reqPayload.body.filterModel,
                            ...gridFilterParams,
                        },
                    };
                    return reqPayload;
                },
                beforeSuccess: async (serverRespPayload) => {
                    if (serverRespPayload.data.length === 0) {
                        clearGrid();
                    }
                },
            }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        .build();
    agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

    agGridInstance.init("serverDataGrid");
}


async function isValidParticipantDisplayId(displayId) {
    const isValidId = /^[a-zA-Z0-9-]{3,14}$/.test(displayId);
    let errorElement = document.getElementById("participantName-error");
    if (!isValidId) {
        errorElement.textContent = "Invalid format. Use only letters,digits and hyphen, 3 to 14 characters.";
        return false;
    } else {
        errorElement.textContent = "";
        return true;
    }
}

async function checkIdExistence(displayId) {
    return new Promise((resolve, reject) => {
        fetchRawData(`/participant/${studyId}/${displayId}/exists`, (data, error) => {
            if (error) {
                reject(error);
            } else {
                const exists = JSON.parse(data.data.exists);
                resolve(exists);
            }
        });
    });
}

function showParticipantDisplayLoader() {
    document.getElementById("participantDisplayId-loader").classList.remove("hidden");
}

function hideParticipantDisplayLoader() {
    document.getElementById("participantDisplayId-loader").classList.add("hidden");
}

function showParticipantUploadLoader() {
    document.getElementById("uploadParticipantLoader").classList.remove("hidden");
}

function hideParticipantUploadLoader() {
    document.getElementById("uploadParticipantLoader").classList.add("hidden");
}

function resetParticipantForm() {
    ["participantName", "age", "diagnosis_icd", "med_rxnorm", "treatment_modalities", "gender", "race", "ethnicity", "bmi", "baseline_hba1c", "diabetes_type", "study_arm"].forEach(id => {
        document.getElementById(id).value = "";
    });
}

function clearParticipantGrid() {
    const gridDiv = document.getElementById("serverDataGrid");
    if (gridDiv) {
        gridDiv.innerHTML = "";
    }
}