let loginPlatform = "github";

document.addEventListener("DOMContentLoaded", function () {
    if (document.getElementById("userName") && document.getElementById("userEmail")) {
        initProfile();
        document.getElementById("save-profile").addEventListener("click", async function () {
            await saveProfile();
        });
    }
    ["userName", "userEmail"].forEach(id => {
        document.getElementById(id)?.addEventListener("blur", async function () {
            let element = document.getElementById(id);
            if (document.getElementById(id)?.value != "") {
                element.classList.remove("border-[#e23636]");
            }
        });
    });

    document.getElementById("add-new-org")?.addEventListener("click", function () {
        document.getElementById("new-institution-ctr").classList.remove("hidden");
        document.getElementById("new-institution-label-ctr").classList.remove("hidden");
    });
    document.getElementById("orcidID")?.addEventListener("blur", async function () {
        let orcidID = document.getElementById("orcidID").value;
        console.log("loginPatform", loginPlatform)
        if (loginPlatform != "orcid") {
            if (isValidOrcid(orcidID)) {
                console.log(`${orcidID} is a valid ORCID.`);
                document.getElementById("orcidID").classList.remove("border-[#e23636]");
                showOrcidLoader();

                try {
                    let orcidDetails = await fetchOrcidDetails(orcidID);


                    if (orcidDetails.userName != null && orcidDetails.userName != undefined) {
                        document.getElementById('userName-label').innerHTML = "Full Name (Your full name fetched from ORCID, modify if needed)<span class='text-red-500' >*</span >";
                    }

                    if (orcidDetails.userEmail != null && orcidDetails.userEmail != undefined) {
                        document.getElementById('userEmail-label').innerHTML = "Email Address (Your email address fetched from ORCID,modify if needed) <span class='text-red-500' >*</span >";
                    }
                    if (orcidDetails.userInstitution != null && orcidDetails.userInstitution != undefined) {
                        document.getElementById('userInstitution-label').innerHTML = "Institution/Organization (The institution fetched from ORCID.modify if needed) <span class='text-red-500' >*</span >";
                    }


                    document.getElementById("userName").value = orcidDetails.userName;
                    document.getElementById("userEmail").value = orcidDetails.userEmail;
                    document.getElementById("userInstitution").value = orcidDetails.userInstitution;

                    console.log(orcidDetails);
                } catch (error) {
                    console.error("Error fetching ORCID details:", error);
                    // Optionally, show an error message to the user
                } finally {
                    hideOrcidLoader(); // Ensure the loader is hidden regardless of success or error
                }
            } else {
                if (orcidID != "" && orcidID != undefined)
                    document.getElementById("orcidID").classList.add("border-[#e23636]");
                console.log(`${orcidID} is not a valid ORCID.`);
            }
        }

    });

    document.getElementById("userEmail")?.addEventListener("blur", async function () {
        let userEmail = document.getElementById("userEmail").value;
        document.getElementById("userEmail-error").innerHTML = "";
        if (userEmail != "" && userEmail != undefined) {
            if (isValiduserEmail(userEmail)) {
                const uniqueEmail = await emailExistenceCheck(userEmail);
                console.log("uniqueEmail", uniqueEmail);
            }
            else {
                if (userEmail != "" && userEmail != undefined)
                    document.getElementById("userEmail-error").innerHTML = "Please enter a valid email address";
            }
        }

    });
});

function initProfile() {
    if (document.getElementById('userInstitution')) {
        var control = initInstitution('userInstitution');
    }
    if (document.getElementById('userNewInstitution')) {
        var control2 = initNewInstitution();
    }
    fetch(`/orcid/user-info`)
        .then((response) => {
            if (!response.ok) {
                throw new Error(
                    "Network response was not ok " + response.statusText
                );
            }

            return response.json();
        }).
        then((responsedata) => {
            const data = responsedata.data;
            if (data.orcidId != null && data.orcidId != undefined) {
                document.getElementById('orcidID-label').innerHTML = "ORCID ID (ORCID ID based on your login)";

                if (data.userName != null && data.userName != undefined) {
                    document.getElementById('userName-label').innerHTML = "Full Name (Your full name fetched from ORCID, modify if needed)<span class='text-red-500' >*</span >";
                }

                if (data.userEmail != null && data.userEmail != undefined) {
                    document.getElementById('userEmail-label').innerHTML = "Email Address (Your email address fetched from ORCID, modify if needed)<span class='text-red-500' >*</span >";
                }
                if (data.userInstitution != null && data.userInstitution != undefined) {
                    document.getElementById('userInstitution-label').innerHTML = "Institution/Organization (The institution fetched from ORCID, modify if needed)<span class='text-red-500' >*</span >";
                }
                document.getElementById('orcidID').value = data.orcidId;
                if (data.orcidId != null && data.orcidId != undefined) {
                    loginPlatform = "orcid";
                    document.getElementById('orcidID').setAttribute("readonly", true);
                    document.getElementById('orcidID').classList.add("bg-gray-100");
                }
                document.getElementById('userName').value = data.userName;
                // document.getElementById('userEmail').value = data.userEmail || verificationEmail;
            }
            else {
                //Github
                document.getElementById('orcidID-label').innerHTML = "ORCID ID (Please provide your ORCID ID if you have it, and we will fetch details from ORCID)";
                document.getElementById('userName-label').innerHTML = "Full Name (Please enter your full name)<span class='text-red-500' >*</span >";
                document.getElementById('userEmail-label').innerHTML = "Email Address (Please enter Your email address)<span class='text-red-500' >*</span >";
                document.getElementById('userInstitution-label').innerHTML = "Institution/Organization (Please select your organization from the existing list)<span class='text-red-500' >*</span >";
            }
            if (data.userInstitution != null || data.userInstitution != undefined)
                control.setValue([data.userInstitution]);
        }).catch((error) => {
            console.error(
                "There was a problem with the fetch operation:",
                error
            );
        })
}


async function saveProfile() {

    const orcidID = document.getElementById("orcidID").value;
    const userName = document.getElementById("userName").value;
    const userEmail = document.getElementById("userEmail").value;
    const userInstitution = document.getElementById("userInstitution").value;
    const userNewInstitution = document.getElementById("userNewInstitution").value;
    console.log("userInstitution", userInstitution);
    console.log("userNewInstitution", userNewInstitution);

    let fieldsToValidate = [];
    let isValid = true;
    fieldsToValidate = [
        "userName",
        "userEmail",
    ];
    let errorClass = "border-[#e23636]";

    // Validate all fields
    fieldsToValidate.forEach((fieldId) => {
        const isFieldValid = validateField(fieldId);
        if (!isFieldValid) {
            isValid = false;
        }
        else {
            if (fieldId == 'orcidID') {
                if (isValidOrcid(orcidID)) {
                    console.log(`${orcidID} is a valid ORCID.`);
                } else {
                    isValid = false;
                    document.getElementById(fieldId).classList.add(errorClass);
                    console.log(`${orcidID} is not a valid ORCID.`);
                }
            }
        }
    });
    if (isValid) {
        if (userEmail != "" && userEmail != undefined) {
            if (isValiduserEmail(userEmail)) {
                const uniqueEmail = await emailExistenceCheck(userEmail);
                uniqueEmail ? isValid = true : isValid = false;
            }
            else {
                if (userEmail != "" && userEmail != undefined)
                    document.getElementById("userEmail-error").innerHTML = "Please enter a valid email address";
                return;
            }
        }
    }
    if (isValid) {
        console.log(userInstitution, userNewInstitution)
        let organization_party_id = userInstitution != "" ? userInstitution : (userNewInstitution != "" ? localStorage.getItem("savedOrganizationPartyId") : "");
        if (userInstitution == '' && userNewInstitution == '') {
            showToast("Please select your organization", "error");
            return;
        }
        else {
            const profileParams = {
                "name": userName,
                "orcid": orcidID || "",
                "email": [
                    userEmail
                ],
                "organizationPartyId": organization_party_id
            };
            postData(`/practitioners`, profileParams, (res) => {
                console.log(res.data.practitionerProfile)
                if (res && res.status === "success") {
                    let practitionerProfile = JSON.parse(res.data.practitionerProfile);
                    if (practitionerProfile.practitioner_party_id != null && practitionerProfile.practitioner_party_id != undefined) {
                        localStorage.setItem("userId", practitionerProfile.drh_user_id);
                        localStorage.setItem("practitionerPartyId", practitionerProfile.practitioner_party_id);
                        localStorage.setItem("organizationPartyId", practitionerProfile.organization_party_id);
                        localStorage.setItem("tenant_id", practitionerProfile.tenant_id);
                        localStorage.setItem("profileStatus", "COMPLETE");
                        showToast("Profile created successfully!", "success");
                    }
                    else {
                        showToast(practitionerProfile.message, "error");
                    }

                    window.location.href = "/studies/create-study?prstat=complete";
                }
                else {
                    showToast(res.message, "error");
                }
            });
        }
    }
}

function fetchOrcidDetails(orcidID) {
    return new Promise((resolve, reject) => {
        fetchData(
            `/orcid/user-details/${orcidID}`,
            (data, error) => {
                hideOrcidLoader(); // Ensure loader is hidden regardless of success or error
                if (error) {
                    reject(error); // Reject the Promise with the error
                } else {
                    resolve(data.data); // Resolve the Promise with the result
                }
            }
        );
    });
}

function showOrcidLoader() {
    document.getElementById("orcid-loader").classList.remove("hidden");
}

function hideOrcidLoader() {
    document.getElementById("orcid-loader").classList.add("hidden");
}
function showuserEmailLoader() {
    document.getElementById("userEmail-loader")?.classList.remove("hidden");
}

function hideuserEmailLoader() {
    document.getElementById("userEmail-loader")?.classList.add("hidden");
}

function showOrgLoader() {
    document.getElementById("organization-loader").classList.remove("hidden");
}

function hideOrgLoader() {
    document.getElementById("organization-loader").classList.add("hidden");
}
function isValiduserEmail(userEmail) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(userEmail);
}
async function emailExistenceCheck(userEmail) {
    return new Promise((resolve, reject) => {
        let isUniqueEmail = true;
        document.getElementById("userEmail").classList.remove("border-[#e23636]");
        showuserEmailLoader();
        fetchData(
            `/practitioners/unique-email?email=${encodeURIComponent(userEmail)}`,
            async (res, error) => {
                if (!error) {
                    if (res.status === "success") {
                        if (res.data.isUniqueEmail) {
                            document.getElementById("userEmail").classList.remove("border-[#e23636]");
                            document.getElementById("userEmail-error").innerHTML = "";
                        }
                        else {
                            isUniqueEmail = false;
                            document.getElementById("userEmail-error").innerHTML = "Email already exists";
                        }
                    }
                }
                hideuserEmailLoader();
                resolve(isUniqueEmail);
            })
    })
}

