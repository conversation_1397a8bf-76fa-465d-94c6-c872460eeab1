<configuration>
<property name="profile" value="${SPRING_PROFILES_ACTIVE}" />
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ORG_DRH_HUB_PRIME_LOG_FILE}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${ORG_DRH_HUB_PRIME_LOG_FILE}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="otel" class="io.opentelemetry.instrumentation.logback.appender.v1_0.OpenTelemetryAppender">
        <serviceName>Diabetes-Research-Hub</serviceName>
    </appender>
    

    <root level="info">
        <springProfile name="sandbox">
            <appender-ref ref="CONSOLE" />
        </springProfile>
        <appender-ref ref="FILE" />
        <appender-ref ref="otel"/>
    </root>
</configuration>
