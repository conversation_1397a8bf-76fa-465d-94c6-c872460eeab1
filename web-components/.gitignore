# .vscode folder (VS Code settings, extensions, etc.)
.vscode/

# Node modules (for Node.js projects)
node_modules/

# Build output directories
/dist/
/build/

# Logs
*.log

# Dependency directories
/.pnp/
/.pnp.js

# .env files (Environment variables)
.env
.env.local
.env.*.local

# MacOS specific
.DS_Store

# Windows specific
Thumbs.db

# VS Code workspace settings
.vscode/settings.json

# Optional: Ignore compiled TypeScript files
*.tsbuildinfo
