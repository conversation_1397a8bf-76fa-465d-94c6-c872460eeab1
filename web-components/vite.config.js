// vite.config.js
import { defineConfig } from 'vite';
import { defineConfig as vitestConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,        // Enables global test functions like `describe` and `it`
    environment: 'happy-dom', // Use Happy DOM (lightweight DOM implementation)
  },
  root: '.',
  build: {
    outDir: '../hub-prime/src/main/resources/static/js/wc',
    rollupOptions: {
      input: {
        'd3/stacked-bar-chart': path.resolve(__dirname, 'src/components/stacked-bar-chart.ts'),
        'd3/agp-chart': path.resolve(__dirname, 'src/components/agp-chart.ts'),
        'd3/dgp-chart': path.resolve(__dirname, 'src/components/dgp-chart.js'),
        'd3/gri-chart': path.resolve(__dirname, 'src/components/gri-chart.ts'),
        'glucose-statistics-and-targets': path.resolve(__dirname, 'src/components/glucose-statistics-and-targets.ts'),
        'formula-component': path.resolve(__dirname, 'src/components/formula-component.ts'),
      },
      output: {
        entryFileNames: '[name].js', // Each component has a separate output file
        format: 'es', // ES module format
      },
    },
  },
});
