       *,
       ::before,
       ::after {
           --tw-border-spacing-x: 0;
           --tw-border-spacing-y: 0;
           --tw-translate-x: 0;
           --tw-translate-y: 0;
           --tw-rotate: 0;
           --tw-skew-x: 0;
           --tw-skew-y: 0;
           --tw-scale-x: 1;
           --tw-scale-y: 1;
           --tw-pan-x: ;
           --tw-pan-y: ;
           --tw-pinch-zoom: ;
           --tw-scroll-snap-strictness: proximity;
           --tw-gradient-from-position: ;
           --tw-gradient-via-position: ;
           --tw-gradient-to-position: ;
           --tw-ordinal: ;
           --tw-slashed-zero: ;
           --tw-numeric-figure: ;
           --tw-numeric-spacing: ;
           --tw-numeric-fraction: ;
           --tw-ring-inset: ;
           --tw-ring-offset-width: 0px;
           --tw-ring-offset-color: #fff;
           --tw-ring-color: rgb(59 130 246 / 0.5);
           --tw-ring-offset-shadow: 0 0 #0000;
           --tw-ring-shadow: 0 0 #0000;
           --tw-shadow: 0 0 #0000;
           --tw-shadow-colored: 0 0 #0000;
           --tw-blur: ;
           --tw-brightness: ;
           --tw-contrast: ;
           --tw-grayscale: ;
           --tw-hue-rotate: ;
           --tw-invert: ;
           --tw-saturate: ;
           --tw-sepia: ;
           --tw-drop-shadow: ;
           --tw-backdrop-blur: ;
           --tw-backdrop-brightness: ;
           --tw-backdrop-contrast: ;
           --tw-backdrop-grayscale: ;
           --tw-backdrop-hue-rotate: ;
           --tw-backdrop-invert: ;
           --tw-backdrop-opacity: ;
           --tw-backdrop-saturate: ;
           --tw-backdrop-sepia: ;
           --tw-contain-size: ;
           --tw-contain-layout: ;
           --tw-contain-paint: ;
           --tw-contain-style:
       }

       ::backdrop {
           --tw-border-spacing-x: 0;
           --tw-border-spacing-y: 0;
           --tw-translate-x: 0;
           --tw-translate-y: 0;
           --tw-rotate: 0;
           --tw-skew-x: 0;
           --tw-skew-y: 0;
           --tw-scale-x: 1;
           --tw-scale-y: 1;
           --tw-pan-x: ;
           --tw-pan-y: ;
           --tw-pinch-zoom: ;
           --tw-scroll-snap-strictness: proximity;
           --tw-gradient-from-position: ;
           --tw-gradient-via-position: ;
           --tw-gradient-to-position: ;
           --tw-ordinal: ;
           --tw-slashed-zero: ;
           --tw-numeric-figure: ;
           --tw-numeric-spacing: ;
           --tw-numeric-fraction: ;
           --tw-ring-inset: ;
           --tw-ring-offset-width: 0px;
           --tw-ring-offset-color: #fff;
           --tw-ring-color: rgb(59 130 246 / 0.5);
           --tw-ring-offset-shadow: 0 0 #0000;
           --tw-ring-shadow: 0 0 #0000;
           --tw-shadow: 0 0 #0000;
           --tw-shadow-colored: 0 0 #0000;
           --tw-blur: ;
           --tw-brightness: ;
           --tw-contrast: ;
           --tw-grayscale: ;
           --tw-hue-rotate: ;
           --tw-invert: ;
           --tw-saturate: ;
           --tw-sepia: ;
           --tw-drop-shadow: ;
           --tw-backdrop-blur: ;
           --tw-backdrop-brightness: ;
           --tw-backdrop-contrast: ;
           --tw-backdrop-grayscale: ;
           --tw-backdrop-hue-rotate: ;
           --tw-backdrop-invert: ;
           --tw-backdrop-opacity: ;
           --tw-backdrop-saturate: ;
           --tw-backdrop-sepia: ;
           --tw-contain-size: ;
           --tw-contain-layout: ;
           --tw-contain-paint: ;
           --tw-contain-style:
       }

       /* ! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com */
       *,
       ::after,
       ::before {
           box-sizing: border-box;
           border-width: 0;
           border-style: solid;
           border-color: #e5e7eb
       }

       ::after,
       ::before {
           --tw-content: ''
       }

       :host,
       html {
           line-height: 1.5;
           -webkit-text-size-adjust: 100%;
           -moz-tab-size: 4;
           tab-size: 4;
           font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
           font-feature-settings: normal;
           font-variation-settings: normal;
           -webkit-tap-highlight-color: transparent
       }

       body {
           margin: 0;
           line-height: inherit
       }

       hr {
           height: 0;
           color: inherit;
           border-top-width: 1px
       }

       abbr:where([title]) {
           -webkit-text-decoration: underline dotted;
           text-decoration: underline dotted
       }

       h1,
       h2,
       h3,
       h4,
       h5,
       h6 {
           font-size: inherit;
           font-weight: inherit
       }

       a {
           color: inherit;
           text-decoration: inherit
       }

       b,
       strong {
           font-weight: bolder
       }

       code,
       kbd,
       pre,
       samp {
           font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
           font-feature-settings: normal;
           font-variation-settings: normal;
           font-size: 1em
       }

       small {
           font-size: 80%
       }

       sub,
       sup {
           font-size: 75%;
           line-height: 0;
           position: relative;
           vertical-align: baseline
       }

       sub {
           bottom: -.25em
       }

       sup {
           top: -.5em
       }

       table {
           text-indent: 0;
           border-color: inherit;
           border-collapse: collapse
       }

       button,
       input,
       optgroup,
       select,
       textarea {
           font-family: inherit;
           font-feature-settings: inherit;
           font-variation-settings: inherit;
           font-size: 100%;
           font-weight: inherit;
           line-height: inherit;
           letter-spacing: inherit;
           color: inherit;
           margin: 0;
           padding: 0
       }

       button,
       select {
           text-transform: none
       }

       button,
       input:where([type=button]),
       input:where([type=reset]),
       input:where([type=submit]) {
           -webkit-appearance: button;
           background-color: transparent;
           background-image: none
       }

       :-moz-focusring {
           outline: auto
       }

       :-moz-ui-invalid {
           box-shadow: none
       }

       progress {
           vertical-align: baseline
       }

       ::-webkit-inner-spin-button,
       ::-webkit-outer-spin-button {
           height: auto
       }

       [type=search] {
           -webkit-appearance: textfield;
           outline-offset: -2px
       }

       ::-webkit-search-decoration {
           -webkit-appearance: none
       }

       ::-webkit-file-upload-button {
           -webkit-appearance: button;
           font: inherit
       }

       summary {
           display: list-item
       }

       blockquote,
       dd,
       dl,
       figure,
       h1,
       h2,
       h3,
       h4,
       h5,
       h6,
       hr,
       p,
       pre {
           margin: 0
       }

       fieldset {
           margin: 0;
           padding: 0
       }

       legend {
           padding: 0
       }

       menu,
       ol,
       ul {
           list-style: none;
           margin: 0;
           padding: 0
       }

       dialog {
           padding: 0
       }

       textarea {
           resize: vertical
       }

       input::placeholder,
       textarea::placeholder {
           opacity: 1;
           color: #9ca3af
       }

       [role=button],
       button {
           cursor: pointer
       }

       :disabled {
           cursor: default
       }

       audio,
       canvas,
       embed,
       iframe,
       img,
       object,
       svg,
       video {
           display: block;
           vertical-align: middle
       }

       img,
       video {
           max-width: 100%;
           height: auto
       }

       [hidden]:where(:not([hidden=until-found])) {
           display: none
       }

       [type='text'],
       input:where(:not([type])),
       [type='email'],
       [type='url'],
       [type='password'],
       [type='number'],
       [type='date'],
       [type='datetime-local'],
       [type='month'],
       [type='search'],
       [type='tel'],
       [type='time'],
       [type='week'],
       [multiple],
       textarea,
       select {
           -webkit-appearance: none;
           appearance: none;
           background-color: #fff;
           border-color: #6b7280;
           border-width: 1px;
           border-radius: 0px;
           padding-top: 0.5rem;
           padding-right: 0.75rem;
           padding-bottom: 0.5rem;
           padding-left: 0.75rem;
           font-size: 1rem;
           line-height: 1.5rem;
           --tw-shadow: 0 0 #0000;
       }

       [type='text']:focus,
       input:where(:not([type])):focus,
       [type='email']:focus,
       [type='url']:focus,
       [type='password']:focus,
       [type='number']:focus,
       [type='date']:focus,
       [type='datetime-local']:focus,
       [type='month']:focus,
       [type='search']:focus,
       [type='tel']:focus,
       [type='time']:focus,
       [type='week']:focus,
       [multiple]:focus,
       textarea:focus,
       select:focus {
           outline: 2px solid transparent;
           outline-offset: 2px;
           --tw-ring-inset: var(--tw-empty,
                   /*!*/
                   /*!*/
               );
           --tw-ring-offset-width: 0px;
           --tw-ring-offset-color: #fff;
           --tw-ring-color: #2563eb;
           --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
           --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
           box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
           border-color: #2563eb
       }

       input::placeholder,
       textarea::placeholder {
           color: #6b7280;
           opacity: 1
       }

       ::-webkit-datetime-edit-fields-wrapper {
           padding: 0
       }

       ::-webkit-date-and-time-value {
           min-height: 1.5em;
           text-align: inherit
       }

       ::-webkit-datetime-edit {
           display: inline-flex
       }

       ::-webkit-datetime-edit,
       ::-webkit-datetime-edit-year-field,
       ::-webkit-datetime-edit-month-field,
       ::-webkit-datetime-edit-day-field,
       ::-webkit-datetime-edit-hour-field,
       ::-webkit-datetime-edit-minute-field,
       ::-webkit-datetime-edit-second-field,
       ::-webkit-datetime-edit-millisecond-field,
       ::-webkit-datetime-edit-meridiem-field {
           padding-top: 0;
           padding-bottom: 0
       }

       select {
           background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
           background-position: right 0.5rem center;
           background-repeat: no-repeat;
           background-size: 1.5em 1.5em;
           padding-right: 2.5rem;
           print-color-adjust: exact
       }

       [multiple],
       [size]:where(select:not([size="1"])) {
           background-image: initial;
           background-position: initial;
           background-repeat: unset;
           background-size: initial;
           padding-right: 0.75rem;
           print-color-adjust: unset
       }

       [type='checkbox'],
       [type='radio'] {
           -webkit-appearance: none;
           appearance: none;
           padding: 0;
           print-color-adjust: exact;
           display: inline-block;
           vertical-align: middle;
           background-origin: border-box;
           -webkit-user-select: none;
           user-select: none;
           flex-shrink: 0;
           height: 1rem;
           width: 1rem;
           color: #2563eb;
           background-color: #fff;
           border-color: #6b7280;
           border-width: 1px;
           --tw-shadow: 0 0 #0000
       }

       [type='checkbox'] {
           border-radius: 0px
       }

       [type='radio'] {
           border-radius: 100%
       }

       [type='checkbox']:focus,
       [type='radio']:focus {
           outline: 2px solid transparent;
           outline-offset: 2px;
           --tw-ring-inset: var(--tw-empty,
                   /*!*/
                   /*!*/
               );
           --tw-ring-offset-width: 2px;
           --tw-ring-offset-color: #fff;
           --tw-ring-color: #2563eb;
           --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
           --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
           box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
       }

       [type='checkbox']:checked,
       [type='radio']:checked {
           border-color: transparent;
           background-color: currentColor;
           background-size: 100% 100%;
           background-position: center;
           background-repeat: no-repeat
       }

       [type='checkbox']:checked {
           background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
       }

       @media (forced-colors: active) {
           [type='checkbox']:checked {
               -webkit-appearance: auto;
               appearance: auto
           }
       }

       [type='radio']:checked {
           background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
       }

       @media (forced-colors: active) {
           [type='radio']:checked {
               -webkit-appearance: auto;
               appearance: auto
           }
       }

       [type='checkbox']:checked:hover,
       [type='checkbox']:checked:focus,
       [type='radio']:checked:hover,
       [type='radio']:checked:focus {
           border-color: transparent;
           background-color: currentColor
       }

       [type='checkbox']:indeterminate {
           background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
           border-color: transparent;
           background-color: currentColor;
           background-size: 100% 100%;
           background-position: center;
           background-repeat: no-repeat;
       }

       @media (forced-colors: active) {
           [type='checkbox']:indeterminate {
               -webkit-appearance: auto;
               appearance: auto
           }
       }

       [type='checkbox']:indeterminate:hover,
       [type='checkbox']:indeterminate:focus {
           border-color: transparent;
           background-color: currentColor
       }

       [type='file'] {
           background: unset;
           border-color: inherit;
           border-width: 0;
           border-radius: 0;
           padding: 0;
           font-size: unset;
           line-height: inherit
       }

       [type='file']:focus {
           outline: 1px solid ButtonText;
           outline: 1px auto -webkit-focus-ring-color
       }

       .sr-only {
           position: absolute;
           width: 1px;
           height: 1px;
           padding: 0;
           margin: -1px;
           overflow: hidden;
           clip: rect(0, 0, 0, 0);
           white-space: nowrap;
           border-width: 0
       }

       .absolute {
           position: absolute
       }

       .relative {
           position: relative
       }

       .sticky {
           position: sticky
       }

       .-inset-1\.5 {
           inset: -0.375rem
       }

       .-inset-0\.5 {
           inset: -0.125rem
       }

       .left-0 {
           left: 0px
       }

       .right-0 {
           right: 0px
       }

       .right-2 {
           right: 0.5rem
       }

       .top-0 {
           top: 0px
       }

       .top-full {
           top: 100%
       }

       .right-2\.5 {
           right: 0.625rem
       }

       .top-\[55px\] {
           top: 55px
       }

       .z-10 {
           z-index: 10
       }

       .z-\[1\] {
           z-index: 1
       }

       .col-span-12 {
           grid-column: span 12 / span 12
       }

       .float-right {
           float: right
       }

       .float-left {
           float: left
       }

       .m-2 {
           margin: 0.5rem
       }

       .mx-auto {
           margin-left: auto;
           margin-right: auto
       }

       .mx-1 {
           margin-left: 0.25rem;
           margin-right: 0.25rem
       }

       .my-6 {
           margin-top: 1.5rem;
           margin-bottom: 1.5rem
       }

       .-mr-2 {
           margin-right: -0.5rem
       }

       .ml-10 {
           margin-left: 2.5rem
       }

       .ml-3 {
           margin-left: 0.75rem
       }

       .mr-2 {
           margin-right: 0.5rem
       }

       .mt-1 {
           margin-top: 0.25rem
       }

       .mt-2 {
           margin-top: 0.5rem
       }

       .mb-2 {
           margin-bottom: 0.5rem
       }

       .mb-4 {
           margin-bottom: 1rem
       }

       .me-2\.5 {
           margin-inline-end: 0.625rem
       }

       .ml-5 {
           margin-left: 1.25rem
       }

       .mr-3 {
           margin-right: 0.75rem
       }

       .ms-1 {
           margin-inline-start: 0.25rem
       }

       .ml-auto {
           margin-left: auto
       }

       .mt-3 {
           margin-top: 0.75rem
       }

       .mt-6 {
           margin-top: 1.5rem
       }

       .mb-6 {
           margin-bottom: 1.5rem
       }

       .ms-5 {
           margin-inline-start: 1.25rem
       }

       .mt-4 {
           margin-top: 1rem
       }

       .block {
           display: block
       }

       .inline-block {
           display: inline-block
       }

       .flex {
           display: flex
       }

       .inline-flex {
           display: inline-flex
       }

       .grid {
           display: grid
       }

       .hidden {
           display: none
       }

       .h-12 {
           height: 3rem
       }

       .h-16 {
           height: 4rem
       }

       .h-5 {
           height: 1.25rem
       }

       .h-6 {
           height: 1.5rem
       }

       .h-8 {
           height: 2rem
       }

       .h-full {
           height: 100%
       }

       .h-3 {
           height: 0.75rem
       }

       .h-\[25rem\] {
           height: 25rem
       }

       .h-\[35rem\] {
           height: 35rem
       }

       .h-\[45rem\] {
           height: 45rem
       }

       .h-fit {
           height: -moz-fit-content;
           height: fit-content
       }

       .h-4 {
           height: 1rem
       }

       .min-h-full {
           min-height: 100%
       }

       .w-12 {
           width: 3rem
       }

       .w-32 {
           width: 8rem
       }

       .w-48 {
           width: 12rem
       }

       .w-5 {
           width: 1.25rem
       }

       .w-6 {
           width: 1.5rem
       }

       .w-8 {
           width: 2rem
       }

       .w-3 {
           width: 0.75rem
       }

       .w-\[100\%\] {
           width: 100%
       }

       .w-full {
           width: 100%
       }

       .w-4 {
           width: 1rem
       }

       .min-w-52 {
           min-width: 13rem
       }

       .max-w-xs {
           max-width: 20rem
       }

       .flex-shrink-0 {
           flex-shrink: 0
       }

       .table-auto {
           table-layout: auto
       }

       .border-collapse {
           border-collapse: collapse
       }

       .origin-top-right {
           transform-origin: top right
       }

       @keyframes spin {
           to {
               transform: rotate(360deg)
           }
       }

       .animate-spin {
           animation: spin 1s linear infinite
       }

       .\!cursor-default {
           cursor: default !important
       }

       .cursor-default {
           cursor: default
       }

       .cursor-pointer {
           cursor: pointer
       }

       .list-disc {
           list-style-type: disc
       }

       .grid-cols-1 {
           grid-template-columns: repeat(1, minmax(0, 1fr))
       }

       .grid-cols-12 {
           grid-template-columns: repeat(12, minmax(0, 1fr))
       }

       .flex-row {
           flex-direction: row
       }

       .flex-col {
           flex-direction: column
       }

       .flex-wrap {
           flex-wrap: wrap
       }

       .content-center {
           align-content: center
       }

       .items-center {
           align-items: center
       }

       .items-baseline {
           align-items: baseline
       }

       .justify-end {
           justify-content: flex-end
       }

       .justify-center {
           justify-content: center
       }

       .justify-between {
           justify-content: space-between
       }

       .gap-6 {
           gap: 1.5rem
       }

       .gap-1 {
           gap: 0.25rem
       }

       .gap-4 {
           gap: 1rem
       }

       .space-x-1> :not([hidden])~ :not([hidden]) {
           --tw-space-x-reverse: 0;
           margin-right: calc(0.25rem * var(--tw-space-x-reverse));
           margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)))
       }

       .space-x-4> :not([hidden])~ :not([hidden]) {
           --tw-space-x-reverse: 0;
           margin-right: calc(1rem * var(--tw-space-x-reverse));
           margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)))
       }

       .space-x-2> :not([hidden])~ :not([hidden]) {
           --tw-space-x-reverse: 0;
           margin-right: calc(0.5rem * var(--tw-space-x-reverse));
           margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))
       }

       .overflow-auto {
           overflow: auto
       }

       .whitespace-nowrap {
           white-space: nowrap
       }

       .rounded-full {
           border-radius: 9999px
       }

       .rounded-md {
           border-radius: 0.375rem
       }

       .rounded {
           border-radius: 0.25rem
       }

       .rounded-lg {
           border-radius: 0.5rem
       }

       .rounded-\[3px\] {
           border-radius: 3px
       }

       .rounded-\[5px\] {
           border-radius: 5px
       }

       .border {
           border-width: 1px
       }

       .border-b-\[0\.5px\] {
           border-bottom-width: 0.5px
       }

       .border-solid {
           border-style: solid
       }

       .border-gray-400 {
           --tw-border-opacity: 1;
           border-color: rgb(156 163 175 / var(--tw-border-opacity, 1))
       }

       .border-\[\#dbdbdb\] {
           --tw-border-opacity: 1;
           border-color: rgb(219 219 219 / var(--tw-border-opacity, 1))
       }

       .border-slate-500 {
           --tw-border-opacity: 1;
           border-color: rgb(100 116 139 / var(--tw-border-opacity, 1))
       }

       .border-\[\#ada7a7\] {
           --tw-border-opacity: 1;
           border-color: rgb(173 167 167 / var(--tw-border-opacity, 1))
       }

       .border-gray-200 {
           --tw-border-opacity: 1;
           border-color: rgb(229 231 235 / var(--tw-border-opacity, 1))
       }

       .bg-gray-100 {
           --tw-bg-opacity: 1;
           background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1))
       }

       .bg-gray-800 {
           --tw-bg-opacity: 1;
           background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1))
       }

       .bg-white {
           --tw-bg-opacity: 1;
           background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1))
       }

       .bg-\[\#e5e7eb\] {
           --tw-bg-opacity: 1;
           background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1))
       }

       .bg-gray-200 {
           --tw-bg-opacity: 1;
           background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1))
       }

       .bg-gray-300 {
           --tw-bg-opacity: 1;
           background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1))
       }

       .bg-zinc-200 {
           --tw-bg-opacity: 1;
           background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1))
       }

       .bg-\[\#557dae\] {
           --tw-bg-opacity: 1;
           background-color: rgb(85 125 174 / var(--tw-bg-opacity, 1))
       }

       .bg-\[\#a3b8d3ab\] {
           background-color: #a3b8d3ab
       }

       .bg-\[url\(\/chart-skeletons\/tir-skel-gr\.png\)\] {
           background-image: url(/chart-skeletons/tir-skel-gr.png)
       }

       .bg-\[url\(\/chart-skeletons\/agp-skel-gr\.png\)\] {
           background-image: url(/chart-skeletons/agp-skel-gr.png)
       }

       .bg-\[url\(\/chart-skeletons\/dgp-skel-gr\.png\)\] {
           background-image: url(/chart-skeletons/dgp-skel-gr.png)
       }

       .bg-\[url\(\/chart-skeletons\/gri-skel-gr\.png\)\] {
           background-image: url(/chart-skeletons/gri-skel-gr.png)
       }

       .bg-contain {
           background-size: contain
       }

       .bg-center {
           background-position: center
       }

       .bg-no-repeat {
           background-repeat: no-repeat
       }

       .fill-blue-600 {
           fill: #2563eb
       }

       .p-1 {
           padding: 0.25rem
       }

       .p-2 {
           padding: 0.5rem
       }

       .p-5 {
           padding: 1.25rem
       }

       .p-6 {
           padding: 1.5rem
       }

       .p-4 {
           padding: 1rem
       }

       .p-8 {
           padding: 2rem
       }

       .px-2 {
           padding-left: 0.5rem;
           padding-right: 0.5rem
       }

       .px-3 {
           padding-left: 0.75rem;
           padding-right: 0.75rem
       }

       .px-4 {
           padding-left: 1rem;
           padding-right: 1rem
       }

       .py-1 {
           padding-top: 0.25rem;
           padding-bottom: 0.25rem
       }

       .py-2 {
           padding-top: 0.5rem;
           padding-bottom: 0.5rem
       }

       .py-4 {
           padding-top: 1rem;
           padding-bottom: 1rem
       }

       .py-6 {
           padding-top: 1.5rem;
           padding-bottom: 1.5rem
       }

       .pb-2 {
           padding-bottom: 0.5rem
       }

       .pl-3 {
           padding-left: 0.75rem
       }

       .pt-2 {
           padding-top: 0.5rem
       }

       .pt-4 {
           padding-top: 1rem
       }

       .pb-4 {
           padding-bottom: 1rem
       }

       .pb-\[1px\] {
           padding-bottom: 1px
       }

       .pt-0 {
           padding-top: 0px
       }

       .text-left {
           text-align: left
       }

       .text-center {
           text-align: center
       }

       .text-right {
           text-align: right
       }

       .font-mono {
           font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
       }

       .font-sans {
           font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
       }

       .text-base {
           font-size: 1rem;
           line-height: 1.5rem
       }

       .text-sm {
           font-size: 0.875rem;
           line-height: 1.25rem
       }

       .text-lg {
           font-size: 1.125rem;
           line-height: 1.75rem
       }

       .text-xs {
           font-size: 0.75rem;
           line-height: 1rem
       }

       .font-medium {
           font-weight: 500
       }

       .font-bold {
           font-weight: 700
       }

       .font-normal {
           font-weight: 400
       }

       .font-semibold {
           font-weight: 600
       }

       .uppercase {
           text-transform: uppercase
       }

       .leading-\[1\.75rem\] {
           line-height: 1.75rem
       }

       .leading-6 {
           line-height: 1.5rem
       }

       .leading-\[26px\] {
           line-height: 26px
       }

       .text-gray-300 {
           --tw-text-opacity: 1;
           color: rgb(209 213 219 / var(--tw-text-opacity, 1))
       }

       .text-gray-400 {
           --tw-text-opacity: 1;
           color: rgb(156 163 175 / var(--tw-text-opacity, 1))
       }

       .text-gray-700 {
           --tw-text-opacity: 1;
           color: rgb(55 65 81 / var(--tw-text-opacity, 1))
       }

       .text-white {
           --tw-text-opacity: 1;
           color: rgb(255 255 255 / var(--tw-text-opacity, 1))
       }

       .text-black {
           --tw-text-opacity: 1;
           color: rgb(0 0 0 / var(--tw-text-opacity, 1))
       }

       .text-gray-200 {
           --tw-text-opacity: 1;
           color: rgb(229 231 235 / var(--tw-text-opacity, 1))
       }

       .text-gray-500 {
           --tw-text-opacity: 1;
           color: rgb(107 114 128 / var(--tw-text-opacity, 1))
       }

       .text-gray-800 {
           --tw-text-opacity: 1;
           color: rgb(31 41 55 / var(--tw-text-opacity, 1))
       }

       .text-gray-900 {
           --tw-text-opacity: 1;
           color: rgb(17 24 39 / var(--tw-text-opacity, 1))
       }

       .opacity-0 {
           opacity: 0
       }

       .shadow-lg {
           --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
           --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
           box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
       }

       .shadow-sm {
           --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
           --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
           box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
       }

       .ring-1 {
           --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
           --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
           box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
       }

       .ring-black {
           --tw-ring-opacity: 1;
           --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1))
       }

       .ring-opacity-5 {
           --tw-ring-opacity: 0.05
       }

       .transition-\[opacity\] {
           transition-property: opacity;
           transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
           transition-duration: 150ms
       }

       .duration-300 {
           transition-duration: 300ms
       }

       .hover\:bg-gray-100:hover {
           --tw-bg-opacity: 1;
           background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1))
       }

       .hover\:bg-gray-700:hover {
           --tw-bg-opacity: 1;
           background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1))
       }

       .hover\:text-white:hover {
           --tw-text-opacity: 1;
           color: rgb(255 255 255 / var(--tw-text-opacity, 1))
       }

       .hover\:text-blue-600:hover {
           --tw-text-opacity: 1;
           color: rgb(37 99 235 / var(--tw-text-opacity, 1))
       }

       .hover\:text-gray-900:hover {
           --tw-text-opacity: 1;
           color: rgb(17 24 39 / var(--tw-text-opacity, 1))
       }

       .hover\:underline:hover {
           -webkit-text-decoration-line: underline;
           text-decoration-line: underline
       }

       .focus\:outline-none:focus {
           outline: 2px solid transparent;
           outline-offset: 2px
       }

       .focus\:ring-2:focus {
           --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
           --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
           box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
       }

       .focus\:ring-white:focus {
           --tw-ring-opacity: 1;
           --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1))
       }

       .focus\:ring-offset-2:focus {
           --tw-ring-offset-width: 2px
       }

       .focus\:ring-offset-gray-800:focus {
           --tw-ring-offset-color: #1f2937
       }

       .group:hover .group-hover\:visible {
           visibility: visible
       }

       .group:hover .group-hover\:block {
           display: block
       }

       .group:hover .group-hover\:opacity-100 {
           opacity: 1
       }

       @media (min-width: 640px) {
           .sm\:mx-auto {
               margin-left: auto;
               margin-right: auto
           }

           .sm\:mt-0 {
               margin-top: 0px
           }

           .sm\:flex {
               display: flex
           }

           .sm\:items-center {
               align-items: center
           }

           .sm\:justify-center {
               justify-content: center
           }

           .sm\:justify-between {
               justify-content: space-between
           }

           .sm\:px-6 {
               padding-left: 1.5rem;
               padding-right: 1.5rem
           }

           .sm\:text-center {
               text-align: center
           }
       }

       @media (min-width: 768px) {
           .md\:col-span-4 {
               grid-column: span 4 / span 4
           }

           .md\:col-span-8 {
               grid-column: span 8 / span 8
           }

           .md\:ms-2 {
               margin-inline-start: 0.5rem
           }

           .md\:grid-cols-1 {
               grid-template-columns: repeat(1, minmax(0, 1fr))
           }

           .md\:space-x-2> :not([hidden])~ :not([hidden]) {
               --tw-space-x-reverse: 0;
               margin-right: calc(0.5rem * var(--tw-space-x-reverse));
               margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))
           }
       }

       @media (min-width: 1024px) {
           .lg\:col-span-10 {
               grid-column: span 10 / span 10
           }

           .lg\:col-span-2 {
               grid-column: span 2 / span 2
           }

           .lg\:my-8 {
               margin-top: 2rem;
               margin-bottom: 2rem
           }

           .lg\:block {
               display: block
           }

           .lg\:hidden {
               display: none
           }

           .lg\:grid-cols-2 {
               grid-template-columns: repeat(2, minmax(0, 1fr))
           }

           .lg\:px-8 {
               padding-left: 2rem;
               padding-right: 2rem
           }

           .lg\:py-8 {
               padding-top: 2rem;
               padding-bottom: 2rem
           }
       }

       @media (min-width: 1280px) {
           .xl\:pt-3 {
               padding-top: 0.75rem
           }
       }

       .rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *) {
           --tw-rotate: 180deg;
           transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
       }

       .rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *)> :not([hidden])~ :not([hidden]) {
           --tw-space-x-reverse: 1
       }

       @media (prefers-color-scheme: dark) {
           .dark\:border-gray-700 {
               --tw-border-opacity: 1;
               border-color: rgb(55 65 81 / var(--tw-border-opacity, 1))
           }

           .dark\:bg-gray-900 {
               --tw-bg-opacity: 1;
               background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1))
           }

           .dark\:text-gray-400 {
               --tw-text-opacity: 1;
               color: rgb(156 163 175 / var(--tw-text-opacity, 1))
           }

           .dark\:text-gray-600 {
               --tw-text-opacity: 1;
               color: rgb(75 85 99 / var(--tw-text-opacity, 1))
           }

           .dark\:text-white {
               --tw-text-opacity: 1;
               color: rgb(255 255 255 / var(--tw-text-opacity, 1))
           }

           .dark\:hover\:text-white:hover {
               --tw-text-opacity: 1;
               color: rgb(255 255 255 / var(--tw-text-opacity, 1))
           }
       }

      