import { html, fixture, expect } from '@open-wc/testing';
import './gri-chart';

describe('GRIChart Component', () => {
    let element;

    beforeEach(async () => {
        element = await fixture(html`<gri-chart></gri-chart>`);
    });

    it('should render the component', async () => {
        expect(element).to.be.instanceOf(HTMLElement);
    })

    it('should initialize default properties', () => {
        expect(element.data).to.be.null;
        expect(element.error).to.equal(0);
        expect(element.bgImage).to.be.a('string');
    });


    it('should create scales based on data', async () => {
        element.data = { Hypoglycemia_Component: 10, Hyperglycemia_Component: 20 };
        element.createScales();
        expect(element.xScale.domain()).to.eql([0, 30]);
        expect(element.yScale.domain()).to.eql([0, 60]);
    });
   
})