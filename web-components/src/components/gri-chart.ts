import { LitElement, html, css, PropertyValues, unsafeCSS } from 'lit';
import * as d3 from 'd3';
import { customElement, property, state } from 'lit/decorators.js';
import { bgImage } from '../lib/bgImage';
import tailwindStyle from "../assets/styles.css?inline";
import customStyle from "../assets/custom.css?inline";
import { bgGriNoData } from '../lib/bgGriNoData';


const tailwindElement = unsafeCSS(tailwindStyle);
const custom = unsafeCSS(customStyle);
const current = css`
:host {
  display: block;
  width: 100%;
  height: auto;
}

svg {
  width: 100%;
  height: 100%;
}
`;

@customElement('gri-chart')
export class GRIChart extends LitElement {
    // Define properties for the chart
    @property({ type: Object }) data: any = null;
    @property({ type: Number }) error: number = 0;
    @property({ type: String }) bgImage: string = bgImage;
    @property({ type: Boolean })
    noDataFound: boolean = false;

    @state()
    private visibleNoData = false;

    // Chart dimensions and margins
    private width: number = 150;
    private height: number = 150;
    private margin = { top: 20, right: 0, bottom: 10, left: 30 };

    // Define scales
    private xScale: d3.ScaleLinear<number, number> = d3.scaleLinear();
    private yScale: d3.ScaleLinear<number, number> = d3.scaleLinear();

    static styles = [tailwindElement, custom, current];

    // Constructor to initialize properties
    constructor() {
        super();
        this.visibleNoData = this.noDataFound;
    }


    protected updated(_changedProperties: PropertyValues) {
        super.updated(_changedProperties);
        if (_changedProperties.has('noDataFound')) {
            this.visibleNoData = this.noDataFound;
            this.requestUpdate();
        }
        if (this.data) {
            this.renderChart();
        }
    }

    // Create the SVG element
    private createSVG(): any {
        const svg = d3.select(this.shadowRoot!.querySelector("#chart"));
        svg.selectAll("*").remove();
        return d3
            .select(this.shadowRoot!.querySelector('#chart'))
            .attr(
                'viewBox',
                `0 0 ${this.width + this.margin.left} ${this.height + this.margin.bottom + this.margin.top}`
            )
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .append('g')
            .attr('transform', `translate(${this.margin.left},${this.margin.top})`);
    }

    // Create the scale functions for the axes
    private createScales() {
        this.xScale = d3.scaleLinear().domain([0, 30]).range([0, this.width / 1.2]);
        this.yScale = d3.scaleLinear().domain([0, 60]).range([this.height / 1.2, 0]);
    }

    // Create axes and append them to the SVG
    private createAxes(svg: any) {
        const referencex = [0, 5, 10, 15, 20, 25, 30];
        const referencey = [0, 10, 20, 30, 40, 50, 60];
        const xAxis = d3.axisBottom(this.xScale).tickSize(0).tickValues(referencex);
        const yAxis = d3.axisLeft(this.yScale).tickSize(0).tickValues(referencey);

        svg
            .append('image')
            .attr('x', 0)
            .attr('y', 0)
            .attr('height', '125px')
            .attr('width', '125px')
            .attr('xlink:href', this.bgImage);

        svg
            .append('g')
            .attr('transform', `translate(0,${this.height / 1.2})`)
            .attr('class', 'axis')
            .call(xAxis)
            .selectAll('text')
            .style('font-size', '3px');

        svg
            .append('g')
            .attr('transform', `translate(0,0)`)
            .call(yAxis)
            .attr('class', 'axis')
            .selectAll('text')
            .style('font-size', '3px');

        svg.selectAll('.axis path').attr('stroke-width', 0.5);
    }

    // Draw lines along the axes
    private drawLines(svg: any) {
        svg
            .append('line')
            .attr('x1', 0)
            .attr('y1', 0)
            .attr('x2', this.width / 1.2)
            .attr('y2', 0)
            .attr('stroke', 'black')
            .attr('stroke-width', 0.5);

        svg
            .append('line')
            .attr('x1', this.width / 1.2)
            .attr('y1', 0)
            .attr('y2', this.height / 1.2)
            .attr('x2', this.width / 1.2)
            .attr('stroke', 'black')
            .attr('stroke-width', 0.5);
    }

    // Draw the legend for the zones
    private drawLegend(svg: any) {
        const zones = [
            { name: 'Zone A (0-20)', color: '#79BF7A' },
            { name: 'Zone B (21-40)', color: '#F6F27E' },
            { name: 'Zone C (41-60)', color: '#FFD079' },
            { name: 'Zone D (61-80)', color: '#F2787A' },
            { name: 'Zone E (81-100)', color: '#CF9390' },
        ];

        const legend = svg
            .append('g')
            .attr('transform', `translate(${this.width - 65}, 5)`);

        legend
            .append('rect')
            .attr('x', -2.5)
            .attr('y', -1)
            .attr('width', 35)
            .attr('height', zones.length * 5 + 5)
            .attr('fill', 'white')
            .attr('stroke', 'black')
            .attr('stroke-width', 0.2);

        zones.forEach((zone, i) => {
            const legendRow = legend
                .append('g')
                .attr('transform', `translate(0, ${i * 5 + 2.5})`);

            legendRow
                .append('rect')
                .attr('width', 2.5)
                .attr('height', 2.5)
                .attr('fill', zone.color);

            legendRow
                .append('text')
                .attr('x', 5)
                .attr('y', 2)
                .style('font-size', '3px')
                .text(zone.name);
        });
    }

    // Draw the reference point (circle)
    private drawReferencePoint(svg: any) {
        svg
            .append('circle')
            .attr('cx', this.xScale(this.data.Hypoglycemia_Component))
            .attr('cy', this.yScale(this.data.Hyperglycemia_Component))
            .attr('r', 1.5)
            .attr('fill', 'blue')
            .attr('stroke', 'black')
            .attr('stroke-width', 0);
    }

    // Draw the chart labels
    private drawLabels(svg: any) {
        svg
            .append('text')
            .attr('x', this.width / 2.5)
            .attr('y', this.height / 1.2 + this.margin.bottom + 8)
            .attr('text-anchor', 'middle')
            .style('font-size', '4px')
            .text('Hypoglycemia Component (%)');

        svg
            .append('text')
            .attr('x', -this.height / 2)
            .attr('y', -this.margin.left + 15)
            .attr('transform', 'rotate(-90)')
            .attr('text-anchor', 'middle')
            .style('font-size', '4px')
            .text('Hyperglycemia Component (%)');
    }

    // Main render function for the chart
    private renderChart() {
        const svg = this.createSVG();
        this.createScales();
        this.createAxes(svg);
        this.drawLines(svg);
        this.drawLegend(svg);
        this.drawReferencePoint(svg);
        this.drawLabels(svg);
    }

    // Render the component as HTML
    protected render() {
        return html`
        ${this.visibleNoData
                ? html`
           <div class="gri-chart-element-no-data-error">
            <div>
            <div class="flex justify-center z-[10] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain bg-[url('${bgGriNoData}')] h-[45rem]">
            <div class="content-center">
                <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
                <p>No data Found, Try again with another date range.</p>
                </div>
            </div>
            </div>
        </div>
        </div>
          `
                : html`
                <svg id="chart"></svg>
                `}

        `;
    }
}


