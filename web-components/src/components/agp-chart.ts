import { LitElement, html, css, PropertyValues, unsafeCSS } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import * as d3 from 'd3';
import tailwindStyle from "../assets/styles.css?inline";
import customStyle from "../assets/custom.css?inline";
import { bgAgpNoData } from '../lib/bgAgpNoData';


interface AveragedData {
    hour: string;
    avgp5: number;
    avgp25: number;
    avgp50: number;
    avgp75: number;
    avgp95: number;
}

const tailwindElement = unsafeCSS(tailwindStyle);
const custom = unsafeCSS(customStyle);
const current = css`
:host {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
}
svg {
  font-family: sans-serif;
}
.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}
.text-font-11px {
    font-size: 11px;
}
`;

@customElement('agp-chart')
export class AG<PERSON>hart extends LitElement {
    // Properties to pass data and dimensions
    @property({ type: Array }) data: Array<any> = [];
    @property({ type: Number }) width: number = 800;
    @property({ type: Number }) height: number = 400;
    @property({ type: Object }) margin: { top: number; right: number; bottom: number; left: number } = { top: 20, right: 30, bottom: 40, left: 60 };
    @property({ type: Boolean })
    noDataFound: boolean = false;

    @state()
    private visibleNoData = false;

    public chartInstance: AGPChartD3Aide | undefined;

    constructor() {
        super();
        this.visibleNoData = this.noDataFound;
    }


    static styles = [tailwindElement, custom, current];

    render() {
        return html`
        ${this.visibleNoData
                ? html`
           <div class="agp-chart-element-no-data-error">
            <div>
            <div
                class="flex justify-center z-[10] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain bg-[url('${bgAgpNoData}')] h-[35rem]">
                <div class="content-center">
                <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
                    <p>No data Found, Try again with another date range.</p>
                </div>
                </div>
            </div>
            </div>
        </div>
          `
                : html`
                <div id="agp-chart-ctr"></div>
                `}

        `;
    }

    protected updated(_changedProperties: PropertyValues) {
        super.updated(_changedProperties);
        if (_changedProperties.has('noDataFound')) {
            this.visibleNoData = this.noDataFound;
            this.requestUpdate();
        }
        this.initChart();
    }

    // Initialize the chart
    private initChart() {
        let container = this.renderRoot.querySelector('#agp-chart-ctr') as HTMLElement;
        if (container && this.data.length) {
            this.chartInstance = new AGPChartD3Aide(
                container,
                this.data,
                this.width,
                this.height,
                this.margin
            );
        }
    }
}


class AGPChartD3Aide {
    container: HTMLElement;
    data: Array<any>;
    width: number;
    height: number;
    margin: { top: number; right: number; bottom: number; left: number };
    svg: any;
    x: any;
    y: any;
    lineNormal: any;
    areaLowToHigh: any;
    areaVeryLowToVeryHigh: any;

    constructor(container: HTMLElement, data: Array<any>, width: number, height: number, margin: any) {
        this.container = container;
        this.data = data;
        this.width = width;
        this.height = height;
        this.margin = margin;

        this.initChart();
    }

    initChart() {
        this.createSVG();
        this.createScales();
        this.defineLinesAndAreas();
        this.calculateAverages();
        this.formatData();
        this.drawAreas();
        this.drawLines();
        this.addAxes();
        this.addGridlines();
        this.addReferenceAreas();
        this.addReferenceLabels();
    }

    createSVG() {
        const svg = d3.select(this.container);
        svg.selectAll('*').remove();

        this.svg = d3
            .select(this.container)
            .append('svg')
            .attr(
                'viewBox',
                `0 0 ${this.width + this.margin.left + this.margin.right} ${this.height + this.margin.top + this.margin.bottom
                }`
            )
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .append('g')
            .attr('transform', `translate(${this.margin.left},${this.margin.top})`);
    }

    createScales() {
        this.x = d3
            .scaleBand()
            .domain([...Array(24).keys()].map((d) => d.toString().padStart(2, '0')))
            .range([0, this.width])
            .padding(0);

        this.y = d3.scaleLinear().range([this.height, 0]).domain([0, 350]);
    }

    defineLinesAndAreas() {
        this.lineNormal = d3
            .line()
            .x((d: any) => this.x(d.hour) + this.x.bandwidth() / 2)
            .y((d: any) => this.y(d.avgp50))
            .curve(d3.curveCatmullRom);

        this.areaLowToHigh = d3
            .area()
            .x((d: any) => this.x(d.hour) + this.x.bandwidth() / 2)
            .y0((d: any) => this.y(d.avgp25))
            .y1((d: any) => this.y(d.avgp75))
            .curve(d3.curveCatmullRom);

        this.areaVeryLowToVeryHigh = d3
            .area()
            .x((d: any) => this.x(d.hour) + this.x.bandwidth() / 2)
            .y0((d: any) => this.y(d.avgp5))
            .y1((d: any) => this.y(d.avgp95))
            .curve(d3.curveCatmullRom);
    }



    calculateAverages(): void {
        const result: Record<string, { count: number; totalP5: number; totalP25: number; totalP50: number; totalP75: number; totalP95: number }> = {};

        this.data.forEach((entry) => {
            const { hour, p5, p25, p50, p75, p95 } = entry;

            if (!result[hour]) {
                result[hour] = {
                    count: 0,
                    totalP5: 0,
                    totalP25: 0,
                    totalP50: 0,
                    totalP75: 0,
                    totalP95: 0,
                };
            }

            result[hour].count++;
            result[hour].totalP5 += p5;
            result[hour].totalP25 += p25;
            result[hour].totalP50 += p50;
            result[hour].totalP75 += p75;
            result[hour].totalP95 += p95;
        });

        this.data = Object.keys(result).map((hour) => {
            const { count, totalP5, totalP25, totalP50, totalP75, totalP95 } = result[hour];
            return {
                hour,
                avgp5: totalP5 / count,
                avgp25: totalP25 / count,
                avgp50: totalP50 / count,
                avgp75: totalP75 / count,
                avgp95: totalP95 / count,
            } as AveragedData;
        });

        this.data.sort((a, b) => d3.ascending(a.hour, b.hour));
    }

    formatData() {
        this.data.forEach((d) => {
            d.avgp5 = +d.avgp5;
            d.avgp25 = +d.avgp25;
            d.avgp50 = +d.avgp50;
            d.avgp75 = +d.avgp75;
            d.avgp95 = +d.avgp95;
        });
    }

    drawAreas() {
        this.svg
            .append('path')
            .data([this.data])
            .attr('class', 'area')
            .attr('d', this.areaLowToHigh)
            .style('fill', 'rgb(223 173 115 / 92%)');

        this.svg
            .append('path')
            .data([this.data])
            .attr('class', 'area')
            .attr('d', this.areaVeryLowToVeryHigh)
            .style('fill', 'rgb(240 215 183 / 69%)');
    }

    drawLines() {
        this.svg
            .append('path')
            .data([this.data])
            .attr('class', 'line')
            .attr('d', this.lineNormal)
            .style('stroke', '#ce7a35')
            .style('fill', 'none')
            .style('stroke-width', 4);
    }

    addAxes() {
        const xAxis = d3
            .axisBottom(this.x)
            .tickValues(["00", "03", "06", "09", "12", "15", "18", "21", "23"])
            .tickFormat((d) => this.formatTick(d))
            .tickSize(-this.height)
            .tickSizeInner(6);

        this.svg
            .append("g")
            .attr("class", "x-axis")
            .attr("transform", `translate(0,${this.height})`)
            .call(xAxis)
            .selectAll(".tick line")
            .attr("stroke", "#ccc")
            .attr("stroke-width", "1px");

        this.svg
            .append("g")
            .attr("class", "y-axis")
            .call(d3.axisLeft(this.y).tickValues([0, 54, 70, 180, 250, 350]));
    }

    addGridlines() {
        const xGrid = d3
            .axisBottom(this.x)
            .tickValues(["00", "03", "06", "09", "12", "15", "18", "21", "23"])
            .tickSize(-this.height)
            .tickFormat(() => "");

        this.svg
            .append("g")
            .attr("class", "grid")
            .attr("transform", `translate(0,${this.height})`)
            .call(xGrid)
            .selectAll(".tick line")
            .attr("stroke", "#ddd")
            .attr("stroke-width", "1px");

        this.svg
            .append("g")
            .attr("class", "grid")
            .call(d3.axisLeft(this.y).tickSize(-this.width).tickFormat(() => ""));
    }

    addReferenceAreas() {
        const referenceArea = d3
            .area<{ hour: string }>()
            .x((d) => this.x(d.hour) + this.x.bandwidth() / 2)
            .y0(() => this.y(70))
            .y1(() => this.y(180))
            .curve(d3.curveCatmullRom);

        const referenceArea2 = d3
            .area<{ hour: string }>()
            .x((d) => this.x(d.hour) + this.x.bandwidth() / 2)
            .y0(() => this.y(180))
            .y1(() => this.y(250))
            .curve(d3.curveCatmullRom);

        this.svg
            .append("path")
            .data([this.data])
            .attr("class", "opacity-30")
            .attr("d", referenceArea)
            .style("fill", "#00800085");

        this.svg
            .append("path")
            .data([this.data])
            .attr("class", "opacity-30")
            .attr("d", referenceArea2)
            .style("fill", "#ffa5004f");
    }

    addReferenceLabels() {
        // Add "Target" label
        this.svg
            .append("text")
            .attr("x", -30)
            .attr("y", this.y(70) - 70)
            .attr("class", "font-normal text-xs")
            .text("Target")
            .style("font-weight", "bold")
            .style("text-anchor", "middle");

        // Add "Range" label
        this.svg
            .append("text")
            .attr("x", -30)
            .attr("y", this.y(70) - 50)
            .attr("class", "font-normal text-xs")
            .text("Range")
            .style("font-weight", "bold")
            .style("text-anchor", "middle");

        // Iterate over y-values and draw arrows and labels
        const yValues = [5, 25, 50, 75, 95];
        yValues.forEach((val, index) => {
            const length = this.data.length;
            const yPos = this.y(this.data[length-1][`avgp${val}`]); // Get the y-position based on data
            const arrowStartX = this.width - 15; // Start at the Y-axis
            const arrowMidX = this.width - 5; // Middle point for the sloped arrow
            const arrowEndX = this.width + 5; // End for horizontal arrow
            const labelX = this.width + 7; // X position for labels
            let slopedYPos: number = yPos; // Initialize slopedYPos with a default value

            // Alternate the direction based on index
            if (index % 5 === 0) {
                slopedYPos = yPos +25;
            } else if (index % 5 === 1) {
                slopedYPos = yPos + 15;
            } else if (index % 5 === 2) {
                slopedYPos = yPos;
            } else if (index % 5 === 3) {
                slopedYPos = yPos - 15;
            } else if (index % 5 === 4) {
                slopedYPos = yPos - 25;
            }

            // Draw sloped arrow (diagonal part)
            this.svg
                .append("line")
                .attr("x1", arrowStartX)
                .attr("x2", arrowMidX)
                .attr("y1", yPos)
                .attr("y2", slopedYPos)
                .attr("stroke", "#d3d3d3")
                .attr("stroke-width", 2);

            // Draw horizontal arrow (straight line after the slope) without arrowhead
            this.svg
                .append("line")
                .attr("x1", arrowMidX)
                .attr("x2", arrowEndX)
                .attr("y1", slopedYPos)
                .attr("y2", slopedYPos)
                .attr("stroke", "#d3d3d3")
                .attr("stroke-width", 2);

            // Add label at the end of the horizontal arrow
            this.svg
                .append("text")
                .attr("x", labelX)
                .attr("y", slopedYPos + 5)
                .attr("class", "font-normal text-font-11px")
                .text(`${val}%`)
                .style("font-weight", "bold")
                .style("text-anchor", "start");
        });
    }

    formatTick(d: d3.AxisDomain) {
        if (d === "00") return "12AM";
        if (d === "12") return "12PM";
        return d > "12" ? `${+d - 12}PM` : `${+d}AM`;
    }
}

export default AGPChart;
