import { html, fixture, expect, fireEvent } from '@open-wc/testing';
import './formula-component';

describe('FormulaComponent', () => {
    let element;

    beforeEach(async () => {
        element = await fixture(html`<formula-component></formula-component>`);
    });

    it('should render the icon', () => {
        const icon = element.shadowRoot.querySelector('.icon');
        expect(icon).to.exist;
    });

    it('should not render calculation container by default', () => {
        const container = element.shadowRoot.querySelector('.calculation-container');
        expect(container).to.be.null;
    });

    it('should toggle visibility when icon is clicked', async () => {
        const icon = element.shadowRoot.querySelector('.icon');

        icon.click();
        await element.updateComplete;

        expect(element.visible).to.be.true;

        icon.click();
        await element.updateComplete;

        expect(element.visible).to.be.false;
    });

});
