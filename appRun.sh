echo "Allow env"
direnv allow 
echo "env added"
cd udi-prime
echo "Starting Pre Server startup DB Script"
chmod +x databaseSupport.ts
./databaseSupport.ts
echo "Completed DB Script"
cd ../hub-prime
echo "Maven Clean Install"
mvn clean install
echo "Completed Maven Clean Install"
echo "Start Maven site generation"
mvn site
echo "Completed Maven Site Genration"
cd schemaSpy
echo "Generatig SchemaSpy"
chmod +x generate_schema.ts
./generate_schema.ts
echo "SchemaSpy completed"
cd ..
echo "Application About to start"
mvn spring-boot:run -Dspring-boot.run.arguments="--server.port=8080 --server.host=localhost"
