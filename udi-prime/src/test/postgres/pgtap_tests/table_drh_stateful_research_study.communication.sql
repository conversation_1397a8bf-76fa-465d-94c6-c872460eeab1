SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_research_study', 'communication',
    'Should have table drh_stateful_research_study.communication'
);

SELECT has_pk(
    'drh_stateful_research_study', 'communication',
    'Table drh_stateful_research_study.communication should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'communication'::name, ARRAY[
    'id'::name,
    'language_code'::name,
    'preferred'::name,
    'org_party_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'communication', 'id', 'Column drh_stateful_research_study.communication.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'id', 'text', 'Column drh_stateful_research_study.communication.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'communication', 'id', 'Column drh_stateful_research_study.communication.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'id', 'Column drh_stateful_research_study.communication.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'language_code', 'Column drh_stateful_research_study.communication.language_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'language_code', 'character varying(50)', 'Column drh_stateful_research_study.communication.language_code should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'language_code', 'Column drh_stateful_research_study.communication.language_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'language_code', 'Column drh_stateful_research_study.communication.language_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'preferred', 'Column drh_stateful_research_study.communication.preferred should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'preferred', 'character varying(50)', 'Column drh_stateful_research_study.communication.preferred should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'preferred', 'Column drh_stateful_research_study.communication.preferred should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'preferred', 'Column drh_stateful_research_study.communication.preferred should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'org_party_id', 'Column drh_stateful_research_study.communication.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'org_party_id', 'text', 'Column drh_stateful_research_study.communication.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'communication', 'org_party_id', 'Column drh_stateful_research_study.communication.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'org_party_id', 'Column drh_stateful_research_study.communication.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'tenant_id', 'Column drh_stateful_research_study.communication.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'tenant_id', 'text', 'Column drh_stateful_research_study.communication.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'communication', 'tenant_id', 'Column drh_stateful_research_study.communication.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'tenant_id', 'Column drh_stateful_research_study.communication.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'rec_status_id', 'Column drh_stateful_research_study.communication.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.communication.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'communication', 'rec_status_id', 'Column drh_stateful_research_study.communication.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'rec_status_id', 'Column drh_stateful_research_study.communication.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'created_at', 'Column drh_stateful_research_study.communication.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.communication.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'created_at', 'Column drh_stateful_research_study.communication.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'communication', 'created_at', 'Column drh_stateful_research_study.communication.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'communication', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.communication.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'created_by', 'Column drh_stateful_research_study.communication.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'created_by', 'text', 'Column drh_stateful_research_study.communication.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'created_by', 'Column drh_stateful_research_study.communication.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'communication', 'created_by', 'Column drh_stateful_research_study.communication.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'communication', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.communication.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'updated_at', 'Column drh_stateful_research_study.communication.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.communication.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'updated_at', 'Column drh_stateful_research_study.communication.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'updated_at', 'Column drh_stateful_research_study.communication.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'updated_by', 'Column drh_stateful_research_study.communication.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'updated_by', 'text', 'Column drh_stateful_research_study.communication.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'updated_by', 'Column drh_stateful_research_study.communication.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'updated_by', 'Column drh_stateful_research_study.communication.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'deleted_at', 'Column drh_stateful_research_study.communication.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.communication.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'deleted_at', 'Column drh_stateful_research_study.communication.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'deleted_at', 'Column drh_stateful_research_study.communication.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'communication', 'deleted_by', 'Column drh_stateful_research_study.communication.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'communication', 'deleted_by', 'text', 'Column drh_stateful_research_study.communication.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'communication', 'deleted_by', 'Column drh_stateful_research_study.communication.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'communication', 'deleted_by', 'Column drh_stateful_research_study.communication.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
