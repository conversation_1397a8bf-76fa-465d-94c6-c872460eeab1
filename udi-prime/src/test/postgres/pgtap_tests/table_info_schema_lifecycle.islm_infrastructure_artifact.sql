SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(37);

SELECT has_table(
    'info_schema_lifecycle', 'islm_infrastructure_artifact',
    'Should have table info_schema_lifecycle.islm_infrastructure_artifact'
);

SELECT has_pk(
    'info_schema_lifecycle', 'islm_infrastructure_artifact',
    'Table info_schema_lifecycle.islm_infrastructure_artifact should have a primary key'
);

SELECT columns_are('info_schema_lifecycle'::name, 'islm_infrastructure_artifact'::name, ARRAY[
    'artifact_id'::name,
    'artifact_nature'::name,
    'artifact_xml'::name,
    'artifact_json'::name,
    'artifact_text'::name,
    'created_at'::name,
    'created_by'::name,
    'elaboration'::name
]);

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_id', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_id should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_id', 'text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_id should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_id', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_id should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_id', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_id should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_nature', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_nature should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_nature', 'text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_nature should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_nature', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_nature should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_nature', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_nature should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_xml', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_xml should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_xml', 'xml', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_xml should be type xml');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_xml', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_xml should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_xml', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_xml should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_json', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_json should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_json', 'jsonb', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_json should be type jsonb');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_json', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_json should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_json', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_json should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_text should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_text', 'text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_text should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_text should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'artifact_text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.artifact_text should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_at', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_at should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_at', 'timestamp with time zone', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_at should be type timestamp with time zone');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_at', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_at should allow NULL');
SELECT col_has_default(  'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_at', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_at should have a default');
SELECT col_default_is(   'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_at', 'CURRENT_TIMESTAMP', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_at default is');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_by', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_by should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_by', 'text', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_by should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_by', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_by should allow NULL');
SELECT col_has_default(  'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_by', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_by should have a default');
SELECT col_default_is(   'info_schema_lifecycle', 'islm_infrastructure_artifact', 'created_by', 'CURRENT_USER', 'Column info_schema_lifecycle.islm_infrastructure_artifact.created_by default is');

SELECT has_column(       'info_schema_lifecycle', 'islm_infrastructure_artifact', 'elaboration', 'Column info_schema_lifecycle.islm_infrastructure_artifact.elaboration should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'elaboration', 'jsonb', 'Column info_schema_lifecycle.islm_infrastructure_artifact.elaboration should be type jsonb');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_infrastructure_artifact', 'elaboration', 'Column info_schema_lifecycle.islm_infrastructure_artifact.elaboration should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_infrastructure_artifact', 'elaboration', 'Column info_schema_lifecycle.islm_infrastructure_artifact.elaboration should not have a default');

SELECT * FROM finish();
ROLLBACK;
