SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(20);

SELECT has_table(
    'drh_stateful_master', 'study_status_master',
    'Should have table drh_stateful_master.study_status_master'
);

SELECT has_pk(
    'drh_stateful_master', 'study_status_master',
    'Table drh_stateful_master.study_status_master should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'study_status_master'::name, ARRAY[
    'code'::name,
    'display_name'::name,
    'definition'::name,
    'system_url'::name
]);

SELECT has_column(       'drh_stateful_master', 'study_status_master', 'code', 'Column drh_stateful_master.study_status_master.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_status_master', 'code', 'integer', 'Column drh_stateful_master.study_status_master.code should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'study_status_master', 'code', 'Column drh_stateful_master.study_status_master.code should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_status_master', 'code', 'Column drh_stateful_master.study_status_master.code should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_status_master', 'code', 'nextval(''drh_stateful_master.study_status_master_code_seq''::regclass)', 'Column drh_stateful_master.study_status_master.code default is');

SELECT has_column(       'drh_stateful_master', 'study_status_master', 'display_name', 'Column drh_stateful_master.study_status_master.display_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_status_master', 'display_name', 'text', 'Column drh_stateful_master.study_status_master.display_name should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_status_master', 'display_name', 'Column drh_stateful_master.study_status_master.display_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_status_master', 'display_name', 'Column drh_stateful_master.study_status_master.display_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_status_master', 'definition', 'Column drh_stateful_master.study_status_master.definition should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_status_master', 'definition', 'text', 'Column drh_stateful_master.study_status_master.definition should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_status_master', 'definition', 'Column drh_stateful_master.study_status_master.definition should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_status_master', 'definition', 'Column drh_stateful_master.study_status_master.definition should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_status_master', 'system_url', 'Column drh_stateful_master.study_status_master.system_url should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_status_master', 'system_url', 'text', 'Column drh_stateful_master.study_status_master.system_url should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_status_master', 'system_url', 'Column drh_stateful_master.study_status_master.system_url should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_status_master', 'system_url', 'Column drh_stateful_master.study_status_master.system_url should not have a default');

SELECT * FROM finish();
ROLLBACK;
