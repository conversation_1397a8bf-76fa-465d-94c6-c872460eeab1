SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(57);

SELECT has_table(
    'drh_stateful_party', 'party_relation',
    'Should have table drh_stateful_party.party_relation'
);

SELECT has_pk(
    'drh_stateful_party', 'party_relation',
    'Table drh_stateful_party.party_relation should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'party_relation'::name, ARRAY[
    'party_relation_id'::name,
    'party_id'::name,
    'related_party_id'::name,
    'relation_type_id'::name,
    'party_role_id'::name,
    'elaboration'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'activity_log'::name
]);

SELECT has_column(       'drh_stateful_party', 'party_relation', 'party_relation_id', 'Column drh_stateful_party.party_relation.party_relation_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'party_relation_id', 'text', 'Column drh_stateful_party.party_relation.party_relation_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_relation', 'party_relation_id', 'Column drh_stateful_party.party_relation.party_relation_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'party_relation_id', 'Column drh_stateful_party.party_relation.party_relation_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'party_id', 'Column drh_stateful_party.party_relation.party_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'party_id', 'text', 'Column drh_stateful_party.party_relation.party_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_relation', 'party_id', 'Column drh_stateful_party.party_relation.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'party_id', 'Column drh_stateful_party.party_relation.party_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'related_party_id', 'Column drh_stateful_party.party_relation.related_party_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'related_party_id', 'text', 'Column drh_stateful_party.party_relation.related_party_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_relation', 'related_party_id', 'Column drh_stateful_party.party_relation.related_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'related_party_id', 'Column drh_stateful_party.party_relation.related_party_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'relation_type_id', 'Column drh_stateful_party.party_relation.relation_type_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'relation_type_id', 'text', 'Column drh_stateful_party.party_relation.relation_type_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_relation', 'relation_type_id', 'Column drh_stateful_party.party_relation.relation_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'relation_type_id', 'Column drh_stateful_party.party_relation.relation_type_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'party_role_id', 'Column drh_stateful_party.party_relation.party_role_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'party_role_id', 'text', 'Column drh_stateful_party.party_relation.party_role_id should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'party_role_id', 'Column drh_stateful_party.party_relation.party_role_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'party_role_id', 'Column drh_stateful_party.party_relation.party_role_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'elaboration', 'Column drh_stateful_party.party_relation.elaboration should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'elaboration', 'jsonb', 'Column drh_stateful_party.party_relation.elaboration should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'elaboration', 'Column drh_stateful_party.party_relation.elaboration should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'elaboration', 'Column drh_stateful_party.party_relation.elaboration should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'created_at', 'Column drh_stateful_party.party_relation.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.party_relation.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'created_at', 'Column drh_stateful_party.party_relation.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_relation', 'created_at', 'Column drh_stateful_party.party_relation.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_relation', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.party_relation.created_at default is');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'created_by', 'Column drh_stateful_party.party_relation.created_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'created_by', 'text', 'Column drh_stateful_party.party_relation.created_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'created_by', 'Column drh_stateful_party.party_relation.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_relation', 'created_by', 'Column drh_stateful_party.party_relation.created_by should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_relation', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_party.party_relation.created_by default is');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'updated_at', 'Column drh_stateful_party.party_relation.updated_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_party.party_relation.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'updated_at', 'Column drh_stateful_party.party_relation.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'updated_at', 'Column drh_stateful_party.party_relation.updated_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'updated_by', 'Column drh_stateful_party.party_relation.updated_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'updated_by', 'text', 'Column drh_stateful_party.party_relation.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'updated_by', 'Column drh_stateful_party.party_relation.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'updated_by', 'Column drh_stateful_party.party_relation.updated_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'deleted_at', 'Column drh_stateful_party.party_relation.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_party.party_relation.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'deleted_at', 'Column drh_stateful_party.party_relation.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'deleted_at', 'Column drh_stateful_party.party_relation.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'deleted_by', 'Column drh_stateful_party.party_relation.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'deleted_by', 'text', 'Column drh_stateful_party.party_relation.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'deleted_by', 'Column drh_stateful_party.party_relation.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'deleted_by', 'Column drh_stateful_party.party_relation.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_relation', 'activity_log', 'Column drh_stateful_party.party_relation.activity_log should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_relation', 'activity_log', 'jsonb', 'Column drh_stateful_party.party_relation.activity_log should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party_relation', 'activity_log', 'Column drh_stateful_party.party_relation.activity_log should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_relation', 'activity_log', 'Column drh_stateful_party.party_relation.activity_log should not have a default');

SELECT * FROM finish();
ROLLBACK;
