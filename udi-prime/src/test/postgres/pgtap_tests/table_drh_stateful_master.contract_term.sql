SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(69);

SELECT has_table(
    'drh_stateful_master', 'contract_term',
    'Should have table drh_stateful_master.contract_term'
);

SELECT has_pk(
    'drh_stateful_master', 'contract_term',
    'Table drh_stateful_master.contract_term should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'contract_term'::name, ARRAY[
    'term_id'::name,
    'term_reference_id'::name,
    'contract_id'::name,
    'issued_at'::name,
    'term_period_start'::name,
    'term_period_end'::name,
    'type_code'::name,
    'sub_type_code'::name,
    'term_statement'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'contract_term', 'term_id', 'Column drh_stateful_master.contract_term.term_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'term_id', 'text', 'Column drh_stateful_master.contract_term.term_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'contract_term', 'term_id', 'Column drh_stateful_master.contract_term.term_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'term_id', 'Column drh_stateful_master.contract_term.term_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'term_reference_id', 'Column drh_stateful_master.contract_term.term_reference_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'term_reference_id', 'character varying(50)', 'Column drh_stateful_master.contract_term.term_reference_id should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'term_reference_id', 'Column drh_stateful_master.contract_term.term_reference_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'term_reference_id', 'Column drh_stateful_master.contract_term.term_reference_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'contract_id', 'Column drh_stateful_master.contract_term.contract_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'contract_id', 'character varying(50)', 'Column drh_stateful_master.contract_term.contract_id should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'contract_term', 'contract_id', 'Column drh_stateful_master.contract_term.contract_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'contract_id', 'Column drh_stateful_master.contract_term.contract_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'issued_at', 'Column drh_stateful_master.contract_term.issued_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'issued_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.issued_at should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_master', 'contract_term', 'issued_at', 'Column drh_stateful_master.contract_term.issued_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'issued_at', 'Column drh_stateful_master.contract_term.issued_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'term_period_start', 'Column drh_stateful_master.contract_term.term_period_start should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'term_period_start', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.term_period_start should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'term_period_start', 'Column drh_stateful_master.contract_term.term_period_start should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'term_period_start', 'Column drh_stateful_master.contract_term.term_period_start should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'term_period_end', 'Column drh_stateful_master.contract_term.term_period_end should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'term_period_end', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.term_period_end should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'term_period_end', 'Column drh_stateful_master.contract_term.term_period_end should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'term_period_end', 'Column drh_stateful_master.contract_term.term_period_end should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'type_code', 'Column drh_stateful_master.contract_term.type_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'type_code', 'character varying(100)', 'Column drh_stateful_master.contract_term.type_code should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'type_code', 'Column drh_stateful_master.contract_term.type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'type_code', 'Column drh_stateful_master.contract_term.type_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'sub_type_code', 'Column drh_stateful_master.contract_term.sub_type_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'sub_type_code', 'character varying(100)', 'Column drh_stateful_master.contract_term.sub_type_code should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'sub_type_code', 'Column drh_stateful_master.contract_term.sub_type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'sub_type_code', 'Column drh_stateful_master.contract_term.sub_type_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'term_statement', 'Column drh_stateful_master.contract_term.term_statement should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'term_statement', 'text', 'Column drh_stateful_master.contract_term.term_statement should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'term_statement', 'Column drh_stateful_master.contract_term.term_statement should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'term_statement', 'Column drh_stateful_master.contract_term.term_statement should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'rec_status_id', 'Column drh_stateful_master.contract_term.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'rec_status_id', 'integer', 'Column drh_stateful_master.contract_term.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'contract_term', 'rec_status_id', 'Column drh_stateful_master.contract_term.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'rec_status_id', 'Column drh_stateful_master.contract_term.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'created_at', 'Column drh_stateful_master.contract_term.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'created_at', 'Column drh_stateful_master.contract_term.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contract_term', 'created_at', 'Column drh_stateful_master.contract_term.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contract_term', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.contract_term.created_at default is');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'created_by', 'Column drh_stateful_master.contract_term.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'created_by', 'text', 'Column drh_stateful_master.contract_term.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'created_by', 'Column drh_stateful_master.contract_term.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contract_term', 'created_by', 'Column drh_stateful_master.contract_term.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contract_term', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.contract_term.created_by default is');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'updated_at', 'Column drh_stateful_master.contract_term.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'updated_at', 'Column drh_stateful_master.contract_term.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'updated_at', 'Column drh_stateful_master.contract_term.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'updated_by', 'Column drh_stateful_master.contract_term.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'updated_by', 'text', 'Column drh_stateful_master.contract_term.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'updated_by', 'Column drh_stateful_master.contract_term.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'updated_by', 'Column drh_stateful_master.contract_term.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'deleted_at', 'Column drh_stateful_master.contract_term.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_term.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'deleted_at', 'Column drh_stateful_master.contract_term.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'deleted_at', 'Column drh_stateful_master.contract_term.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_term', 'deleted_by', 'Column drh_stateful_master.contract_term.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_term', 'deleted_by', 'text', 'Column drh_stateful_master.contract_term.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_term', 'deleted_by', 'Column drh_stateful_master.contract_term.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_term', 'deleted_by', 'Column drh_stateful_master.contract_term.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
