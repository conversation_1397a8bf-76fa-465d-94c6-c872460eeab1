SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(24);

SELECT has_table(
    'drh_stateful_master', 'migration_status',
    'Should have table drh_stateful_master.migration_status'
);

SELECT has_pk(
    'drh_stateful_master', 'migration_status',
    'Table drh_stateful_master.migration_status should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'migration_status'::name, ARRAY[
    'stage_id'::name,
    'stage_name'::name,
    'stage_description'::name,
    'created_at'::name,
    'updated_at'::name
]);

SELECT has_column(       'drh_stateful_master', 'migration_status', 'stage_id', 'Column drh_stateful_master.migration_status.stage_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'migration_status', 'stage_id', 'integer', 'Column drh_stateful_master.migration_status.stage_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'migration_status', 'stage_id', 'Column drh_stateful_master.migration_status.stage_id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'migration_status', 'stage_id', 'Column drh_stateful_master.migration_status.stage_id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'migration_status', 'stage_id', 'nextval(''drh_stateful_master.migration_status_stage_id_seq''::regclass)', 'Column drh_stateful_master.migration_status.stage_id default is');

SELECT has_column(       'drh_stateful_master', 'migration_status', 'stage_name', 'Column drh_stateful_master.migration_status.stage_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'migration_status', 'stage_name', 'text', 'Column drh_stateful_master.migration_status.stage_name should be type text');
SELECT col_not_null(     'drh_stateful_master', 'migration_status', 'stage_name', 'Column drh_stateful_master.migration_status.stage_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'migration_status', 'stage_name', 'Column drh_stateful_master.migration_status.stage_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'migration_status', 'stage_description', 'Column drh_stateful_master.migration_status.stage_description should exist');
SELECT col_type_is(      'drh_stateful_master', 'migration_status', 'stage_description', 'text', 'Column drh_stateful_master.migration_status.stage_description should be type text');
SELECT col_is_null(      'drh_stateful_master', 'migration_status', 'stage_description', 'Column drh_stateful_master.migration_status.stage_description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'migration_status', 'stage_description', 'Column drh_stateful_master.migration_status.stage_description should not have a default');

SELECT has_column(       'drh_stateful_master', 'migration_status', 'created_at', 'Column drh_stateful_master.migration_status.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'migration_status', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.migration_status.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'migration_status', 'created_at', 'Column drh_stateful_master.migration_status.created_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'migration_status', 'created_at', 'Column drh_stateful_master.migration_status.created_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'migration_status', 'updated_at', 'Column drh_stateful_master.migration_status.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'migration_status', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.migration_status.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'migration_status', 'updated_at', 'Column drh_stateful_master.migration_status.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'migration_status', 'updated_at', 'Column drh_stateful_master.migration_status.updated_at should not have a default');

SELECT * FROM finish();
ROLLBACK;
