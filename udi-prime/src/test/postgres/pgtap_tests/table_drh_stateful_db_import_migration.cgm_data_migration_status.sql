SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(43);

SELECT has_table(
    'drh_stateful_db_import_migration', 'cgm_data_migration_status',
    'Should have table drh_stateful_db_import_migration.cgm_data_migration_status'
);

SELECT hasnt_pk(
    'drh_stateful_db_import_migration', 'cgm_data_migration_status',
    'Table drh_stateful_db_import_migration.cgm_data_migration_status should have a primary key'
);

SELECT columns_are('drh_stateful_db_import_migration'::name, 'cgm_data_migration_status'::name, ARRAY[
    'cgm_migrate_id'::name,
    'cgm_raw_data_id'::name,
    'db_file_id'::name,
    'study_id'::name,
    'participant_id'::name,
    'cgm_migration_status'::name,
    'migration_start_time'::name,
    'migration_end_time'::name,
    'last_updated_at'::name,
    'participant_display_id'::name
]);

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migrate_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migrate_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migrate_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migrate_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migrate_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migrate_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migrate_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migrate_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_raw_data_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_raw_data_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_raw_data_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_raw_data_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_raw_data_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_raw_data_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_raw_data_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_raw_data_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'db_file_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.db_file_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'db_file_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.db_file_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'db_file_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.db_file_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'db_file_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.db_file_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'study_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.study_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'study_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.study_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'study_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'study_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.study_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migration_status', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migration_status should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migration_status', 'integer', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migration_status should be type integer');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migration_status', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migration_status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'cgm_migration_status', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.cgm_migration_status should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_start_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_start_time should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_start_time', 'timestamp with time zone', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_start_time should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_start_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_start_time should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_start_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_start_time should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_end_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_end_time should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_end_time', 'timestamp with time zone', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_end_time should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_end_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_end_time should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'migration_end_time', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.migration_end_time should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'last_updated_at', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.last_updated_at should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'last_updated_at', 'timestamp with time zone', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.last_updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'last_updated_at', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.last_updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'last_updated_at', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.last_updated_at should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_display_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_display_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_display_id', 'text', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_display_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_display_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_display_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'cgm_data_migration_status', 'participant_display_id', 'Column drh_stateful_db_import_migration.cgm_data_migration_status.participant_display_id should not have a default');

SELECT * FROM finish();
ROLLBACK;
