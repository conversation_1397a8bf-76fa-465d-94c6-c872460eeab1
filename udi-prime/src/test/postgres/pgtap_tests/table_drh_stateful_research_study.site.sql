SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(68);

SELECT has_table(
    'drh_stateful_research_study', 'site',
    'Should have table drh_stateful_research_study.site'
);

SELECT has_pk(
    'drh_stateful_research_study', 'site',
    'Table drh_stateful_research_study.site should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'site'::name, ARRAY[
    'site_id'::name,
    'site_name'::name,
    'site_description'::name,
    'organization_id'::name,
    'address'::name,
    'telecom'::name,
    'latitude'::name,
    'longitude'::name,
    'status'::name,
    'study_id'::name,
    'participant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'updated_at'::name,
    'created_by'::name,
    'updated_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'site', 'site_id', 'Column drh_stateful_research_study.site.site_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'site_id', 'text', 'Column drh_stateful_research_study.site.site_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'site_id', 'Column drh_stateful_research_study.site.site_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'site_id', 'Column drh_stateful_research_study.site.site_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'site_name', 'Column drh_stateful_research_study.site.site_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'site_name', 'text', 'Column drh_stateful_research_study.site.site_name should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'site_name', 'Column drh_stateful_research_study.site.site_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'site_name', 'Column drh_stateful_research_study.site.site_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'site_description', 'Column drh_stateful_research_study.site.site_description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'site_description', 'text', 'Column drh_stateful_research_study.site.site_description should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'site_description', 'Column drh_stateful_research_study.site.site_description should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'site_description', 'Column drh_stateful_research_study.site.site_description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'organization_id', 'Column drh_stateful_research_study.site.organization_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'organization_id', 'text', 'Column drh_stateful_research_study.site.organization_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'organization_id', 'Column drh_stateful_research_study.site.organization_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'organization_id', 'Column drh_stateful_research_study.site.organization_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'address', 'Column drh_stateful_research_study.site.address should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'address', 'text', 'Column drh_stateful_research_study.site.address should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'address', 'Column drh_stateful_research_study.site.address should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'address', 'Column drh_stateful_research_study.site.address should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'telecom', 'Column drh_stateful_research_study.site.telecom should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'telecom', 'text', 'Column drh_stateful_research_study.site.telecom should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'telecom', 'Column drh_stateful_research_study.site.telecom should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'telecom', 'Column drh_stateful_research_study.site.telecom should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'latitude', 'Column drh_stateful_research_study.site.latitude should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'latitude', 'double precision', 'Column drh_stateful_research_study.site.latitude should be type double precision');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'latitude', 'Column drh_stateful_research_study.site.latitude should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'latitude', 'Column drh_stateful_research_study.site.latitude should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'longitude', 'Column drh_stateful_research_study.site.longitude should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'longitude', 'double precision', 'Column drh_stateful_research_study.site.longitude should be type double precision');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'longitude', 'Column drh_stateful_research_study.site.longitude should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'longitude', 'Column drh_stateful_research_study.site.longitude should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'status', 'Column drh_stateful_research_study.site.status should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'status', 'character varying(50)', 'Column drh_stateful_research_study.site.status should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'status', 'Column drh_stateful_research_study.site.status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'status', 'Column drh_stateful_research_study.site.status should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'study_id', 'Column drh_stateful_research_study.site.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'study_id', 'text', 'Column drh_stateful_research_study.site.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'study_id', 'Column drh_stateful_research_study.site.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'study_id', 'Column drh_stateful_research_study.site.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'participant_id', 'Column drh_stateful_research_study.site.participant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'participant_id', 'text', 'Column drh_stateful_research_study.site.participant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'participant_id', 'Column drh_stateful_research_study.site.participant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'participant_id', 'Column drh_stateful_research_study.site.participant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'rec_status_id', 'Column drh_stateful_research_study.site.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.site.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'site', 'rec_status_id', 'Column drh_stateful_research_study.site.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'rec_status_id', 'Column drh_stateful_research_study.site.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'created_at', 'Column drh_stateful_research_study.site.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.site.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'created_at', 'Column drh_stateful_research_study.site.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'site', 'created_at', 'Column drh_stateful_research_study.site.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'site', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.site.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'site', 'updated_at', 'Column drh_stateful_research_study.site.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.site.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'updated_at', 'Column drh_stateful_research_study.site.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'updated_at', 'Column drh_stateful_research_study.site.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'created_by', 'Column drh_stateful_research_study.site.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'created_by', 'text', 'Column drh_stateful_research_study.site.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'created_by', 'Column drh_stateful_research_study.site.created_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'created_by', 'Column drh_stateful_research_study.site.created_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'site', 'updated_by', 'Column drh_stateful_research_study.site.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'site', 'updated_by', 'text', 'Column drh_stateful_research_study.site.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'site', 'updated_by', 'Column drh_stateful_research_study.site.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'site', 'updated_by', 'Column drh_stateful_research_study.site.updated_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
