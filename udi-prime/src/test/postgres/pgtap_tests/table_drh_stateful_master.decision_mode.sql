SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_master', 'decision_mode',
    'Should have table drh_stateful_master.decision_mode'
);

SELECT has_pk(
    'drh_stateful_master', 'decision_mode',
    'Table drh_stateful_master.decision_mode should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'decision_mode'::name, ARRAY[
    'decision_mode_id'::name,
    'code'::name,
    'system'::name,
    'display'::name,
    'definition'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'decision_mode_id', 'Column drh_stateful_master.decision_mode.decision_mode_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'decision_mode_id', 'text', 'Column drh_stateful_master.decision_mode.decision_mode_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'decision_mode', 'decision_mode_id', 'Column drh_stateful_master.decision_mode.decision_mode_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'decision_mode_id', 'Column drh_stateful_master.decision_mode.decision_mode_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'code', 'Column drh_stateful_master.decision_mode.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'code', 'character varying(50)', 'Column drh_stateful_master.decision_mode.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'decision_mode', 'code', 'Column drh_stateful_master.decision_mode.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'code', 'Column drh_stateful_master.decision_mode.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'system', 'Column drh_stateful_master.decision_mode.system should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'system', 'character varying(255)', 'Column drh_stateful_master.decision_mode.system should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'system', 'Column drh_stateful_master.decision_mode.system should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'system', 'Column drh_stateful_master.decision_mode.system should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'display', 'Column drh_stateful_master.decision_mode.display should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'display', 'character varying(255)', 'Column drh_stateful_master.decision_mode.display should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'display', 'Column drh_stateful_master.decision_mode.display should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'display', 'Column drh_stateful_master.decision_mode.display should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'definition', 'Column drh_stateful_master.decision_mode.definition should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'definition', 'text', 'Column drh_stateful_master.decision_mode.definition should be type text');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'definition', 'Column drh_stateful_master.decision_mode.definition should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'definition', 'Column drh_stateful_master.decision_mode.definition should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'rec_status_id', 'Column drh_stateful_master.decision_mode.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'rec_status_id', 'integer', 'Column drh_stateful_master.decision_mode.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'decision_mode', 'rec_status_id', 'Column drh_stateful_master.decision_mode.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'rec_status_id', 'Column drh_stateful_master.decision_mode.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'created_at', 'Column drh_stateful_master.decision_mode.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.decision_mode.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'created_at', 'Column drh_stateful_master.decision_mode.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'decision_mode', 'created_at', 'Column drh_stateful_master.decision_mode.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'decision_mode', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.decision_mode.created_at default is');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'created_by', 'Column drh_stateful_master.decision_mode.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'created_by', 'text', 'Column drh_stateful_master.decision_mode.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'created_by', 'Column drh_stateful_master.decision_mode.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'decision_mode', 'created_by', 'Column drh_stateful_master.decision_mode.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'decision_mode', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.decision_mode.created_by default is');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'updated_at', 'Column drh_stateful_master.decision_mode.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.decision_mode.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'updated_at', 'Column drh_stateful_master.decision_mode.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'updated_at', 'Column drh_stateful_master.decision_mode.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'updated_by', 'Column drh_stateful_master.decision_mode.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'updated_by', 'text', 'Column drh_stateful_master.decision_mode.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'updated_by', 'Column drh_stateful_master.decision_mode.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'updated_by', 'Column drh_stateful_master.decision_mode.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'deleted_at', 'Column drh_stateful_master.decision_mode.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.decision_mode.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'deleted_at', 'Column drh_stateful_master.decision_mode.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'deleted_at', 'Column drh_stateful_master.decision_mode.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'decision_mode', 'deleted_by', 'Column drh_stateful_master.decision_mode.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'decision_mode', 'deleted_by', 'text', 'Column drh_stateful_master.decision_mode.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'decision_mode', 'deleted_by', 'Column drh_stateful_master.decision_mode.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'decision_mode', 'deleted_by', 'Column drh_stateful_master.decision_mode.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
