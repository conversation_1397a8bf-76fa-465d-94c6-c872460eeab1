SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(43);

SELECT has_table(
    'drh_stateful_ai_insights', 'filter_interaction',
    'Should have table drh_stateful_ai_insights.filter_interaction'
);

SELECT has_pk(
    'drh_stateful_ai_insights', 'filter_interaction',
    'Table drh_stateful_ai_insights.filter_interaction should have a primary key'
);

SELECT columns_are('drh_stateful_ai_insights'::name, 'filter_interaction'::name, ARRAY[
    'filter_interaction_id'::name,
    'filter_name'::name,
    'filter_type'::name,
    'filter_description'::name,
    'view_mode'::name,
    'created_by'::name,
    'updated_by'::name,
    'created_at'::name,
    'updated_at'::name,
    'filter'::name
]);

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'filter_interaction_id', 'Column drh_stateful_ai_insights.filter_interaction.filter_interaction_id should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'filter_interaction_id', 'text', 'Column drh_stateful_ai_insights.filter_interaction.filter_interaction_id should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'filter_interaction_id', 'Column drh_stateful_ai_insights.filter_interaction.filter_interaction_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'filter_interaction_id', 'Column drh_stateful_ai_insights.filter_interaction.filter_interaction_id should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'filter_name', 'Column drh_stateful_ai_insights.filter_interaction.filter_name should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'filter_name', 'text', 'Column drh_stateful_ai_insights.filter_interaction.filter_name should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'filter_name', 'Column drh_stateful_ai_insights.filter_interaction.filter_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'filter_name', 'Column drh_stateful_ai_insights.filter_interaction.filter_name should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'filter_type', 'Column drh_stateful_ai_insights.filter_interaction.filter_type should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'filter_type', 'text', 'Column drh_stateful_ai_insights.filter_interaction.filter_type should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'filter_type', 'Column drh_stateful_ai_insights.filter_interaction.filter_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'filter_type', 'Column drh_stateful_ai_insights.filter_interaction.filter_type should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'filter_description', 'Column drh_stateful_ai_insights.filter_interaction.filter_description should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'filter_description', 'text', 'Column drh_stateful_ai_insights.filter_interaction.filter_description should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'filter_description', 'Column drh_stateful_ai_insights.filter_interaction.filter_description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'filter_description', 'Column drh_stateful_ai_insights.filter_interaction.filter_description should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'view_mode', 'Column drh_stateful_ai_insights.filter_interaction.view_mode should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'view_mode', 'text', 'Column drh_stateful_ai_insights.filter_interaction.view_mode should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'view_mode', 'Column drh_stateful_ai_insights.filter_interaction.view_mode should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'view_mode', 'Column drh_stateful_ai_insights.filter_interaction.view_mode should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'created_by', 'Column drh_stateful_ai_insights.filter_interaction.created_by should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'created_by', 'text', 'Column drh_stateful_ai_insights.filter_interaction.created_by should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'created_by', 'Column drh_stateful_ai_insights.filter_interaction.created_by should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'created_by', 'Column drh_stateful_ai_insights.filter_interaction.created_by should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'updated_by', 'Column drh_stateful_ai_insights.filter_interaction.updated_by should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'updated_by', 'text', 'Column drh_stateful_ai_insights.filter_interaction.updated_by should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'updated_by', 'Column drh_stateful_ai_insights.filter_interaction.updated_by should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'updated_by', 'Column drh_stateful_ai_insights.filter_interaction.updated_by should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'created_at', 'Column drh_stateful_ai_insights.filter_interaction.created_at should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'created_at', 'text', 'Column drh_stateful_ai_insights.filter_interaction.created_at should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'created_at', 'Column drh_stateful_ai_insights.filter_interaction.created_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'created_at', 'Column drh_stateful_ai_insights.filter_interaction.created_at should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'updated_at', 'Column drh_stateful_ai_insights.filter_interaction.updated_at should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'updated_at', 'text', 'Column drh_stateful_ai_insights.filter_interaction.updated_at should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'updated_at', 'Column drh_stateful_ai_insights.filter_interaction.updated_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'updated_at', 'Column drh_stateful_ai_insights.filter_interaction.updated_at should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'filter_interaction', 'filter', 'Column drh_stateful_ai_insights.filter_interaction.filter should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'filter_interaction', 'filter', 'text', 'Column drh_stateful_ai_insights.filter_interaction.filter should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'filter_interaction', 'filter', 'Column drh_stateful_ai_insights.filter_interaction.filter should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'filter_interaction', 'filter', 'Column drh_stateful_ai_insights.filter_interaction.filter should not have a default');

SELECT * FROM finish();
ROLLBACK;
