SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(69);

SELECT has_table(
    'drh_stateful_research_study', 'term_offer',
    'Should have table drh_stateful_research_study.term_offer'
);

SELECT has_pk(
    'drh_stateful_research_study', 'term_offer',
    'Table drh_stateful_research_study.term_offer should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'term_offer'::name, ARRAY[
    'offer_id'::name,
    'term_id'::name,
    'term_offer_desc'::name,
    'decision_mode'::name,
    'topic_reference'::name,
    'type_system'::name,
    'type_code'::name,
    'decision_type_id'::name,
    'decision_mode_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'offer_id', 'Column drh_stateful_research_study.term_offer.offer_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'offer_id', 'text', 'Column drh_stateful_research_study.term_offer.offer_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer', 'offer_id', 'Column drh_stateful_research_study.term_offer.offer_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'offer_id', 'Column drh_stateful_research_study.term_offer.offer_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'term_id', 'Column drh_stateful_research_study.term_offer.term_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'term_id', 'text', 'Column drh_stateful_research_study.term_offer.term_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer', 'term_id', 'Column drh_stateful_research_study.term_offer.term_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'term_id', 'Column drh_stateful_research_study.term_offer.term_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'term_offer_desc', 'Column drh_stateful_research_study.term_offer.term_offer_desc should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'term_offer_desc', 'text', 'Column drh_stateful_research_study.term_offer.term_offer_desc should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'term_offer_desc', 'Column drh_stateful_research_study.term_offer.term_offer_desc should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'term_offer_desc', 'Column drh_stateful_research_study.term_offer.term_offer_desc should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'decision_mode', 'Column drh_stateful_research_study.term_offer.decision_mode should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'decision_mode', 'character varying(100)', 'Column drh_stateful_research_study.term_offer.decision_mode should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'decision_mode', 'Column drh_stateful_research_study.term_offer.decision_mode should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'decision_mode', 'Column drh_stateful_research_study.term_offer.decision_mode should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'topic_reference', 'Column drh_stateful_research_study.term_offer.topic_reference should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'topic_reference', 'character varying(255)', 'Column drh_stateful_research_study.term_offer.topic_reference should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'topic_reference', 'Column drh_stateful_research_study.term_offer.topic_reference should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'topic_reference', 'Column drh_stateful_research_study.term_offer.topic_reference should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'type_system', 'Column drh_stateful_research_study.term_offer.type_system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'type_system', 'character varying(255)', 'Column drh_stateful_research_study.term_offer.type_system should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'type_system', 'Column drh_stateful_research_study.term_offer.type_system should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'type_system', 'Column drh_stateful_research_study.term_offer.type_system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'type_code', 'Column drh_stateful_research_study.term_offer.type_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'type_code', 'character varying(100)', 'Column drh_stateful_research_study.term_offer.type_code should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'type_code', 'Column drh_stateful_research_study.term_offer.type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'type_code', 'Column drh_stateful_research_study.term_offer.type_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'decision_type_id', 'Column drh_stateful_research_study.term_offer.decision_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'decision_type_id', 'text', 'Column drh_stateful_research_study.term_offer.decision_type_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer', 'decision_type_id', 'Column drh_stateful_research_study.term_offer.decision_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'decision_type_id', 'Column drh_stateful_research_study.term_offer.decision_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'decision_mode_id', 'Column drh_stateful_research_study.term_offer.decision_mode_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'decision_mode_id', 'text', 'Column drh_stateful_research_study.term_offer.decision_mode_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer', 'decision_mode_id', 'Column drh_stateful_research_study.term_offer.decision_mode_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'decision_mode_id', 'Column drh_stateful_research_study.term_offer.decision_mode_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'rec_status_id', 'Column drh_stateful_research_study.term_offer.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.term_offer.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer', 'rec_status_id', 'Column drh_stateful_research_study.term_offer.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'rec_status_id', 'Column drh_stateful_research_study.term_offer.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'created_at', 'Column drh_stateful_research_study.term_offer.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'created_at', 'Column drh_stateful_research_study.term_offer.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'term_offer', 'created_at', 'Column drh_stateful_research_study.term_offer.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'term_offer', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.term_offer.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'created_by', 'Column drh_stateful_research_study.term_offer.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'created_by', 'text', 'Column drh_stateful_research_study.term_offer.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'created_by', 'Column drh_stateful_research_study.term_offer.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'term_offer', 'created_by', 'Column drh_stateful_research_study.term_offer.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'term_offer', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.term_offer.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'updated_at', 'Column drh_stateful_research_study.term_offer.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'updated_at', 'Column drh_stateful_research_study.term_offer.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'updated_at', 'Column drh_stateful_research_study.term_offer.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'updated_by', 'Column drh_stateful_research_study.term_offer.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'updated_by', 'text', 'Column drh_stateful_research_study.term_offer.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'updated_by', 'Column drh_stateful_research_study.term_offer.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'updated_by', 'Column drh_stateful_research_study.term_offer.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'deleted_at', 'Column drh_stateful_research_study.term_offer.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'deleted_at', 'Column drh_stateful_research_study.term_offer.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'deleted_at', 'Column drh_stateful_research_study.term_offer.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer', 'deleted_by', 'Column drh_stateful_research_study.term_offer.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer', 'deleted_by', 'text', 'Column drh_stateful_research_study.term_offer.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer', 'deleted_by', 'Column drh_stateful_research_study.term_offer.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer', 'deleted_by', 'Column drh_stateful_research_study.term_offer.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
