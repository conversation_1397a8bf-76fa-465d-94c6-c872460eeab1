SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_research_study', 'study_collaboration',
    'Should have table drh_stateful_research_study.study_collaboration'
);

SELECT has_pk(
    'drh_stateful_research_study', 'study_collaboration',
    'Table drh_stateful_research_study.study_collaboration should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'study_collaboration'::name, ARRAY[
    'collab_id'::name,
    'study_id'::name,
    'user_id'::name,
    'access_level'::name,
    'shared_at'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'collab_id', 'Column drh_stateful_research_study.study_collaboration.collab_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'collab_id', 'text', 'Column drh_stateful_research_study.study_collaboration.collab_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_collaboration', 'collab_id', 'Column drh_stateful_research_study.study_collaboration.collab_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'collab_id', 'Column drh_stateful_research_study.study_collaboration.collab_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'study_id', 'Column drh_stateful_research_study.study_collaboration.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'study_id', 'text', 'Column drh_stateful_research_study.study_collaboration.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_collaboration', 'study_id', 'Column drh_stateful_research_study.study_collaboration.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'study_id', 'Column drh_stateful_research_study.study_collaboration.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'user_id', 'Column drh_stateful_research_study.study_collaboration.user_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'user_id', 'text', 'Column drh_stateful_research_study.study_collaboration.user_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'user_id', 'Column drh_stateful_research_study.study_collaboration.user_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'user_id', 'Column drh_stateful_research_study.study_collaboration.user_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'access_level', 'Column drh_stateful_research_study.study_collaboration.access_level should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'access_level', 'character varying(20)', 'Column drh_stateful_research_study.study_collaboration.access_level should be type character varying(20)');
SELECT col_not_null(     'drh_stateful_research_study', 'study_collaboration', 'access_level', 'Column drh_stateful_research_study.study_collaboration.access_level should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'access_level', 'Column drh_stateful_research_study.study_collaboration.access_level should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'shared_at', 'Column drh_stateful_research_study.study_collaboration.shared_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'shared_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_collaboration.shared_at should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_research_study', 'study_collaboration', 'shared_at', 'Column drh_stateful_research_study.study_collaboration.shared_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'shared_at', 'Column drh_stateful_research_study.study_collaboration.shared_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'rec_status_id', 'Column drh_stateful_research_study.study_collaboration.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.study_collaboration.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'study_collaboration', 'rec_status_id', 'Column drh_stateful_research_study.study_collaboration.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'rec_status_id', 'Column drh_stateful_research_study.study_collaboration.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'created_at', 'Column drh_stateful_research_study.study_collaboration.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_collaboration.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'created_at', 'Column drh_stateful_research_study.study_collaboration.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_collaboration', 'created_at', 'Column drh_stateful_research_study.study_collaboration.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_collaboration', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.study_collaboration.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'created_by', 'Column drh_stateful_research_study.study_collaboration.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'created_by', 'text', 'Column drh_stateful_research_study.study_collaboration.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'created_by', 'Column drh_stateful_research_study.study_collaboration.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_collaboration', 'created_by', 'Column drh_stateful_research_study.study_collaboration.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_collaboration', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.study_collaboration.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'updated_at', 'Column drh_stateful_research_study.study_collaboration.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_collaboration.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'updated_at', 'Column drh_stateful_research_study.study_collaboration.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'updated_at', 'Column drh_stateful_research_study.study_collaboration.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'updated_by', 'Column drh_stateful_research_study.study_collaboration.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'updated_by', 'text', 'Column drh_stateful_research_study.study_collaboration.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'updated_by', 'Column drh_stateful_research_study.study_collaboration.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'updated_by', 'Column drh_stateful_research_study.study_collaboration.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'deleted_at', 'Column drh_stateful_research_study.study_collaboration.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_collaboration.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'deleted_at', 'Column drh_stateful_research_study.study_collaboration.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'deleted_at', 'Column drh_stateful_research_study.study_collaboration.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_collaboration', 'deleted_by', 'Column drh_stateful_research_study.study_collaboration.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_collaboration', 'deleted_by', 'text', 'Column drh_stateful_research_study.study_collaboration.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_collaboration', 'deleted_by', 'Column drh_stateful_research_study.study_collaboration.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_collaboration', 'deleted_by', 'Column drh_stateful_research_study.study_collaboration.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
