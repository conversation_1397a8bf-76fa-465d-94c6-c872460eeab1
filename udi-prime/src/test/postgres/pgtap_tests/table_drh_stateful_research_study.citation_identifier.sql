SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_research_study', 'citation_identifier',
    'Should have table drh_stateful_research_study.citation_identifier'
);

SELECT has_pk(
    'drh_stateful_research_study', 'citation_identifier',
    'Table drh_stateful_research_study.citation_identifier should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'citation_identifier'::name, ARRAY[
    'id'::name,
    'citation_id'::name,
    'identifier_system'::name,
    'identifier_value'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'id', 'Column drh_stateful_research_study.citation_identifier.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'id', 'text', 'Column drh_stateful_research_study.citation_identifier.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_identifier', 'id', 'Column drh_stateful_research_study.citation_identifier.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'id', 'Column drh_stateful_research_study.citation_identifier.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'citation_id', 'Column drh_stateful_research_study.citation_identifier.citation_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'citation_id', 'integer', 'Column drh_stateful_research_study.citation_identifier.citation_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_identifier', 'citation_id', 'Column drh_stateful_research_study.citation_identifier.citation_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'citation_id', 'Column drh_stateful_research_study.citation_identifier.citation_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'identifier_system', 'Column drh_stateful_research_study.citation_identifier.identifier_system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'identifier_system', 'text', 'Column drh_stateful_research_study.citation_identifier.identifier_system should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_identifier', 'identifier_system', 'Column drh_stateful_research_study.citation_identifier.identifier_system should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'identifier_system', 'Column drh_stateful_research_study.citation_identifier.identifier_system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'identifier_value', 'Column drh_stateful_research_study.citation_identifier.identifier_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'identifier_value', 'text', 'Column drh_stateful_research_study.citation_identifier.identifier_value should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_identifier', 'identifier_value', 'Column drh_stateful_research_study.citation_identifier.identifier_value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'identifier_value', 'Column drh_stateful_research_study.citation_identifier.identifier_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'rec_status_id', 'Column drh_stateful_research_study.citation_identifier.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.citation_identifier.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_identifier', 'rec_status_id', 'Column drh_stateful_research_study.citation_identifier.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'rec_status_id', 'Column drh_stateful_research_study.citation_identifier.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'created_at', 'Column drh_stateful_research_study.citation_identifier.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_identifier.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'created_at', 'Column drh_stateful_research_study.citation_identifier.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation_identifier', 'created_at', 'Column drh_stateful_research_study.citation_identifier.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation_identifier', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.citation_identifier.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'created_by', 'Column drh_stateful_research_study.citation_identifier.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'created_by', 'text', 'Column drh_stateful_research_study.citation_identifier.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'created_by', 'Column drh_stateful_research_study.citation_identifier.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation_identifier', 'created_by', 'Column drh_stateful_research_study.citation_identifier.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation_identifier', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.citation_identifier.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'updated_at', 'Column drh_stateful_research_study.citation_identifier.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_identifier.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'updated_at', 'Column drh_stateful_research_study.citation_identifier.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'updated_at', 'Column drh_stateful_research_study.citation_identifier.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'updated_by', 'Column drh_stateful_research_study.citation_identifier.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'updated_by', 'text', 'Column drh_stateful_research_study.citation_identifier.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'updated_by', 'Column drh_stateful_research_study.citation_identifier.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'updated_by', 'Column drh_stateful_research_study.citation_identifier.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'deleted_at', 'Column drh_stateful_research_study.citation_identifier.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_identifier.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'deleted_at', 'Column drh_stateful_research_study.citation_identifier.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'deleted_at', 'Column drh_stateful_research_study.citation_identifier.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_identifier', 'deleted_by', 'Column drh_stateful_research_study.citation_identifier.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_identifier', 'deleted_by', 'text', 'Column drh_stateful_research_study.citation_identifier.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_identifier', 'deleted_by', 'Column drh_stateful_research_study.citation_identifier.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_identifier', 'deleted_by', 'Column drh_stateful_research_study.citation_identifier.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
