SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(81);

SELECT has_table(
    'drh_stateful_research_study', 'address',
    'Should have table drh_stateful_research_study.address'
);

SELECT has_pk(
    'drh_stateful_research_study', 'address',
    'Table drh_stateful_research_study.address should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'address'::name, ARRAY[
    'id'::name,
    'org_party_id'::name,
    'address_type'::name,
    'line1'::name,
    'line2'::name,
    'city'::name,
    'state'::name,
    'postal_code'::name,
    'country'::name,
    'tenant_id'::name,
    'contact_point_address_type_id'::name,
    'contact_point_address_use_type_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'address', 'id', 'Column drh_stateful_research_study.address.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'id', 'text', 'Column drh_stateful_research_study.address.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'id', 'Column drh_stateful_research_study.address.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'id', 'Column drh_stateful_research_study.address.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'org_party_id', 'Column drh_stateful_research_study.address.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'org_party_id', 'text', 'Column drh_stateful_research_study.address.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'org_party_id', 'Column drh_stateful_research_study.address.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'org_party_id', 'Column drh_stateful_research_study.address.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'address_type', 'Column drh_stateful_research_study.address.address_type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'address_type', 'character varying(50)', 'Column drh_stateful_research_study.address.address_type should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'address_type', 'Column drh_stateful_research_study.address.address_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'address_type', 'Column drh_stateful_research_study.address.address_type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'line1', 'Column drh_stateful_research_study.address.line1 should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'line1', 'character varying(255)', 'Column drh_stateful_research_study.address.line1 should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'line1', 'Column drh_stateful_research_study.address.line1 should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'line1', 'Column drh_stateful_research_study.address.line1 should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'line2', 'Column drh_stateful_research_study.address.line2 should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'line2', 'character varying(255)', 'Column drh_stateful_research_study.address.line2 should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'line2', 'Column drh_stateful_research_study.address.line2 should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'line2', 'Column drh_stateful_research_study.address.line2 should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'city', 'Column drh_stateful_research_study.address.city should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'city', 'character varying(100)', 'Column drh_stateful_research_study.address.city should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'city', 'Column drh_stateful_research_study.address.city should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'city', 'Column drh_stateful_research_study.address.city should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'state', 'Column drh_stateful_research_study.address.state should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'state', 'character varying(100)', 'Column drh_stateful_research_study.address.state should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'state', 'Column drh_stateful_research_study.address.state should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'state', 'Column drh_stateful_research_study.address.state should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'postal_code', 'Column drh_stateful_research_study.address.postal_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'postal_code', 'character varying(20)', 'Column drh_stateful_research_study.address.postal_code should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'postal_code', 'Column drh_stateful_research_study.address.postal_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'postal_code', 'Column drh_stateful_research_study.address.postal_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'country', 'Column drh_stateful_research_study.address.country should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'country', 'character varying(100)', 'Column drh_stateful_research_study.address.country should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'country', 'Column drh_stateful_research_study.address.country should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'country', 'Column drh_stateful_research_study.address.country should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'tenant_id', 'Column drh_stateful_research_study.address.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'tenant_id', 'text', 'Column drh_stateful_research_study.address.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'tenant_id', 'Column drh_stateful_research_study.address.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'tenant_id', 'Column drh_stateful_research_study.address.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'contact_point_address_type_id', 'Column drh_stateful_research_study.address.contact_point_address_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'contact_point_address_type_id', 'text', 'Column drh_stateful_research_study.address.contact_point_address_type_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'contact_point_address_type_id', 'Column drh_stateful_research_study.address.contact_point_address_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'contact_point_address_type_id', 'Column drh_stateful_research_study.address.contact_point_address_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'contact_point_address_use_type_id', 'Column drh_stateful_research_study.address.contact_point_address_use_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'contact_point_address_use_type_id', 'text', 'Column drh_stateful_research_study.address.contact_point_address_use_type_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'contact_point_address_use_type_id', 'Column drh_stateful_research_study.address.contact_point_address_use_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'contact_point_address_use_type_id', 'Column drh_stateful_research_study.address.contact_point_address_use_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'rec_status_id', 'Column drh_stateful_research_study.address.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.address.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'address', 'rec_status_id', 'Column drh_stateful_research_study.address.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'rec_status_id', 'Column drh_stateful_research_study.address.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'created_at', 'Column drh_stateful_research_study.address.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.address.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'created_at', 'Column drh_stateful_research_study.address.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'address', 'created_at', 'Column drh_stateful_research_study.address.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'address', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.address.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'address', 'created_by', 'Column drh_stateful_research_study.address.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'created_by', 'text', 'Column drh_stateful_research_study.address.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'created_by', 'Column drh_stateful_research_study.address.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'address', 'created_by', 'Column drh_stateful_research_study.address.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'address', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.address.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'address', 'updated_at', 'Column drh_stateful_research_study.address.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.address.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'updated_at', 'Column drh_stateful_research_study.address.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'updated_at', 'Column drh_stateful_research_study.address.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'updated_by', 'Column drh_stateful_research_study.address.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'updated_by', 'text', 'Column drh_stateful_research_study.address.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'updated_by', 'Column drh_stateful_research_study.address.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'updated_by', 'Column drh_stateful_research_study.address.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'deleted_at', 'Column drh_stateful_research_study.address.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.address.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'deleted_at', 'Column drh_stateful_research_study.address.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'deleted_at', 'Column drh_stateful_research_study.address.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'address', 'deleted_by', 'Column drh_stateful_research_study.address.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'address', 'deleted_by', 'text', 'Column drh_stateful_research_study.address.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'address', 'deleted_by', 'Column drh_stateful_research_study.address.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'address', 'deleted_by', 'Column drh_stateful_research_study.address.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
