SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(45);

SELECT has_table(
    'drh_stateful_party', 'party_role',
    'Should have table drh_stateful_party.party_role'
);

SELECT has_pk(
    'drh_stateful_party', 'party_role',
    'Table drh_stateful_party.party_role should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'party_role'::name, ARRAY[
    'party_role_id'::name,
    'code'::name,
    'value'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'activity_log'::name
]);

SELECT has_column(       'drh_stateful_party', 'party_role', 'party_role_id', 'Column drh_stateful_party.party_role.party_role_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'party_role_id', 'text', 'Column drh_stateful_party.party_role.party_role_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_role', 'party_role_id', 'Column drh_stateful_party.party_role.party_role_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'party_role_id', 'Column drh_stateful_party.party_role.party_role_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'code', 'Column drh_stateful_party.party_role.code should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'code', 'text', 'Column drh_stateful_party.party_role.code should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_role', 'code', 'Column drh_stateful_party.party_role.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'code', 'Column drh_stateful_party.party_role.code should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'value', 'Column drh_stateful_party.party_role.value should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'value', 'text', 'Column drh_stateful_party.party_role.value should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_role', 'value', 'Column drh_stateful_party.party_role.value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'value', 'Column drh_stateful_party.party_role.value should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'created_at', 'Column drh_stateful_party.party_role.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.party_role.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'created_at', 'Column drh_stateful_party.party_role.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_role', 'created_at', 'Column drh_stateful_party.party_role.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_role', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.party_role.created_at default is');

SELECT has_column(       'drh_stateful_party', 'party_role', 'created_by', 'Column drh_stateful_party.party_role.created_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'created_by', 'text', 'Column drh_stateful_party.party_role.created_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'created_by', 'Column drh_stateful_party.party_role.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_role', 'created_by', 'Column drh_stateful_party.party_role.created_by should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_role', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_party.party_role.created_by default is');

SELECT has_column(       'drh_stateful_party', 'party_role', 'updated_at', 'Column drh_stateful_party.party_role.updated_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_party.party_role.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'updated_at', 'Column drh_stateful_party.party_role.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'updated_at', 'Column drh_stateful_party.party_role.updated_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'updated_by', 'Column drh_stateful_party.party_role.updated_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'updated_by', 'text', 'Column drh_stateful_party.party_role.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'updated_by', 'Column drh_stateful_party.party_role.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'updated_by', 'Column drh_stateful_party.party_role.updated_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'deleted_at', 'Column drh_stateful_party.party_role.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_party.party_role.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'deleted_at', 'Column drh_stateful_party.party_role.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'deleted_at', 'Column drh_stateful_party.party_role.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'deleted_by', 'Column drh_stateful_party.party_role.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'deleted_by', 'text', 'Column drh_stateful_party.party_role.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'deleted_by', 'Column drh_stateful_party.party_role.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'deleted_by', 'Column drh_stateful_party.party_role.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_role', 'activity_log', 'Column drh_stateful_party.party_role.activity_log should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_role', 'activity_log', 'jsonb', 'Column drh_stateful_party.party_role.activity_log should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party_role', 'activity_log', 'Column drh_stateful_party.party_role.activity_log should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_role', 'activity_log', 'Column drh_stateful_party.party_role.activity_log should not have a default');

SELECT * FROM finish();
ROLLBACK;
