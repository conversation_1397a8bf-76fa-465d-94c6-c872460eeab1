SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(12);

SELECT has_table(
    'drh_stateful_master', 'accepted_file_formats',
    'Should have table drh_stateful_master.accepted_file_formats'
);

SELECT has_pk(
    'drh_stateful_master', 'accepted_file_formats',
    'Table drh_stateful_master.accepted_file_formats should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'accepted_file_formats'::name, ARRAY[
    'accepted_file_format_id'::name,
    'title'::name
]);

SELECT has_column(       'drh_stateful_master', 'accepted_file_formats', 'accepted_file_format_id', 'Column drh_stateful_master.accepted_file_formats.accepted_file_format_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'accepted_file_formats', 'accepted_file_format_id', 'integer', 'Column drh_stateful_master.accepted_file_formats.accepted_file_format_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'accepted_file_formats', 'accepted_file_format_id', 'Column drh_stateful_master.accepted_file_formats.accepted_file_format_id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'accepted_file_formats', 'accepted_file_format_id', 'Column drh_stateful_master.accepted_file_formats.accepted_file_format_id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'accepted_file_formats', 'accepted_file_format_id', 'nextval(''drh_stateful_master.accepted_file_formats_accepted_file_format_id_seq''::regclass)', 'Column drh_stateful_master.accepted_file_formats.accepted_file_format_id default is');

SELECT has_column(       'drh_stateful_master', 'accepted_file_formats', 'title', 'Column drh_stateful_master.accepted_file_formats.title should exist');
SELECT col_type_is(      'drh_stateful_master', 'accepted_file_formats', 'title', 'text', 'Column drh_stateful_master.accepted_file_formats.title should be type text');
SELECT col_not_null(     'drh_stateful_master', 'accepted_file_formats', 'title', 'Column drh_stateful_master.accepted_file_formats.title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'accepted_file_formats', 'title', 'Column drh_stateful_master.accepted_file_formats.title should not have a default');

SELECT * FROM finish();
ROLLBACK;
