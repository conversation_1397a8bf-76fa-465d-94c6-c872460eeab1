SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(65);

SELECT has_table(
    'drh_stateful_research_study', 'study_consent',
    'Should have table drh_stateful_research_study.study_consent'
);

SELECT has_pk(
    'drh_stateful_research_study', 'study_consent',
    'Table drh_stateful_research_study.study_consent should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'study_consent'::name, ARRAY[
    'consent_id'::name,
    'status_type_id'::name,
    'subject_reference_id'::name,
    'grantor_reference_id'::name,
    'start_date'::name,
    'end_date'::name,
    'decision_type_id'::name,
    'category_code'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'consent_id', 'Column drh_stateful_research_study.study_consent.consent_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'consent_id', 'text', 'Column drh_stateful_research_study.study_consent.consent_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_consent', 'consent_id', 'Column drh_stateful_research_study.study_consent.consent_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'consent_id', 'Column drh_stateful_research_study.study_consent.consent_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'status_type_id', 'Column drh_stateful_research_study.study_consent.status_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'status_type_id', 'text', 'Column drh_stateful_research_study.study_consent.status_type_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'status_type_id', 'Column drh_stateful_research_study.study_consent.status_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'status_type_id', 'Column drh_stateful_research_study.study_consent.status_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'subject_reference_id', 'Column drh_stateful_research_study.study_consent.subject_reference_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'subject_reference_id', 'text', 'Column drh_stateful_research_study.study_consent.subject_reference_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_consent', 'subject_reference_id', 'Column drh_stateful_research_study.study_consent.subject_reference_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'subject_reference_id', 'Column drh_stateful_research_study.study_consent.subject_reference_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'grantor_reference_id', 'Column drh_stateful_research_study.study_consent.grantor_reference_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'grantor_reference_id', 'text', 'Column drh_stateful_research_study.study_consent.grantor_reference_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_consent', 'grantor_reference_id', 'Column drh_stateful_research_study.study_consent.grantor_reference_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'grantor_reference_id', 'Column drh_stateful_research_study.study_consent.grantor_reference_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'start_date', 'Column drh_stateful_research_study.study_consent.start_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'start_date', 'date', 'Column drh_stateful_research_study.study_consent.start_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'start_date', 'Column drh_stateful_research_study.study_consent.start_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'start_date', 'Column drh_stateful_research_study.study_consent.start_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'end_date', 'Column drh_stateful_research_study.study_consent.end_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'end_date', 'date', 'Column drh_stateful_research_study.study_consent.end_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'end_date', 'Column drh_stateful_research_study.study_consent.end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'end_date', 'Column drh_stateful_research_study.study_consent.end_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'decision_type_id', 'Column drh_stateful_research_study.study_consent.decision_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'decision_type_id', 'integer', 'Column drh_stateful_research_study.study_consent.decision_type_id should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'decision_type_id', 'Column drh_stateful_research_study.study_consent.decision_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'decision_type_id', 'Column drh_stateful_research_study.study_consent.decision_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'category_code', 'Column drh_stateful_research_study.study_consent.category_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'category_code', 'integer', 'Column drh_stateful_research_study.study_consent.category_code should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'category_code', 'Column drh_stateful_research_study.study_consent.category_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'category_code', 'Column drh_stateful_research_study.study_consent.category_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'rec_status_id', 'Column drh_stateful_research_study.study_consent.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.study_consent.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'study_consent', 'rec_status_id', 'Column drh_stateful_research_study.study_consent.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'rec_status_id', 'Column drh_stateful_research_study.study_consent.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'created_at', 'Column drh_stateful_research_study.study_consent.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_consent.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'created_at', 'Column drh_stateful_research_study.study_consent.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_consent', 'created_at', 'Column drh_stateful_research_study.study_consent.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_consent', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.study_consent.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'created_by', 'Column drh_stateful_research_study.study_consent.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'created_by', 'text', 'Column drh_stateful_research_study.study_consent.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'created_by', 'Column drh_stateful_research_study.study_consent.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_consent', 'created_by', 'Column drh_stateful_research_study.study_consent.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_consent', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.study_consent.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'updated_at', 'Column drh_stateful_research_study.study_consent.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_consent.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'updated_at', 'Column drh_stateful_research_study.study_consent.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'updated_at', 'Column drh_stateful_research_study.study_consent.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'updated_by', 'Column drh_stateful_research_study.study_consent.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'updated_by', 'text', 'Column drh_stateful_research_study.study_consent.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'updated_by', 'Column drh_stateful_research_study.study_consent.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'updated_by', 'Column drh_stateful_research_study.study_consent.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'deleted_at', 'Column drh_stateful_research_study.study_consent.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_consent.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'deleted_at', 'Column drh_stateful_research_study.study_consent.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'deleted_at', 'Column drh_stateful_research_study.study_consent.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_consent', 'deleted_by', 'Column drh_stateful_research_study.study_consent.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_consent', 'deleted_by', 'text', 'Column drh_stateful_research_study.study_consent.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_consent', 'deleted_by', 'Column drh_stateful_research_study.study_consent.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_consent', 'deleted_by', 'Column drh_stateful_research_study.study_consent.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
