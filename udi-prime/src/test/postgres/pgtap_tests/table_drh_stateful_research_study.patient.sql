SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(125);

SELECT has_table(
    'drh_stateful_research_study', 'patient',
    'Should have table drh_stateful_research_study.patient'
);

SELECT has_pk(
    'drh_stateful_research_study', 'patient',
    'Table drh_stateful_research_study.patient should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'patient'::name, ARRAY[
    'id'::name,
    'identifier_system'::name,
    'identifier_value'::name,
    'name_use'::name,
    'name_family'::name,
    'name_given'::name,
    'gender_type_id'::name,
    'birth_date'::name,
    'age'::name,
    'address_use'::name,
    'address_line1'::name,
    'address_city'::name,
    'address_state'::name,
    'address_postal_code'::name,
    'address_country'::name,
    'contact_relationship'::name,
    'contact_name_family'::name,
    'contact_name_given'::name,
    'contact_telecom_system'::name,
    'contact_telecom_value'::name,
    'contact_telecom_use'::name,
    'tenant_id'::name,
    'org_party_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'patient', 'id', 'Column drh_stateful_research_study.patient.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'id', 'text', 'Column drh_stateful_research_study.patient.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'patient', 'id', 'Column drh_stateful_research_study.patient.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'id', 'Column drh_stateful_research_study.patient.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'identifier_system', 'Column drh_stateful_research_study.patient.identifier_system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'identifier_system', 'character varying(255)', 'Column drh_stateful_research_study.patient.identifier_system should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'identifier_system', 'Column drh_stateful_research_study.patient.identifier_system should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'identifier_system', 'Column drh_stateful_research_study.patient.identifier_system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'identifier_value', 'Column drh_stateful_research_study.patient.identifier_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'identifier_value', 'character varying(255)', 'Column drh_stateful_research_study.patient.identifier_value should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'identifier_value', 'Column drh_stateful_research_study.patient.identifier_value should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'identifier_value', 'Column drh_stateful_research_study.patient.identifier_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'name_use', 'Column drh_stateful_research_study.patient.name_use should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'name_use', 'character varying(50)', 'Column drh_stateful_research_study.patient.name_use should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'name_use', 'Column drh_stateful_research_study.patient.name_use should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'name_use', 'Column drh_stateful_research_study.patient.name_use should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'name_family', 'Column drh_stateful_research_study.patient.name_family should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'name_family', 'character varying(100)', 'Column drh_stateful_research_study.patient.name_family should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'name_family', 'Column drh_stateful_research_study.patient.name_family should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'name_family', 'Column drh_stateful_research_study.patient.name_family should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'name_given', 'Column drh_stateful_research_study.patient.name_given should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'name_given', 'character varying(100)', 'Column drh_stateful_research_study.patient.name_given should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'name_given', 'Column drh_stateful_research_study.patient.name_given should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'name_given', 'Column drh_stateful_research_study.patient.name_given should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'gender_type_id', 'Column drh_stateful_research_study.patient.gender_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'gender_type_id', 'text', 'Column drh_stateful_research_study.patient.gender_type_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'gender_type_id', 'Column drh_stateful_research_study.patient.gender_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'gender_type_id', 'Column drh_stateful_research_study.patient.gender_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'birth_date', 'Column drh_stateful_research_study.patient.birth_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'birth_date', 'date', 'Column drh_stateful_research_study.patient.birth_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'birth_date', 'Column drh_stateful_research_study.patient.birth_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'birth_date', 'Column drh_stateful_research_study.patient.birth_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'age', 'Column drh_stateful_research_study.patient.age should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'age', 'integer', 'Column drh_stateful_research_study.patient.age should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'age', 'Column drh_stateful_research_study.patient.age should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'age', 'Column drh_stateful_research_study.patient.age should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_use', 'Column drh_stateful_research_study.patient.address_use should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_use', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_use should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_use', 'Column drh_stateful_research_study.patient.address_use should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_use', 'Column drh_stateful_research_study.patient.address_use should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_line1', 'Column drh_stateful_research_study.patient.address_line1 should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_line1', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_line1 should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_line1', 'Column drh_stateful_research_study.patient.address_line1 should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_line1', 'Column drh_stateful_research_study.patient.address_line1 should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_city', 'Column drh_stateful_research_study.patient.address_city should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_city', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_city should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_city', 'Column drh_stateful_research_study.patient.address_city should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_city', 'Column drh_stateful_research_study.patient.address_city should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_state', 'Column drh_stateful_research_study.patient.address_state should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_state', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_state should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_state', 'Column drh_stateful_research_study.patient.address_state should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_state', 'Column drh_stateful_research_study.patient.address_state should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_postal_code', 'Column drh_stateful_research_study.patient.address_postal_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_postal_code', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_postal_code should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_postal_code', 'Column drh_stateful_research_study.patient.address_postal_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_postal_code', 'Column drh_stateful_research_study.patient.address_postal_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'address_country', 'Column drh_stateful_research_study.patient.address_country should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'address_country', 'character varying(4096)', 'Column drh_stateful_research_study.patient.address_country should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'address_country', 'Column drh_stateful_research_study.patient.address_country should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'address_country', 'Column drh_stateful_research_study.patient.address_country should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_relationship', 'Column drh_stateful_research_study.patient.contact_relationship should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_relationship', 'character varying(50)', 'Column drh_stateful_research_study.patient.contact_relationship should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_relationship', 'Column drh_stateful_research_study.patient.contact_relationship should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_relationship', 'Column drh_stateful_research_study.patient.contact_relationship should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_name_family', 'Column drh_stateful_research_study.patient.contact_name_family should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_name_family', 'character varying(100)', 'Column drh_stateful_research_study.patient.contact_name_family should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_name_family', 'Column drh_stateful_research_study.patient.contact_name_family should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_name_family', 'Column drh_stateful_research_study.patient.contact_name_family should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_name_given', 'Column drh_stateful_research_study.patient.contact_name_given should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_name_given', 'character varying(100)', 'Column drh_stateful_research_study.patient.contact_name_given should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_name_given', 'Column drh_stateful_research_study.patient.contact_name_given should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_name_given', 'Column drh_stateful_research_study.patient.contact_name_given should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_telecom_system', 'Column drh_stateful_research_study.patient.contact_telecom_system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_telecom_system', 'character varying(50)', 'Column drh_stateful_research_study.patient.contact_telecom_system should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_telecom_system', 'Column drh_stateful_research_study.patient.contact_telecom_system should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_telecom_system', 'Column drh_stateful_research_study.patient.contact_telecom_system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_telecom_value', 'Column drh_stateful_research_study.patient.contact_telecom_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_telecom_value', 'character varying(50)', 'Column drh_stateful_research_study.patient.contact_telecom_value should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_telecom_value', 'Column drh_stateful_research_study.patient.contact_telecom_value should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_telecom_value', 'Column drh_stateful_research_study.patient.contact_telecom_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'contact_telecom_use', 'Column drh_stateful_research_study.patient.contact_telecom_use should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'contact_telecom_use', 'character varying(50)', 'Column drh_stateful_research_study.patient.contact_telecom_use should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'contact_telecom_use', 'Column drh_stateful_research_study.patient.contact_telecom_use should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'contact_telecom_use', 'Column drh_stateful_research_study.patient.contact_telecom_use should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'tenant_id', 'Column drh_stateful_research_study.patient.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'tenant_id', 'text', 'Column drh_stateful_research_study.patient.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'patient', 'tenant_id', 'Column drh_stateful_research_study.patient.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'tenant_id', 'Column drh_stateful_research_study.patient.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'org_party_id', 'Column drh_stateful_research_study.patient.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'org_party_id', 'text', 'Column drh_stateful_research_study.patient.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'patient', 'org_party_id', 'Column drh_stateful_research_study.patient.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'org_party_id', 'Column drh_stateful_research_study.patient.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'rec_status_id', 'Column drh_stateful_research_study.patient.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.patient.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'patient', 'rec_status_id', 'Column drh_stateful_research_study.patient.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'rec_status_id', 'Column drh_stateful_research_study.patient.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'created_at', 'Column drh_stateful_research_study.patient.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.patient.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'created_at', 'Column drh_stateful_research_study.patient.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'patient', 'created_at', 'Column drh_stateful_research_study.patient.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'patient', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.patient.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'created_by', 'Column drh_stateful_research_study.patient.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'created_by', 'text', 'Column drh_stateful_research_study.patient.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'created_by', 'Column drh_stateful_research_study.patient.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'patient', 'created_by', 'Column drh_stateful_research_study.patient.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'patient', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.patient.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'updated_at', 'Column drh_stateful_research_study.patient.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.patient.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'updated_at', 'Column drh_stateful_research_study.patient.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'updated_at', 'Column drh_stateful_research_study.patient.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'updated_by', 'Column drh_stateful_research_study.patient.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'updated_by', 'text', 'Column drh_stateful_research_study.patient.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'updated_by', 'Column drh_stateful_research_study.patient.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'updated_by', 'Column drh_stateful_research_study.patient.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'deleted_at', 'Column drh_stateful_research_study.patient.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.patient.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'deleted_at', 'Column drh_stateful_research_study.patient.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'deleted_at', 'Column drh_stateful_research_study.patient.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'patient', 'deleted_by', 'Column drh_stateful_research_study.patient.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'patient', 'deleted_by', 'text', 'Column drh_stateful_research_study.patient.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'patient', 'deleted_by', 'Column drh_stateful_research_study.patient.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'patient', 'deleted_by', 'Column drh_stateful_research_study.patient.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
