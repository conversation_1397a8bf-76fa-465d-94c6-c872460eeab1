SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_research_study', 'term_offer_party',
    'Should have table drh_stateful_research_study.term_offer_party'
);

SELECT has_pk(
    'drh_stateful_research_study', 'term_offer_party',
    'Table drh_stateful_research_study.term_offer_party should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'term_offer_party'::name, ARRAY[
    'id'::name,
    'party_id'::name,
    'offer_id'::name,
    'role_code'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'id', 'Column drh_stateful_research_study.term_offer_party.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'id', 'text', 'Column drh_stateful_research_study.term_offer_party.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer_party', 'id', 'Column drh_stateful_research_study.term_offer_party.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'id', 'Column drh_stateful_research_study.term_offer_party.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'party_id', 'Column drh_stateful_research_study.term_offer_party.party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'party_id', 'text', 'Column drh_stateful_research_study.term_offer_party.party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer_party', 'party_id', 'Column drh_stateful_research_study.term_offer_party.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'party_id', 'Column drh_stateful_research_study.term_offer_party.party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'offer_id', 'Column drh_stateful_research_study.term_offer_party.offer_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'offer_id', 'text', 'Column drh_stateful_research_study.term_offer_party.offer_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer_party', 'offer_id', 'Column drh_stateful_research_study.term_offer_party.offer_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'offer_id', 'Column drh_stateful_research_study.term_offer_party.offer_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'role_code', 'Column drh_stateful_research_study.term_offer_party.role_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'role_code', 'character varying(255)', 'Column drh_stateful_research_study.term_offer_party.role_code should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'role_code', 'Column drh_stateful_research_study.term_offer_party.role_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'role_code', 'Column drh_stateful_research_study.term_offer_party.role_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'rec_status_id', 'Column drh_stateful_research_study.term_offer_party.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.term_offer_party.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'term_offer_party', 'rec_status_id', 'Column drh_stateful_research_study.term_offer_party.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'rec_status_id', 'Column drh_stateful_research_study.term_offer_party.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'created_at', 'Column drh_stateful_research_study.term_offer_party.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer_party.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'created_at', 'Column drh_stateful_research_study.term_offer_party.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'term_offer_party', 'created_at', 'Column drh_stateful_research_study.term_offer_party.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'term_offer_party', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.term_offer_party.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'created_by', 'Column drh_stateful_research_study.term_offer_party.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'created_by', 'text', 'Column drh_stateful_research_study.term_offer_party.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'created_by', 'Column drh_stateful_research_study.term_offer_party.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'term_offer_party', 'created_by', 'Column drh_stateful_research_study.term_offer_party.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'term_offer_party', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.term_offer_party.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'updated_at', 'Column drh_stateful_research_study.term_offer_party.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer_party.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'updated_at', 'Column drh_stateful_research_study.term_offer_party.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'updated_at', 'Column drh_stateful_research_study.term_offer_party.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'updated_by', 'Column drh_stateful_research_study.term_offer_party.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'updated_by', 'text', 'Column drh_stateful_research_study.term_offer_party.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'updated_by', 'Column drh_stateful_research_study.term_offer_party.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'updated_by', 'Column drh_stateful_research_study.term_offer_party.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'deleted_at', 'Column drh_stateful_research_study.term_offer_party.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.term_offer_party.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'deleted_at', 'Column drh_stateful_research_study.term_offer_party.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'deleted_at', 'Column drh_stateful_research_study.term_offer_party.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'term_offer_party', 'deleted_by', 'Column drh_stateful_research_study.term_offer_party.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'term_offer_party', 'deleted_by', 'text', 'Column drh_stateful_research_study.term_offer_party.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'term_offer_party', 'deleted_by', 'Column drh_stateful_research_study.term_offer_party.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'term_offer_party', 'deleted_by', 'Column drh_stateful_research_study.term_offer_party.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
