SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(97);

SELECT has_table(
    'drh_stateful_research_study', 'laboratory',
    'Should have table drh_stateful_research_study.laboratory'
);

SELECT has_pk(
    'drh_stateful_research_study', 'laboratory',
    'Table drh_stateful_research_study.laboratory should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'laboratory'::name, ARRAY[
    'id'::name,
    'system'::name,
    'name'::name,
    'type'::name,
    'address_line'::name,
    'city'::name,
    'state'::name,
    'postal_code'::name,
    'country'::name,
    'phone'::name,
    'email'::name,
    'contact_name'::name,
    'contact_phone'::name,
    'parent_org_id'::name,
    'org_party_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'id', 'Column drh_stateful_research_study.laboratory.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'id', 'text', 'Column drh_stateful_research_study.laboratory.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'laboratory', 'id', 'Column drh_stateful_research_study.laboratory.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'id', 'Column drh_stateful_research_study.laboratory.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'system', 'Column drh_stateful_research_study.laboratory.system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'system', 'character varying(100)', 'Column drh_stateful_research_study.laboratory.system should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'system', 'Column drh_stateful_research_study.laboratory.system should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'system', 'Column drh_stateful_research_study.laboratory.system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'name', 'Column drh_stateful_research_study.laboratory.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'name', 'character varying(255)', 'Column drh_stateful_research_study.laboratory.name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'laboratory', 'name', 'Column drh_stateful_research_study.laboratory.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'name', 'Column drh_stateful_research_study.laboratory.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'type', 'Column drh_stateful_research_study.laboratory.type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'type', 'character varying(50)', 'Column drh_stateful_research_study.laboratory.type should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'type', 'Column drh_stateful_research_study.laboratory.type should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'type', 'Column drh_stateful_research_study.laboratory.type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'address_line', 'Column drh_stateful_research_study.laboratory.address_line should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'address_line', 'character varying(255)', 'Column drh_stateful_research_study.laboratory.address_line should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'address_line', 'Column drh_stateful_research_study.laboratory.address_line should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'address_line', 'Column drh_stateful_research_study.laboratory.address_line should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'city', 'Column drh_stateful_research_study.laboratory.city should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'city', 'character varying(100)', 'Column drh_stateful_research_study.laboratory.city should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'city', 'Column drh_stateful_research_study.laboratory.city should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'city', 'Column drh_stateful_research_study.laboratory.city should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'state', 'Column drh_stateful_research_study.laboratory.state should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'state', 'character varying(50)', 'Column drh_stateful_research_study.laboratory.state should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'state', 'Column drh_stateful_research_study.laboratory.state should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'state', 'Column drh_stateful_research_study.laboratory.state should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'postal_code', 'Column drh_stateful_research_study.laboratory.postal_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'postal_code', 'character varying(20)', 'Column drh_stateful_research_study.laboratory.postal_code should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'postal_code', 'Column drh_stateful_research_study.laboratory.postal_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'postal_code', 'Column drh_stateful_research_study.laboratory.postal_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'country', 'Column drh_stateful_research_study.laboratory.country should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'country', 'character varying(50)', 'Column drh_stateful_research_study.laboratory.country should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'country', 'Column drh_stateful_research_study.laboratory.country should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'country', 'Column drh_stateful_research_study.laboratory.country should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'phone', 'Column drh_stateful_research_study.laboratory.phone should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'phone', 'character varying(20)', 'Column drh_stateful_research_study.laboratory.phone should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'phone', 'Column drh_stateful_research_study.laboratory.phone should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'phone', 'Column drh_stateful_research_study.laboratory.phone should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'email', 'Column drh_stateful_research_study.laboratory.email should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'email', 'character varying(255)', 'Column drh_stateful_research_study.laboratory.email should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'email', 'Column drh_stateful_research_study.laboratory.email should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'email', 'Column drh_stateful_research_study.laboratory.email should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'contact_name', 'Column drh_stateful_research_study.laboratory.contact_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'contact_name', 'character varying(255)', 'Column drh_stateful_research_study.laboratory.contact_name should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'contact_name', 'Column drh_stateful_research_study.laboratory.contact_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'contact_name', 'Column drh_stateful_research_study.laboratory.contact_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'contact_phone', 'Column drh_stateful_research_study.laboratory.contact_phone should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'contact_phone', 'character varying(20)', 'Column drh_stateful_research_study.laboratory.contact_phone should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'contact_phone', 'Column drh_stateful_research_study.laboratory.contact_phone should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'contact_phone', 'Column drh_stateful_research_study.laboratory.contact_phone should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'parent_org_id', 'Column drh_stateful_research_study.laboratory.parent_org_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'parent_org_id', 'character varying(255)', 'Column drh_stateful_research_study.laboratory.parent_org_id should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'parent_org_id', 'Column drh_stateful_research_study.laboratory.parent_org_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'parent_org_id', 'Column drh_stateful_research_study.laboratory.parent_org_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'org_party_id', 'Column drh_stateful_research_study.laboratory.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'org_party_id', 'text', 'Column drh_stateful_research_study.laboratory.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'laboratory', 'org_party_id', 'Column drh_stateful_research_study.laboratory.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'org_party_id', 'Column drh_stateful_research_study.laboratory.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'tenant_id', 'Column drh_stateful_research_study.laboratory.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'tenant_id', 'text', 'Column drh_stateful_research_study.laboratory.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'laboratory', 'tenant_id', 'Column drh_stateful_research_study.laboratory.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'tenant_id', 'Column drh_stateful_research_study.laboratory.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'rec_status_id', 'Column drh_stateful_research_study.laboratory.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.laboratory.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'laboratory', 'rec_status_id', 'Column drh_stateful_research_study.laboratory.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'rec_status_id', 'Column drh_stateful_research_study.laboratory.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'created_at', 'Column drh_stateful_research_study.laboratory.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.laboratory.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'created_at', 'Column drh_stateful_research_study.laboratory.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'laboratory', 'created_at', 'Column drh_stateful_research_study.laboratory.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'laboratory', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.laboratory.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'created_by', 'Column drh_stateful_research_study.laboratory.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'created_by', 'text', 'Column drh_stateful_research_study.laboratory.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'created_by', 'Column drh_stateful_research_study.laboratory.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'laboratory', 'created_by', 'Column drh_stateful_research_study.laboratory.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'laboratory', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.laboratory.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'updated_at', 'Column drh_stateful_research_study.laboratory.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.laboratory.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'updated_at', 'Column drh_stateful_research_study.laboratory.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'updated_at', 'Column drh_stateful_research_study.laboratory.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'updated_by', 'Column drh_stateful_research_study.laboratory.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'updated_by', 'text', 'Column drh_stateful_research_study.laboratory.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'updated_by', 'Column drh_stateful_research_study.laboratory.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'updated_by', 'Column drh_stateful_research_study.laboratory.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'deleted_at', 'Column drh_stateful_research_study.laboratory.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.laboratory.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'deleted_at', 'Column drh_stateful_research_study.laboratory.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'deleted_at', 'Column drh_stateful_research_study.laboratory.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'laboratory', 'deleted_by', 'Column drh_stateful_research_study.laboratory.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'laboratory', 'deleted_by', 'text', 'Column drh_stateful_research_study.laboratory.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'laboratory', 'deleted_by', 'Column drh_stateful_research_study.laboratory.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'laboratory', 'deleted_by', 'Column drh_stateful_research_study.laboratory.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
