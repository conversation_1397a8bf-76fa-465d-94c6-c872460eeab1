SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(73);

SELECT has_table(
    'drh_stateful_research_study', 'citation_author',
    'Should have table drh_stateful_research_study.citation_author'
);

SELECT has_pk(
    'drh_stateful_research_study', 'citation_author',
    'Table drh_stateful_research_study.citation_author should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'citation_author'::name, ARRAY[
    'id'::name,
    'citation_id'::name,
    'first_name'::name,
    'last_name'::name,
    'middle_name'::name,
    'affiliation'::name,
    'orcid'::name,
    'email'::name,
    'role_id'::name,
    'party_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'id', 'Column drh_stateful_research_study.citation_author.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'id', 'text', 'Column drh_stateful_research_study.citation_author.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_author', 'id', 'Column drh_stateful_research_study.citation_author.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'id', 'Column drh_stateful_research_study.citation_author.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'citation_id', 'Column drh_stateful_research_study.citation_author.citation_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'citation_id', 'text', 'Column drh_stateful_research_study.citation_author.citation_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_author', 'citation_id', 'Column drh_stateful_research_study.citation_author.citation_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'citation_id', 'Column drh_stateful_research_study.citation_author.citation_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'first_name', 'Column drh_stateful_research_study.citation_author.first_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'first_name', 'text', 'Column drh_stateful_research_study.citation_author.first_name should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_author', 'first_name', 'Column drh_stateful_research_study.citation_author.first_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'first_name', 'Column drh_stateful_research_study.citation_author.first_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'last_name', 'Column drh_stateful_research_study.citation_author.last_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'last_name', 'text', 'Column drh_stateful_research_study.citation_author.last_name should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_author', 'last_name', 'Column drh_stateful_research_study.citation_author.last_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'last_name', 'Column drh_stateful_research_study.citation_author.last_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'middle_name', 'Column drh_stateful_research_study.citation_author.middle_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'middle_name', 'text', 'Column drh_stateful_research_study.citation_author.middle_name should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'middle_name', 'Column drh_stateful_research_study.citation_author.middle_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'middle_name', 'Column drh_stateful_research_study.citation_author.middle_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'affiliation', 'Column drh_stateful_research_study.citation_author.affiliation should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'affiliation', 'text', 'Column drh_stateful_research_study.citation_author.affiliation should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'affiliation', 'Column drh_stateful_research_study.citation_author.affiliation should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'affiliation', 'Column drh_stateful_research_study.citation_author.affiliation should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'orcid', 'Column drh_stateful_research_study.citation_author.orcid should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'orcid', 'text', 'Column drh_stateful_research_study.citation_author.orcid should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'orcid', 'Column drh_stateful_research_study.citation_author.orcid should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'orcid', 'Column drh_stateful_research_study.citation_author.orcid should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'email', 'Column drh_stateful_research_study.citation_author.email should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'email', 'text', 'Column drh_stateful_research_study.citation_author.email should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'email', 'Column drh_stateful_research_study.citation_author.email should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'email', 'Column drh_stateful_research_study.citation_author.email should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'role_id', 'Column drh_stateful_research_study.citation_author.role_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'role_id', 'text', 'Column drh_stateful_research_study.citation_author.role_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'role_id', 'Column drh_stateful_research_study.citation_author.role_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'role_id', 'Column drh_stateful_research_study.citation_author.role_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'party_id', 'Column drh_stateful_research_study.citation_author.party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'party_id', 'text', 'Column drh_stateful_research_study.citation_author.party_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'party_id', 'Column drh_stateful_research_study.citation_author.party_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'party_id', 'Column drh_stateful_research_study.citation_author.party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'rec_status_id', 'Column drh_stateful_research_study.citation_author.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.citation_author.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'citation_author', 'rec_status_id', 'Column drh_stateful_research_study.citation_author.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'rec_status_id', 'Column drh_stateful_research_study.citation_author.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'created_at', 'Column drh_stateful_research_study.citation_author.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_author.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'created_at', 'Column drh_stateful_research_study.citation_author.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation_author', 'created_at', 'Column drh_stateful_research_study.citation_author.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation_author', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.citation_author.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'created_by', 'Column drh_stateful_research_study.citation_author.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'created_by', 'text', 'Column drh_stateful_research_study.citation_author.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'created_by', 'Column drh_stateful_research_study.citation_author.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation_author', 'created_by', 'Column drh_stateful_research_study.citation_author.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation_author', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.citation_author.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'updated_at', 'Column drh_stateful_research_study.citation_author.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_author.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'updated_at', 'Column drh_stateful_research_study.citation_author.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'updated_at', 'Column drh_stateful_research_study.citation_author.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'updated_by', 'Column drh_stateful_research_study.citation_author.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'updated_by', 'text', 'Column drh_stateful_research_study.citation_author.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'updated_by', 'Column drh_stateful_research_study.citation_author.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'updated_by', 'Column drh_stateful_research_study.citation_author.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'deleted_at', 'Column drh_stateful_research_study.citation_author.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation_author.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'deleted_at', 'Column drh_stateful_research_study.citation_author.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'deleted_at', 'Column drh_stateful_research_study.citation_author.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation_author', 'deleted_by', 'Column drh_stateful_research_study.citation_author.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation_author', 'deleted_by', 'text', 'Column drh_stateful_research_study.citation_author.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation_author', 'deleted_by', 'Column drh_stateful_research_study.citation_author.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation_author', 'deleted_by', 'Column drh_stateful_research_study.citation_author.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
