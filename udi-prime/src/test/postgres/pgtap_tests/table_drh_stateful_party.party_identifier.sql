SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(57);

SELECT has_table(
    'drh_stateful_party', 'party_identifier',
    'Should have table drh_stateful_party.party_identifier'
);

SELECT has_pk(
    'drh_stateful_party', 'party_identifier',
    'Table drh_stateful_party.party_identifier should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'party_identifier'::name, ARRAY[
    'party_identifier_id'::name,
    'identifier_name'::name,
    'identifier_value'::name,
    'party_identifier_type_id'::name,
    'party_id'::name,
    'elaboration'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'activity_log'::name
]);

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'party_identifier_id', 'Column drh_stateful_party.party_identifier.party_identifier_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'party_identifier_id', 'text', 'Column drh_stateful_party.party_identifier.party_identifier_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_identifier', 'party_identifier_id', 'Column drh_stateful_party.party_identifier.party_identifier_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'party_identifier_id', 'Column drh_stateful_party.party_identifier.party_identifier_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'identifier_name', 'Column drh_stateful_party.party_identifier.identifier_name should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'identifier_name', 'text', 'Column drh_stateful_party.party_identifier.identifier_name should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_identifier', 'identifier_name', 'Column drh_stateful_party.party_identifier.identifier_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'identifier_name', 'Column drh_stateful_party.party_identifier.identifier_name should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'identifier_value', 'Column drh_stateful_party.party_identifier.identifier_value should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'identifier_value', 'text', 'Column drh_stateful_party.party_identifier.identifier_value should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_identifier', 'identifier_value', 'Column drh_stateful_party.party_identifier.identifier_value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'identifier_value', 'Column drh_stateful_party.party_identifier.identifier_value should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'party_identifier_type_id', 'Column drh_stateful_party.party_identifier.party_identifier_type_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'party_identifier_type_id', 'text', 'Column drh_stateful_party.party_identifier.party_identifier_type_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_identifier', 'party_identifier_type_id', 'Column drh_stateful_party.party_identifier.party_identifier_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'party_identifier_type_id', 'Column drh_stateful_party.party_identifier.party_identifier_type_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'party_id', 'Column drh_stateful_party.party_identifier.party_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'party_id', 'text', 'Column drh_stateful_party.party_identifier.party_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party_identifier', 'party_id', 'Column drh_stateful_party.party_identifier.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'party_id', 'Column drh_stateful_party.party_identifier.party_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'elaboration', 'Column drh_stateful_party.party_identifier.elaboration should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'elaboration', 'jsonb', 'Column drh_stateful_party.party_identifier.elaboration should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'elaboration', 'Column drh_stateful_party.party_identifier.elaboration should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'elaboration', 'Column drh_stateful_party.party_identifier.elaboration should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'created_at', 'Column drh_stateful_party.party_identifier.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.party_identifier.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'created_at', 'Column drh_stateful_party.party_identifier.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_identifier', 'created_at', 'Column drh_stateful_party.party_identifier.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_identifier', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.party_identifier.created_at default is');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'created_by', 'Column drh_stateful_party.party_identifier.created_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'created_by', 'text', 'Column drh_stateful_party.party_identifier.created_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'created_by', 'Column drh_stateful_party.party_identifier.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party_identifier', 'created_by', 'Column drh_stateful_party.party_identifier.created_by should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party_identifier', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_party.party_identifier.created_by default is');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'updated_at', 'Column drh_stateful_party.party_identifier.updated_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_party.party_identifier.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'updated_at', 'Column drh_stateful_party.party_identifier.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'updated_at', 'Column drh_stateful_party.party_identifier.updated_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'updated_by', 'Column drh_stateful_party.party_identifier.updated_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'updated_by', 'text', 'Column drh_stateful_party.party_identifier.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'updated_by', 'Column drh_stateful_party.party_identifier.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'updated_by', 'Column drh_stateful_party.party_identifier.updated_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'deleted_at', 'Column drh_stateful_party.party_identifier.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_party.party_identifier.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'deleted_at', 'Column drh_stateful_party.party_identifier.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'deleted_at', 'Column drh_stateful_party.party_identifier.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'deleted_by', 'Column drh_stateful_party.party_identifier.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'deleted_by', 'text', 'Column drh_stateful_party.party_identifier.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'deleted_by', 'Column drh_stateful_party.party_identifier.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'deleted_by', 'Column drh_stateful_party.party_identifier.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party_identifier', 'activity_log', 'Column drh_stateful_party.party_identifier.activity_log should exist');
SELECT col_type_is(      'drh_stateful_party', 'party_identifier', 'activity_log', 'jsonb', 'Column drh_stateful_party.party_identifier.activity_log should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party_identifier', 'activity_log', 'Column drh_stateful_party.party_identifier.activity_log should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party_identifier', 'activity_log', 'Column drh_stateful_party.party_identifier.activity_log should not have a default');

SELECT * FROM finish();
ROLLBACK;
