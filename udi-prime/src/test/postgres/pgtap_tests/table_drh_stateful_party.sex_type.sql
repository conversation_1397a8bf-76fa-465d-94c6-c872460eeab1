SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(45);

SELECT has_table(
    'drh_stateful_party', 'sex_type',
    'Should have table drh_stateful_party.sex_type'
);

SELECT has_pk(
    'drh_stateful_party', 'sex_type',
    'Table drh_stateful_party.sex_type should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'sex_type'::name, ARRAY[
    'sex_type_id'::name,
    'code'::name,
    'value'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'activity_log'::name
]);

SELECT has_column(       'drh_stateful_party', 'sex_type', 'sex_type_id', 'Column drh_stateful_party.sex_type.sex_type_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'sex_type_id', 'text', 'Column drh_stateful_party.sex_type.sex_type_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'sex_type', 'sex_type_id', 'Column drh_stateful_party.sex_type.sex_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'sex_type_id', 'Column drh_stateful_party.sex_type.sex_type_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'code', 'Column drh_stateful_party.sex_type.code should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'code', 'text', 'Column drh_stateful_party.sex_type.code should be type text');
SELECT col_not_null(     'drh_stateful_party', 'sex_type', 'code', 'Column drh_stateful_party.sex_type.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'code', 'Column drh_stateful_party.sex_type.code should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'value', 'Column drh_stateful_party.sex_type.value should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'value', 'text', 'Column drh_stateful_party.sex_type.value should be type text');
SELECT col_not_null(     'drh_stateful_party', 'sex_type', 'value', 'Column drh_stateful_party.sex_type.value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'value', 'Column drh_stateful_party.sex_type.value should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'created_at', 'Column drh_stateful_party.sex_type.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.sex_type.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'created_at', 'Column drh_stateful_party.sex_type.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'sex_type', 'created_at', 'Column drh_stateful_party.sex_type.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'sex_type', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.sex_type.created_at default is');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'created_by', 'Column drh_stateful_party.sex_type.created_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'created_by', 'text', 'Column drh_stateful_party.sex_type.created_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'created_by', 'Column drh_stateful_party.sex_type.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'sex_type', 'created_by', 'Column drh_stateful_party.sex_type.created_by should have a default');
SELECT col_default_is(   'drh_stateful_party', 'sex_type', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_party.sex_type.created_by default is');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'updated_at', 'Column drh_stateful_party.sex_type.updated_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_party.sex_type.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'updated_at', 'Column drh_stateful_party.sex_type.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'updated_at', 'Column drh_stateful_party.sex_type.updated_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'updated_by', 'Column drh_stateful_party.sex_type.updated_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'updated_by', 'text', 'Column drh_stateful_party.sex_type.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'updated_by', 'Column drh_stateful_party.sex_type.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'updated_by', 'Column drh_stateful_party.sex_type.updated_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'deleted_at', 'Column drh_stateful_party.sex_type.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_party.sex_type.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'deleted_at', 'Column drh_stateful_party.sex_type.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'deleted_at', 'Column drh_stateful_party.sex_type.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'deleted_by', 'Column drh_stateful_party.sex_type.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'deleted_by', 'text', 'Column drh_stateful_party.sex_type.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'deleted_by', 'Column drh_stateful_party.sex_type.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'deleted_by', 'Column drh_stateful_party.sex_type.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'sex_type', 'activity_log', 'Column drh_stateful_party.sex_type.activity_log should exist');
SELECT col_type_is(      'drh_stateful_party', 'sex_type', 'activity_log', 'jsonb', 'Column drh_stateful_party.sex_type.activity_log should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'sex_type', 'activity_log', 'Column drh_stateful_party.sex_type.activity_log should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'sex_type', 'activity_log', 'Column drh_stateful_party.sex_type.activity_log should not have a default');

SELECT * FROM finish();
ROLLBACK;
