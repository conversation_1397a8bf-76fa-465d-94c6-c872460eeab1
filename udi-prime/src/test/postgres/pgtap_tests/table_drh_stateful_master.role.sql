SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(61);

SELECT has_table(
    'drh_stateful_master', 'role',
    'Should have table drh_stateful_master.role'
);

SELECT has_pk(
    'drh_stateful_master', 'role',
    'Table drh_stateful_master.role should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'role'::name, ARRAY[
    'role_id'::name,
    'role_type_id'::name,
    'org_party_id'::name,
    'role_name'::name,
    'description'::name,
    'is_system_role'::name,
    'metadata'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'role', 'role_id', 'Column drh_stateful_master.role.role_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'role_id', 'text', 'Column drh_stateful_master.role.role_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'role', 'role_id', 'Column drh_stateful_master.role.role_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'role_id', 'Column drh_stateful_master.role.role_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'role_type_id', 'Column drh_stateful_master.role.role_type_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'role_type_id', 'text', 'Column drh_stateful_master.role.role_type_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'role', 'role_type_id', 'Column drh_stateful_master.role.role_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'role_type_id', 'Column drh_stateful_master.role.role_type_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'org_party_id', 'Column drh_stateful_master.role.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'org_party_id', 'text', 'Column drh_stateful_master.role.org_party_id should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role', 'org_party_id', 'Column drh_stateful_master.role.org_party_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'org_party_id', 'Column drh_stateful_master.role.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'role_name', 'Column drh_stateful_master.role.role_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'role_name', 'character varying(50)', 'Column drh_stateful_master.role.role_name should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'role', 'role_name', 'Column drh_stateful_master.role.role_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'role_name', 'Column drh_stateful_master.role.role_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'description', 'Column drh_stateful_master.role.description should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'description', 'text', 'Column drh_stateful_master.role.description should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role', 'description', 'Column drh_stateful_master.role.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'description', 'Column drh_stateful_master.role.description should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'is_system_role', 'Column drh_stateful_master.role.is_system_role should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'is_system_role', 'boolean', 'Column drh_stateful_master.role.is_system_role should be type boolean');
SELECT col_not_null(     'drh_stateful_master', 'role', 'is_system_role', 'Column drh_stateful_master.role.is_system_role should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'is_system_role', 'Column drh_stateful_master.role.is_system_role should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'metadata', 'Column drh_stateful_master.role.metadata should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'metadata', 'jsonb', 'Column drh_stateful_master.role.metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_master', 'role', 'metadata', 'Column drh_stateful_master.role.metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'metadata', 'Column drh_stateful_master.role.metadata should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'rec_status_id', 'Column drh_stateful_master.role.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'rec_status_id', 'integer', 'Column drh_stateful_master.role.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'role', 'rec_status_id', 'Column drh_stateful_master.role.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'rec_status_id', 'Column drh_stateful_master.role.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'created_at', 'Column drh_stateful_master.role.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.role.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role', 'created_at', 'Column drh_stateful_master.role.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'role', 'created_at', 'Column drh_stateful_master.role.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'role', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.role.created_at default is');

SELECT has_column(       'drh_stateful_master', 'role', 'created_by', 'Column drh_stateful_master.role.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'created_by', 'text', 'Column drh_stateful_master.role.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role', 'created_by', 'Column drh_stateful_master.role.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'role', 'created_by', 'Column drh_stateful_master.role.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'role', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.role.created_by default is');

SELECT has_column(       'drh_stateful_master', 'role', 'updated_at', 'Column drh_stateful_master.role.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.role.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role', 'updated_at', 'Column drh_stateful_master.role.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'updated_at', 'Column drh_stateful_master.role.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'updated_by', 'Column drh_stateful_master.role.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'updated_by', 'text', 'Column drh_stateful_master.role.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role', 'updated_by', 'Column drh_stateful_master.role.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'updated_by', 'Column drh_stateful_master.role.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'deleted_at', 'Column drh_stateful_master.role.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.role.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role', 'deleted_at', 'Column drh_stateful_master.role.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'deleted_at', 'Column drh_stateful_master.role.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'role', 'deleted_by', 'Column drh_stateful_master.role.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role', 'deleted_by', 'text', 'Column drh_stateful_master.role.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role', 'deleted_by', 'Column drh_stateful_master.role.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role', 'deleted_by', 'Column drh_stateful_master.role.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
