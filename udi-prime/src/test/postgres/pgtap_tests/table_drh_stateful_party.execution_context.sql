SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(16);

SELECT has_table(
    'drh_stateful_party', 'execution_context',
    'Should have table drh_stateful_party.execution_context'
);

SELECT has_pk(
    'drh_stateful_party', 'execution_context',
    'Table drh_stateful_party.execution_context should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'execution_context'::name, ARRAY[
    'code'::name,
    'value'::name,
    'created_at'::name
]);

SELECT has_column(       'drh_stateful_party', 'execution_context', 'code', 'Column drh_stateful_party.execution_context.code should exist');
SELECT col_type_is(      'drh_stateful_party', 'execution_context', 'code', 'text', 'Column drh_stateful_party.execution_context.code should be type text');
SELECT col_not_null(     'drh_stateful_party', 'execution_context', 'code', 'Column drh_stateful_party.execution_context.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'execution_context', 'code', 'Column drh_stateful_party.execution_context.code should not have a default');

SELECT has_column(       'drh_stateful_party', 'execution_context', 'value', 'Column drh_stateful_party.execution_context.value should exist');
SELECT col_type_is(      'drh_stateful_party', 'execution_context', 'value', 'text', 'Column drh_stateful_party.execution_context.value should be type text');
SELECT col_not_null(     'drh_stateful_party', 'execution_context', 'value', 'Column drh_stateful_party.execution_context.value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'execution_context', 'value', 'Column drh_stateful_party.execution_context.value should not have a default');

SELECT has_column(       'drh_stateful_party', 'execution_context', 'created_at', 'Column drh_stateful_party.execution_context.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'execution_context', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.execution_context.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'execution_context', 'created_at', 'Column drh_stateful_party.execution_context.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'execution_context', 'created_at', 'Column drh_stateful_party.execution_context.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'execution_context', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.execution_context.created_at default is');

SELECT * FROM finish();
ROLLBACK;
