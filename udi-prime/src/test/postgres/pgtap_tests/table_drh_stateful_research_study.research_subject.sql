SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(85);

SELECT has_table(
    'drh_stateful_research_study', 'research_subject',
    'Should have table drh_stateful_research_study.research_subject'
);

SELECT has_pk(
    'drh_stateful_research_study', 'research_subject',
    'Table drh_stateful_research_study.research_subject should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'research_subject'::name, ARRAY[
    'rsubject_id'::name,
    'participant_identifier'::name,
    'study_reference'::name,
    'individual_reference'::name,
    'status_id'::name,
    'group'::name,
    'diabetes_type'::name,
    'diagnosis_icd'::name,
    'med_rxnorm'::name,
    'treatment_modality'::name,
    'race_type_id'::name,
    'ethnicity_type_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'rsubject_id', 'Column drh_stateful_research_study.research_subject.rsubject_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'rsubject_id', 'text', 'Column drh_stateful_research_study.research_subject.rsubject_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'rsubject_id', 'Column drh_stateful_research_study.research_subject.rsubject_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'rsubject_id', 'Column drh_stateful_research_study.research_subject.rsubject_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'participant_identifier', 'Column drh_stateful_research_study.research_subject.participant_identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'participant_identifier', 'character varying(255)', 'Column drh_stateful_research_study.research_subject.participant_identifier should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'participant_identifier', 'Column drh_stateful_research_study.research_subject.participant_identifier should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'participant_identifier', 'Column drh_stateful_research_study.research_subject.participant_identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'study_reference', 'Column drh_stateful_research_study.research_subject.study_reference should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'study_reference', 'text', 'Column drh_stateful_research_study.research_subject.study_reference should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'study_reference', 'Column drh_stateful_research_study.research_subject.study_reference should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'study_reference', 'Column drh_stateful_research_study.research_subject.study_reference should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'individual_reference', 'Column drh_stateful_research_study.research_subject.individual_reference should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'individual_reference', 'text', 'Column drh_stateful_research_study.research_subject.individual_reference should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'individual_reference', 'Column drh_stateful_research_study.research_subject.individual_reference should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'individual_reference', 'Column drh_stateful_research_study.research_subject.individual_reference should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'status_id', 'Column drh_stateful_research_study.research_subject.status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'status_id', 'integer', 'Column drh_stateful_research_study.research_subject.status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'status_id', 'Column drh_stateful_research_study.research_subject.status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'status_id', 'Column drh_stateful_research_study.research_subject.status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'group', 'Column drh_stateful_research_study.research_subject."group" should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'group', 'character varying(255)', 'Column drh_stateful_research_study.research_subject."group" should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'group', 'Column drh_stateful_research_study.research_subject."group" should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'group', 'Column drh_stateful_research_study.research_subject."group" should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'diabetes_type', 'Column drh_stateful_research_study.research_subject.diabetes_type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'diabetes_type', 'character varying(50)', 'Column drh_stateful_research_study.research_subject.diabetes_type should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'diabetes_type', 'Column drh_stateful_research_study.research_subject.diabetes_type should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'diabetes_type', 'Column drh_stateful_research_study.research_subject.diabetes_type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'diagnosis_icd', 'Column drh_stateful_research_study.research_subject.diagnosis_icd should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'diagnosis_icd', 'character varying(255)', 'Column drh_stateful_research_study.research_subject.diagnosis_icd should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'diagnosis_icd', 'Column drh_stateful_research_study.research_subject.diagnosis_icd should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'diagnosis_icd', 'Column drh_stateful_research_study.research_subject.diagnosis_icd should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'med_rxnorm', 'Column drh_stateful_research_study.research_subject.med_rxnorm should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'med_rxnorm', 'character varying(255)', 'Column drh_stateful_research_study.research_subject.med_rxnorm should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'med_rxnorm', 'Column drh_stateful_research_study.research_subject.med_rxnorm should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'med_rxnorm', 'Column drh_stateful_research_study.research_subject.med_rxnorm should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'treatment_modality', 'Column drh_stateful_research_study.research_subject.treatment_modality should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'treatment_modality', 'text', 'Column drh_stateful_research_study.research_subject.treatment_modality should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'treatment_modality', 'Column drh_stateful_research_study.research_subject.treatment_modality should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'treatment_modality', 'Column drh_stateful_research_study.research_subject.treatment_modality should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'race_type_id', 'Column drh_stateful_research_study.research_subject.race_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'race_type_id', 'text', 'Column drh_stateful_research_study.research_subject.race_type_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'race_type_id', 'Column drh_stateful_research_study.research_subject.race_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'race_type_id', 'Column drh_stateful_research_study.research_subject.race_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'ethnicity_type_id', 'Column drh_stateful_research_study.research_subject.ethnicity_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'ethnicity_type_id', 'text', 'Column drh_stateful_research_study.research_subject.ethnicity_type_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'ethnicity_type_id', 'Column drh_stateful_research_study.research_subject.ethnicity_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'ethnicity_type_id', 'Column drh_stateful_research_study.research_subject.ethnicity_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'tenant_id', 'Column drh_stateful_research_study.research_subject.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'tenant_id', 'text', 'Column drh_stateful_research_study.research_subject.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'tenant_id', 'Column drh_stateful_research_study.research_subject.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'tenant_id', 'Column drh_stateful_research_study.research_subject.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'rec_status_id', 'Column drh_stateful_research_study.research_subject.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.research_subject.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'research_subject', 'rec_status_id', 'Column drh_stateful_research_study.research_subject.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'rec_status_id', 'Column drh_stateful_research_study.research_subject.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'created_at', 'Column drh_stateful_research_study.research_subject.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_subject.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'created_at', 'Column drh_stateful_research_study.research_subject.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_subject', 'created_at', 'Column drh_stateful_research_study.research_subject.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_subject', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.research_subject.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'created_by', 'Column drh_stateful_research_study.research_subject.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'created_by', 'text', 'Column drh_stateful_research_study.research_subject.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'created_by', 'Column drh_stateful_research_study.research_subject.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_subject', 'created_by', 'Column drh_stateful_research_study.research_subject.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_subject', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.research_subject.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'updated_at', 'Column drh_stateful_research_study.research_subject.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_subject.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'updated_at', 'Column drh_stateful_research_study.research_subject.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'updated_at', 'Column drh_stateful_research_study.research_subject.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'updated_by', 'Column drh_stateful_research_study.research_subject.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'updated_by', 'text', 'Column drh_stateful_research_study.research_subject.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'updated_by', 'Column drh_stateful_research_study.research_subject.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'updated_by', 'Column drh_stateful_research_study.research_subject.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'deleted_at', 'Column drh_stateful_research_study.research_subject.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_subject.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'deleted_at', 'Column drh_stateful_research_study.research_subject.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'deleted_at', 'Column drh_stateful_research_study.research_subject.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_subject', 'deleted_by', 'Column drh_stateful_research_study.research_subject.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_subject', 'deleted_by', 'text', 'Column drh_stateful_research_study.research_subject.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_subject', 'deleted_by', 'Column drh_stateful_research_study.research_subject.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_subject', 'deleted_by', 'Column drh_stateful_research_study.research_subject.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
