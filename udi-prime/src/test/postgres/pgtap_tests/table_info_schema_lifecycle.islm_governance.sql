SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(70);

SELECT has_table(
    'info_schema_lifecycle', 'islm_governance',
    'Should have table info_schema_lifecycle.islm_governance'
);

SELECT has_pk(
    'info_schema_lifecycle', 'islm_governance',
    'Table info_schema_lifecycle.islm_governance should have a primary key'
);

SELECT columns_are('info_schema_lifecycle'::name, 'islm_governance'::name, ARRAY[
    'islm_version'::name,
    'migration_routine_name'::name,
    'migration_version'::name,
    'migration_reason'::name,
    'is_idempotent'::name,
    'migration_undo_routine_name'::name,
    'migration_status_routine_name'::name,
    'scxml_id'::name,
    'from_state'::name,
    'to_state'::name,
    'transition_result'::name,
    'transition_reason'::name,
    'transition_count'::name,
    'created_at'::name,
    'created_by'::name,
    'elaboration'::name
]);

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'islm_version', 'Column info_schema_lifecycle.islm_governance.islm_version should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'islm_version', 'text', 'Column info_schema_lifecycle.islm_governance.islm_version should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'islm_version', 'Column info_schema_lifecycle.islm_governance.islm_version should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'islm_version', 'Column info_schema_lifecycle.islm_governance.islm_version should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'migration_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_routine_name should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'migration_routine_name', 'text', 'Column info_schema_lifecycle.islm_governance.migration_routine_name should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'migration_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_routine_name should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'migration_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_routine_name should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'migration_version', 'Column info_schema_lifecycle.islm_governance.migration_version should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'migration_version', 'text', 'Column info_schema_lifecycle.islm_governance.migration_version should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'migration_version', 'Column info_schema_lifecycle.islm_governance.migration_version should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'migration_version', 'Column info_schema_lifecycle.islm_governance.migration_version should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'migration_reason', 'Column info_schema_lifecycle.islm_governance.migration_reason should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'migration_reason', 'text', 'Column info_schema_lifecycle.islm_governance.migration_reason should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'migration_reason', 'Column info_schema_lifecycle.islm_governance.migration_reason should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'migration_reason', 'Column info_schema_lifecycle.islm_governance.migration_reason should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'is_idempotent', 'Column info_schema_lifecycle.islm_governance.is_idempotent should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'is_idempotent', 'boolean', 'Column info_schema_lifecycle.islm_governance.is_idempotent should be type boolean');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'is_idempotent', 'Column info_schema_lifecycle.islm_governance.is_idempotent should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'is_idempotent', 'Column info_schema_lifecycle.islm_governance.is_idempotent should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'migration_undo_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_undo_routine_name should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'migration_undo_routine_name', 'text', 'Column info_schema_lifecycle.islm_governance.migration_undo_routine_name should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'migration_undo_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_undo_routine_name should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'migration_undo_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_undo_routine_name should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'migration_status_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_status_routine_name should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'migration_status_routine_name', 'text', 'Column info_schema_lifecycle.islm_governance.migration_status_routine_name should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'migration_status_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_status_routine_name should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'migration_status_routine_name', 'Column info_schema_lifecycle.islm_governance.migration_status_routine_name should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'scxml_id', 'Column info_schema_lifecycle.islm_governance.scxml_id should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'scxml_id', 'text', 'Column info_schema_lifecycle.islm_governance.scxml_id should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'scxml_id', 'Column info_schema_lifecycle.islm_governance.scxml_id should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'scxml_id', 'Column info_schema_lifecycle.islm_governance.scxml_id should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'from_state', 'Column info_schema_lifecycle.islm_governance.from_state should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'from_state', 'text', 'Column info_schema_lifecycle.islm_governance.from_state should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'from_state', 'Column info_schema_lifecycle.islm_governance.from_state should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'from_state', 'Column info_schema_lifecycle.islm_governance.from_state should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'to_state', 'Column info_schema_lifecycle.islm_governance.to_state should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'to_state', 'text', 'Column info_schema_lifecycle.islm_governance.to_state should be type text');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'to_state', 'Column info_schema_lifecycle.islm_governance.to_state should be NOT NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'to_state', 'Column info_schema_lifecycle.islm_governance.to_state should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'transition_result', 'Column info_schema_lifecycle.islm_governance.transition_result should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'transition_result', 'jsonb', 'Column info_schema_lifecycle.islm_governance.transition_result should be type jsonb');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'transition_result', 'Column info_schema_lifecycle.islm_governance.transition_result should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'transition_result', 'Column info_schema_lifecycle.islm_governance.transition_result should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'transition_reason', 'Column info_schema_lifecycle.islm_governance.transition_reason should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'transition_reason', 'text', 'Column info_schema_lifecycle.islm_governance.transition_reason should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'transition_reason', 'Column info_schema_lifecycle.islm_governance.transition_reason should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'transition_reason', 'Column info_schema_lifecycle.islm_governance.transition_reason should not have a default');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'transition_count', 'Column info_schema_lifecycle.islm_governance.transition_count should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'transition_count', 'integer', 'Column info_schema_lifecycle.islm_governance.transition_count should be type integer');
SELECT col_not_null(     'info_schema_lifecycle', 'islm_governance', 'transition_count', 'Column info_schema_lifecycle.islm_governance.transition_count should be NOT NULL');
SELECT col_has_default(  'info_schema_lifecycle', 'islm_governance', 'transition_count', 'Column info_schema_lifecycle.islm_governance.transition_count should have a default');
SELECT col_default_is(   'info_schema_lifecycle', 'islm_governance', 'transition_count', '0', 'Column info_schema_lifecycle.islm_governance.transition_count default is');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'created_at', 'Column info_schema_lifecycle.islm_governance.created_at should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'created_at', 'timestamp with time zone', 'Column info_schema_lifecycle.islm_governance.created_at should be type timestamp with time zone');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'created_at', 'Column info_schema_lifecycle.islm_governance.created_at should allow NULL');
SELECT col_has_default(  'info_schema_lifecycle', 'islm_governance', 'created_at', 'Column info_schema_lifecycle.islm_governance.created_at should have a default');
SELECT col_default_is(   'info_schema_lifecycle', 'islm_governance', 'created_at', 'CURRENT_TIMESTAMP', 'Column info_schema_lifecycle.islm_governance.created_at default is');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'created_by', 'Column info_schema_lifecycle.islm_governance.created_by should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'created_by', 'text', 'Column info_schema_lifecycle.islm_governance.created_by should be type text');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'created_by', 'Column info_schema_lifecycle.islm_governance.created_by should allow NULL');
SELECT col_has_default(  'info_schema_lifecycle', 'islm_governance', 'created_by', 'Column info_schema_lifecycle.islm_governance.created_by should have a default');
SELECT col_default_is(   'info_schema_lifecycle', 'islm_governance', 'created_by', 'CURRENT_USER', 'Column info_schema_lifecycle.islm_governance.created_by default is');

SELECT has_column(       'info_schema_lifecycle', 'islm_governance', 'elaboration', 'Column info_schema_lifecycle.islm_governance.elaboration should exist');
SELECT col_type_is(      'info_schema_lifecycle', 'islm_governance', 'elaboration', 'jsonb', 'Column info_schema_lifecycle.islm_governance.elaboration should be type jsonb');
SELECT col_is_null(      'info_schema_lifecycle', 'islm_governance', 'elaboration', 'Column info_schema_lifecycle.islm_governance.elaboration should allow NULL');
SELECT col_hasnt_default('info_schema_lifecycle', 'islm_governance', 'elaboration', 'Column info_schema_lifecycle.islm_governance.elaboration should not have a default');

SELECT * FROM finish();
ROLLBACK;
