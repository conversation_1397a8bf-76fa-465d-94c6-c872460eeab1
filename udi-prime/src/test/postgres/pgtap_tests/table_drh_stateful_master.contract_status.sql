SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_master', 'contract_status',
    'Should have table drh_stateful_master.contract_status'
);

SELECT has_pk(
    'drh_stateful_master', 'contract_status',
    'Table drh_stateful_master.contract_status should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'contract_status'::name, ARRAY[
    'contract_status_type_id'::name,
    'code'::name,
    'system'::name,
    'display'::name,
    'definition'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'contract_status', 'contract_status_type_id', 'Column drh_stateful_master.contract_status.contract_status_type_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'contract_status_type_id', 'text', 'Column drh_stateful_master.contract_status.contract_status_type_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'contract_status', 'contract_status_type_id', 'Column drh_stateful_master.contract_status.contract_status_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'contract_status_type_id', 'Column drh_stateful_master.contract_status.contract_status_type_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'code', 'Column drh_stateful_master.contract_status.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'code', 'character varying(50)', 'Column drh_stateful_master.contract_status.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'contract_status', 'code', 'Column drh_stateful_master.contract_status.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'code', 'Column drh_stateful_master.contract_status.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'system', 'Column drh_stateful_master.contract_status.system should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'system', 'character varying(255)', 'Column drh_stateful_master.contract_status.system should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'system', 'Column drh_stateful_master.contract_status.system should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'system', 'Column drh_stateful_master.contract_status.system should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'display', 'Column drh_stateful_master.contract_status.display should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'display', 'character varying(255)', 'Column drh_stateful_master.contract_status.display should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'display', 'Column drh_stateful_master.contract_status.display should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'display', 'Column drh_stateful_master.contract_status.display should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'definition', 'Column drh_stateful_master.contract_status.definition should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'definition', 'text', 'Column drh_stateful_master.contract_status.definition should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'definition', 'Column drh_stateful_master.contract_status.definition should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'definition', 'Column drh_stateful_master.contract_status.definition should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'rec_status_id', 'Column drh_stateful_master.contract_status.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'rec_status_id', 'integer', 'Column drh_stateful_master.contract_status.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'contract_status', 'rec_status_id', 'Column drh_stateful_master.contract_status.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'rec_status_id', 'Column drh_stateful_master.contract_status.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'created_at', 'Column drh_stateful_master.contract_status.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_status.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'created_at', 'Column drh_stateful_master.contract_status.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contract_status', 'created_at', 'Column drh_stateful_master.contract_status.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contract_status', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.contract_status.created_at default is');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'created_by', 'Column drh_stateful_master.contract_status.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'created_by', 'text', 'Column drh_stateful_master.contract_status.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'created_by', 'Column drh_stateful_master.contract_status.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contract_status', 'created_by', 'Column drh_stateful_master.contract_status.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contract_status', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.contract_status.created_by default is');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'updated_at', 'Column drh_stateful_master.contract_status.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_status.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'updated_at', 'Column drh_stateful_master.contract_status.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'updated_at', 'Column drh_stateful_master.contract_status.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'updated_by', 'Column drh_stateful_master.contract_status.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'updated_by', 'text', 'Column drh_stateful_master.contract_status.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'updated_by', 'Column drh_stateful_master.contract_status.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'updated_by', 'Column drh_stateful_master.contract_status.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'deleted_at', 'Column drh_stateful_master.contract_status.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.contract_status.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'deleted_at', 'Column drh_stateful_master.contract_status.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'deleted_at', 'Column drh_stateful_master.contract_status.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contract_status', 'deleted_by', 'Column drh_stateful_master.contract_status.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contract_status', 'deleted_by', 'text', 'Column drh_stateful_master.contract_status.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contract_status', 'deleted_by', 'Column drh_stateful_master.contract_status.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contract_status', 'deleted_by', 'Column drh_stateful_master.contract_status.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
