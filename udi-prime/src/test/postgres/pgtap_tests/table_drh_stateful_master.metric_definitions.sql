SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(15);

SELECT has_table(
    'drh_stateful_master', 'metric_definitions',
    'Should have table drh_stateful_master.metric_definitions'
);

SELECT has_pk(
    'drh_stateful_master', 'metric_definitions',
    'Table drh_stateful_master.metric_definitions should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'metric_definitions'::name, ARRAY[
    'metric_id'::name,
    'metric_name'::name,
    'metric_info'::name
]);

SELECT has_column(       'drh_stateful_master', 'metric_definitions', 'metric_id', 'Column drh_stateful_master.metric_definitions.metric_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'metric_definitions', 'metric_id', 'text', 'Column drh_stateful_master.metric_definitions.metric_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'metric_definitions', 'metric_id', 'Column drh_stateful_master.metric_definitions.metric_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'metric_definitions', 'metric_id', 'Column drh_stateful_master.metric_definitions.metric_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'metric_definitions', 'metric_name', 'Column drh_stateful_master.metric_definitions.metric_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'metric_definitions', 'metric_name', 'text', 'Column drh_stateful_master.metric_definitions.metric_name should be type text');
SELECT col_not_null(     'drh_stateful_master', 'metric_definitions', 'metric_name', 'Column drh_stateful_master.metric_definitions.metric_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'metric_definitions', 'metric_name', 'Column drh_stateful_master.metric_definitions.metric_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'metric_definitions', 'metric_info', 'Column drh_stateful_master.metric_definitions.metric_info should exist');
SELECT col_type_is(      'drh_stateful_master', 'metric_definitions', 'metric_info', 'text', 'Column drh_stateful_master.metric_definitions.metric_info should be type text');
SELECT col_not_null(     'drh_stateful_master', 'metric_definitions', 'metric_info', 'Column drh_stateful_master.metric_definitions.metric_info should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'metric_definitions', 'metric_info', 'Column drh_stateful_master.metric_definitions.metric_info should not have a default');

SELECT * FROM finish();
ROLLBACK;
