SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(133);

SELECT has_table(
    'drh_stateful_research_study', 'research_study',
    'Should have table drh_stateful_research_study.research_study'
);

SELECT has_pk(
    'drh_stateful_research_study', 'research_study',
    'Table drh_stateful_research_study.research_study should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'research_study'::name, ARRAY[
    'study_id'::name,
    'research_study_identifier'::name,
    'study_display_id'::name,
    'title'::name,
    'description'::name,
    'status_id'::name,
    'start_date'::name,
    'end_date'::name,
    'focus_id'::name,
    'condition_code'::name,
    'region_code'::name,
    'site_id'::name,
    'treatment_modality'::name,
    'protocol_reference_id'::name,
    'phase'::name,
    'primary_purpose_type'::name,
    'keywords'::name,
    'progress_status'::name,
    'why_stopped'::name,
    'eligibility_criteria'::name,
    'target_enrollment'::name,
    'actual_enrollment'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'visibility'::name,
    'archive_status'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'study_id', 'Column drh_stateful_research_study.research_study.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'study_id', 'text', 'Column drh_stateful_research_study.research_study.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'study_id', 'Column drh_stateful_research_study.research_study.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'study_id', 'Column drh_stateful_research_study.research_study.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'research_study_identifier', 'Column drh_stateful_research_study.research_study.research_study_identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'research_study_identifier', 'jsonb', 'Column drh_stateful_research_study.research_study.research_study_identifier should be type jsonb');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'research_study_identifier', 'Column drh_stateful_research_study.research_study.research_study_identifier should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'research_study_identifier', 'Column drh_stateful_research_study.research_study.research_study_identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'study_display_id', 'Column drh_stateful_research_study.research_study.study_display_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'study_display_id', 'character varying(10)', 'Column drh_stateful_research_study.research_study.study_display_id should be type character varying(10)');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'study_display_id', 'Column drh_stateful_research_study.research_study.study_display_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'study_display_id', 'Column drh_stateful_research_study.research_study.study_display_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'title', 'Column drh_stateful_research_study.research_study.title should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'title', 'character varying(255)', 'Column drh_stateful_research_study.research_study.title should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'title', 'Column drh_stateful_research_study.research_study.title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'title', 'Column drh_stateful_research_study.research_study.title should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'description', 'Column drh_stateful_research_study.research_study.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'description', 'text', 'Column drh_stateful_research_study.research_study.description should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'description', 'Column drh_stateful_research_study.research_study.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'description', 'Column drh_stateful_research_study.research_study.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'status_id', 'Column drh_stateful_research_study.research_study.status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'status_id', 'integer', 'Column drh_stateful_research_study.research_study.status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'status_id', 'Column drh_stateful_research_study.research_study.status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'status_id', 'Column drh_stateful_research_study.research_study.status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'start_date', 'Column drh_stateful_research_study.research_study.start_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'start_date', 'date', 'Column drh_stateful_research_study.research_study.start_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'start_date', 'Column drh_stateful_research_study.research_study.start_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'start_date', 'Column drh_stateful_research_study.research_study.start_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'end_date', 'Column drh_stateful_research_study.research_study.end_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'end_date', 'date', 'Column drh_stateful_research_study.research_study.end_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'end_date', 'Column drh_stateful_research_study.research_study.end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'end_date', 'Column drh_stateful_research_study.research_study.end_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'focus_id', 'Column drh_stateful_research_study.research_study.focus_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'focus_id', 'character varying(255)', 'Column drh_stateful_research_study.research_study.focus_id should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'focus_id', 'Column drh_stateful_research_study.research_study.focus_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'focus_id', 'Column drh_stateful_research_study.research_study.focus_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'condition_code', 'Column drh_stateful_research_study.research_study.condition_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'condition_code', 'character varying(255)', 'Column drh_stateful_research_study.research_study.condition_code should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'condition_code', 'Column drh_stateful_research_study.research_study.condition_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'condition_code', 'Column drh_stateful_research_study.research_study.condition_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'region_code', 'Column drh_stateful_research_study.research_study.region_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'region_code', 'character varying(255)', 'Column drh_stateful_research_study.research_study.region_code should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'region_code', 'Column drh_stateful_research_study.research_study.region_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'region_code', 'Column drh_stateful_research_study.research_study.region_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'site_id', 'Column drh_stateful_research_study.research_study.site_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'site_id', 'text', 'Column drh_stateful_research_study.research_study.site_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'site_id', 'Column drh_stateful_research_study.research_study.site_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'site_id', 'Column drh_stateful_research_study.research_study.site_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'treatment_modality', 'Column drh_stateful_research_study.research_study.treatment_modality should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'treatment_modality', 'text', 'Column drh_stateful_research_study.research_study.treatment_modality should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'treatment_modality', 'Column drh_stateful_research_study.research_study.treatment_modality should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'treatment_modality', 'Column drh_stateful_research_study.research_study.treatment_modality should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'protocol_reference_id', 'Column drh_stateful_research_study.research_study.protocol_reference_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'protocol_reference_id', 'text', 'Column drh_stateful_research_study.research_study.protocol_reference_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'protocol_reference_id', 'Column drh_stateful_research_study.research_study.protocol_reference_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'protocol_reference_id', 'Column drh_stateful_research_study.research_study.protocol_reference_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'phase', 'Column drh_stateful_research_study.research_study.phase should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'phase', 'character varying(50)', 'Column drh_stateful_research_study.research_study.phase should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'phase', 'Column drh_stateful_research_study.research_study.phase should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'phase', 'Column drh_stateful_research_study.research_study.phase should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'primary_purpose_type', 'Column drh_stateful_research_study.research_study.primary_purpose_type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'primary_purpose_type', 'character varying(50)', 'Column drh_stateful_research_study.research_study.primary_purpose_type should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'primary_purpose_type', 'Column drh_stateful_research_study.research_study.primary_purpose_type should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'primary_purpose_type', 'Column drh_stateful_research_study.research_study.primary_purpose_type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'keywords', 'Column drh_stateful_research_study.research_study.keywords should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'keywords', 'character varying(250)', 'Column drh_stateful_research_study.research_study.keywords should be type character varying(250)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'keywords', 'Column drh_stateful_research_study.research_study.keywords should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'keywords', 'Column drh_stateful_research_study.research_study.keywords should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'progress_status', 'Column drh_stateful_research_study.research_study.progress_status should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'progress_status', 'character varying(50)', 'Column drh_stateful_research_study.research_study.progress_status should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'progress_status', 'Column drh_stateful_research_study.research_study.progress_status should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'progress_status', 'Column drh_stateful_research_study.research_study.progress_status should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'why_stopped', 'Column drh_stateful_research_study.research_study.why_stopped should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'why_stopped', 'character varying(255)', 'Column drh_stateful_research_study.research_study.why_stopped should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'why_stopped', 'Column drh_stateful_research_study.research_study.why_stopped should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'why_stopped', 'Column drh_stateful_research_study.research_study.why_stopped should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'eligibility_criteria', 'Column drh_stateful_research_study.research_study.eligibility_criteria should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'eligibility_criteria', 'text', 'Column drh_stateful_research_study.research_study.eligibility_criteria should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'eligibility_criteria', 'Column drh_stateful_research_study.research_study.eligibility_criteria should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'eligibility_criteria', 'Column drh_stateful_research_study.research_study.eligibility_criteria should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'target_enrollment', 'Column drh_stateful_research_study.research_study.target_enrollment should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'target_enrollment', 'integer', 'Column drh_stateful_research_study.research_study.target_enrollment should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'target_enrollment', 'Column drh_stateful_research_study.research_study.target_enrollment should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'target_enrollment', 'Column drh_stateful_research_study.research_study.target_enrollment should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'actual_enrollment', 'Column drh_stateful_research_study.research_study.actual_enrollment should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'actual_enrollment', 'integer', 'Column drh_stateful_research_study.research_study.actual_enrollment should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'actual_enrollment', 'Column drh_stateful_research_study.research_study.actual_enrollment should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'actual_enrollment', 'Column drh_stateful_research_study.research_study.actual_enrollment should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'tenant_id', 'Column drh_stateful_research_study.research_study.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'tenant_id', 'text', 'Column drh_stateful_research_study.research_study.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'tenant_id', 'Column drh_stateful_research_study.research_study.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'tenant_id', 'Column drh_stateful_research_study.research_study.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'rec_status_id', 'Column drh_stateful_research_study.research_study.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.research_study.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'rec_status_id', 'Column drh_stateful_research_study.research_study.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'rec_status_id', 'Column drh_stateful_research_study.research_study.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'created_at', 'Column drh_stateful_research_study.research_study.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'created_at', 'Column drh_stateful_research_study.research_study.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_study', 'created_at', 'Column drh_stateful_research_study.research_study.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_study', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.research_study.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'created_by', 'Column drh_stateful_research_study.research_study.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'created_by', 'text', 'Column drh_stateful_research_study.research_study.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'created_by', 'Column drh_stateful_research_study.research_study.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_study', 'created_by', 'Column drh_stateful_research_study.research_study.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_study', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.research_study.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'updated_at', 'Column drh_stateful_research_study.research_study.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'updated_at', 'Column drh_stateful_research_study.research_study.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'updated_at', 'Column drh_stateful_research_study.research_study.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'updated_by', 'Column drh_stateful_research_study.research_study.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'updated_by', 'text', 'Column drh_stateful_research_study.research_study.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'updated_by', 'Column drh_stateful_research_study.research_study.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'updated_by', 'Column drh_stateful_research_study.research_study.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'deleted_at', 'Column drh_stateful_research_study.research_study.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'deleted_at', 'Column drh_stateful_research_study.research_study.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'deleted_at', 'Column drh_stateful_research_study.research_study.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'deleted_by', 'Column drh_stateful_research_study.research_study.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'deleted_by', 'text', 'Column drh_stateful_research_study.research_study.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study', 'deleted_by', 'Column drh_stateful_research_study.research_study.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'deleted_by', 'Column drh_stateful_research_study.research_study.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'visibility', 'Column drh_stateful_research_study.research_study.visibility should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'visibility', 'integer', 'Column drh_stateful_research_study.research_study.visibility should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'visibility', 'Column drh_stateful_research_study.research_study.visibility should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'visibility', 'Column drh_stateful_research_study.research_study.visibility should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study', 'archive_status', 'Column drh_stateful_research_study.research_study.archive_status should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study', 'archive_status', 'boolean', 'Column drh_stateful_research_study.research_study.archive_status should be type boolean');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study', 'archive_status', 'Column drh_stateful_research_study.research_study.archive_status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study', 'archive_status', 'Column drh_stateful_research_study.research_study.archive_status should not have a default');

SELECT * FROM finish();
ROLLBACK;
