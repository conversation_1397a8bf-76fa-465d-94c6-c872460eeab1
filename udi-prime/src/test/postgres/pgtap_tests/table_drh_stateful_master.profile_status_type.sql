SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(16);

SELECT has_table(
    'drh_stateful_master', 'profile_status_type',
    'Should have table drh_stateful_master.profile_status_type'
);

SELECT has_pk(
    'drh_stateful_master', 'profile_status_type',
    'Table drh_stateful_master.profile_status_type should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'profile_status_type'::name, ARRAY[
    'profile_status_type_id'::name,
    'code'::name,
    'description'::name
]);

SELECT has_column(       'drh_stateful_master', 'profile_status_type', 'profile_status_type_id', 'Column drh_stateful_master.profile_status_type.profile_status_type_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'profile_status_type', 'profile_status_type_id', 'integer', 'Column drh_stateful_master.profile_status_type.profile_status_type_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'profile_status_type', 'profile_status_type_id', 'Column drh_stateful_master.profile_status_type.profile_status_type_id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'profile_status_type', 'profile_status_type_id', 'Column drh_stateful_master.profile_status_type.profile_status_type_id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'profile_status_type', 'profile_status_type_id', 'nextval(''drh_stateful_master.profile_status_type_profile_status_type_id_seq''::regclass)', 'Column drh_stateful_master.profile_status_type.profile_status_type_id default is');

SELECT has_column(       'drh_stateful_master', 'profile_status_type', 'code', 'Column drh_stateful_master.profile_status_type.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'profile_status_type', 'code', 'text', 'Column drh_stateful_master.profile_status_type.code should be type text');
SELECT col_not_null(     'drh_stateful_master', 'profile_status_type', 'code', 'Column drh_stateful_master.profile_status_type.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'profile_status_type', 'code', 'Column drh_stateful_master.profile_status_type.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'profile_status_type', 'description', 'Column drh_stateful_master.profile_status_type.description should exist');
SELECT col_type_is(      'drh_stateful_master', 'profile_status_type', 'description', 'character varying(255)', 'Column drh_stateful_master.profile_status_type.description should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'profile_status_type', 'description', 'Column drh_stateful_master.profile_status_type.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'profile_status_type', 'description', 'Column drh_stateful_master.profile_status_type.description should not have a default');

SELECT * FROM finish();
ROLLBACK;
