SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(56);

SELECT has_table(
    'drh_stateful_activity_audit', 'exception_log',
    'Should have table drh_stateful_activity_audit.exception_log'
);

SELECT has_pk(
    'drh_stateful_activity_audit', 'exception_log',
    'Table drh_stateful_activity_audit.exception_log should have a primary key'
);

SELECT columns_are('drh_stateful_activity_audit'::name, 'exception_log'::name, ARRAY[
    'id'::name,
    'function_name'::name,
    'error_code'::name,
    'error_message'::name,
    'error_detail'::name,
    'error_hint'::name,
    'error_context'::name,
    'query'::name,
    'parameters'::name,
    'occurred_at'::name,
    'resolved'::name,
    'resolved_at'::name,
    'resolver_comments'::name
]);

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'id', 'Column drh_stateful_activity_audit.exception_log.id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'id', 'integer', 'Column drh_stateful_activity_audit.exception_log.id should be type integer');
SELECT col_not_null(     'drh_stateful_activity_audit', 'exception_log', 'id', 'Column drh_stateful_activity_audit.exception_log.id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_activity_audit', 'exception_log', 'id', 'Column drh_stateful_activity_audit.exception_log.id should have a default');
SELECT col_default_is(   'drh_stateful_activity_audit', 'exception_log', 'id', 'nextval(''drh_stateful_activity_audit.exception_log_id_seq''::regclass)', 'Column drh_stateful_activity_audit.exception_log.id default is');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'function_name', 'Column drh_stateful_activity_audit.exception_log.function_name should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'function_name', 'character varying(255)', 'Column drh_stateful_activity_audit.exception_log.function_name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_activity_audit', 'exception_log', 'function_name', 'Column drh_stateful_activity_audit.exception_log.function_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'function_name', 'Column drh_stateful_activity_audit.exception_log.function_name should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'error_code', 'Column drh_stateful_activity_audit.exception_log.error_code should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'error_code', 'character varying(50)', 'Column drh_stateful_activity_audit.exception_log.error_code should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'error_code', 'Column drh_stateful_activity_audit.exception_log.error_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'error_code', 'Column drh_stateful_activity_audit.exception_log.error_code should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'error_message', 'Column drh_stateful_activity_audit.exception_log.error_message should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'error_message', 'character varying(255)', 'Column drh_stateful_activity_audit.exception_log.error_message should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_activity_audit', 'exception_log', 'error_message', 'Column drh_stateful_activity_audit.exception_log.error_message should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'error_message', 'Column drh_stateful_activity_audit.exception_log.error_message should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'error_detail', 'Column drh_stateful_activity_audit.exception_log.error_detail should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'error_detail', 'text', 'Column drh_stateful_activity_audit.exception_log.error_detail should be type text');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'error_detail', 'Column drh_stateful_activity_audit.exception_log.error_detail should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'error_detail', 'Column drh_stateful_activity_audit.exception_log.error_detail should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'error_hint', 'Column drh_stateful_activity_audit.exception_log.error_hint should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'error_hint', 'text', 'Column drh_stateful_activity_audit.exception_log.error_hint should be type text');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'error_hint', 'Column drh_stateful_activity_audit.exception_log.error_hint should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'error_hint', 'Column drh_stateful_activity_audit.exception_log.error_hint should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'error_context', 'Column drh_stateful_activity_audit.exception_log.error_context should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'error_context', 'text', 'Column drh_stateful_activity_audit.exception_log.error_context should be type text');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'error_context', 'Column drh_stateful_activity_audit.exception_log.error_context should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'error_context', 'Column drh_stateful_activity_audit.exception_log.error_context should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'query', 'Column drh_stateful_activity_audit.exception_log.query should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'query', 'text', 'Column drh_stateful_activity_audit.exception_log.query should be type text');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'query', 'Column drh_stateful_activity_audit.exception_log.query should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'query', 'Column drh_stateful_activity_audit.exception_log.query should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'parameters', 'Column drh_stateful_activity_audit.exception_log.parameters should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'parameters', 'jsonb', 'Column drh_stateful_activity_audit.exception_log.parameters should be type jsonb');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'parameters', 'Column drh_stateful_activity_audit.exception_log.parameters should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'parameters', 'Column drh_stateful_activity_audit.exception_log.parameters should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'occurred_at', 'Column drh_stateful_activity_audit.exception_log.occurred_at should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'occurred_at', 'timestamp with time zone', 'Column drh_stateful_activity_audit.exception_log.occurred_at should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_activity_audit', 'exception_log', 'occurred_at', 'Column drh_stateful_activity_audit.exception_log.occurred_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'occurred_at', 'Column drh_stateful_activity_audit.exception_log.occurred_at should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'resolved', 'Column drh_stateful_activity_audit.exception_log.resolved should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'resolved', 'character varying(50)', 'Column drh_stateful_activity_audit.exception_log.resolved should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_activity_audit', 'exception_log', 'resolved', 'Column drh_stateful_activity_audit.exception_log.resolved should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'resolved', 'Column drh_stateful_activity_audit.exception_log.resolved should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'resolved_at', 'Column drh_stateful_activity_audit.exception_log.resolved_at should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'resolved_at', 'timestamp with time zone', 'Column drh_stateful_activity_audit.exception_log.resolved_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'resolved_at', 'Column drh_stateful_activity_audit.exception_log.resolved_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'resolved_at', 'Column drh_stateful_activity_audit.exception_log.resolved_at should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'exception_log', 'resolver_comments', 'Column drh_stateful_activity_audit.exception_log.resolver_comments should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'exception_log', 'resolver_comments', 'text', 'Column drh_stateful_activity_audit.exception_log.resolver_comments should be type text');
SELECT col_is_null(      'drh_stateful_activity_audit', 'exception_log', 'resolver_comments', 'Column drh_stateful_activity_audit.exception_log.resolver_comments should allow NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'exception_log', 'resolver_comments', 'Column drh_stateful_activity_audit.exception_log.resolver_comments should not have a default');

SELECT * FROM finish();
ROLLBACK;
