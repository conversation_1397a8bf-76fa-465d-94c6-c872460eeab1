SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(85);

SELECT has_table(
    'drh_stateful_raw_data', 'cgm_raw_db',
    'Should have table drh_stateful_raw_data.cgm_raw_db'
);

SELECT has_pk(
    'drh_stateful_raw_data', 'cgm_raw_db',
    'Table drh_stateful_raw_data.cgm_raw_db should have a primary key'
);

SELECT columns_are('drh_stateful_raw_data'::name, 'cgm_raw_db'::name, ARRAY[
    'db_file_id'::name,
    'file_name'::name,
    'file_url'::name,
    'upload_timestamp'::name,
    'uploaded_by'::name,
    'file_size'::name,
    'is_processed'::name,
    'processed_at'::name,
    'process_status'::name,
    'db_file_metadata'::name,
    'db_type'::name,
    'study_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_id', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_id', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'db_file_id', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_db.file_name should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'file_name', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_db.file_name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_db.file_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_db.file_name should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_db.file_url should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'file_url', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.file_url should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_db.file_url should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_db.file_url should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_db.upload_timestamp should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'upload_timestamp', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_db.upload_timestamp should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_db.upload_timestamp should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_db.upload_timestamp should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_db.uploaded_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'uploaded_by', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_db.uploaded_by should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_db.uploaded_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_db.uploaded_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_db.file_size should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'file_size', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_db.file_size should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_db.file_size should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_db.file_size should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_db.is_processed should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'is_processed', 'boolean', 'Column drh_stateful_raw_data.cgm_raw_db.is_processed should be type boolean');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_db.is_processed should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_db.is_processed should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_db.processed_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'processed_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_db.processed_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_db.processed_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_db.processed_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'process_status', 'Column drh_stateful_raw_data.cgm_raw_db.process_status should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'process_status', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_db.process_status should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'process_status', 'Column drh_stateful_raw_data.cgm_raw_db.process_status should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'process_status', 'Column drh_stateful_raw_data.cgm_raw_db.process_status should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_metadata', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_metadata should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_metadata', 'jsonb', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'db_file_metadata', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'db_file_metadata', 'Column drh_stateful_raw_data.cgm_raw_db.db_file_metadata should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'db_type', 'Column drh_stateful_raw_data.cgm_raw_db.db_type should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'db_type', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_db.db_type should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'db_type', 'Column drh_stateful_raw_data.cgm_raw_db.db_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'db_type', 'Column drh_stateful_raw_data.cgm_raw_db.db_type should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_db.study_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'study_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.study_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_db.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_db.study_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_db.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'tenant_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_db.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_db.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_db.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'rec_status_id', 'integer', 'Column drh_stateful_raw_data.cgm_raw_db.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_db', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_db.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_db.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_db.created_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'created_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_db.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_db.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'cgm_raw_db', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_db.created_at should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'cgm_raw_db', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_raw_data.cgm_raw_db.created_at default is');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_db.created_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'created_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.created_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_db.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'cgm_raw_db', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_db.created_by should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'cgm_raw_db', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_raw_data.cgm_raw_db.created_by default is');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_db.updated_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_db.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_db.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_db.updated_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_db.updated_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'updated_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_db.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_db.updated_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_db', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_db', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_db.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
