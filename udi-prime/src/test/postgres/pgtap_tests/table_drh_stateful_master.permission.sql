SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(57);

SELECT has_table(
    'drh_stateful_master', 'permission',
    'Should have table drh_stateful_master.permission'
);

SELECT has_pk(
    'drh_stateful_master', 'permission',
    'Table drh_stateful_master.permission should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'permission'::name, ARRAY[
    'permission_id'::name,
    'code'::name,
    'permission_name'::name,
    'description'::name,
    'resource_type'::name,
    'action_type'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'permission', 'permission_id', 'Column drh_stateful_master.permission.permission_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'permission_id', 'text', 'Column drh_stateful_master.permission.permission_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'permission', 'permission_id', 'Column drh_stateful_master.permission.permission_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'permission_id', 'Column drh_stateful_master.permission.permission_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'code', 'Column drh_stateful_master.permission.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'code', 'character varying(50)', 'Column drh_stateful_master.permission.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'permission', 'code', 'Column drh_stateful_master.permission.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'code', 'Column drh_stateful_master.permission.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'permission_name', 'Column drh_stateful_master.permission.permission_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'permission_name', 'character varying(255)', 'Column drh_stateful_master.permission.permission_name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'permission', 'permission_name', 'Column drh_stateful_master.permission.permission_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'permission_name', 'Column drh_stateful_master.permission.permission_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'description', 'Column drh_stateful_master.permission.description should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'description', 'text', 'Column drh_stateful_master.permission.description should be type text');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'description', 'Column drh_stateful_master.permission.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'description', 'Column drh_stateful_master.permission.description should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'resource_type', 'Column drh_stateful_master.permission.resource_type should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'resource_type', 'character varying(50)', 'Column drh_stateful_master.permission.resource_type should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'resource_type', 'Column drh_stateful_master.permission.resource_type should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'resource_type', 'Column drh_stateful_master.permission.resource_type should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'action_type', 'Column drh_stateful_master.permission.action_type should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'action_type', 'character varying(50)', 'Column drh_stateful_master.permission.action_type should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'permission', 'action_type', 'Column drh_stateful_master.permission.action_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'action_type', 'Column drh_stateful_master.permission.action_type should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'rec_status_id', 'Column drh_stateful_master.permission.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'rec_status_id', 'integer', 'Column drh_stateful_master.permission.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'permission', 'rec_status_id', 'Column drh_stateful_master.permission.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'rec_status_id', 'Column drh_stateful_master.permission.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'created_at', 'Column drh_stateful_master.permission.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.permission.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'created_at', 'Column drh_stateful_master.permission.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'permission', 'created_at', 'Column drh_stateful_master.permission.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'permission', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.permission.created_at default is');

SELECT has_column(       'drh_stateful_master', 'permission', 'created_by', 'Column drh_stateful_master.permission.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'created_by', 'text', 'Column drh_stateful_master.permission.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'created_by', 'Column drh_stateful_master.permission.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'permission', 'created_by', 'Column drh_stateful_master.permission.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'permission', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.permission.created_by default is');

SELECT has_column(       'drh_stateful_master', 'permission', 'updated_at', 'Column drh_stateful_master.permission.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.permission.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'updated_at', 'Column drh_stateful_master.permission.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'updated_at', 'Column drh_stateful_master.permission.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'updated_by', 'Column drh_stateful_master.permission.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'updated_by', 'text', 'Column drh_stateful_master.permission.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'updated_by', 'Column drh_stateful_master.permission.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'updated_by', 'Column drh_stateful_master.permission.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'deleted_at', 'Column drh_stateful_master.permission.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.permission.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'deleted_at', 'Column drh_stateful_master.permission.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'deleted_at', 'Column drh_stateful_master.permission.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'permission', 'deleted_by', 'Column drh_stateful_master.permission.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'permission', 'deleted_by', 'text', 'Column drh_stateful_master.permission.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'permission', 'deleted_by', 'Column drh_stateful_master.permission.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'permission', 'deleted_by', 'Column drh_stateful_master.permission.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
