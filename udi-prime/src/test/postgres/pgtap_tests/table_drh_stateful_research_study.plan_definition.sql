SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(101);

SELECT has_table(
    'drh_stateful_research_study', 'plan_definition',
    'Should have table drh_stateful_research_study.plan_definition'
);

SELECT has_pk(
    'drh_stateful_research_study', 'plan_definition',
    'Table drh_stateful_research_study.plan_definition should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'plan_definition'::name, ARRAY[
    'id'::name,
    'url'::name,
    'version'::name,
    'name'::name,
    'title'::name,
    'status_id'::name,
    'experimental'::name,
    'description'::name,
    'purpose'::name,
    'usage'::name,
    'copyright'::name,
    'publisher'::name,
    'jurisdiction_code'::name,
    'related_artifact'::name,
    'org_party_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'study_id'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'id', 'Column drh_stateful_research_study.plan_definition.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'id', 'text', 'Column drh_stateful_research_study.plan_definition.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'id', 'Column drh_stateful_research_study.plan_definition.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'id', 'Column drh_stateful_research_study.plan_definition.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'url', 'Column drh_stateful_research_study.plan_definition.url should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'url', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.url should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'url', 'Column drh_stateful_research_study.plan_definition.url should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'url', 'Column drh_stateful_research_study.plan_definition.url should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'version', 'Column drh_stateful_research_study.plan_definition.version should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'version', 'character varying(50)', 'Column drh_stateful_research_study.plan_definition.version should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'version', 'Column drh_stateful_research_study.plan_definition.version should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'version', 'Column drh_stateful_research_study.plan_definition.version should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'name', 'Column drh_stateful_research_study.plan_definition.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'name', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'name', 'Column drh_stateful_research_study.plan_definition.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'name', 'Column drh_stateful_research_study.plan_definition.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'title', 'Column drh_stateful_research_study.plan_definition.title should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'title', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.title should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'title', 'Column drh_stateful_research_study.plan_definition.title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'title', 'Column drh_stateful_research_study.plan_definition.title should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'status_id', 'Column drh_stateful_research_study.plan_definition.status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'status_id', 'integer', 'Column drh_stateful_research_study.plan_definition.status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'status_id', 'Column drh_stateful_research_study.plan_definition.status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'status_id', 'Column drh_stateful_research_study.plan_definition.status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'experimental', 'Column drh_stateful_research_study.plan_definition.experimental should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'experimental', 'character varying(50)', 'Column drh_stateful_research_study.plan_definition.experimental should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'experimental', 'Column drh_stateful_research_study.plan_definition.experimental should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'experimental', 'Column drh_stateful_research_study.plan_definition.experimental should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'description', 'Column drh_stateful_research_study.plan_definition.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'description', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.description should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'description', 'Column drh_stateful_research_study.plan_definition.description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'description', 'Column drh_stateful_research_study.plan_definition.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'purpose', 'Column drh_stateful_research_study.plan_definition.purpose should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'purpose', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.purpose should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'purpose', 'Column drh_stateful_research_study.plan_definition.purpose should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'purpose', 'Column drh_stateful_research_study.plan_definition.purpose should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'usage', 'Column drh_stateful_research_study.plan_definition.usage should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'usage', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.usage should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'usage', 'Column drh_stateful_research_study.plan_definition.usage should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'usage', 'Column drh_stateful_research_study.plan_definition.usage should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'copyright', 'Column drh_stateful_research_study.plan_definition.copyright should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'copyright', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.copyright should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'copyright', 'Column drh_stateful_research_study.plan_definition.copyright should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'copyright', 'Column drh_stateful_research_study.plan_definition.copyright should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'publisher', 'Column drh_stateful_research_study.plan_definition.publisher should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'publisher', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.publisher should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'publisher', 'Column drh_stateful_research_study.plan_definition.publisher should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'publisher', 'Column drh_stateful_research_study.plan_definition.publisher should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'jurisdiction_code', 'Column drh_stateful_research_study.plan_definition.jurisdiction_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'jurisdiction_code', 'character varying(100)', 'Column drh_stateful_research_study.plan_definition.jurisdiction_code should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'jurisdiction_code', 'Column drh_stateful_research_study.plan_definition.jurisdiction_code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'jurisdiction_code', 'Column drh_stateful_research_study.plan_definition.jurisdiction_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'related_artifact', 'Column drh_stateful_research_study.plan_definition.related_artifact should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'related_artifact', 'character varying(255)', 'Column drh_stateful_research_study.plan_definition.related_artifact should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'related_artifact', 'Column drh_stateful_research_study.plan_definition.related_artifact should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'related_artifact', 'Column drh_stateful_research_study.plan_definition.related_artifact should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'org_party_id', 'Column drh_stateful_research_study.plan_definition.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'org_party_id', 'text', 'Column drh_stateful_research_study.plan_definition.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'org_party_id', 'Column drh_stateful_research_study.plan_definition.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'org_party_id', 'Column drh_stateful_research_study.plan_definition.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'tenant_id', 'Column drh_stateful_research_study.plan_definition.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'tenant_id', 'text', 'Column drh_stateful_research_study.plan_definition.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'tenant_id', 'Column drh_stateful_research_study.plan_definition.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'tenant_id', 'Column drh_stateful_research_study.plan_definition.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'rec_status_id', 'Column drh_stateful_research_study.plan_definition.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.plan_definition.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'rec_status_id', 'Column drh_stateful_research_study.plan_definition.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'rec_status_id', 'Column drh_stateful_research_study.plan_definition.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'created_at', 'Column drh_stateful_research_study.plan_definition.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.plan_definition.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'created_at', 'Column drh_stateful_research_study.plan_definition.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'plan_definition', 'created_at', 'Column drh_stateful_research_study.plan_definition.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'plan_definition', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.plan_definition.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'created_by', 'Column drh_stateful_research_study.plan_definition.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'created_by', 'text', 'Column drh_stateful_research_study.plan_definition.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'created_by', 'Column drh_stateful_research_study.plan_definition.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'plan_definition', 'created_by', 'Column drh_stateful_research_study.plan_definition.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'plan_definition', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.plan_definition.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'updated_at', 'Column drh_stateful_research_study.plan_definition.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.plan_definition.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'updated_at', 'Column drh_stateful_research_study.plan_definition.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'updated_at', 'Column drh_stateful_research_study.plan_definition.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'updated_by', 'Column drh_stateful_research_study.plan_definition.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'updated_by', 'text', 'Column drh_stateful_research_study.plan_definition.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'updated_by', 'Column drh_stateful_research_study.plan_definition.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'updated_by', 'Column drh_stateful_research_study.plan_definition.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'deleted_at', 'Column drh_stateful_research_study.plan_definition.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.plan_definition.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'deleted_at', 'Column drh_stateful_research_study.plan_definition.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'deleted_at', 'Column drh_stateful_research_study.plan_definition.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'deleted_by', 'Column drh_stateful_research_study.plan_definition.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'deleted_by', 'text', 'Column drh_stateful_research_study.plan_definition.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'plan_definition', 'deleted_by', 'Column drh_stateful_research_study.plan_definition.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'deleted_by', 'Column drh_stateful_research_study.plan_definition.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'plan_definition', 'study_id', 'Column drh_stateful_research_study.plan_definition.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'plan_definition', 'study_id', 'text', 'Column drh_stateful_research_study.plan_definition.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'plan_definition', 'study_id', 'Column drh_stateful_research_study.plan_definition.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'plan_definition', 'study_id', 'Column drh_stateful_research_study.plan_definition.study_id should not have a default');

SELECT * FROM finish();
ROLLBACK;
