SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(63);

SELECT has_table(
    'drh_stateful_db_import_migration', 'participant',
    'Should have table drh_stateful_db_import_migration.participant'
);

SELECT hasnt_pk(
    'drh_stateful_db_import_migration', 'participant',
    'Table drh_stateful_db_import_migration.participant should have a primary key'
);

SELECT columns_are('drh_stateful_db_import_migration'::name, 'participant'::name, ARRAY[
    'db_file_id'::name,
    'tenant_id'::name,
    'study_display_id'::name,
    'participant_display_id'::name,
    'site_id'::name,
    'diagnosis_icd'::name,
    'med_rxnorm'::name,
    'treatment_modality'::name,
    'gender'::name,
    'race_ethnicity'::name,
    'age'::name,
    'bmi'::name,
    'baseline_hba1c'::name,
    'diabetes_type'::name,
    'study_arm'::name
]);

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'db_file_id', 'Column drh_stateful_db_import_migration.participant.db_file_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'db_file_id', 'text', 'Column drh_stateful_db_import_migration.participant.db_file_id should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'db_file_id', 'Column drh_stateful_db_import_migration.participant.db_file_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'db_file_id', 'Column drh_stateful_db_import_migration.participant.db_file_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'tenant_id', 'Column drh_stateful_db_import_migration.participant.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'tenant_id', 'text', 'Column drh_stateful_db_import_migration.participant.tenant_id should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'tenant_id', 'Column drh_stateful_db_import_migration.participant.tenant_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'tenant_id', 'Column drh_stateful_db_import_migration.participant.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'study_display_id', 'Column drh_stateful_db_import_migration.participant.study_display_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'study_display_id', 'text', 'Column drh_stateful_db_import_migration.participant.study_display_id should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'study_display_id', 'Column drh_stateful_db_import_migration.participant.study_display_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'study_display_id', 'Column drh_stateful_db_import_migration.participant.study_display_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'participant_display_id', 'Column drh_stateful_db_import_migration.participant.participant_display_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'participant_display_id', 'text', 'Column drh_stateful_db_import_migration.participant.participant_display_id should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'participant_display_id', 'Column drh_stateful_db_import_migration.participant.participant_display_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'participant_display_id', 'Column drh_stateful_db_import_migration.participant.participant_display_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'site_id', 'Column drh_stateful_db_import_migration.participant.site_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'site_id', 'text', 'Column drh_stateful_db_import_migration.participant.site_id should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'site_id', 'Column drh_stateful_db_import_migration.participant.site_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'site_id', 'Column drh_stateful_db_import_migration.participant.site_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'diagnosis_icd', 'Column drh_stateful_db_import_migration.participant.diagnosis_icd should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'diagnosis_icd', 'text', 'Column drh_stateful_db_import_migration.participant.diagnosis_icd should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'diagnosis_icd', 'Column drh_stateful_db_import_migration.participant.diagnosis_icd should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'diagnosis_icd', 'Column drh_stateful_db_import_migration.participant.diagnosis_icd should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'med_rxnorm', 'Column drh_stateful_db_import_migration.participant.med_rxnorm should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'med_rxnorm', 'text', 'Column drh_stateful_db_import_migration.participant.med_rxnorm should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'med_rxnorm', 'Column drh_stateful_db_import_migration.participant.med_rxnorm should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'med_rxnorm', 'Column drh_stateful_db_import_migration.participant.med_rxnorm should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'treatment_modality', 'Column drh_stateful_db_import_migration.participant.treatment_modality should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'treatment_modality', 'text', 'Column drh_stateful_db_import_migration.participant.treatment_modality should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'treatment_modality', 'Column drh_stateful_db_import_migration.participant.treatment_modality should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'treatment_modality', 'Column drh_stateful_db_import_migration.participant.treatment_modality should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'gender', 'Column drh_stateful_db_import_migration.participant.gender should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'gender', 'text', 'Column drh_stateful_db_import_migration.participant.gender should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'gender', 'Column drh_stateful_db_import_migration.participant.gender should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'gender', 'Column drh_stateful_db_import_migration.participant.gender should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'race_ethnicity', 'Column drh_stateful_db_import_migration.participant.race_ethnicity should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'race_ethnicity', 'text', 'Column drh_stateful_db_import_migration.participant.race_ethnicity should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'race_ethnicity', 'Column drh_stateful_db_import_migration.participant.race_ethnicity should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'race_ethnicity', 'Column drh_stateful_db_import_migration.participant.race_ethnicity should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'age', 'Column drh_stateful_db_import_migration.participant.age should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'age', 'text', 'Column drh_stateful_db_import_migration.participant.age should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'age', 'Column drh_stateful_db_import_migration.participant.age should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'age', 'Column drh_stateful_db_import_migration.participant.age should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'bmi', 'Column drh_stateful_db_import_migration.participant.bmi should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'bmi', 'text', 'Column drh_stateful_db_import_migration.participant.bmi should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'bmi', 'Column drh_stateful_db_import_migration.participant.bmi should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'bmi', 'Column drh_stateful_db_import_migration.participant.bmi should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'baseline_hba1c', 'Column drh_stateful_db_import_migration.participant.baseline_hba1c should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'baseline_hba1c', 'text', 'Column drh_stateful_db_import_migration.participant.baseline_hba1c should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'baseline_hba1c', 'Column drh_stateful_db_import_migration.participant.baseline_hba1c should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'baseline_hba1c', 'Column drh_stateful_db_import_migration.participant.baseline_hba1c should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'diabetes_type', 'Column drh_stateful_db_import_migration.participant.diabetes_type should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'diabetes_type', 'text', 'Column drh_stateful_db_import_migration.participant.diabetes_type should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'diabetes_type', 'Column drh_stateful_db_import_migration.participant.diabetes_type should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'diabetes_type', 'Column drh_stateful_db_import_migration.participant.diabetes_type should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'participant', 'study_arm', 'Column drh_stateful_db_import_migration.participant.study_arm should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'participant', 'study_arm', 'text', 'Column drh_stateful_db_import_migration.participant.study_arm should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'participant', 'study_arm', 'Column drh_stateful_db_import_migration.participant.study_arm should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'participant', 'study_arm', 'Column drh_stateful_db_import_migration.participant.study_arm should not have a default');

SELECT * FROM finish();
ROLLBACK;
