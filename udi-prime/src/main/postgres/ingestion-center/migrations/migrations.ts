import * as ic1 from "./migrate-basic-infrastructure.ts";
import * as ic2 from "./migrate-models-research-study.ts";
import * as ic3 from "./seed-data/migrate-master-seed-dml.ts";
import * as ic4 from "./functions/migrate-util-functions.ts";
import * as ic5 from "./indexes/migrate-master-create-index-dml.ts";
import * as ic6 from "./functions/migrate-add-user-profile.ts";
import * as ic7 from "./functions/migrate-create-study.ts";
import * as ic8 from "./synthetic-data/migrate-synthetic-test-data.ts";
import * as ic9 from "./functions/migrate-add-participant.ts";
import * as ic10 from "./views/migrate-master-schema-views.ts";
import * as ic11 from "./functions/migrate-save-participant-file.ts";
import * as ic12 from "./functions/migrate-cgm-data-management.ts";
import * as ic13 from "./functions/migrate-db-migration.ts";
import * as ic14 from "./functions/migrate-activity-interaction.ts";
import * as ic15 from "./functions/migrate-db-rls.ts";
import * as ic16 from "./functions/migrate-authentication.ts";
import * as ic17 from "./functions/migrate-ai-permission.ts";
import * as ic18 from "./functions/migrate-ai-insights.ts";

// Create an array containing the modules
const ic = [
    ic1,
    ic2,
    ic3,
    ic4,
    ic5,
    ic6,
    ic7,
    ic8,
    ic9,
    ic10,
    ic11,
    ic12,
    ic13,
    ic14,
    ic15,
    ic16,
    ic17,
    ic18,
];

// Export the array
export { ic };
