 SET search_path TO "drh_stateful_party";
 -- Indexes  for drh_stateful_party schema

 -- Indexes for record_status
CREATE INDEX IF NOT EXISTS idx_record_status_created_at ON record_status (created_at);
CREATE INDEX IF NOT EXISTS idx_record_status_deleted_at ON record_status (deleted_at);
CREATE INDEX IF NOT EXISTS idx_record_status_code ON record_status(code);


-- Indexes for entity
-- CREATE INDEX IF NOT EXISTS  idx_entity_type ON entity_type(entity_type);
-- CREATE INDEX IF NOT EXISTS  idx_entity_name ON entity_type(name);
-- CREATE INDEX IF NOT EXISTS  idx_entity_rec_status ON entity_type(rec_status_id);


 SET search_path TO "drh_stateful_research_study";
  -- Indexes  for drh_stateful_research_study schema


 -- Indexes for associated_party_type
CREATE INDEX IF NOT EXISTS idx_associated_party_type_name ON associated_party_type (name);
CREATE INDEX IF NOT EXISTS idx_associated_party_type_rec_status ON associated_party_type(rec_status_id);


----- Indexes for laboratory
CREATE INDEX IF NOT EXISTS idx_laboratory_name ON laboratory(name);
CREATE INDEX IF NOT EXISTS idx_laboratory_city ON laboratory(city);
CREATE INDEX IF NOT EXISTS idx_laboratory_tenant ON laboratory(tenant_id);
CREATE INDEX IF NOT EXISTS idx_laboratory_rec_status ON laboratory(rec_status_id);
CREATE INDEX IF NOT EXISTS idx_laboratory_parent_org ON laboratory(parent_org_id);

-- Indexes for organization
CREATE INDEX IF NOT EXISTS idx_organization_name ON organization(name);
CREATE INDEX IF NOT EXISTS idx_organization_type_code ON organization(type_code);
CREATE INDEX IF NOT EXISTS  idx_organization_state ON organization(state);
CREATE INDEX IF NOT EXISTS  idx_organization_rec_status ON organization(rec_status_id);
CREATE INDEX IF NOT EXISTS idx_organization_city ON organization(city);
CREATE INDEX IF NOT EXISTS idx_organization_parent ON organization(parent_organization_id);


-- Indexes for location
CREATE INDEX IF NOT EXISTS idx_location_name ON  location(name);
CREATE INDEX IF NOT EXISTS idx_location_type_code ON location(type_code);
CREATE INDEX IF NOT EXISTS idx_location_city ON location(city);
CREATE INDEX IF NOT EXISTS idx_location_state ON location(state);
CREATE INDEX IF NOT EXISTS idx_location_rec_status ON location (rec_status_id);
CREATE INDEX IF NOT EXISTS idx_location_part_of ON location(part_of_id);


-- Indexes for practitioner
CREATE INDEX IF NOT EXISTS idx_practitioner_name ON  practitioner(name);
CREATE INDEX IF NOT EXISTS idx_practitioner_gender ON  practitioner(gender_type_id);
CREATE INDEX IF NOT EXISTS idx_practitioner_tenant ON practitioner(tenant_id);
CREATE INDEX IF NOT EXISTS idx_practitioner_rec_status ON practitioner(rec_status_id);




-- Indexes for qualification
CREATE INDEX IF NOT EXISTS idx_qualification_code ON qualification (qualification_code);
CREATE INDEX IF NOT EXISTS idx_qualification_issuer ON qualification(issuer_name);
-- CREATE INDEX IF NOT EXISTS idx_qualification_entity ON qualification(entity_id);
CREATE INDEX IF NOT EXISTS idx_qualification_tenant ON qualification(tenant_id);
CREATE INDEX IF NOT EXISTS idx_qualification_rec_status ON qualification(rec_status_id);


-- Indexes for telecom
-- CREATE INDEX IF NOT EXISTS idx_telecom_entity ON telecom(entity_id);
CREATE INDEX IF NOT EXISTS idx_telecom_type ON telecom(telecom_type);
CREATE INDEX IF NOT EXISTS idx_telecom_value ON telecom(telecom_value);
CREATE INDEX IF NOT EXISTS idx_telecom_tenant ON telecom(tenant_id);
CREATE INDEX IF NOT EXISTS idx_telecom_rec_status ON telecom(rec_status_id);


-- Indexes for address
-- CREATE INDEX IF NOT EXISTS idx_address_entity ON address(entity_id);
CREATE INDEX IF NOT EXISTS idx_address_type ON address(address_type);
CREATE INDEX IF NOT EXISTS idx_address_city ON address(city);



 SET search_path TO "drh_stateful_master"; 
  -- Indexes  for drh_stateful_master schema

   -- Indexes for research_study table
CREATE INDEX IF NOT EXISTS idx_research_study_study_id ON drh_stateful_research_study.research_study(study_id);
CREATE INDEX IF NOT EXISTS idx_research_study_tenant_id ON drh_stateful_research_study.research_study(tenant_id);
CREATE INDEX IF NOT EXISTS idx_research_study_visibility ON drh_stateful_research_study.research_study(visibility);
CREATE INDEX IF NOT EXISTS idx_research_study_deleted_at ON drh_stateful_research_study.research_study(deleted_at) WHERE deleted_at IS NULL;

-- Indexes for research_subject table
CREATE INDEX IF NOT EXISTS idx_research_subject_study_reference ON drh_stateful_research_study.research_subject(study_reference);
CREATE INDEX IF NOT EXISTS idx_research_subject_rsubject_id ON drh_stateful_research_study.research_subject(rsubject_id);
CREATE INDEX IF NOT EXISTS idx_research_subject_individual_reference ON drh_stateful_research_study.research_subject(individual_reference);

 -- Indexes for research_study_focus
CREATE INDEX IF NOT EXISTS idx_research_study_focus_code ON research_study_focus(code);
CREATE INDEX IF NOT EXISTS idx_research_study_focus_display ON research_study_focus(display);
CREATE INDEX IF NOT EXISTS idx_research_study_focus_rec_status ON research_study_focus(rec_status_id);



-- Indexes for research_study_condition
CREATE INDEX IF NOT EXISTS idx_research_study_condition_code ON research_study_condition(code);
CREATE INDEX IF NOT EXISTS idx_research_study_condition_display ON research_study_condition(display);
CREATE INDEX IF NOT EXISTS idx_research_study_condition_rec_status ON research_study_condition(rec_status_id);


-- Indexes for cgm_raw_zip_data
CREATE INDEX IF NOT EXISTS idx_cgm_raw_zip_data_study_id ON drh_stateful_raw_data.cgm_raw_zip_data(study_id);
CREATE INDEX IF NOT EXISTS idx_cgm_raw_zip_data_tenant_id ON drh_stateful_raw_data.cgm_raw_zip_data(tenant_id);

CREATE INDEX IF NOT EXISTS idx_cgm_raw_zip_data_study_tenant 
ON drh_stateful_raw_data.cgm_raw_zip_data(study_id, tenant_id);


-- Indexes for cgm_raw_upload_data
CREATE INDEX IF NOT EXISTS idx_cgm_raw_upload_data_study_id ON drh_stateful_raw_data.cgm_raw_upload_data(study_id);
CREATE INDEX IF NOT EXISTS idx_cgm_raw_upload_data_tenant_id ON drh_stateful_raw_data.cgm_raw_upload_data(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cgm_raw_upload_data_zip_file_id ON drh_stateful_raw_data.cgm_raw_upload_data(zip_file_id);
CREATE INDEX IF NOT EXISTS idx_cgm_raw_upload_data_study_tenant 
ON drh_stateful_raw_data.cgm_raw_upload_data(study_id, tenant_id);

-- Indexes for raw_cgm_extract_data
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_data_raw_file_id ON drh_stateful_raw_data.raw_cgm_extract_data(raw_file_id);
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_data_study_id ON drh_stateful_raw_data.raw_cgm_extract_data(study_id);
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_data_tenant_id ON drh_stateful_raw_data.raw_cgm_extract_data(tenant_id);
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_data_participant_sid ON drh_stateful_raw_data.raw_cgm_extract_data(participant_sid);
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_data_deleted_at ON drh_stateful_raw_data.raw_cgm_extract_data(deleted_at) WHERE deleted_at IS NULL;


-- GIN Index for jsonb column in raw_cgm_extract_data
CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_jsonb 
ON drh_stateful_raw_data.raw_cgm_extract_data USING gin (file_meta_data);

-- Indexes for cgm_observation
CREATE INDEX IF NOT EXISTS idx_cgm_obs_study_participant_date 
ON drh_stateful_raw_observation.cgm_observation (study_id, research_subject_id, tenant_id, date_time);
CREATE INDEX IF NOT EXISTS idx_cgm_observation_deleted_at ON drh_stateful_raw_observation.cgm_observation(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_cgm_observation_study_id ON drh_stateful_raw_observation.cgm_observation(study_id);
CREATE INDEX IF NOT EXISTS idx_cgm_observation_research_subject_id ON drh_stateful_raw_observation.cgm_observation(research_subject_id);

CREATE INDEX IF NOT EXISTS idx_raw_cgm_extract_participant 
ON drh_stateful_raw_data.raw_cgm_extract_data (participant_sid, study_id, tenant_id);


CREATE INDEX IF NOT EXISTS idx_nutrition_intake_subject_id ON drh_stateful_research_study.nutrition_intake(subject_id);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_occurrence_time ON drh_stateful_research_study.nutrition_intake(occurrence_time);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_status_code ON drh_stateful_research_study.nutrition_intake(status_code);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_meal_type_id ON drh_stateful_research_study.nutrition_intake(meal_type_id);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_value_unit_id ON drh_stateful_research_study.nutrition_intake(value_unit_id);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_subject_time ON drh_stateful_research_study.nutrition_intake(subject_id, occurrence_time);
CREATE INDEX IF NOT EXISTS idx_nutrition_intake_status_time ON drh_stateful_research_study.nutrition_intake(status_code, occurrence_time);

CREATE INDEX IF NOT EXISTS idx_observation_fitness_subject_id ON drh_stateful_research_study.observation_fitness(subject_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_effective_datetime ON drh_stateful_research_study.observation_fitness(effective_datetime);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_status_id ON drh_stateful_research_study.observation_fitness(status_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_category_id ON drh_stateful_research_study.observation_fitness(category_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_activity_type_id ON drh_stateful_research_study.observation_fitness(activity_type_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_device_id ON drh_stateful_research_study.observation_fitness(device_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_intensity_type_id ON drh_stateful_research_study.observation_fitness(intensity_type_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_intensity_value_id ON drh_stateful_research_study.observation_fitness(intensity_value_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_rec_status_id ON drh_stateful_research_study.observation_fitness(rec_status_id);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_subject_time ON drh_stateful_research_study.observation_fitness(subject_id, effective_datetime);
CREATE INDEX IF NOT EXISTS idx_observation_fitness_category_activity ON drh_stateful_research_study.observation_fitness(category_id, activity_type_id);

CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_observation_id ON drh_stateful_research_study.observation_fitness_component(observation_id);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_component_type_id ON drh_stateful_research_study.observation_fitness_component(component_type_id);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_method_id ON drh_stateful_research_study.observation_fitness_component(method_id);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_unit_code ON drh_stateful_research_study.observation_fitness_component(unit_code);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_rec_status_id ON drh_stateful_research_study.observation_fitness_component(rec_status_id);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_observation_component ON drh_stateful_research_study.observation_fitness_component(observation_id, component_type_id);
CREATE INDEX IF NOT EXISTS idx_obs_fit_comp_method_unit ON drh_stateful_research_study.observation_fitness_component(method_id, unit_code);
