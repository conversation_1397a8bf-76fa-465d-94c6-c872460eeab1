#!/usr/bin/env -S deno run --allow-all

/**
 * This TypeScript file implements a SQL migration feature for PostgreSQL databases using Deno.
 * It provides methods for defining and executing migrations.
 *
 * @module Information_Schema_Lifecycle_Management_Migration
 */

import * as ws from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/lib/universal/whitespace.ts";
import * as SQLa from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/mod.ts";
import * as typ from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/typical/mod.ts";
import * as udm from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/udm/mod.ts";
import { boolean } from "https://deno.land/x/zod@v3.21.4/types.ts";
import { bigFloatNullable } from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/udm/models.ts";
// import * as mod from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/dialect/pg/routine.ts";
// import * as tmpl from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/emit/mod.ts";

interface MigrationVersion {
  readonly description: string;
  readonly dateTime: Date;
}

export const migrationInput: MigrationVersion = {
  description: "research-study",
  dateTime: new Date(2024, 6, 28, 13, 16),
};
//export const { gm, gts } = udm;

// const { typical: typ, typical: { SQLa, ws } } = typ;

type EmitContext = typ.typical.SQLa.SqlEmitContext;

//export const { gm, gts } = udm;

const gts = typ.governedTemplateState<
  typ.TypicalDomainQS,
  typ.TypicalDomainsQS,
  EmitContext
>();
const gm = typ.governedModel<
  typ.TypicalDomainQS,
  typ.TypicalDomainsQS,
  EmitContext
>(gts.ddlOptions);

const {
  text,
  textNullable,
  integer,
  date,
  varChar,
  varCharNullable,
  integerNullable,
  //boolean,
  dateTimeNullable,
  dateNullable,
  dateTime,
  floatNullable,
  ulidNullable,
  ulid,
  jsonbNullable,
  jsonB,
  //booleanNullable,
  blobTextNullable,
  bigFloat,
} = gm.domains;
const { autoIncPrimaryKey: autoIncPK, ulidPrimaryKey: primaryKey } = gm.keys;

export const researchSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_research_study",
  {
    isIdempotent: true,
  },
);

export const rawSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_raw_observation",
  {
    isIdempotent: true,
  },
);

export const rawDataSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_raw_data",
  {
    isIdempotent: true,
  },
);

export const activitySchema = SQLa.sqlSchemaDefn(
  "drh_stateful_activity_audit",
  {
    isIdempotent: true,
  },
);

export const aiSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_ai_insights",
  {
    isIdempotent: true,
  },
);

export const partySchema = SQLa.sqlSchemaDefn(
  "drh_stateful_party",
  {
    isIdempotent: true,
  },
);

export const authSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_authentication",
  {
    isIdempotent: true,
  },
);

export const masterSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_master",
  {
    isIdempotent: true,
  },
);

export const statelessStudySchema = SQLa.sqlSchemaDefn(
  "drh_stateless_research_study",
  {
    isIdempotent: true,
  },
);

export const statelessRawObservation = SQLa.sqlSchemaDefn(
  "drh_stateless_raw_observation",
  {
    isIdempotent: true,
  },
);

export const statelessAuthentication = SQLa.sqlSchemaDefn(
  "drh_stateless_authentication",
  {
    isIdempotent: true,
  },
);

export const statelessUtilSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_util",
  {
    isIdempotent: true,
  },
);

export const dbImportMigrateSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_db_import_migration",
  {
    isIdempotent: true,
  },
);

export const statelessactivitySchema = SQLa.sqlSchemaDefn(
  "drh_stateless_activity_audit",
  {
    isIdempotent: true,
  },
);

export const statelessAiSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_ai_insights",
  {
    isIdempotent: true,
  },
);

export const udmts = typ.governedTemplateState<EmitContext>({
  defaultNS: researchSchema,
});

export const cgmts = typ.governedTemplateState<EmitContext>({
  defaultNS: rawSchema,
});

export const cgmdts = typ.governedTemplateState<EmitContext>({
  defaultNS: rawDataSchema,
});

export const auditts = typ.governedTemplateState<EmitContext>({
  defaultNS: activitySchema,
});

export const aits = typ.governedTemplateState<EmitContext>({
  defaultNS: aiSchema,
});

export const pyts = typ.governedTemplateState<EmitContext>({
  defaultNS: partySchema,
});

export const authts = typ.governedTemplateState<EmitContext>({
  defaultNS: authSchema,
});

export const mts = typ.governedTemplateState<EmitContext>({
  defaultNS: masterSchema,
});

export const cts = typ.governedTemplateState<EmitContext>({
  defaultNS: statelessStudySchema,
});

export const utl = typ.governedTemplateState<EmitContext>({
  defaultNS: statelessUtilSchema,
});

export const rob = typ.governedTemplateState<EmitContext>({
  defaultNS: statelessRawObservation,
});

export const authat = typ.governedTemplateState<EmitContext>({
  defaultNS: statelessAuthentication,
});
export const imd = typ.governedTemplateState<EmitContext>({
  defaultNS: dbImportMigrateSchema,
});

export const ais = typ.governedTemplateState<EmitContext>({
  defaultNS: statelessAiSchema,
});

const ingressSchema = SQLa.sqlSchemaDefn("drh_udi_ingress", {
  isIdempotent: true,
});

enum EnumFileExchangeProtocol {
  SFTP = "SFTP",
  S3 = "S3",
}

export const tcf = SQLa.tableColumnFactory<Any, Any, typ.TypicalDomainQS>();

const fileExchangeProtocol = typ.textEnumTable(
  "file_exchange_protocol",
  EnumFileExchangeProtocol,
  { isIdempotent: true, sqlNS: ingressSchema },
);

// Specify the file path for the er diagram plantUML...
const filePath = "er-diagram.puml";
//export const {ulidPrimaryKey: primaryKey } = typ.keys;

//drh_stateful_research_study schema

//common tables

export const recordStatus = SQLa.tableDefinition("record_status", {
  id: autoIncPK(), // Unique identifier for each focus entry (Primary Key)
  code: varChar(25), // Mandatory,Individual code (alpha-numeric string) of the record_status like ACTIVE, PENDING, DELETED etc
  value: integer(), // Mandatory,Individual values of the record_status eg: 1- ACTIVE, 2-PENDING etc
  created_at: gm.housekeeping.columns.created_at,
  created_by: gm.housekeeping.columns.created_by,
  updated_at: gm.housekeeping.columns.updated_at,
  updated_by: gm.housekeeping.columns.updated_by,
  deleted_at: gm.housekeeping.columns.deleted_at,
  deleted_by: gm.housekeeping.columns.deleted_by,
}, {
  isIdempotent: true,
  sqlNS: partySchema,
  sqlPartial: (destination) => {
    if (destination === "after all column definitions") {
      return [];
    }
    return [];
  },
  constraints: (props, tableName) => {
    const c = SQLa.tableConstraints(tableName, props);
    return [
      c.unique("code"),
    ];
  },
});

export const userVerificationStatus = SQLa.tableDefinition("user_verification_status", {
  id: primaryKey(), // Unique identifier for each focus entry (Primary Key)
  code: varChar(50), // Mandatory,Individual code (alpha-numeric string) of the status like PENDING, FAILED etc
  title: varChar(50), // Mandatory,Individual values of the status eg: pending,failed etc
  description: textNullable(), // Description for the title
  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at,
  created_by: gm.housekeeping.columns.created_by,
  updated_at: gm.housekeeping.columns.updated_at,
  updated_by: gm.housekeeping.columns.updated_by,
  deleted_at: gm.housekeeping.columns.deleted_at,
  deleted_by: gm.housekeeping.columns.deleted_by,
}, {
  isIdempotent: true,
  sqlNS: masterSchema,
  sqlPartial: (destination) => {
    if (destination === "after all column definitions") {
      return [];
    }
    return [];
  },
  constraints: (props, tableName) => {
    const c = SQLa.tableConstraints(tableName, props);
    return [
      c.unique("code"),
    ];
  },
});
//exception log

export const exceptionLog = SQLa.tableDefinition("exception_log", {
  id: autoIncPK(), // Unique identifier for the exception, primary key
  function_name: varChar(255), // Name of the function where the exception occurred
  error_code: varCharNullable(50), // PostgreSQL error code
  error_message: varChar(255), // Detailed error message
  error_detail: textNullable(), // Additional details about the error
  error_hint: textNullable(), // Hint for resolving the error
  error_context: textNullable(), // Context of the error execution
  query: textNullable(), // Query that caused the error
  parameters: jsonbNullable(), // Parameters involved in the query/function
  occurred_at: dateTime(), // Timestamp of the exception
  resolved: varChar(50), // Indicates if the issue has been resolved
  resolved_at: dateTimeNullable(), // Timestamp when resolved
  resolver_comments: textNullable(), // Comments about the resolution
}, {
  isIdempotent: true,
  sqlNS: activitySchema, // Schema namespace
});

//organization

export const organization = SQLa.tableDefinition("organization", {
  id: primaryKey(), // Unique identifier for the organization, primary key
  party_id: ulid(), // Reference to the party Id (foreign key)
  identifier_system_value: jsonbNullable(), // Value of the identifier
  name: varChar(255), // Official name of the organization
  alias: varCharNullable(255), // Alternative name(s) for the organization
  type_code: varCharNullable(100), // Code representing the organization type
  type_display: varCharNullable(255), // Display name of the organization type
  address_text: varCharNullable(255), // Full address in free-text format
  address_line: varCharNullable(255), // Specific address line information
  city: varCharNullable(100), // City of the organization
  state: varCharNullable(100), // State or province of the organization
  postal_code: varCharNullable(20), // Postal or ZIP code
  country: varCharNullable(100), // Country of the organization
  phone: varCharNullable(20), // Phone number for contact
  email: varCharNullable(255), // Email address for contact
  website_url: textNullable(),
  parent_organization_id: ulidNullable(), // References the parent organization (foreign key)
  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator's ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT organization_party_id_fkey FOREIGN KEY (${organization.columns.party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

export const activityLevel = SQLa.tableDefinition("activity_level", {
  id: primaryKey(), // Unique identifier for the activity_level (auto-generated)
  title: text(), // Unique activity_level identifier title (e.g., "ERROR_LOG_LEVELS","PRIMARY_MENU"), mandatory
  description: textNullable(), // description for activity_level
  level: integer(), // Level of the activity_level (e.g., 0,1,2), mandatory
  rec_status_id: recordStatus.references.id(), // Foreign key referencing the record_status table, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
}, {
  isIdempotent: true,
  sqlNS: masterSchema,
  sqlPartial: (destination) => {
    if (destination === "after all column definitions") {
      return [];
    }
    return [];
  },
  constraints: (props, tableName) => {
    const c = SQLa.tableConstraints(tableName, props);
    return [c.unique("title")];
  },
});

export const activityLevelMapping = SQLa.tableDefinition(
  "activity_level_mapping",
  {
    id: primaryKey(), // Unique identifier for the activity_level_mapping (auto-generated)
    title: text(), // Unique activity_level_mapping identifier title (e.g., "SKIP_LOGIN","ORCID_LOGIN"), mandatory
    activity_level_id: activityLevel.references.id(), // References activity level table
    description: textNullable(), // description for activity_level
    rec_status_id: recordStatus.references.id(), // Foreign key referencing the record_status table, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("title")];
    },
  },
);

export const sessionAuditLog = SQLa.tableDefinition(
  "session_audit_log",
  {
    session_id: primaryKey(), // Primary identifier for the session
    start_time: dateTimeNullable(), // When the session started
    end_time: dateTimeNullable(), // When the session ended
    user_party_id: varCharNullable(255), // Reference to party ID of the user
    username: varChar(255), // Username of the session owner
    session_inactive_source: textNullable(), // Username of the session owner
    rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema, // Using the activity audit schema as in original SQL
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT fk_user_party_id FOREIGN KEY (${sessionAuditLog.columns.user_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
          },
        ];
      }
      return [];
    },
  },
);
export const profileStatusType = SQLa.tableDefinition("profile_status_type", {
  // Unique identifier for the profile status type (Primary Key)
  profile_status_type_id: autoIncPK(), // Auto-generated integer ID for the profile status type

  // Code for the profile status (e.g., 'NOT_CREATED', INCOMPLETE', 'COMPLETE', 'PENDING_VERIFICATION')
  code: tcf.unique(text()), // Unique code for the profile status

  // Description of the profile status type
  description: varCharNullable(255), // Optional description of the status type
}, {
  isIdempotent: true, // Ensures the table definition can be safely re-executed without errors
  sqlNS: masterSchema, // Defines the schema namespace for logical grouping
});

export const userAccount = SQLa.tableDefinition("user_account", {
  // Unique identifier for the user (Primary Key)
  user_id: primaryKey(), // Auto-generated unique identifier for the user

  // User's personal information
  username: varChar(50), // User's username (Mandatory, Unique)
  email: varChar(255), // User's email address (Mandatory, Unique)
  first_name: varChar(50), // User’s first name (Mandatory)
  last_name: varCharNullable(50), // User’s last name (Optional)

  // Optional login-related information
  last_login_at: dateTimeNullable(), // Last login timestamp (Optional)

  // Foreign key referencing the profile_status_type table
  profile_status: profileStatusType.references.profile_status_type_id(), // Foreign key (Optional)
  // JSONB for additional metadata
  metadata: jsonbNullable(), // Additional metadata, if any (Default to empty JSON)

  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator's ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  party_id: ulid(), //reference to party table
}, {
  isIdempotent: true, // Ensures the table definition can be safely re-executed without errors
  sqlNS: authSchema, // Defines the schema namespace for logical grouping
});

export const userCredentials = SQLa.tableDefinition("user_credentials", {
  // Unique identifier for the user (Primary Key)
  id: primaryKey(), // Auto-generated unique identifier for the user
  user_id: userAccount.references.user_id(),
  password_hash: varChar(255), // Hashed password for the user (Mandatory)
  password_salt: varCharNullable(255), // Salt used for hashing the password (Optional)
  password_updated_at: dateTime().default(() => new Date()),
  failed_login_attempts: integer().default(0), // Number of failed login attempts (Default to 0)
  last_failed_login: dateTimeNullable(), // Timestamp of the last failed login attempt (Optional)
  is_locked: boolean().default(false), // Indicates if the account is locked (Default to false)
}, {
  isIdempotent: true, // Ensures the table definition can be safely re-executed without errors
  sqlNS: authSchema, // Defines the schema namespace for logical grouping
});

//location

export const location = SQLa.tableDefinition("location", {
  id: primaryKey(), // Primary key; unique identifier for the location
  name: varCharNullable(255), // Name of the location as used by humans
  alias: varCharNullable(255), // Comma-separated alternate names for the location
  description: varCharNullable(), // Additional details about the location
  type_code: varCharNullable(50), // Code representing the type of location
  type_display: varCharNullable(255), // Display VARCHAR for the type of location
  address_line: varCharNullable(255), // Street address of the location
  city: varCharNullable(100), // City of the location
  state: varCharNullable(100), // State of the location
  postal_code: varCharNullable(20), // Postal code of the location
  country: varCharNullable(100), // Country of the location
  longitude: floatNullable(), // Longitude coordinate (WGS84 datum)
  latitude: floatNullable(), // Latitude coordinate (WGS84 datum)
  managing_org_id: ulidNullable(), // Foreign key reference to Organization table
  part_of_id: ulidNullable(), // Foreign key reference to the parent Location table
  characteristic_code: varCharNullable(50), // Code representing the characteristic of the location
  characteristic_display: varCharNullable(255), // Display for the characteristic of the location
  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});
//practitioner table

export const practitioner = SQLa.tableDefinition("practitioner", {
  id: primaryKey(), // Unique identifier for the practitioner, primary key
  system_identifier: varCharNullable(255), // External identifier for the practitioner
  name: varChar(255), // Full name of the practitioner
  gender_type_id: ulidNullable(), // Gender of the practitioner (e.g., male, female)
  birth_date: dateNullable(), // Birth date of the practitioner
  photo_url: varCharNullable(255), // URL for the practitioner’s photo
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  tenant_id: organization.references.id(), // Reference to the organization Id (foreign key)
  practitioner_party_id: ulidNullable(), // Reference to the practitioner party Id (foreign key)
  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT practitioner_party_id_fkey FOREIGN KEY (${practitioner.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName}),
              CONSTRAINT practitioner_gender_type_id_fkey FOREIGN KEY (${practitioner.columns.gender_type_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.genderType.tableName}(${udm.genderType.columns.gender_type_id.columnName}),
              CONSTRAINT practitioner_grp_party_id_fkey FOREIGN KEY (${practitioner.columns.practitioner_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

//patient table

export const patient = SQLa.tableDefinition("patient", {
  id: primaryKey(), // Unique identifier for the patient, primary key
  identifier_system: varCharNullable(255), // Identifier system (e.g., URL)
  identifier_value: varCharNullable(255), // Unique identifier value for the patient
  name_use: varCharNullable(50), // Name usage (e.g., official)
  name_family: varCharNullable(100), // Family name (last name)
  name_given: varCharNullable(100), // Given name (first name), mandatory
  gender_type_id: ulidNullable(), // Gender of the patient (e.g., male, female), mandatory
  birth_date: dateNullable(), // Date of birth, mandatory
  age: integerNullable(), //As the participant data is anonymized, only age is available and stored in this column, while all other identifiable information is excluded.
  address_use: varCharNullable(), // Type of address (e.g., home)
  address_line1: varCharNullable(), // First line of address (street)
  address_city: varCharNullable(), // City of the address
  address_state: varCharNullable(), // State of the address
  address_postal_code: varCharNullable(), // Postal code of the address
  address_country: varCharNullable(), // Country of the address
  contact_relationship: varCharNullable(50), // Contact relationship type (e.g., emergency contact)
  contact_name_family: varCharNullable(100), // Family name of the contact person
  contact_name_given: varCharNullable(100), // Given name of the contact person, mandatory
  contact_telecom_system: varCharNullable(50), // Telecom system (e.g., phone)
  contact_telecom_value: varCharNullable(50), // Contact phone number or telecom details
  contact_telecom_use: varCharNullable(50), // Use of telecom (e.g., mobile)
  tenant_id: organization.references.id(), // Reference to the organization Id (foreign key)
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT patient_party_id_fkey FOREIGN KEY (${patient.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName}),
            CONSTRAINT gender_type_id_fkey FOREIGN KEY (${practitioner.columns.gender_type_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.genderType.tableName}(${udm.genderType.columns.gender_type_id.columnName})
            `,
        },
      ];
    }
  },
});

//qualification table

export const qualification = SQLa.tableDefinition("qualification", {
  id: primaryKey(), // Unique identifier for the qualification, primary key
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  qualification_code: varCharNullable(50), // Qualification code (e.g., MD, PhD)
  issuer_name: varCharNullable(255), // Name of the organization that issued the qualification
  start_date: dateNullable(), // Start date of the qualification
  end_date: dateNullable(), // End date of the qualification
  tenant_id: organization.references.id(), // Foreign key reference to the organization
  rec_status_id: recordStatus.references.id(), // Foreign key to record status
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT qualification_party_id_fkey FOREIGN KEY (${qualification.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

export const raceType = SQLa.tableDefinition(
  "race_type",
  {
    race_type_id: primaryKey(), // Unique identifier for the race entry
    code: varChar(50), //Code representing the race (e.g., 2028-9 ).
    system_uri: varChar(100), // The uri where the code is defined, e.g. http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity'
    system_oid: varCharNullable(100), //Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)
    display: varChar(100), //Human-readable race description (e.g., "Asian").
    race_text: varChar(100), //Optional free-text description.
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code", "system_uri", "system_oid")];
    },
  },
);

export const roleType = SQLa.tableDefinition(
  "role_type",
  {
    role_type_id: primaryKey(), // Unique identifier for the role type (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    definition: textNullable(), // Description of the role from the standard
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const role = SQLa.tableDefinition(
  "role",
  {
    role_id: primaryKey(), // Unique identifier for the role (PK)
    role_type_id: roleType.references.role_type_id(), // FK linking to role_type table
    org_party_id: ulidNullable(), // Organization reference (optional)
    role_name: varChar(50), // Role name
    description: textNullable(), // Role description
    is_system_role: boolean(), // System role flag, default false
    metadata: jsonbNullable(), // Additional metadata, default to empty JSON
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const userRole = SQLa.tableDefinition(
  "user_role",
  {
    user_role_id: primaryKey(), // Unique identifier for the user role (PK)
    role_id: ulid(), // FK linking to the role table
    user_id: ulidNullable(), // FK linking to the users table, nullable
    user_group_id: ulidNullable(), // FK linking to the user group table, nullable
    start_date: dateNullable(), // Start date of the role assignment
    end_date: dateNullable(), // End date of the role assignment (NULL when active)
    metadata: jsonbNullable(), // Additional metadata, default empty object
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: authSchema, // Schema namespace
  },
);

export const permission = SQLa.tableDefinition(
  "permission",
  {
    permission_id: primaryKey(), // Unique identifier for the permission (PK)
    code: varChar(50), // Unique permission code
    permission_name: varChar(255), // Permission name
    description: textNullable(), // Permission description
    resource_type: varCharNullable(50), // Type of resource applicable
    action_type: varChar(50), // Type of action
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

// Role Permission Table Definition
export const rolePermission = SQLa.tableDefinition(
  "role_permission",
  {
    role_permission_id: primaryKey(), // Unique identifier for the role_permission (PK)
    role_id: role.references.role_id(), // Role reference (FK)
    permission_id: permission.references.permission_id(), // Permission reference (FK)
    metadata: jsonbNullable(), // Additional metadata, default '{}' if empty
    rec_status_id: recordStatus.references.id(), // Status of the record (e.g., active), FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Your schema name
  },
);

// Group Type Table Definition
export const groupType = SQLa.tableDefinition(
  "group_type",
  {
    group_type_id: primaryKey(), // Unique identifier for the user group (ULID)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., http://hl7.org/fhir/group-type)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    definition: textNullable(), // Description of the role from the standard
    rec_status_id: recordStatus.references.id(), // Status of the record (e.g., active), FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Your schema name
  },
);

// Group Member Table Definition
export const groupMember = SQLa.tableDefinition(
  "group_member",
  {
    group_member_id: primaryKey(), // Unique identifier for the user group member (ULID)
    group_type_id: groupType.references.group_type_id(), // Foreign key referencing the id of the user_group_type table
    user_id: ulidNullable(), // Nullable foreign key referencing user_account table
    member_party_id: ulidNullable(), // Nullable foreign key referencing party table
    period_start: dateNullable(), // Start of membership period
    period_end: dateNullable(), // End of membership period
    rec_status_id: recordStatus.references.id(), // Status of the record (e.g., active), FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: authSchema, // Your schema name
    sqlPartial: (destination) => {
      if (destination == "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT user_id_fkey FOREIGN KEY (${groupMember.columns.user_id.columnName}) REFERENCES ${authSchema.sqlNamespace}.${userAccount.tableName}(${userAccount.columns.user_id.columnName}),
              CONSTRAINT member_party_id_fkey FOREIGN KEY (${groupMember.columns.member_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})
              `,
          },
        ];
      }
    },
  },
);

export const group = SQLa.tableDefinition(
  "group",
  {
    group_id: primaryKey(), // Unique identifier for the group
    grp_identifier: varChar(255), // Short form or ID/Label for the group
    title: varChar(255), // Human-friendly title of the group
    status_type_id: integerNullable(), // FK to status type (draft, active, retired, unknown)
    description: textNullable(), // Description of the group
    purpose: textNullable(), // Purpose of the group
    group_type_id: ulidNullable(), // FK to group type (person, device, organization, etc.)
    rec_status_id: recordStatus.references.id(), // FK to record status (e.g., active)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: authSchema, // Schema namespace for the group table
  },
);

export const consentCategory = SQLa.tableDefinition(
  "consent_category",
  {
    category_id: primaryKey(), // Auto generated, Unique identifier for the role type
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varCharNullable(255), // URI of the coding system (e.g., http://hl7.org/fhir/ValueSet/consent-category and http://loinc.org)
    display: varCharNullable(255), // Display name of the role (e.g., Doctor, Nurse)
    definition: textNullable(), // Description of the role from the standard
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const consentStatusCode = SQLa.tableDefinition(
  "consent_status_code",
  {
    status_code_id: primaryKey(), // Unique identifier for the consent status
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varCharNullable(255), // URI of the coding system (e.g., http://hl7.org/fhir/ValueSet/consent-category)
    display: varCharNullable(255), // Display name of the status (e.g., "Doctor", "Nurse")
    definition: textNullable(), // Description of the role from the standard
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const consentDecisionType = SQLa.tableDefinition(
  "consent_decision_type",
  {
    decision_type_id: primaryKey(), // Unique identifier for the decision type
    code: varChar(50), // Code value
    system: varCharNullable(255), // URI of the coding system
    display: varCharNullable(255), // Display name
    definition: textNullable(), // Description of the role
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const ethnicityType = SQLa.tableDefinition(
  "ethnicity_type",
  {
    ethnicity_type_id: primaryKey(), // Unique identifier for the race entry
    code: varChar(50), //Code representing the race (e.g., 2028-9 ).
    system_uri: varCharNullable(100), // The uri where the code is defined, e.g. http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity'
    system_oid: varCharNullable(100), //Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)
    display: varChar(100), //Human-readable race description (e.g., "Asian").
    ethnicity_text: varChar(100), //Optional free-text description.
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const contactPointSystem = SQLa.tableDefinition(
  "contact_point_system",
  {
    id: primaryKey(), // Unique identifier for the contactPointSystem, primary key
    code: varChar(50), // Code representing the telecom type (e.g., phone, email)
    system: varCharNullable(100), // Specifies the source or context for the telecom type
    value: varChar(100), //Human-readable display name (e.g., "Phone", "Email")
    description: textNullable(), // Optional description of the contact point system
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const contactPointUse = SQLa.tableDefinition(
  "contact_point_use",
  {
    id: primaryKey(), // Unique identifier for the contactPointUse, primary key
    code: varChar(50), // Code representing the telecom type (e.g., phone, email)
    system: varCharNullable(100), // Specifies the source or context for the telecom type
    value: varChar(100), //Human-readable display name (e.g., "Phone", "Email")
    description: textNullable(), // Optional description of the contact point system
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const contactPointAddressType = SQLa.tableDefinition(
  "contact_point_address_type",
  {
    id: primaryKey(), // Unique identifier for the contactPointSystem, primary key
    code: varChar(50), // Code representing the telecom type (e.g., phone, email)
    system: varCharNullable(100), // Specifies the source or context for the telecom type
    value: varChar(100), //Human-readable display name (e.g., "Phone", "Email")
    description: textNullable(), // Optional description of the contact point use
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const contactPointAddressUse = SQLa.tableDefinition(
  "contact_point_address_use",
  {
    id: primaryKey(), // Unique identifier for the contactAddressUse, primary key
    code: varChar(50), // Code representing the telecom type (e.g., phone, email)
    system: varCharNullable(100), // Specifies the source or context for the telecom type
    value: varChar(100), //Human-readable display name (e.g., "Phone", "Email")
    description: textNullable(), // Optional description of the contact point address use
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const address = SQLa.tableDefinition("address", {
  id: primaryKey(), // Unique identifier for the address entry, primary key
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  address_type: varChar(50), // Type of address (e.g., work, home), mandatory
  line1: varCharNullable(255), // Address line 1, optional
  line2: varCharNullable(255), // Address line 2, optional
  city: varCharNullable(100), // City of the address, optional
  state: varCharNullable(100), // State or region of the address, optional
  postal_code: varCharNullable(20), // Postal code, optional
  country: varCharNullable(100), // Country of the address, optional
  tenant_id: organization.references.id(), // Foreign key reference to the organization
  contact_point_address_type_id: ulid(), // Foreign key referencing the contact_point_address_type table.
  contact_point_address_use_type_id: ulid(), //Foreign key referencing the contact_point_address_use table.
  rec_status_id: recordStatus.references.id(), // Foreign key to record status
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT address_party_id_fkey FOREIGN KEY (${address.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName}),
              CONSTRAINT fk_address_type FOREIGN KEY (${address.columns.contact_point_address_type_id.columnName}) REFERENCES ${masterSchema.sqlNamespace}.${contactPointAddressType.tableName}(${contactPointAddressType.columns.id.columnName}),
            CONSTRAINT fk_address_use_type FOREIGN KEY (${address.columns.contact_point_address_use_type_id.columnName}) REFERENCES ${masterSchema.sqlNamespace}.${contactPointAddressUse.tableName}(${contactPointAddressUse.columns.id.columnName})
            `,
        },
      ];
    }
  },
});

export const communication = SQLa.tableDefinition("communication", {
  id: primaryKey(), // Unique identifier for the communication entry, primary key
  language_code: varCharNullable(50), // Language code (e.g., “en” for English), optional
  preferred: varCharNullable(50), // Whether this language is preferred (true/false), optional
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  tenant_id: organization.references.id(), // Foreign key reference to the organization
  rec_status_id: recordStatus.references.id(), // Foreign key to record status
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT communication_party_id_fkey FOREIGN KEY (${communication.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

export const associatedPartyType = SQLa.tableDefinition(
  "associated_party_type",
  {
    id: primaryKey(), // Unique identifier for the associated party type
    name: varChar(100), // Name of the associated party type (e.g., Sponsor), mandatory
    description: varCharNullable(), // Detailed description of the associated party type, optional
    rec_status_id: recordStatus.references.id(), // Foreign key to record status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const laboratory = SQLa.tableDefinition("laboratory", {
  id: primaryKey(), // Unique identifier for the laboratory, primary key
  system: varCharNullable(100), // Identifier system (e.g., URL), mandatory
  name: varChar(255), // Name of the laboratory, mandatory
  type: varCharNullable(50), // Type of the organization (e.g., lab), optional
  address_line: varCharNullable(255), // Address line of the laboratory, optional
  city: varCharNullable(100), // City of the lab, optional
  state: varCharNullable(50), // State of the lab, optional
  postal_code: varCharNullable(20), // Postal code, optional
  country: varCharNullable(50), // Country, optional
  phone: varCharNullable(20), // Primary contact number, optional
  email: varCharNullable(255), // Email address, optional
  contact_name: varCharNullable(255), // Name of the primary contact, optional
  contact_phone: varCharNullable(20), // Phone number of the primary contact, optional
  parent_org_id: varCharNullable(255), // ID of the parent organization, optional
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
  rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT laboratory_party_id_fkey FOREIGN KEY (${laboratory.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

export const telecom = SQLa.tableDefinition("telecom", {
  id: primaryKey(), // Unique identifier for the telecom entry, primary key
  party_id: ulid(), // Reference to the party Id (foreign key)
  telecom_type: varChar(50), // Type of telecom (e.g., phone, email), mandatory
  telecom_value: varChar(255), // Value of the telecom (e.g., phone number, email), mandatory
  telecom_use: varCharNullable(50), // Use of telecom (e.g., work, personal)
  contact_point_system_id: ulid(),
  contact_point_use_type_id: ulid(),
  tenant_id: organization.references.id(), // Foreign key reference to the organization
  rec_status_id: recordStatus.references.id(), // Foreign key to record status
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT telecom_party_id_fkey FOREIGN KEY (${telecom.columns.party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName}),
              CONSTRAINT fk_contact_point_system_id FOREIGN KEY (${telecom.columns.contact_point_system_id.columnName}) REFERENCES ${masterSchema.sqlNamespace}.${contactPointSystem.tableName}(${contactPointSystem.columns.id.columnName}),
            CONSTRAINT fk_contact_point_use_type_id FOREIGN KEY (${telecom.columns.contact_point_use_type_id.columnName}) REFERENCES ${masterSchema.sqlNamespace}.${contactPointUse.tableName}(${contactPointUse.columns.id.columnName})

            `,
        },
      ];
    }
  },
});

//master tables

export const researchStudyFocus = SQLa.tableDefinition(
  "research_study_focus",
  {
    id: autoIncPK(), // Unique identifier for each focus entry (auto-generated)
    coding_system: varChar(255), // System URL for coding (e.g., "http://snomed.info/sct"), mandatory
    code: tcf.unique(text()), // Code representing the focus (e.g., "73211009"), mandatory
    display: varChar(255), // Human-readable description (e.g., "Diabetes mellitus"), mandatory
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status table (e.g., active), mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const researchStudyCondition = SQLa.tableDefinition(
  "research_study_condition",
  {
    id: autoIncPK(), // Unique identifier for each condition entry (auto-generated)
    coding_system: varChar(255), // System URL for coding (e.g., "http://snomed.info/sct"), optional
    code: tcf.unique(text()), // Code representing the condition (e.g., "44054006"), mandatory
    display: varChar(255), // Human-readable description (e.g., "Type 1 Diabetes Mellitus"), optional
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status table (e.g., active), mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const loincCodes = SQLa.tableDefinition("loinc_codes", {
  loinc_code_id: autoIncPK(), // Unique identifier for the LOINC code
  loinc_code: tcf.unique(text()), // LOINC code itself (e.g., '12345-6'), mandatory
  loinc_description: text(), // Description of the LOINC code (e.g., 'Glucose [Mass/volume] in Blood'), mandatory
  loinc_class: varChar(100), // Class of the LOINC code (e.g., 'Laboratory', 'Clinical', etc.), mandatory
  loinc_type: varChar(100), // Type of the LOINC code (e.g., 'Observation', 'Measurement'), mandatory
  rec_status_id: recordStatus.references.id(), // Status of the LOINC code (e.g., 'active', 'retired'), mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
}, {
  isIdempotent: true,
  sqlNS: masterSchema, // Schema namespace
});

export const studyVisibility = SQLa.tableDefinition(
  "study_visibility",
  {
    visibility_id: autoIncPK(), // Unique identifier for each visibility type
    visibility_name: tcf.unique(text()), // Name of the visibility (e.g., 'public', 'private'), mandatory
    visibility_description: text(), // Description of the visibility type, optional
    rec_status_id: recordStatus.references.id(), // Foreign key referencing the record_status table, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID (drh user id)
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const metricDefinitions = SQLa.tableDefinition(
  "metric_definitions",
  {
    metric_id: text(), // Unique identifier for the metric
    metric_name: text(), // Name of the metric, must be unique, mandatory
    metric_info: text(), // Description or additional information about the metric, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const studyStatusDefinitions = SQLa.tableDefinition(
  "study_status_master",
  {
    code: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    display_name: tcf.unique(text()), // Display name of the status, mandatory
    definition: text(), // Detailed definition or description of the status, mandatory
    system_url: text(), // URL for the system that defines the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const researchSubjectStatusDefinitions = SQLa.tableDefinition(
  "research_subject_status_master", // Table name
  {
    code: autoIncPK(), // Integer code for the status (e.g., 1, 2, 3, etc.), mandatory
    display_name: tcf.unique(text()), // Display name of the status (e.g., "Candidate", "Eligible"), mandatory
    definition: text(), // Detailed definition or description of the status, mandatory
    system_url: text(), // URL for the system that defines the status (FHIR URL), mandatory
  },
  {
    isIdempotent: true, // Ensures no duplicate schema changes are applied
    sqlNS: masterSchema, // Schema namespace, assuming `masterSchema` is defined elsewhere
  },
);

export const citationStatusDefinitions = SQLa.tableDefinition(
  "citation_status_master",
  {
    code: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    display_name: tcf.unique(text()), // Display name of the status, mandatory
    definition: text(), // Detailed definition or description of the status, mandatory
    system_url: text(), // URL for the system that defines the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const investigatorStudyRoleDefinitions = SQLa.tableDefinition(
  "investigator_study_role",
  {
    code: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    role: tcf.unique(text()), // Display name of the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const acceptedFileFormats = SQLa.tableDefinition(
  "accepted_file_formats",
  {
    accepted_file_format_id: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    title: tcf.unique(text()), // Display name of the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const migrationStatus = SQLa.tableDefinition(
  "migration_status",
  {
    stage_id: autoIncPK(),
    stage_name: tcf.unique(text()),
    stage_description: textNullable(),
    created_at: dateTimeNullable(),
    updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
  },
);

export const interactionStatus = SQLa.tableDefinition(
  "interaction_status",
  {
    id: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    title: tcf.unique(text()), // Display name of the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

export const interactionActionType = SQLa.tableDefinition(
  "interaction_action_type",
  {
    id: autoIncPK(), // Integer code for the status (1, 2, 3, etc.), mandatory
    title: tcf.unique(text()), // Display name of the status, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
  },
);

//drh specific tables

export const device = SQLa.tableDefinition("device", {
  id: primaryKey(), // Unique identifier for the device (auto-generated)
  identifier: varCharNullable(255), // Unique device identifier (e.g., device ID), mandatory
  manufacturer: varChar(255), // Manufacturer of the device (e.g., "Medtronic"), mandatory
  serial_number: varCharNullable(255), // Serial number of the device (e.g., "SN123456789"), mandatory
  device_name: varChar(255), // Name or model of the device (e.g., "Insulin Pump"), mandatory
  status: varChar(50), // Status of the device (e.g., "active", "inactive"), mandatory
  device_type: varCharNullable(100), // Type of the device (e.g., "Medical", "Monitoring"), mandatory
  udi_carrier: varCharNullable(255), // UDI Carrier (e.g., barcode, RFID, etc.), optional
  manufacture_date: dateNullable(), // Date of manufacture of the device, mandatory
  expiration_date: dateNullable(), // Expiration date of the device, optional
  lot_number: varCharNullable(255), // Lot number assigned by the manufacturer, optional
  rec_status_id: recordStatus.references.id(), // Foreign key referencing the record_status table, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const citationIdentifier = SQLa.tableDefinition(
  "citation_identifier",
  {
    id: primaryKey(), // Unique identifier for each citation identifier
    citation_id: integer(), // Foreign key referencing the citation table (id), mandatory
    identifier_system: text(), // Identifier system (e.g., DOI, PubMed), mandatory
    identifier_value: text(), // The actual identifier value (e.g., DOI number, PubMed ID), mandatory
    rec_status_id: recordStatus.references.id(), // Foreign key referencing the record_status table, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const researchStudyBuilder = SQLa.tableDefinition("research_study", {
  study_id: primaryKey(), // Unique identifier for the research study
  research_study_identifier: jsonbNullable(), // Business identifier for the research study, store NCT number unique
  study_display_id: tcf.unique(varChar(10)), // Study Display ID of the study, optional
  title: varChar(255), // Title of the research study, mandatory
  description: textNullable(), // Detailed description of the study
  status_id: integer(), // Foreign key referencing the status table, mandatory
  start_date: dateNullable(), // Start date of the study, mandatory
  end_date: dateNullable(), // End date of the study, mandatory
  focus_id: varCharNullable(255), // Reference to the research focus being studied, optional
  condition_code: varCharNullable(255), // Standardized code for the condition being studied, optional
  region_code: varCharNullable(255), // Geographic region code, optional
  site_id: ulidNullable(), // Reference to the location table, mandatory
  treatment_modality: textNullable(), // Details of the Treatment Modalities, optional
  protocol_reference_id: ulidNullable(), // Reference to the protocol document, optional
  phase: varCharNullable(50), // Phase of the study, optional
  primary_purpose_type: varCharNullable(50), // Primary purpose (e.g., treatment, diagnostic), optional
  keywords: varCharNullable(250), // Keywords for the study, optional
  progress_status: varCharNullable(50), // Study progress status (e.g., recruiting, completed), optional
  why_stopped: varCharNullable(255), // Reason for stopping the study, optional
  eligibility_criteria: textNullable(), // Inclusion/exclusion criteria for participants, optional
  target_enrollment: integerNullable(), // Estimated number of participants, optional
  actual_enrollment: integerNullable(), // Actual number of participants, optional
  tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
  rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  visibility: studyVisibility.references.visibility_id(), // Visibility ID (public, private), mandatory
  archive_status: boolean(), // (archived or not ), readonly
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const researchStudy = gm.textPkTable(
  researchStudyBuilder.tableName,
  researchStudyBuilder.zbSchema,
  {
    sqlNS: researchSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_research_study_focus_id FOREIGN KEY (${researchStudyBuilder.columns.focus_id.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${researchStudyFocus.tableName}(${researchStudyFocus.columns.code.columnName}),

              CONSTRAINT fk_research_study_condition_code_id FOREIGN KEY (${researchStudyBuilder.columns.condition_code.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${researchStudyCondition.tableName}(${researchStudyCondition.columns.code.columnName})

              `,
          },
        ];
      }
    },
  },
);

export const studyConsent = SQLa.tableDefinition(
  "study_consent",
  {
    consent_id: primaryKey(), // Unique identifier for each consent
    status_type_id: ulidNullable(), // FK to consent_status_code (nullable)
    subject_reference_id: researchStudyBuilder.references.study_id(), // FK to research study (NOT NULL)
    grantor_reference_id: ulid(), // FK to party id (NOT NULL)
    start_date: dateNullable(), // Start date of consent validity
    end_date: dateNullable(), // End date of consent validity
    decision_type_id: integerNullable(), // FK to consent_decision_type (nullable)
    category_code: integerNullable(), // FK to category code (nullable)
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
    sqlPartial: (destination) => {
      if (destination == "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT grantor_reference_id_fkey FOREIGN KEY (${studyConsent.columns.grantor_reference_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName}) `,
          },
        ];
      }
    },
  },
);

export const contractType = SQLa.tableDefinition(
  "contract_type",
  {
    contract_type_id: primaryKey(), // Unique identifier for the contract type (Primary Key)
    code: varChar(50), // Code value from the coding system (e.g., privacy, consent)
    system: varChar(255), // URI of the coding system (optional)
    display: varChar(255), // Display name of the contract type (optional)
    definition: textNullable(), // Description of the contract type (optional)
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace for the contract_type table
  },
);

export const contractSubType = SQLa.tableDefinition(
  "contract_sub_type",
  {
    contract_sub_type_id: primaryKey(), // Unique identifier for the contract sub type (Primary Key)
    code: varChar(50), // Code value from the coding system (e.g., disclosure-ca, disclosure-us)
    system: varChar(255), // URI of the coding system (optional)
    display: varChar(255), // Display name of the contract sub type (optional)
    definition: textNullable(), // Description of the contract sub type (optional)
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace for the contract_sub_type table
  },
);

export const contractSignerType = SQLa.tableDefinition(
  "contract_signer_type",
  {
    signer_type_id: primaryKey(), // Unique identifier for the signer type (Primary Key)
    code: varChar(50), // Code value from the coding system (e.g., grantee, grantor) (mandatory)
    system: varChar(255), // URI of the coding system (e.g., http://terminology.hl7.org/CodeSystem/contractsignertypecodes) (optional)
    display: varChar(255), // Display name (optional)
    definition: textNullable(), // Description of the signer type (optional)
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace for the contract_signer_type table
  },
);

export const legalStateCode = SQLa.tableDefinition(
  "legal_state_code",
  {
    state_code_id: primaryKey(), // Unique identifier for the state code (Primary Key)
    code: varChar(50), // Code value (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the legal state code (optional)
    definition: textNullable(), // Description of the legal state (optional)
    rec_status_id: recordStatus.references.id(), // Status of the record, FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Makes sure no duplicate creation of table
    sqlNS: masterSchema, // Schema namespace for the legal_state_code table
  },
);
export const contractExpirationType = SQLa.tableDefinition(
  "contract_expiration_type",
  {
    expiration_type_id: primaryKey(), // Unique identifier for the legal state code (Primary Key)
    code: varChar(50), // Code value (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the legal state code (optional)
    definition: textNullable(), // Description of the legal state (optional)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of the table
    sqlNS: masterSchema, // Schema namespace for the contract_expiration_type table
  },
);

export const contractStatus = SQLa.tableDefinition(
  "contract_status",
  {
    contract_status_type_id: primaryKey(), // Unique identifier for the legal state code (Primary Key)
    code: varChar(50), // Code value (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the legal state code (optional)
    definition: textNullable(), // Description of the legal state (optional)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of the table
    sqlNS: masterSchema, // Schema namespace for the contract_expiration_type table
  },
);

export const decisionType = SQLa.tableDefinition(
  "decision_type",
  {
    decision_type_id: primaryKey(), // Unique identifier for the legal state code (Primary Key)
    code: varChar(50), // Code value (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the legal state code (optional)
    definition: textNullable(), // Description of the legal state (optional)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of the table
    sqlNS: masterSchema, // Schema namespace for the contract_expiration_type table
  },
);

export const decisionMode = SQLa.tableDefinition(
  "decision_mode",
  {
    decision_mode_id: primaryKey(), // Unique identifier for the legal state code (Primary Key)
    code: varChar(50), // Code value (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the legal state code (optional)
    definition: textNullable(), // Description of the legal state (optional)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of the table
    sqlNS: masterSchema, // Schema namespace for the contract_expiration_type table
  },
);

export const signatureType = SQLa.tableDefinition(
  "signature_type",
  {
    signature_type_id: primaryKey(), // Unique identifier for the signature type (Primary Key)
    code: varChar(50), // Code value of the signature type (mandatory)
    system: varCharNullable(255), // URI of the coding system (optional)
    display: varCharNullable(255), // Display name of the signature type (optional)
    definition: textNullable(), // Description of the signature type (optional)
    rec_status_id: recordStatus.references.id(), // Status of the record (foreign key referencing record_status)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of table
    sqlNS: researchSchema, // Schema namespace for the signature_type table
  },
);

export const contractTerm = SQLa.tableDefinition(
  "contract_term",
  {
    term_id: primaryKey(), // Unique identifier for the contract term (Primary Key)
    term_reference_id: varCharNullable(50), // Publicly exposed ID for reference (optional)
    contract_id: varChar(50), // Foreign key referencing the contract table (mandatory)
    issued_at: dateTime(), // Contract term issue date and time (mandatory)
    term_period_start: dateTimeNullable(), // Start of the contract term effective period (optional)
    term_period_end: dateTimeNullable(), // End of the contract term effective period (optional)
    type_code: varCharNullable(100), // Type classification code for the term (optional)
    sub_type_code: varCharNullable(100), // Sub-type classification code for the term (optional)
    term_statement: textNullable(), // Detailed statement or description of the term (optional)
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status (mandatory)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of table
    sqlNS: masterSchema, // Schema namespace for the contract_term table
  },
);

export const termOffer = SQLa.tableDefinition(
  "term_offer",
  {
    offer_id: primaryKey(), // Unique identifier for the offer (Primary Key, Auto-generated)
    term_id: contractTerm.references.term_id(), // Foreign key referencing contract_term (Mandatory)
    term_offer_desc: textNullable(), // Human-readable description of the offer (Optional)
    decision_mode: varCharNullable(100), // Mode or method used to make decisions about the offer (Optional)
    topic_reference: varCharNullable(255), // Reference to the subject or topic the offer is related to (Optional)
    type_system: varCharNullable(255), // Coding system for classifying the offer type (Optional)
    type_code: varCharNullable(100), // Specific offer type code (Optional)
    decision_type_id: decisionType.references.decision_type_id(), // Foreign key to decision_type table (Optional)
    decision_mode_id: decisionMode.references.decision_mode_id(), // Foreign key to decision_mode table (Optional)
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status (Mandatory)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (Optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (Optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of table
    sqlNS: researchSchema, // Schema namespace for the term_offer table
  },
);

export const termOfferParty = SQLa.tableDefinition(
  "term_offer_party",
  {
    id: primaryKey(), // Unique identifier for the entry (Primary Key, Auto-generated)
    party_id: ulid(), // Foreign key linking to the party (Mandatory)
    offer_id: termOffer.references.offer_id(), // Foreign key referencing term_offer (Mandatory)
    role_code: varCharNullable(255), // Code representing the role of the party (Optional)
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status (Mandatory)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (Optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (Optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of table
    sqlNS: researchSchema, // Schema namespace for the term_offer_party table
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              ` CONSTRAINT fk_term_offer_party_id FOREIGN KEY (${termOfferParty.columns.party_id.columnName}) REFERENCES
              ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
          },
        ];
      }
    },
  },
);

export const studyContract = SQLa.tableDefinition(
  "study_contract",
  {
    contract_id: primaryKey(), // Unique identifier for the study contract (Primary Key)
    subject_reference_id: researchStudyBuilder.references.study_id(), // Foreign Key referencing studies
    status_type_id: contractStatus.references.contract_status_type_id(), // Foreign Key referencing contract status
    legal_state_code: legalStateCode.references.state_code_id(), // Foreign Key referencing studies
    issued_date: dateTime(), // Date of issue
    applies_start_date: dateTime(), // Start date
    applies_end_date: dateTimeNullable(), // End date (optional, default NULL)
    expiration_type_code: varCharNullable(255), // Foreign Key referencing expiration types
    contract_name: varCharNullable(255), // Computer-friendly designation
    title: varChar(255), // Title of the contract
    subtitle: varCharNullable(255), // Subtitle of the contract
    alias: varCharNullable(255), // Alias name of the contract
    authority_org_reference: organization.references.id(), // Foreign Key referencing organizations
    location_id: ulidNullable(), // Foreign Key referencing locations
    site_id: ulidNullable(), // Foreign Key referencing sites
    contract_type_code: ulidNullable(), // Foreign Key referencing contract types
    author_reference_id: ulid(), // Foreign Key referencing parties
    scope_code: varCharNullable(), // FHIR defined system scope
    legally_binding_reference_id: ulidNullable(), // Foreign Key referencing document references
    rec_status_id: recordStatus.references.id(), // Foreign Key referencing record_status (mandatory)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp (optional)
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter (optional)
  },
  {
    isIdempotent: true, // Ensures no duplicate creation of table
    sqlNS: masterSchema, // Schema namespace for the study_contract table
  },
);

export const researchSubjectBuilder = SQLa.tableDefinition("research_subject", {
  rsubject_id: primaryKey(), // Unique identifier for the ResearchSubject resource
  participant_identifier: varChar(255), // Study-specific identifier for the participant, mandatory
  study_reference: researchStudy.references.study_id(), // Reference to the associated ResearchStudy resource, mandatory
  individual_reference: ulidNullable(), // Reference to the Patient resource, mandatory
  status_id: researchSubjectStatusDefinitions.references.code(), // Current status of the subject in the study, mandatory
  group: varCharNullable(255), // Study arm or group assigned to the subject, optional
  diabetes_type: varCharNullable(50), // Type of diabetes diagnosed for the participant, optional
  // site_id: ulidNullable(), // Identifier for the study location, mandatory
  diagnosis_icd: varCharNullable(255), // ICD code for the participant’s diagnosis, optional
  med_rxnorm: varCharNullable(255), // RxNorm code for the participant’s medication, optional
  treatment_modality: textNullable(), // Structured details of treatment modality, optional
  race_type_id: ulidNullable(), // Reference to the race_type
  ethnicity_type_id: ulidNullable(), // Reference to the ethnicity_type
  tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
  rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const researchSubject = gm.textPkTable(
  researchSubjectBuilder.tableName,
  researchSubjectBuilder.zbSchema,
  {
    sqlNS: researchSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_research_subject_patient_id FOREIGN KEY (${researchSubjectBuilder.columns.individual_reference.columnName}) REFERENCES
              ${researchSchema.sqlNamespace}.${patient.tableName}(${patient.columns.id.columnName}),
               CONSTRAINT fk_race_type_id FOREIGN KEY (${researchSubjectBuilder.columns.race_type_id.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${raceType.tableName}(${raceType.columns.race_type_id.columnName}),
              CONSTRAINT fk_ethnicity_type_id FOREIGN KEY (${researchSubjectBuilder.columns.ethnicity_type_id.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${ethnicityType.tableName}(${ethnicityType.columns.ethnicity_type_id.columnName}),
              CONSTRAINT unique_participant_per_study UNIQUE (${researchSubjectBuilder.columns.participant_identifier.columnName}, ${researchSubjectBuilder.columns.study_reference.columnName})


              `,
          },
        ];
      }
    },
  },
);

export const studyFileMapping = SQLa.tableDefinition(
  "study_participant_file_mapping",
  {
    rs_file_map_id: primaryKey(), // Unique identifier for the file mapping record
    study_id: researchStudy.references.study_id(), // Foreign key referencing the study table, mandatory
    file_url: text(), // URL or file path where the file is stored, mandatory
    file_content_json: jsonB, // JSON content of the uploaded file, mandatory
    tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Timestamp of when the record was created
    created_by: gm.housekeeping.columns.created_by, // User ID of the person who created the record
    updated_at: gm.housekeeping.columns.updated_at, // Timestamp of when the record was last updated
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the person who last updated the record
    deleted_at: gm.housekeeping.columns.deleted_at, // Timestamp when the record was deleted, if applicable
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the person who deleted the record, if applicable
  },
  {
    isIdempotent: true, // Ensures that the table creation is idempotent, meaning it can be created multiple times without error
    sqlNS: researchSchema, // Specifies the schema namespace for the table
  },
);

export const planDefinition = SQLa.tableDefinition("plan_definition", {
  id: primaryKey(), // Unique identifier for the PlanDefinition
  url: varChar(255), // Canonical URL, optional
  version: varChar(50), // Version of the PlanDefinition, optional
  name: varChar(255), // Name of the PlanDefinition, mandatory
  title: varChar(255), // Title of the PlanDefinition, mandatory
  status_id: integer(), // Status ID, optional
  experimental: varCharNullable(50), // Indicates if it is an experimental plan, optional
  description: varChar(255), // Description of the PlanDefinition, optional
  purpose: varChar(255), // Purpose of the PlanDefinition, optional
  usage: varChar(255), // Context of use or usage guidance, optional
  copyright: varChar(255), // Copyright information, optional
  publisher: varChar(255), // Publisher of the PlanDefinition, optional
  jurisdiction_code: varChar(100), // Jurisdiction code, optional
  related_artifact: varChar(255), // Related artifacts, optional
  org_party_id: ulid(), // Reference to the party Id (foreign key)
  tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
  rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator’s ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the user who updated the record
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the user who deleted the record
  study_id: ulid(), // Reference to the study ID, mandatory
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination == "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT plan_definition_party_id_fkey FOREIGN KEY (${planDefinition.columns.org_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
        },
      ];
    }
  },
});

export const goal = SQLa.tableDefinition("goal", {
  id: primaryKey(), // Primary key, auto-generated
  plan_definition_id: planDefinition.references.id(), // Reference to PlanDefinition, mandatory
  identifier: varChar(255), // Business identifier, optional
  category: varChar(100), // Category of the goal, optional
  description: varChar(255), // Description of the goal, optional
  priority: varChar(50), // Priority of the goal, optional
  start: varChar(100), // Starting point for the goal, optional
  conditions_addressed: varChar(255), // Issues addressed by the goal, optional
  outcome_code: varChar(255), // Coded outcome, optional
  status_id: integer(), // Status of the goal, optional
  status_reason: varChar(255), // Explanation of the status, optional
  tenant_id: organization.references.id(), // Reference to organization ID, mandatory
  rec_status_id: recordStatus.references.id(), // Record status, mandatory
  created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
  created_by: gm.housekeeping.columns.created_by, // Creator's ID
  updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
  deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
  deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
}, {
  isIdempotent: true,
  sqlNS: researchSchema,
});

export const activityDefinitionBuilder = SQLa.tableDefinition(
  "activity_definition",
  {
    id: primaryKey(), // Auto-generated primary key
    plan_definition_id: planDefinition.references.id(), // Reference to PlanDefinition, mandatory
    identifier: varChar(255), // Business identifier, optional
    status_id: varChar(50), // Status of the activity, optional
    name: varChar(255), // Name of the activity, mandatory
    description: varChar(255), // Detailed description, optional
    timing: varChar(255), // Timing or schedule, optional
    location: location.references.id(), // Reference to Location, optional
    participant: patient.references.id(), // Reference to Participants, mandatory
    type: varChar(100), // Type of activity, optional
    reason_code: varChar(255), // Justification for activity, optional
    goal_id: ulidNullable(), // Reference to Goal, mandatory
    output: varChar(255), // Outputs or deliverables, optional
    tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
    rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);

export const activityDefinition = gm.textPkTable(
  activityDefinitionBuilder.tableName,
  activityDefinitionBuilder.zbSchema,
  {
    sqlNS: researchSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_activity_goal_id FOREIGN KEY (${activityDefinitionBuilder.columns.goal_id.columnName}) REFERENCES
              ${researchSchema.sqlNamespace}.${goal.tableName}(${goal.columns.id.columnName})


              `,
          },
        ];
      }
    },
  },
);

export const subjectObservation = SQLa.tableDefinition(
  "subject_observation",
  {
    id: primaryKey(), // Primary key, auto-generated
    research_subject_id: researchSubject.references.rsubject_id(), // Foreign key to ResearchSubject, mandatory
    code: varChar(50), // Code representing observation type, mandatory
    category: varChar(50), // Observation category, optional
    value: floatNullable(), // Observed value, mandatory
    unit: varChar(50), // Unit of measurement, mandatory
    effective_datetime: dateTimeNullable(), // Date and time of observation, optional
    tenant_id: organization.references.id(), // Reference to the organization ID, mandatory
    rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);

export const citation = SQLa.tableDefinition(
  "citation",
  {
    id: primaryKey(), // Primary key, auto-incremented
    url: textNullable(), // URL for the citation
    identifier_system: text(), // Identifier system (e.g., DOI, PubMed), mandatory
    identifier_value: text(), // Identifier value (e.g., DOI number, PubMed ID), mandatory
    title: text(), // Title of the citation, mandatory
    status: textNullable(), // Status of the citation (e.g., active, withdrawn)
    date: dateNullable(), // Date of publication
    publisher: textNullable(), // Publisher of the citation
    abstract: textNullable(), // Abstract of the citation
    journal_title: textNullable(), // Journal or publication title
    journal_volume: textNullable(), // Journal volume number
    journal_issue: textNullable(), // Journal issue number
    journal_page: textNullable(), // Page numbers in the journal
    publication_date: dateNullable(), // Journal publication date
    rec_status_id: recordStatus.references.id(), // Record status (FK), mandatory
    study_id: researchStudy.references.study_id(), // Reference to the associated ResearchStudy resource, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);
export const studyPartyRole = SQLa.tableDefinition(
  "research_study_party_role",
  {
    study_party_role_id: primaryKey(), // Unique identifier for the study party role, primary key
    code: varChar(50), // Code value from the coding system (e.g., sponsor, lead-sponsor, etc.)
    system: varCharNullable(255), // URI of the coding system (e.g., http://terminology.hl7.org/CodeSystem/practitioner-role)
    display: varCharNullable(255), // Display name of the role (e.g., Doctor, Nurse)
    description: varCharNullable(4096), // Description of the role
    rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const citationAuthor = SQLa.tableDefinition(
  "citation_author",
  {
    id: primaryKey(), // Primary key, auto-incremented
    citation_id: citation.references.id(), // Foreign key referencing citation table
    first_name: text(), // Author's first name, mandatory
    last_name: text(), // Author's last name, mandatory
    middle_name: textNullable(), // Author's middle name
    affiliation: textNullable(), // Author's institutional affiliation
    orcid: textNullable(), // ORCID identifier
    email: textNullable(), // Author's email
    role_id: ulidNullable(),
    party_id: ulidNullable(),
    rec_status_id: recordStatus.references.id(), // Record status (FK), mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // User ID of the updater
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // User ID of the deleter
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_role_id FOREIGN KEY (${citationAuthor.columns.role_id.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${studyPartyRole.tableName}(${studyPartyRole.columns.study_party_role_id.columnName})
            `,
          },
        ];
      }
    },
  },
);

export const investigatorStudy = SQLa.tableDefinition(
  "investigator_study",
  {
    mapping_id: primaryKey(), // Unique identifier for the mapping record
    investigator_id: practitioner.references.id(), // FK to investigator table
    study_id: researchStudy.references.study_id(), // FK to research study table
    role: integerNullable(), // Role of the investigator (e.g., Principal Investigator)
    start_date: dateNullable(), // Start date of the investigator's involvement
    end_date: dateNullable(), // End date of the investigator's involvement
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);

//colloborators(investigators,authors)
export const studyCollaboration = SQLa.tableDefinition(
  "study_collaboration",
  {
    collab_id: primaryKey(),
    study_id: researchStudy.references.study_id(), // FK to study table, mandatory
    user_id: ulidNullable(), // FK to party table, mandatory
    access_level: varChar(20), // Access level (read, write, admin/owner)
    shared_at: dateTime(), // Timestamp when shared
    rec_status_id: recordStatus.references.id(), // Record status, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);

export const externalAuthMappings = SQLa.tableDefinition(
  "external_auth_mappings",
  {
    id: primaryKey(), // Primary key, auto-generated
    user_id: userAccount.references.user_id(), // FK to party table, mandatory
    auth_provider: varChar(50), // Authentication provider (e.g., 'ORCID', 'GitHub', 'Google'), mandatory
    provider_user_id: varChar(255), // Unique identifier for the user on external platform, mandatory
    access_token: varChar(255), // OAuth access token, optional
    refresh_token: varChar(255), // OAuth refresh token, optional
    status: varChar(50), // Status of authentication method (e.g., 'Active', 'Revoked'), mandatory
    rec_status_id: recordStatus.references.id(), // Foreign key referencing record_status.id, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    updated_at: gm.housekeeping.columns.updated_at, // Record update timestamp
  },
  {
    isIdempotent: true,
    sqlNS: authSchema,
  },
);

export const site = SQLa.tableDefinition(
  "site",
  {
    site_id: primaryKey(), // Primary key, mandatory
    site_name: text(), // Site name, mandatory
    site_description: textNullable(), // Site description, optional
    organization_id: organization.references.id(), // Foreign key to Organization, mandatory
    address: textNullable(), // Address, optional
    telecom: textNullable(), // Contact information, optional
    latitude: floatNullable(), // Latitude, optional
    longitude: floatNullable(), // Longitude, optional
    status: varChar(50), // Status of the site, default: 'active', mandatory
    study_id: researchStudy.references.study_id(),
    participant_id: researchSubject.references.rsubject_id(),
    rec_status_id: recordStatus.references.id(),
    created_at: gm.housekeeping.columns.created_at, // Default CURRENT_TIMESTAMP
    updated_at: gm.housekeeping.columns.updated_at, // Default CURRENT_TIMESTAMP
    created_by: textNullable(), // Creator's ID, optional
    updated_by: textNullable(), // Updater's ID, optional
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema,
  },
);

export const cgmDataBuilder = SQLa.tableDefinition(
  "cgm_observation",
  {
    id: ulid(), // Primary key, unique identifier for the CGM record
    study_id: ulid(), // Foreign key referencing the research study, mandatory
    research_subject_id: ulid(), // Foreign key referencing the participant (ResearchSubject), mandatory
    period: varChar(50), // Study period (e.g., "Baseline", "Post Randomization"), mandatory
    date_time: dateTime(), // Date and time when the CGM reading was taken, mandatory
    cgm_value: bigFloat(), // CGM value (mg/dL or mmol/L), mandatory
    unit: varChar(10), // Unit of CGM value (e.g., mg/dL, mmol/L), mandatory
    tenant_id: ulid(), // Foreign key referencing the organization (tenant), mandatory
    rec_status_id: recordStatus.references.id(), // Record status, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  },
  {
    isIdempotent: true,
    sqlNS: rawSchema,
  },
);

export const cgmData = gm.textPkTable(
  cgmDataBuilder.tableName,
  cgmDataBuilder.zbSchema,
  {
    sqlNS: rawSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT cgm_observation_pkey_partition PRIMARY KEY (${cgmDataBuilder.columns.id.columnName},${cgmDataBuilder.columns.tenant_id.columnName},${cgmDataBuilder.columns.study_id.columnName}, ${cgmDataBuilder.columns.research_subject_id.columnName}),

             CONSTRAINT fk_cgm_observation_study_id_fkey FOREIGN KEY (${cgmDataBuilder.columns.study_id.columnName}) REFERENCES
              ${researchSchema.sqlNamespace}.${researchStudy.tableName}(${researchStudy.columns.study_id.columnName}),
               CONSTRAINT fk_research_subject_id_fkey FOREIGN KEY (${cgmDataBuilder.columns.research_subject_id.columnName}) REFERENCES
              ${researchSchema.sqlNamespace}.${researchSubject.tableName}(${researchSubject.columns.rsubject_id.columnName}),
               CONSTRAINT fk_rec_status_id_fkey FOREIGN KEY (${cgmDataBuilder.columns.rec_status_id.columnName}) REFERENCES
              ${partySchema.sqlNamespace}.${recordStatus.tableName}(${recordStatus.columns.id.columnName})


            `,
          },
        ];
      }
      if (destination == "after table definition") {
        return [
          {
            SQL: () =>
              `PARTITION BY LIST (${cgmDataBuilder.columns.research_subject_id.columnName})`,
          },
        ];
      }
    },
  },
);

export const activityLog = SQLa.tableDefinition(
  "activity_log",
  {
    activity_id: primaryKey(), // Primary Key, auto-incremented
    activity_name: text(), // Name of the activity, mandatory
    activity_type: text(), // Type of activity (e.g., "user_action"), mandatory
    activity_description: text(), // Optional description of the activity
    root_id: integer(), // Root activity ID (optional)
    parent_id: integer(), // Parent activity ID (optional)
    activity_hierarchy: text(), // Hierarchy information (optional)
    hierarchy_path: text(), // Path in the hierarchy (optional)
    request_url: text(), // Associated request URL (optional)
    tenant_id: integer(), // Tenant ID for multi-tenancy (optional)
    platform: text(), // Platform where the activity occurred (optional)
    environment: text(), // Environment (e.g., "production", "staging") (optional)
    created_by: text(), // User who created the activity log (optional)
    user_name: text(), // Username of the individual triggering the activity (optional)
    created_at: text(), // Timestamp when logged (default: current timestamp)
    app_version: text(), // Application version (optional)
    test_case: text(), // Associated test case (optional)
    session_id: text(), // Session ID of the activity (optional)
    linkage_id: text(), // Linkage identifier for related events (optional)
    ip_address: text(), // IP address where the activity was logged (optional)
    location_latitude: text(), // Latitude of activity location (optional)
    location_longitude: text(), // Longitude of activity location (optional)
    activity_data: text(), // Additional activity data (optional)
    activity_log_level: text(), // Log level (e.g., "info", "debug") (optional)
    session_unique_id: text(), // Unique session identifier, mandatory
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const filterInteraction = SQLa.tableDefinition(
  "filter_interaction",
  {
    filter_interaction_id: primaryKey(), // Primary Key, auto-incremented
    filter_name: text(), // Name of the filter, mandatory
    filter_type: text(), // Type of filter (e.g., "date", "category"), mandatory
    filter_description: text(), // Optional description
    view_mode: text(), // Optional view mode
    created_by: text(), // User who created the filter interaction (optional)
    updated_by: text(), // User who last updated the filter interaction (optional)
    created_at: text(), // Timestamp when created (default: current timestamp)
    updated_at: text(), // Timestamp when last updated (default: current timestamp)
    filter: text(), // Filter criteria or configuration (must be provided)
  },
  {
    isIdempotent: true,
    sqlNS: aiSchema,
  },
);

export const vannaAiRequestResponse = SQLa.tableDefinition(
  "vanna_ai_request_response",
  {
    id: text(), // Primary Key, unique identifier for the request response
    question: text(), // Question asked in the AI request, mandatory
    sql_query: text(), // Optional SQL query related to the AI request
    results: text(), // Optional results from the AI query
    json_result: text(), // Optional JSON result from the AI response
    created_at: dateTime(), // Timestamp when created (default: current timestamp)
    updated_at: dateTime(), // Timestamp when last updated (default: current timestamp)
    created_by: text(), // User who created the request response (optional)
  },
  {
    isIdempotent: true,
    sqlNS: aiSchema,
  },
);

export const organizationType = SQLa.tableDefinition(
  "organization_type",
  {
    organization_type_id: primaryKey(), // Unique identifier for the organization type (PK)
    code: varChar(50), // Code value for the organization type (e.g., NIH, FDA, etc.)
    system_uri: varCharNullable(255), // URI of the coding system (e.g., http://hl7.org/fhir/research-study-party-organization-type)
    display: varCharNullable(255), // Display name (e.g., NIH, FDA, Government)
    description: varCharNullable(4096), // Description of the organization type
    rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema, // Schema namespace
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const fileContentType = SQLa.tableDefinition(
  "file_content_type",
  {
    id: primaryKey(),
    title: varChar(100),
    description: textNullable(),
    rec_status_id: recordStatus.references.id(), // Status of the record (foreign key)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [
        c.unique("title"),
      ];
    },
  },
);

export const researchStudyAssociatedPartyBuilder = SQLa.tableDefinition(
  "research_study_associated_party",
  {
    associated_party_id: primaryKey(), // Unique identifier for the associated party (PK)
    research_study_id: researchStudy.references.study_id(), // FK linking to ResearchStudy(id)
    party_role_type_id: studyPartyRole.references.study_party_role_id(), // FK linking to research-study-party-role
    party_id: varCharNullable(255), // FK linking to the party table if details are known
    party_name: varCharNullable(255), // Name of the associated party
    classifier_id: ulidNullable(), // FK linking to organization_type or classifier table
    period_start: dateNullable(), // Start date of the association
    period_end: dateNullable(), // End date of the association
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const researchStudyAssociatedParty = gm.textPkTable(
  researchStudyAssociatedPartyBuilder.tableName,
  researchStudyAssociatedPartyBuilder.zbSchema,
  {
    sqlNS: researchSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_classifier_id FOREIGN KEY (${researchStudyAssociatedPartyBuilder.columns.classifier_id.columnName}) REFERENCES
              ${masterSchema.sqlNamespace}.${organizationType.tableName}(${organizationType.columns.organization_type_id.columnName})
            `,
          },
        ];
      }
    },
  },
);

export const cgmRawDBDataBuilder = SQLa.tableDefinition(
  "cgm_raw_db",
  {
    db_file_id: primaryKey(), // Unique identifier for the uploaded SQLite file (Primary Key)
    file_name: varChar(255), // Name of the SQLite file
    file_url: textNullable(), // AWS S3 URL or local storage location
    upload_timestamp: dateTime(), // Timestamp of file upload
    uploaded_by: varCharNullable(255), // Reference to the user who uploaded the file
    file_size: varCharNullable(50), // Size of the uploaded file
    is_processed: boolean(), // Indicates whether the file has been processed
    processed_at: dateTimeNullable(), // Nullable; indicates when the file was processed
    process_status: varCharNullable(50), // Status of the db file (e.g., 'uploaded', 'processing', 'migration complete')
    db_file_metadata: jsonbNullable(), // Stores all file metadata
    db_type: varChar(50), // File type
    study_id: researchStudy.references.study_id(), // Study identifier associated with the DB file
    tenant_id: organization.references.id(), // Foreign key reference to the organization table
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema, // Schema namespace
  },
);

export const cgmRawDBData = gm.textPkTable(
  cgmRawDBDataBuilder.tableName,
  cgmRawDBDataBuilder.zbSchema,
  {
    sqlNS: rawDataSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT cgm_raw_db_data_uploaded_by_fkey FOREIGN KEY (${cgmRawDBData.columns.uploaded_by.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})
            `,
          },
        ];
      }
    },
  },
);

export const cgmRawZipData = SQLa.tableDefinition(
  "cgm_raw_zip_data",
  {
    zip_file_id: primaryKey(), // Unique identifier for the ZIP file (primary key)
    tenant_id: organization.references.id(), // Reference to the organization Id (foreign key)
    study_id: researchStudy.references.study_id(), // Study identifier associated with the ZIP file
    file_name: varChar(255), // Name of the ZIP file uploaded
    file_url: textNullable(), // Uploaded file path URL
    file_format: varChar(50), // Format of the ZIP file (e.g., zip)
    upload_timestamp: dateTime(), // Timestamp of ZIP file upload
    uploaded_by: ulidNullable(), // Reference to the user who uploaded the file
    file_size: varCharNullable(100), // Size of the ZIP file in bytes
    is_processed: boolean(), // Status of file processing, default false
    processed_at: dateTimeNullable(), // Nullable; indicates when the file was processed
    status: varCharNullable(50), // Status of the file (e.g., 'uploaded', 'processing', 'complete')
    file_metadata: jsonbNullable(), // Metadata about the file in JSON format
    file_content: text(), // Binary content of the ZIP file
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema, // Schema namespace
    sqlPartial: (destination) => {
      if (destination == "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT cgm_raw_zip_data_uploaded_by_fkey FOREIGN KEY (${cgmRawZipData.columns.uploaded_by.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})
            `,
          },
        ];
      }
    },
  },
);

export const cgmRawUploadDataBuilder = SQLa.tableDefinition(
  "cgm_raw_upload_data",
  {
    cgm_raw_file_id: primaryKey(), // Unique identifier for the raw CGM file (primary key)
    file_name: varChar(255), // Name of the uploaded file
    file_url: textNullable(), // Uploaded file path URL
    zip_file_id: varCharNullable(255), // Reference to the ZIP file, nullable if no ZIP is associated
    cgm_raw_data_json: jsonbNullable(), // Stores the CGM raw file in JSON format
    upload_timestamp: dateTime(), // Timestamp of file upload
    uploaded_by: varCharNullable(255), // Reference to the user who uploaded the file
    file_size: varCharNullable(50), // Size of the uploaded file in bytes
    is_processed: boolean(), // Indicates whether the file has been processed
    processed_at: dateTimeNullable(), // Nullable; indicates when the file was processed
    status: varCharNullable(50), // Status of the file (e.g., 'uploaded', 'processing', 'complete')
    file_metadata: jsonbNullable(), // Stores all file metadata
    file_type: varChar(50), // File type (e.g., csv, text, xls, xlsx, json, xml)
    study_id: researchStudy.references.study_id(), // Study identifier associated with the ZIP file
    tenant_id: organization.references.id(), // Foreign key reference to the organization table
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema, // Schema namespace
  },
);

export const cgmRawUploadData = gm.textPkTable(
  cgmRawUploadDataBuilder.tableName,
  cgmRawUploadDataBuilder.zbSchema,
  {
    sqlNS: rawDataSchema, // Replace with the actual schema name if needed
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_zip_file_id FOREIGN KEY (${cgmRawUploadDataBuilder.columns.zip_file_id.columnName}) REFERENCES
              ${rawDataSchema.sqlNamespace}.${cgmRawZipData.tableName}(${cgmRawZipData.columns.zip_file_id.columnName})
              `,
          },
        ];
      }
    },
  },
);

export const fileMetaIngestData = SQLa.tableDefinition(
  "file_meta_ingest_data",
  {
    file_meta_id: text(),
    db_file_id: text(), // Unique identifier for the database file
    participant_sid: text(), // Participant SID
    file_meta_data: textNullable(), // Metadata of the file (JSON format or string)
    cgm_data: textNullable(), // CGM data in raw text format
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema, // Schema namespace
  },
);

export const participant = SQLa.tableDefinition(
  "participant",
  {
    db_file_id: textNullable(), // Unique identifier for the database file
    tenant_id: textNullable(), // Organization or tenant ID
    study_id: textNullable(), // Associated study ID
    participant_id: textNullable(), // Participant unique identifier
    site_id: textNullable(), // Study site ID
    diagnosis_icd: textNullable(), // ICD diagnosis codes
    med_rxnorm: textNullable(), // RxNorm medication codes
    treatment_modality: textNullable(), // Treatment modality
    gender: textNullable(), // Gender
    race_ethnicity: textNullable(), // Race/Ethnicity
    age: textNullable(), // Age in years
    bmi: textNullable(), // Body Mass Index
    baseline_hba1c: textNullable(), // Baseline HbA1c value
    diabetes_type: textNullable(), // Type of diabetes
    study_arm: textNullable(), // Study arm assignment
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema, // Schema namespace
  },
);

export const participantMealFitnessData = SQLa.tableDefinition(
  "participant_meal_fitness_data",
  {
    db_file_id: text(), // Unique identifier for the database file
    tenant_id: text(), // Tenant ID for multi-tenant environments
    study_display_id: text(), // Study identifier
    fitness_meal_id: text(), // Unique identifier for meal/fitness entry
    participant_display_id: text(), // Unique identifier for the participant
    meal_data: textNullable(), // Stores meal tracking data in JSON format
    fitness_data: textNullable(), // Stores fitness tracking data in JSON format
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema, // Schema namespace
  },
);

export const studyMetaData = SQLa.tableDefinition(
  "study_meta_data",
  {
    db_file_id: text(), // Unique identifier for the database file
    study_meta_id: text(), // Unique identifier for the study metadata
    tenant_id: text(), // Tenant ID for multi-tenant environments
    study_display_id: text(), // Study identifier (human-readable)
    study_name: text(), // Name of the study
    start_date: textNullable(), // Start date of the study (stored as text)
    end_date: textNullable(), // End date of the study (stored as text)
    treatment_modalities: textNullable(), // Description of treatment modalities
    funding_source: textNullable(), // Source of funding for the study
    nct_number: textNullable(), // Clinical trial registration number
    study_description: textNullable(), // Detailed study description
    investigators: textNullable(), // List of investigators (JSON array)
    publications: textNullable(), // List of related publications (JSON array)
    authors: textNullable(), // List of study authors (JSON array)
    institutions: textNullable(), // Institutions associated with the study (JSON array)
    labs: textNullable(), // Labs associated with the study (JSON array)
    sites: textNullable(), // Study sites (JSON array)
    elaboration: textNullable(), // Additional details about the study
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema, // Schema namespace
  },
);

export const participantMigrationHistory = SQLa.tableDefinition(
  "participant_migration_history",
  {
    log_id: text(),
    db_file_id: text(), // Identifier of the database file
    study_display_id: textNullable(),
    participant_json: textNullable(), // Full JSON of participant details
    captured_at: dateTimeNullable(), // Timestamp of capture
    captured_by: textNullable(), // Who captured the record
    study_id: textNullable(), // Study ID associated with the participant
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema,
  },
);

export const studyMetadataHistory = SQLa.tableDefinition(
  "study_metadata_history",
  {
    log_id: text(), // Unique log ID
    db_file_id: text(), // Identifier of the database file
    study_meta_id: text(), // Study metadata ID
    study_metadata: textNullable(), // Full JSON of the study metadata
    captured_by: textNullable(), // Who captured the record
    captured_at: dateTimeNullable(), // Timestamp of capture
    study_id: textNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema,
  },
);

export const participantMigrationStatus = SQLa.tableDefinition(
  "participant_migration_status",
  {
    db_file_id: text(),
    study_id: text(),
    participant_display_id: text(),
    participant_id: textNullable(),
    migration_status: migrationStatus.references.stage_id(),
    migration_start_time: dateTimeNullable(),
    migration_end_time: dateTimeNullable(),
    last_updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema,
  },
);

export const rawCgmExtractData = SQLa.tableDefinition(
  "raw_cgm_extract_data",
  {
    cgm_raw_data_id: primaryKey(), // Unique identifier for each CGM data set (primary key)
    raw_file_id: cgmRawUploadData.references.cgm_raw_file_id(), // FK referencing the master table
    study_id: researchStudy.references.study_id(), // FK linking to the research study table
    participant_sid: researchSubject.references.rsubject_id(), // FK linking to the participant in research_subject
    cgm_raw_data_json: jsonbNullable(), // Raw CGM data in JSON file format
    file_url: textNullable(), // Uploaded file path URL
    file_meta_data: jsonbNullable(), // Metadata about the file (e.g., device ID, device name)
    cgm_data: jsonbNullable(), // Converted CGM data in JSON format for uniform representation (optional)
    tenant_id: organization.references.id(), // FK linking to the tenant/organization table
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema, // Schema namespace
  },
);

export const cgmDataMigrationStatus = SQLa.tableDefinition(
  "cgm_data_migration_status",
  {
    cgm_migrate_id: text(),
    cgm_raw_data_id: textNullable(),
    db_file_id: text(),
    study_id: text(),
    participant_id: text(),
    cgm_migration_status: migrationStatus.references.stage_id(),
    migration_start_time: dateTimeNullable(),
    migration_end_time: dateTimeNullable(),
    last_updated_at: dateTimeNullable(),
    participant_display_id: text(),
    file_meta_id: text(),
  },
  {
    isIdempotent: true,
    sqlNS: dbImportMigrateSchema,
  },
);

export const dbMigrationLog = SQLa.tableDefinition(
  "db_migration_log",
  {
    db_migration_log_id: text(), // Unique identifier for the migration log
    db_file_id: text(), // Reference to the database file being migrated
    migration_status_id: migrationStatus.references.stage_id(), // Status ID indicating migration success or failure
    participant_success_records: textNullable(), //  successfully migrated participant records
    participant_failure_records: textNullable(), //  containing failed participant records
    cgm_success_records: textNullable(), //  containing successfully migrated CGM records
    cgm_failure_records: textNullable(), //  containing failed CGM records
    migration_start_time: dateTimeNullable(),
    migration_end_time: dateTimeNullable(),
  },
  {
    isIdempotent: true, // Ensures table creation is idempotent
    sqlNS: dbImportMigrateSchema, // Sets the schema name
  },
);

export const cgmDeviceInfo = SQLa.tableDefinition("cgm_device_info", {
  participant_sid: primaryKey(), // Participant SID
  cgm_devices: textNullable(), // CGM devices, nullable
  cgm_files: textNullable(), // CGM files, nullable
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const cgmMetrics = SQLa.tableDefinition("cgm_metrics", {
  participant_sid: primaryKey(), // Participant SID, nullable
  total_readings: integerNullable(), // Total readings, nullable
  days_of_wear: integerNullable(), // Days of wear, nullable
  data_start_date: dateNullable(), // Data start date, nullable
  data_end_date: dateNullable(), // Data end date, nullable
  avg_glucose: floatNullable(), // Average glucose, nullable
  glucose_stddev: floatNullable(), // Glucose standard deviation, nullable
  in_range_count: integerNullable(), // In-range count, nullable
  very_high_count: integerNullable(), // Very high count, nullable
  high_count: integerNullable(), // High count, nullable
  low_count: integerNullable(), // Low count, nullable
  very_low_count: integerNullable(), // Very low count, nullable
  above_range_count: integerNullable(), // Above range count, nullable
  below_range_count: integerNullable(), // Below range count, nullable
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const participantBase = SQLa.tableDefinition("participant_base", {
  organization_party_id: textNullable(), // Organization party ID, nullable
  organization_id: textNullable(), // Organization ID, nullable
  study_id: textNullable(), // Study ID, nullable
  study_display_id: varCharNullable(10), // Study display ID, nullable
  participant_id: textNullable(), // Participant ID, nullable
  participant_display_id: varCharNullable(255), // Participant display ID, nullable
  gender: textNullable(), // Gender, nullable
  age: integerNullable(), // Age, nullable
  "Study Arm": varCharNullable(255), // Study Arm, nullable
  "Baseline HbA1C": floatNullable(), // Baseline HbA1C, nullable
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
});

export const participantFileMapping = SQLa.tableDefinition(
  "participant_file_mapping",
  {
    id: ulid(),
    file_mapping_id: studyFileMapping.references.rs_file_map_id(),
    r_subject_id: researchSubject.references.rsubject_id(), // Foreign key referencing the research subject
    study_id: researchStudy.references.study_id(),
    tenant_id: organization.references.id(),
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const hubInteraction = SQLa.tableDefinition(
  "hub_interaction",
  {
    hub_interaction_id: text(),
    study_id: textNullable(),
    organization_party_id: textNullable(),
    created_by: textNullable(),
    updated_by: textNullable(),
    created_at: dateTimeNullable(),
    updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const sessionUniqueIdMapping = SQLa.tableDefinition(
  "session_unique_id_mapping",
  {
    id: text(),
    session_unique_id: text(),
    session_id: text(),
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const studyInteraction = SQLa.tableDefinition(
  "study_interaction",
  {
    study_interaction_id: text(),
    hub_interaction_id: textNullable(),
    study_id: textNullable(),
    organization_party_id: textNullable(),
    uri: textNullable(),
    interaction_type: textNullable(), // Create/Edit/Delete
    description: textNullable(),
    request: jsonbNullable(),
    response: jsonbNullable(),
    from_state: textNullable(),
    to_state: textNullable(),
    status: textNullable(), // Success/Failure/Error
    response_code: integerNullable(),
    error_response: textNullable(),
    created_by: textNullable(),
    updated_by: textNullable(),
    created_at: dateTimeNullable(),
    updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const fileInteraction = SQLa.tableDefinition(
  "file_interaction",
  {
    file_interaction_id: text(),
    hub_interaction_id: textNullable(),
    study_id: textNullable(),
    organization_party_id: textNullable(),
    participant_id: textNullable(),
    uri: textNullable(),
    description: textNullable(),
    request: jsonbNullable(),
    response: jsonbNullable(),
    db_file_id: textNullable(),
    file_location: textNullable(),
    file_name: textNullable(),
    file_content_type: textNullable(),
    file_content_json: jsonbNullable(),
    file_category: textNullable(), // Database/Participant/CGM
    file_upload_status: textNullable(), // Success/Failure/Error
    file_processing_initiated_at: dateTimeNullable(),
    file_processing_completed_at: dateTimeNullable(),
    file_processing_status: textNullable(), // In progress/Completed
    response_code: integerNullable(),
    error_response: textNullable(),
    created_by: textNullable(),
    updated_by: textNullable(),
    created_at: dateTimeNullable(),
    updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const studyParticipantInteraction = SQLa.tableDefinition(
  "study_participant_interaction",
  {
    participant_interaction_id: text(),
    hub_interaction_id: textNullable(),
    study_id: textNullable(),
    organization_party_id: textNullable(),
    participant_id: textNullable(),
    uri: textNullable(),
    interaction_type: textNullable(), // Create/Edit/Delete
    description: textNullable(),
    request: jsonbNullable(),
    response: jsonbNullable(),
    from_state: textNullable(),
    to_state: textNullable(),
    status: textNullable(), // Success/Failure/Error
    response_code: integerNullable(),
    error_response: textNullable(),
    created_by: textNullable(),
    updated_by: textNullable(),
    created_at: dateTimeNullable(),
    updated_at: dateTimeNullable(),
  },
  {
    isIdempotent: true,
    sqlNS: activitySchema,
  },
);

export const studyTypeMapping = SQLa.tableDefinition(
  "study_type_mapping",
  {
    study_id: researchStudy.references.study_id(), // Foreign key referencing the research study
    study_type: varChar(20), // Study classification-real or synthetic
  },
  {
    isIdempotent: true, // Ensures safe re-creation
    sqlNS: researchSchema,
  },
);

export const nutritionIntakeStatusCode = SQLa.tableDefinition(
  "nutrition_intake_status_code",
  {
    id: primaryKey(), // Unique identifier for the nutrition intake status code (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const unitOfMeasurement = SQLa.tableDefinition(
  "unit_of_measurement",
  {
    unit_id: primaryKey(), // Unique identifier for the unit of measurement (PK)
    unit: varCharNullable(15), // Unit of measurement (e.g., mg/dL, mmol/L)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const measurementCategory = SQLa.tableDefinition(
  "measurement_category",
  {
    id: primaryKey(), // Unique identifier for the unit of measurement (PK)
    unit_category_name: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
  },
);

export const unitCategoryMapping = SQLa.tableDefinition(
  "unit_category_mapping",
  {
    mapping_id: primaryKey(), // Unique identifier for the unit of measurement (PK)
    unit_id: unitOfMeasurement.references.unit_id(), // FK referencing unit_of_measurement (Unit of measurement)
    category_id: measurementCategory.references.id(), // FK referencing measurement_category (Measurement category)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
  },
);

export const mealType = SQLa.tableDefinition(
  "meal_type",
  {
    meal_type_id: primaryKey(), // Unique identifier for the nutrition intake status code (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const nutritionIntakeBuilder = SQLa.tableDefinition("nutrition_intake", {
  id: ulid(), // Unique identifier for the research study
  status_code: nutritionIntakeStatusCode.references.id(), // Foreign key referencing the status table, mandatory
  study_id: researchStudy.references.study_id(), // Foreign key referencing the research study table, mandatory
  subject_id: researchSubjectBuilder.references.rsubject_id(), // Foreign key referencing the subject table, mandatory
  occurrence_time: dateTime(), // Date and time of the nutrition intake, mandatory
  meal_type_id: mealType.references.meal_type_id(), // Foreign key referencing the meal type table, mandatory
  value_quantity: bigFloat(), // Quantity of the nutrition intake, mandatory
  value_unit_id: unitOfMeasurement.references.unit_id(), // Foreign key referencing the unit of measurement table, mandatory
  glycemic_index: bigFloatNullable(), // Glycemic index of the nutrition intake, optional
  glycemic_load: bigFloatNullable(), // Glycemic load of the nutrition intake, optional
  note: textNullable(), // Additional notes about the nutrition intake, optional
}, {
  isIdempotent: true,
  sqlNS: researchSchema, // Schema namespace
  sqlPartial: (destination) => {
    if (destination === "after all column definitions") {
      return [
        {
          SQL: () =>
            `CONSTRAINT nutrition_intake_pkey_partition PRIMARY KEY (id,subject_id)
            `,
        },
      ];
    }
    if (destination == "after table definition") {
      return [
        {
          SQL: () => `PARTITION BY LIST (subject_id)`,
        },
      ];
    }
  },
});

export const subjectObservationZipDataBuilder = SQLa.tableDefinition(
  "subject_observation_zip_data",
  {
    id: primaryKey(), // Primary key
    tenant_id: organization.references.id(), // Reference to organization table
    study_id: researchStudy.references.study_id(), // Reference to research study
    file_name: varChar(255), // Name of the uploaded file
    file_url: textNullable(), // URL where file is stored
    file_format: varChar(50), // Format of the file
    file_content_type_id: fileContentType.references.id(), // Reference to file content type
    upload_timestamp: dateTime(), // When file was uploaded
    uploaded_by: textNullable(), // Who uploaded the file
    file_size: varCharNullable(100), // Size of file
    is_processed: boolean(), // Processing status flag
    processed_at: dateTimeNullable(), // When processing completed
    status: varCharNullable(50), // Status of the file
    file_metadata: jsonbNullable(), // Additional metadata
    file_content: text(), // The file content
    //file_interaction_id: textNullable(), // Reference to file interaction
    rec_status_id: recordStatus.references.id(), // Record status reference
    created_at: gm.housekeeping.columns.created_at, // Creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator reference
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater reference
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter reference
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema,
  },
);

export const subjectObservationZipData = gm.textPkTable(
  subjectObservationZipDataBuilder.tableName,
  subjectObservationZipDataBuilder.zbSchema,
  {
    sqlNS: rawDataSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_subject_observation_zip_content_type
                FOREIGN KEY (${subjectObservationZipDataBuilder.columns.file_content_type_id.columnName})
                REFERENCES ${masterSchema.sqlNamespace}.file_content_type(id),
              CONSTRAINT fk_subject_observation_zip_uploaded_by
                FOREIGN KEY (${subjectObservationZipDataBuilder.columns.uploaded_by.columnName})
                REFERENCES ${partySchema.sqlNamespace}.party(party_id)
            `,
          },
        ];
      }
      return [];
    },
  },
);

export const subjectObservationUploadDataBuilder = SQLa.tableDefinition(
  "subject_observation_upload_data",
  {
    id: primaryKey(), // Primary key
    file_name: varChar(255), // Name of the uploaded file
    file_url: textNullable(), // URL where file is stored
    zip_file_id: varCharNullable(255), // Reference to the ZIP file
    file_content_type_id: text(), // Reference to file content type
    raw_data_json: jsonbNullable(), // JSON format of raw data
    upload_timestamp: dateTime(), // When file was uploaded
    uploaded_by: varCharNullable(255), // Who uploaded the file
    file_size: varCharNullable(50), // Size of the file
    is_processed: boolean(), // Processing status flag
    processed_at: dateTimeNullable(), // When processing completed
    status: varCharNullable(50), // Status of the file
    file_metadata: jsonbNullable(), // Additional metadata
    file_type: varChar(50), // Type of file
    study_id: researchStudy.references.study_id(), // Study reference
    tenant_id: organization.references.id(), // Organization reference
    database_id: varCharNullable(255), // Database reference ID
    //file_interaction_id: textNullable(), // Reference to file interaction
    rec_status_id: recordStatus.references.id(), // Record status reference
    created_at: gm.housekeeping.columns.created_at, // Creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator reference
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater reference
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter reference
    // raw_data_csv: blobTextNullable(), // CSV format of raw data
    // raw_data_excel: blobTextNullable(), // Excel format of raw data
    // raw_data_xml: textNullable(), // XML format of raw data
    // raw_data_text: blobTextNullable(), // Text format of raw data
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema,
  },
);

export const subjectObservationUploadData = gm.textPkTable(
  subjectObservationUploadDataBuilder.tableName,
  subjectObservationUploadDataBuilder.zbSchema,
  {
    sqlNS: rawDataSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () => `
              CONSTRAINT fk_database_id
                FOREIGN KEY (${subjectObservationUploadDataBuilder.columns.database_id.columnName})
                REFERENCES ${rawDataSchema.sqlNamespace}.cgm_raw_db(db_file_id),
              CONSTRAINT fk_zip_file_id
                FOREIGN KEY (${subjectObservationUploadDataBuilder.columns.zip_file_id.columnName})
                REFERENCES ${rawDataSchema.sqlNamespace}.${subjectObservationZipData.tableName}(${subjectObservationZipDataBuilder.columns.id.columnName})

            `,
          },
        ];
      }
      return [];
    },
  },
);

export const subjectObservationExtractData = SQLa.tableDefinition(
  "subject_observation_extract_data",
  {
    id: primaryKey(), // Primary key
    subject_observation_upload_id: subjectObservationUploadData.references.id(), // Reference to upload data
    study_id: researchStudy.references.study_id(), // Study reference
    participant_sid: researchSubject.references.rsubject_id(), // Participant reference
    file_content_type_id: fileContentType.references.id(), // Content type reference
    subject_observation_data_json: jsonbNullable(), // JSON observation data
    file_url: textNullable(), // File URL
    file_meta_data: jsonbNullable(), // File metadata
    subject_observation_data: jsonbNullable(), // CGM data in JSON
    tenant_id: organization.references.id(), // Organization reference
    //file_interaction_id: textNullable(), // File interaction reference
    rec_status_id: recordStatus.references.id(), // Record status
    created_at: gm.housekeeping.columns.created_at,
    created_by: gm.housekeeping.columns.created_by,
    updated_at: gm.housekeeping.columns.updated_at,
    updated_by: gm.housekeeping.columns.updated_by,
    deleted_at: gm.housekeeping.columns.deleted_at,
    deleted_by: gm.housekeeping.columns.deleted_by,
    // raw_data_csv: blobTextNullable(), // CSV format data
    // raw_data_excel: blobTextNullable(), // Excel format data
    // raw_data_text: blobTextNullable(), // Text format data
    // raw_data_xml: textNullable(), // XML format data
  },
  {
    isIdempotent: true,
    sqlNS: rawDataSchema,
  },
);

export const observationMethod = SQLa.tableDefinition(
  "observation_method",
  {
    method_id: primaryKey(), // Unique identifier for the observation method (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const observationStatus = SQLa.tableDefinition(
  "observation_status",
  {
    id: primaryKey(), // Unique identifier for the observation status (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const observationCategory = SQLa.tableDefinition(
  "observation_category",
  {
    category_id: primaryKey(), // Unique identifier for the observation category (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const nutritionIntakeMapping = SQLa.tableDefinition(
  "nutritionintake_mapping",
  {
    id: ulid(),
    nutrition_mapping_id: ulid(),
    observation_mapping_id: subjectObservationExtractData.references.id(),
    r_subject_id: researchSubject.references.rsubject_id(), // Foreign key referencing the research subject
    study_id: researchStudy.references.study_id(),
    tenant_id: organization.references.id(),
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const activityMaster = SQLa.tableDefinition(
  "activity_master",
  {
    activity_id: primaryKey(), // Unique identifier for the activity (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    text: varChar(1000), // Text description of the activity
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const activityType = SQLa.tableDefinition(
  "activity_type",
  {
    id: primaryKey(), // Unique identifier for the activity (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    title: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    description: textNullable(), // Description of the activity
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const activityComponentType = SQLa.tableDefinition(
  "activity_component_type",
  {
    component_type_id: primaryKey(), // Unique identifier for the activity component type (PK)
    code: varChar(50), // Code value from the coding system (e.g., doctor, nurse)
    system: varChar(255), // URI of the coding system (e.g., HL7 CodeSystem)
    display: varChar(255), // Display name of the role (e.g., Doctor, Nurse)
    rec_status_id: recordStatus.references.id(), // FK referencing record_status (Status of the record)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: masterSchema,
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [];
      }
      return [];
    },
    constraints: (props, tableName) => {
      const c = SQLa.tableConstraints(tableName, props);
      return [c.unique("code")];
    },
  },
);

export const observationFitnessBuilder = SQLa.tableDefinition(
  "observation_fitness",
  {
    observation_id: ulid(), // Unique identifier for the research study
    study_id: researchStudy.references.study_id(), // Foreign key referencing the research study table, mandatory
    subject_id: researchSubjectBuilder.references.rsubject_id(), // Foreign key referencing the subject table, mandatory
    effective_datetime: dateTime(), // Date and time of the observation, mandatory
    status_id: observationStatus.references.id(), // Foreign key referencing the status table, mandatory
    category_id: observationCategory.references.category_id(), // Foreign key referencing the category table, mandatory
    activity_type_id: activityMaster.references.activity_id(), // Foreign key referencing the activity type table, mandatory
    device_id: textNullable(), // Device ID, optional
    intensity_type_id: textNullable(), // Intensity type, optional
    intensity_value_id: textNullable(), // Intensity value, optional
    note: textNullable(), // Additional notes, optional
    rec_status_id: recordStatus.references.id(), // Foreign key referencing the status table, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              ` CONSTRAINT observation_fitness_pkey_partition PRIMARY KEY (observation_id,subject_id),
                UNIQUE (observation_id, subject_id)`,
          },
        ];
      }
      if (destination == "after table definition") {
        return [
          {
            SQL: () => ` PARTITION BY LIST (subject_id)`,
          },
        ];
      }
    },
  },
);

export const observationFitnessComponentBuilder = SQLa.tableDefinition(
  "observation_fitness_component",
  {
    component_id: ulid(), // Unique identifier for the research study
    observation_id: ulid(), // Foreign key referencing the observation table, mandatory
    study_id: researchStudy.references.study_id(), // Foreign key referencing the research study table, mandatory
    subject_id: researchSubjectBuilder.references.rsubject_id(), // Foreign key referencing the subject table, mandatory
    component_type_id: activityComponentType.references.component_type_id(), // Foreign key referencing the component type table, mandatory
    value: bigFloat(), // Value of the component, mandatory
    unit_code: unitOfMeasurement.references.unit_id(), // Foreign key referencing the unit of measurement table, mandatory
    method_id: observationMethod.references.method_id(), // Foreign key referencing the method table, mandatory
    rec_status_id: recordStatus.references.id(), // Foreign key referencing the status table, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
    sqlPartial: (destination) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              ` FOREIGN KEY (observation_id, subject_id) REFERENCES drh_stateful_research_study.observation_fitness (observation_id, subject_id),
                CONSTRAINT observation_fitness_component_pkey PRIMARY KEY (component_id,subject_id)`,
          },
        ];
      }
      if (destination == "after table definition") {
        return [
          {
            SQL: () => ` PARTITION BY LIST (subject_id)`,
          },
        ];
      }
    },
  },
);

export const userAccountVerificationLog = SQLa.tableDefinition(
  "user_account_verification_log",
  {
    id: primaryKey(), // Unique identifier (ULID)
    email: text(), // User's email address, mandatory
    provider_id: textNullable(), // Id of github/orcid id etc, (before creating user profile)    
    verification_token: textNullable(), // Verification token for email confirmation
    token_expires_at: dateTimeNullable(), // Verification token expiration timestamp
    otp_code: textNullable(), // One-time password code
    otp_expires_at: dateTimeNullable(), // OTP expiration timestamp
    attempt_count: integer().default(0), // Number of verification attempts
    last_attempt_at: dateTimeNullable(), // Timestamp of last attempt
    locked_until: dateTimeNullable(), // Account lock expiration timestamp
    is_verified: boolean().default(false), // Verification status
    verification_status_id: userVerificationStatus.references.id(), // Status of user verification (e.g., pending, verified, failed)
    rec_status_id: recordStatus.references.id(), // Status of the record, mandatory
    user_party_id: varCharNullable(255), // Reference to party ID of the user
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  },
  {
    isIdempotent: true,
    sqlNS: authSchema, // Using the auth schema like other auth-related tables
    sqlPartial: (destination: string) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT fk_user_account_verification_log_user_party_id FOREIGN KEY (${userAccountVerificationLog.columns.user_party_id.columnName}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
          },
        ];
      }
      return [];
    }
  },
);
export const fitnessMapping = SQLa.tableDefinition(
  "fitness_mapping",
  {
    id: ulid(),
    fitness_mapping_id: ulid(),
    observation_mapping_id: subjectObservationExtractData.references.id(),
    r_subject_id: researchSubject.references.rsubject_id(), // Foreign key referencing the research subject
    study_id: researchStudy.references.study_id(),
    tenant_id: organization.references.id(),
    rec_status_id: recordStatus.references.id(), // Status ID for the record, mandatory
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator’s ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater’s ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter’s ID
  },
  {
    isIdempotent: true,
    sqlNS: researchSchema, // Schema namespace
  },
);

export const aiConversationLog = SQLa.tableDefinition(
  "ai_conversation_log",
  {
    id: primaryKey(), // Primary key, auto-generated ULID
    message_json: jsonbNullable(), // Stores the question/answer as JSON
    auth_provider_id: textNullable(), // Authentication provider (e.g., ORCID, GitHub)
    user_party_id: varCharNullable(255), // Reference to party table (nullable)
    context_section: textNullable(), // Section/tab/category where the question was asked (renamed from 'source')
    rec_status_id: recordStatus.references.id(), // Record status (FK)
    created_at: gm.housekeeping.columns.created_at, // Record creation timestamp
    created_by: gm.housekeeping.columns.created_by, // Creator's ID
    updated_at: gm.housekeeping.columns.updated_at, // Last update timestamp
    updated_by: gm.housekeeping.columns.updated_by, // Updater's ID
    deleted_at: gm.housekeeping.columns.deleted_at, // Deletion timestamp
    deleted_by: gm.housekeeping.columns.deleted_by, // Deleter's ID
  },
  {
    isIdempotent: true,
    sqlNS: aiSchema,
    sqlPartial: (destination: string) => {
      if (destination === "after all column definitions") {
        return [
          {
            SQL: () =>
              `CONSTRAINT fk_ai_conversation_log_user_party_id FOREIGN KEY (${'user_party_id'}) REFERENCES ${partySchema.sqlNamespace}.${udm.party.tableName}(${udm.party.columns.party_id.columnName})`,
          },
        ];
      }
      return [];
    },
  },
);


function sqlDDLGenerateMigration() {
  return SQLa.SQL<EmitContext>(udm.ddlOptions)`
      ${recordStatus}
      ${organization}
      ${profileStatusType}
      ${userAccount}
      ${location}
      ${practitioner}
      ${patient}
      ${qualification}
      ${contactPointSystem}
      ${contactPointUse}
      ${contactPointAddressType}
      ${contactPointAddressUse}
      ${address}
      ${telecom}
      ${communication}
      ${associatedPartyType}
      ${laboratory}
      ${researchStudyFocus}
      ${researchStudyCondition}
      ${device}
      ${loincCodes}
      ${studyVisibility}
      ${citationIdentifier}
      ${metricDefinitions}
      ${studyStatusDefinitions}
      ${researchSubjectStatusDefinitions}
      ${citationStatusDefinitions}
      ${investigatorStudyRoleDefinitions}
      ${acceptedFileFormats}
      ${interactionStatus}
      ${interactionActionType}
      ${researchStudy}
      ${researchSubject}
      ${studyFileMapping}
      ${planDefinition}
      ${goal}
      ${activityDefinition}
      ${subjectObservation}
      ${citation}
      ${studyPartyRole}
      ${citationAuthor}
      ${investigatorStudy}

      ${externalAuthMappings}
      ${site}
      ${studyCollaboration}
      ${roleType}
      ${role}
      ${userRole}
      ${permission}
      ${rolePermission}
      ${groupType}
      ${groupMember}
      ${consentCategory}
      ${consentStatusCode}
      ${consentDecisionType}
      ${studyConsent}
      ${group}
      ${contractType}
      ${contractSubType}
      ${contractSignerType}
      ${legalStateCode}
     ${contractExpirationType}
     ${signatureType}
     ${contractTerm}
     ${decisionType}
     ${decisionMode}
     ${termOffer}
     ${termOfferParty}
     ${contractStatus}
     ${studyContract}
      ${cgmData}
      ${activityLog}
      ${exceptionLog}
      ${filterInteraction}
      ${vannaAiRequestResponse}
      ${organizationType}
      ${fileContentType}
      ${researchStudyAssociatedParty}
      ${cgmRawDBData}
      ${cgmRawZipData}
      ${cgmRawUploadData}
      ${rawCgmExtractData}
      ${fileMetaIngestData}
      ${participant}
      ${participantMigrationStatus}
      ${cgmDataMigrationStatus}
      ${migrationStatus}
      ${dbMigrationLog}
      ${cgmDeviceInfo}
      ${cgmMetrics}
      ${participantBase}
      ${hubInteraction}
      ${sessionUniqueIdMapping}
      ${studyInteraction}
      ${fileInteraction}
      ${studyParticipantInteraction}
      ${participantMealFitnessData}
      ${studyMetaData}
      ${studyTypeMapping}
      ${nutritionIntakeStatusCode}
      ${unitOfMeasurement}
      ${mealType}
      ${measurementCategory}
      ${unitCategoryMapping}
      ${nutritionIntakeBuilder}
      ${subjectObservationZipData}
      ${subjectObservationUploadData}
      ${subjectObservationExtractData}
      ${observationMethod}
      ${observationStatus}
      ${observationCategory}
      ${activityMaster}
      ${activityType}
      ${activityComponentType}
      ${observationFitnessBuilder}
      ${observationFitnessComponentBuilder}
      ${userCredentials}
      ${sessionAuditLog}
      ${activityLevel}
      ${activityLevelMapping}
      ${userVerificationStatus}
      ${userAccountVerificationLog}
      
      ${participantFileMapping}
      ${nutritionIntakeMapping}
      ${fitnessMapping}
      ${aiConversationLog}

`;
}

export function generated() {
  const ctx = SQLa.typicalSqlEmitContext({
    sqlDialect: SQLa.postgreSqlDialect(),
  });

  // after this execution `ctx` will contain list of all tables which will be
  // passed into `dvts.pumlERD` below (ctx should only be used once)
  const driverGenerateMigrationSQL = ws.unindentWhitespace(
    sqlDDLGenerateMigration().SQL(ctx),
  );
  return {
    driverGenerateMigrationSQL,
    //pumlERD: dvts.pumlERD(ctx).content
  };
}
