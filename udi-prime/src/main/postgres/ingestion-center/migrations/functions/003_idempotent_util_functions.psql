CREATE OR REPLACE FUNCTION drh_stateless_util.generate_unique_id() RETURNS TEXT AS $$
DECLARE
    encoding TEXT = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    timestamp BYTEA = E'\\000\\000\\000\\000\\000\\000';
    output TEXT = '';
    unix_time BIGINT;
    ulid BYTEA;
    i INT;
BEGIN
    -- 6 timestamp bytes
    unix_time = (EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT;
    timestamp = SET_BYTE(timestamp, 0, (unix_time >> 40)::BIT(8)::INTEGER);
    timestamp = SET_BYTE(timestamp, 1, (unix_time >> 32)::BIT(8)::INTEGER);
    timestamp = SET_BYTE(timestamp, 2, (unix_time >> 24)::BIT(8)::INTEGER);
    timestamp = SET_BYTE(timestamp, 3, (unix_time >> 16)::BIT(8)::INTEGER);
    timestamp = SET_BYTE(timestamp, 4, (unix_time >> 8)::BIT(8)::INTEGER);
    timestamp = SET_BYTE(timestamp, 5, unix_time::BIT(8)::INTEGER);

    -- 10 entropy bytes
    ulid = timestamp || drh_stateless_util.gen_random_bytes(10);

    -- Encode the ULID
    FOR i IN 0..15 LOOP
        output = output || SUBSTRING(encoding FROM (GET_BYTE(ulid, i) >> 3) + 1 FOR 1);
        IF i < 15 THEN
            output = output || SUBSTRING(encoding FROM (((GET_BYTE(ulid, i) & 7) << 2) | (GET_BYTE(ulid, i + 1) >> 6)) + 1 FOR 1);
        ELSE
            output = output || SUBSTRING(encoding FROM ((GET_BYTE(ulid, i) & 7) << 2) + 1 FOR 1);
        END IF;
    END LOOP;

    -- Ensure the output is exactly 26 characters
    output = LEFT(output, 26);

    RETURN output;
END;
$$ LANGUAGE plpgsql;

----------------------------------------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION drh_stateless_util.get_unique_id()
RETURNS TEXT AS $$
BEGIN
    RETURN (SELECT drh_stateless_util.generate_unique_id());
END;
$$ LANGUAGE plpgsql;

------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION drh_stateless_util.is_valid_ulid(input_ulid TEXT) RETURNS BOOLEAN AS $$
BEGIN
    -- Check the length is exactly 26 characters
    IF length(input_ulid) <> 26 THEN
        RETURN FALSE;
    END IF;

    -- Validate the ULID using a regular expression for Base32 encoding
    IF input_ulid !~ '^[0-9A-HJKMNP-TV-Z]{26}$' THEN
        RETURN FALSE;
    END IF;

    -- If both checks pass, it is a valid ULID
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;



CREATE OR REPLACE FUNCTION drh_stateless_util.map_gender_to_id(p_gender_text text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_gender_type_id TEXT;
BEGIN
    -- Normalize the input gender text
    SELECT gender_type_id INTO v_gender_type_id
    FROM drh_stateful_party.gender_type
    WHERE LOWER(value) IN (
        CASE LOWER(TRIM(p_gender_text))
            WHEN 'm' THEN 'male'
            WHEN 'f' THEN 'female'
            WHEN 'male' THEN 'male'
            WHEN 'female' THEN 'female'
            WHEN 'other' THEN 'other'
            WHEN 'unknown' THEN 'unknown'
            ELSE NULL
        END
    )
    AND deleted_at IS NULL;
    
    RETURN v_gender_type_id;
END;
$function$
;




CREATE OR REPLACE FUNCTION drh_stateless_util.get_race_ethnicity_ids(input_text text)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    race_name TEXT;
    ethnicity_name TEXT;
    part_name TEXT;
    match_count INT := 0;
    race_id TEXT;
    ethnicity_id TEXT;
BEGIN
    -- Normalize input: Remove special characters, trim spaces
    input_text := regexp_replace(input_text, '[^a-zA-Z, /]', '', 'g');
    input_text := trim(both ' ' FROM input_text);

    -- Handle empty input by setting both to 'Unknown'
    IF input_text IS NULL OR input_text = '' THEN
        race_name := 'Unknown';
        ethnicity_name := 'Unknown';
        RAISE NOTICE 'Input is empty or NULL, setting race and ethnicity to "Unknown".';
    ELSE
        -- Split into race and ethnicity
        IF position(',' IN input_text) > 0 THEN
            race_name := trim(both ' ' FROM split_part(input_text, ',', 1));
            ethnicity_name := trim(both ' ' FROM split_part(input_text, ',', 2));
            RAISE NOTICE 'Input text split into race_name: %, ethnicity_name: %', race_name, ethnicity_name;
        ELSE
            race_name := trim(both ' ' FROM input_text);
            ethnicity_name := 'Unknown';
            RAISE NOTICE 'Input text contains only one part, setting ethnicity_name to "Unknown".';
        END IF;
    END IF;

    -- Convert to lowercase for consistent lookup
    race_name := lower(race_name);
    ethnicity_name := lower(ethnicity_name);

    -- Split the race_name on the '/' delimiter if it exists
    IF position('/' IN race_name) > 0 THEN
        -- Extract individual parts and look for each part separately
        RAISE NOTICE 'Race name contains "/", splitting into parts.';
        FOR part_name IN
            SELECT unnest(string_to_array(race_name, '/'))
        LOOP
            RAISE NOTICE 'Checking part: %', part_name;
            -- Check if part matches any race_text or display value
            IF match_count = 0 THEN
                -- Get race_id from race_type_view using part of the name
                SELECT race_type_id INTO race_id
                FROM drh_stateless_master.race_type_view
                WHERE lower(race_text) ILIKE '%' || trim(both ' ' FROM part_name) || '%'
                   OR lower(display) ILIKE '%' || trim(both ' ' FROM part_name) || '%'
                LIMIT 1;
                
                -- If a match is found, exit loop
                IF race_id IS NOT NULL THEN
                    match_count := 1;
                    RAISE NOTICE 'Match found for part "%", race_id: %', part_name, race_id;
                    EXIT;
                ELSE
                    RAISE NOTICE 'No match found for part "%".', part_name;
                END IF;
            END IF;
        END LOOP;
    ELSE
        -- If no '/' is found, proceed as normal
        RAISE NOTICE 'No "/" delimiter found, checking full race name: %', race_name;
        SELECT race_type_id INTO race_id
        FROM drh_stateless_master.race_type_view
        WHERE lower(race_text) ILIKE '%' || race_name || '%'
           OR lower(display) ILIKE '%' || race_name || '%'
        LIMIT 1;
        
        IF race_id IS NULL THEN
            RAISE NOTICE 'No match found for full race_name: %', race_name;
        ELSE
            RAISE NOTICE 'Match found for race_name: %', race_name;
        END IF;
    END IF;

    -- Get ethnicity_id as ULID from ethnicity_type_view using ethnicity_name
    RAISE NOTICE 'Searching for ethnicity_id with ethnicity_name: %', ethnicity_name;
    SELECT ethnicity_type_id INTO ethnicity_id
    FROM drh_stateless_master.ethnicity_type_view
    WHERE lower(display) ILIKE '%' || ethnicity_name || '%'
    LIMIT 1;

    IF ethnicity_id IS NULL THEN
        RAISE NOTICE 'No match found for ethnicity_name: %, setting to "Unknown".', ethnicity_name;
        SELECT ethnicity_type_id INTO ethnicity_id
        FROM drh_stateless_master.ethnicity_type_view
        WHERE lower(display) = 'unknown'
        LIMIT 1;
    ELSE
        RAISE NOTICE 'Match found for ethnicity_name: %, ethnicity_id: %', ethnicity_name, ethnicity_id;
    END IF;

    -- Handle NULL cases by assigning default 'Unknown' ULID if applicable
    IF race_id IS NULL THEN
        RAISE NOTICE 'No race_id found, setting to "Unknown".';
        SELECT race_type_id INTO race_id
        FROM drh_stateless_master.race_type_view
        WHERE lower(race_text) = 'unknown'
        LIMIT 1;
    END IF;

    IF ethnicity_id IS NULL THEN
        RAISE NOTICE 'No ethnicity_id found, setting to "Unknown".';
        SELECT ethnicity_type_id INTO ethnicity_id
        FROM drh_stateless_master.ethnicity_type_view
        WHERE lower(display) = 'unknown'
        LIMIT 1;
    END IF;

    -- Return result as JSONB
    RAISE NOTICE 'Returning race_id: %, ethnicity_id: %', race_id, ethnicity_id;
    RETURN jsonb_build_object(
        'race_id', race_id,
        'ethnicity_id', ethnicity_id
    );
END;
$function$
;

