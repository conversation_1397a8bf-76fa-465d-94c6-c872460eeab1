-----------------------------------------------------------------------------------------------------------------
--------------------------ADD INTERACTION LOG AND MIGRATION LOG--------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
DROP function if exists drh_stateless_db_import_migration.migrate_all_participants(p_db_file_id text);
DROP function if exists drh_stateless_db_import_migration.migrate_all_participants(p_db_file_id text, last_file_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_all_participants(p_db_file_id text, last_file_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    participant_record RECORD;
    p_tenant_id text;
    p_study_id text;
    p_gender_id text;
    p_race_ethnicity_json jsonb;
    p_race_id text;
    p_ethnicity_id text;
    p_participant_migrate_json jsonb;
    p_created_by text;
    successful_participants text[] := '{}';
    failed_participants text[] := '{}';
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_db_import_migration.migrate_all_participants';
    current_query text := pg_catalog.current_query();
    result jsonb;
    participant_count INT;
    i INT;

    p_db_migration_log_id TEXT;
    v_migration_status INTEGER;
    v_migration_start_time TIMESTAMPTZ;
    v_migration_end_time TIMESTAMPTZ;

    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_name text;
    v_file_interaction_params JSONB;
    v_response text;
    v_error_response text;
    v_interaction_status  text;
    v_description text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
        RAISE NOTICE 'Starting participant migration for db_file_id: %', p_db_file_id;
        parameters_lst := jsonb_build_object(
        'p_db_file_id', p_db_file_id,
        'last_file_interaction_id', last_file_interaction_id
        );
        result:= jsonb_build_object('status', 'failure',
                    'message', 'DB participant records migration failure','db_id', p_db_file_id,'successful_participants','[]' , 'failed_participants','[]' );


        

        -- Insert migration log
        INSERT INTO drh_stateful_db_import_migration.db_migration_log (
            db_migration_log_id, 
            db_file_id, 
            migration_status_id,
            migration_start_time
        ) 
        VALUES(
            drh_stateless_util.get_unique_id(),
            p_db_file_id,
            (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'IN_PROGRESS' LIMIT 1),
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        ) 
        RETURNING db_migration_log_id INTO p_db_migration_log_id;

    --BEGIN
        IF p_db_file_id IS NOT NULL THEN
            SELECT crdv.study_id, crdv.tenant_id ,crdv.uploaded_by,crdv.file_name  
            INTO p_study_id, p_tenant_id , p_created_by,v_file_name
            FROM drh_stateless_raw_data.cgm_raw_db_view crdv
            WHERE crdv.db_file_id = p_db_file_id LIMIT 1;
           
            RAISE NOTICE 'p_study_id: %, p_tenant_id: % , p_created_by: %',p_study_id,p_tenant_id,p_created_by;

       	-- Call save_file_interaction_log to get the file interaction ID
	    v_file_interaction_params := jsonb_build_object(
	        'last_file_interaction_id', last_file_interaction_id,
	        'interaction_action_type', 'PARTICIPANT MIGRATION',
	        'interaction_status', 'IN PROGRESS',
	        'description', 'Participant migration started',
	        'db_file_id', p_db_file_id,
	        'file_name', v_file_name,
	        'file_category', 'Database',
	        'created_by', p_created_by
	    );
		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    
        -- Extract file_interaction_id from result
        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;
          
            -- Count the number of records
		    SELECT COUNT(*)
		    INTO participant_count
		    FROM drh_stateful_db_import_migration.participant pa
		    WHERE pa.db_file_id = p_db_file_id;
		   
		    RAISE NOTICE 'participant_count: %',participant_count;
		
		    -- If count > 0, enter the loop
		    IF participant_count > 0 THEN
            
                FOR participant_record in            
                SELECT 
                    db_file_id::TEXT AS db_file_id,
                    tenant_id::TEXT AS tenant_id,
                    study_display_id,
                    participant_display_id,
                    site_id,
                    diagnosis_icd,
                    med_rxnorm,
                    treatment_modality,
                    gender,
                    race_ethnicity,
                    age,
                    CASE 
                        WHEN bmi = 'NA' OR bmi IS NULL OR bmi = '' THEN NULL  -- Convert 'NA' and empty strings to NULL
                        ELSE bmi::double precision 
                    END AS bmi,
                    CASE 
                        WHEN baseline_hba1c = 'NA' OR baseline_hba1c IS NULL OR baseline_hba1c = '' THEN NULL  -- Convert 'NA' and empty strings to NULL
                        ELSE baseline_hba1c::double precision 
                    END AS baseline_hba1c,
                    diabetes_type,
                    study_arm                      
                    FROM drh_stateful_db_import_migration.participant pa
                    WHERE pa.db_file_id = p_db_file_id
                loop
                
                    RAISE NOTICE 'participant_record: %',participant_record.participant_display_id::text;
                    BEGIN
                        IF NOT EXISTS (
                            SELECT 1
                            FROM drh_stateful_db_import_migration.participant_migration_status pm
                            WHERE pm.db_file_id = p_db_file_id
                            AND pm.participant_display_id = participant_record.participant_display_id
                        ) THEN
                            INSERT INTO drh_stateful_db_import_migration.participant_migration_status (
                                db_file_id, study_id, participant_display_id, participant_id,
                                migration_status, migration_start_time, migration_end_time, last_updated_at
                            )
                            VALUES (
                                p_db_file_id, p_study_id, participant_record.participant_display_id, NULL,
                                (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'PENDING' LIMIT 1),
                                CURRENT_TIMESTAMP AT TIME ZONE 'UTC', NULL, CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                            );
                            
                            IF participant_record.gender IS NOT NULL AND participant_record.gender != '' THEN
                                p_gender_id := drh_stateless_util.map_gender_to_id(participant_record.gender);
                            ELSE
                                p_gender_id := NULL;
                            END IF;
                            
                            IF participant_record.race_ethnicity IS NOT NULL AND participant_record.race_ethnicity != '' THEN
                                p_race_ethnicity_json := drh_stateless_util.get_race_ethnicity_ids(participant_record.race_ethnicity);
                                p_race_id := p_race_ethnicity_json->>'race_id';
                                p_ethnicity_id := p_race_ethnicity_json->>'ethnicity_id';
                            ELSE
                                -- Assign the "Unknown" race ID
                                SELECT race_type_id INTO p_race_id
                                FROM drh_stateless_master.race_type_view
                                WHERE lower(race_text) = 'unknown'
                                LIMIT 1;
                            
                                -- Assign the "Unknown" ethnicity ID
                                SELECT ethnicity_type_id INTO p_ethnicity_id
                                FROM drh_stateless_master.ethnicity_type_view
                                WHERE lower(display) = 'unknown'
                                LIMIT 1;
                            
                            END IF;

                        
                            RAISE NOTICE 'gender_id: %, race_id: % , etnicityid: %',p_gender_id,p_race_id,p_ethnicity_id;
                            
                            p_participant_migrate_json := drh_stateless_db_import_migration.migrate_individual_participant_data(
                                p_study_id, p_tenant_id, participant_record.participant_display_id,
                                p_gender_id, participant_record.age::int4, p_created_by,
                                participant_record.diagnosis_icd, participant_record.med_rxnorm,
                                participant_record.treatment_modality, p_race_id, p_ethnicity_id,
                                participant_record.bmi::double precision, participant_record.baseline_hba1c::double precision,
                                participant_record.diabetes_type, participant_record.study_arm
                            );
                            
                            IF p_participant_migrate_json IS NOT NULL AND p_participant_migrate_json->>'status' = 'success' THEN
                                UPDATE drh_stateful_db_import_migration.participant_migration_status ps
                                SET migration_status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1),
                                    migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                    participant_id = p_participant_migrate_json->>'participant_id',
                                    last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                                WHERE ps.db_file_id = p_db_file_id
                                AND ps.participant_display_id = participant_record.participant_display_id;
                                
                                successful_participants := array_append(successful_participants, participant_record.participant_display_id);

                            ELSE
                                UPDATE drh_stateful_db_import_migration.participant_migration_status ms
                                SET migration_status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
                                    migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                    last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                                WHERE ms.db_file_id = p_db_file_id
                                AND ms.participant_display_id = participant_record.participant_display_id;
                                
                                failed_participants := array_append(failed_participants, participant_record.participant_display_id);
                                
                            END IF;

                            -- Update migration status on success
                            v_migration_status := (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1); -- Set success status    
                            -- Update migration log
                            UPDATE drh_stateful_db_import_migration.db_migration_log 
                            SET 
                                migration_status_id = v_migration_status,
                                participant_success_records = successful_participants, 
                                participant_failure_records = failed_participants,
                                migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC' 
                            WHERE db_migration_log_id = p_db_migration_log_id;
                        END IF;
                    END;
                END LOOP;
               
               -- Determine interaction status and description based on failures
			IF array_length(failed_participants, 1) IS NOT NULL AND array_length(failed_participants, 1) > 0 THEN
			    v_interaction_status := 'PARTIAL SUCCESS';
			    v_description := format('Participant migration completed with %s failed records.', array_length(failed_participants, 1));
			    v_response := successful_participants;
			    v_error_response := failed_participants;
			ELSE
			    v_interaction_status := 'SUCCESS';
			    v_description := 'Participant migration completed';
			    v_response := successful_participants;
			    v_error_response := NULL;
			END IF;


            INSERT INTO drh_stateful_db_import_migration.participant_migration_history (
					  log_id,
					  db_file_id,
					  study_display_id,
					  participant_json,
					  captured_by,
					  captured_at,
					  study_id
					)
					SELECT
					  drh_stateless_util.get_unique_id(),
					    MIN(pa.db_file_id) AS db_file_id,
                        MIN(pa.study_display_id) AS study_display_id,
					  jsonb_agg(
					    jsonb_build_object(
					      'participant_display_id', pa.participant_display_id,
					      'site_id', pa.site_id,
					      'diagnosis_icd', pa.diagnosis_icd,
					      'med_rxnorm', pa.med_rxnorm,
					      'treatment_modality', pa.treatment_modality,
					      'gender', pa.gender,
					      'race_ethnicity', pa.race_ethnicity,
					      'age', pa.age,
					      'bmi', CASE 
					               WHEN pa.bmi = 'NA' OR pa.bmi IS NULL OR pa.bmi = '' THEN NULL
					               ELSE pa.bmi::double precision
					             END,
					      'baseline_hba1c', CASE 
					                          WHEN pa.baseline_hba1c = 'NA' OR pa.baseline_hba1c IS NULL OR pa.baseline_hba1c = '' THEN NULL
					                          ELSE pa.baseline_hba1c::double precision
					                        END,
					      'diabetes_type', pa.diabetes_type,
					      'study_arm', pa.study_arm
					    )
					  ) AS participant_json,
					  p_created_by,
					  CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
					  MIN(rs.study_id) AS study_id
					FROM drh_stateful_db_import_migration.participant pa
					LEFT JOIN drh_stateful_research_study.research_study rs
					  ON rs.study_display_id = pa.study_display_id
					WHERE pa.db_file_id = p_db_file_id					
					LIMIT 1;


               -- Call save_file_interaction_log to get the file interaction ID
			    v_file_interaction_params := jsonb_build_object(
			        'last_file_interaction_id', inprogress_file_interaction_id,
			        'interaction_action_type', 'PARTICIPANT MIGRATION',
			        'interaction_status', v_interaction_status,
			        'description', v_description,
			        'db_file_id', p_db_file_id,
			       'file_name', v_file_name,
			    'file_category', 'Database',
			    'created_by', p_created_by,
			    'response', v_response,
			    'error_response', v_error_response
			    );
            	file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
                -- Extract file_interaction_id from result
                IF file_interaction_result ->> 'status' = 'success' THEN
                    completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';      
                ELSE
                    RETURN jsonb_build_object(
                        'status', 'failure',
                        'message', 'Failed to insert file interaction',
                        'error_details', file_interaction_result -> 'error_details'
                    );
                END IF;
                
                RAISE NOTICE 'Migration completed. Successful: %, Failed: %', array_length(successful_participants, 1), array_length(failed_participants, 1);
                result:= jsonb_build_object('status', 'success',
                    'message', 'DB participant records migrated successfully',
                    'db_id', p_db_file_id,
                    'successful_participants', successful_participants, 
                    'failed_participants', failed_participants,
                    'file_interaction_id', completed_file_interaction_id
                    );
                
            ELSE
                RAISE NOTICE 'No records found for db_file_id: %', p_db_file_id;
                result:= jsonb_build_object('status', 'failure','message', 'DB participants not available','db_id', p_db_file_id,'successful_participants','[]' , 'failed_participants','[]' );
            END IF;

        END IF;

        
        -- Return success result
        RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN	     
	   -- Call save_file_interaction_log to get the file interaction ID
	    v_file_interaction_params := jsonb_build_object(
	        'last_file_interaction_id', inprogress_file_interaction_id,
	        'interaction_action_type', 'PARTICIPANT MIGRATION',
	        'interaction_status', 'FAILED',
	        'description', 'Participant migration failed',
	        'db_file_id', p_db_file_id,
	        'file_name', v_file_name,
	        'file_category', 'Database',
	        'created_by', NULL
	    );
    	file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   END IF;

    -- Update db_migration_log with ERROR status
    IF p_db_migration_log_id IS NOT NULL THEN
        UPDATE drh_stateful_db_import_migration.db_migration_log
        SET
            migration_status_id = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
            participant_success_records = successful_participants,
            participant_failure_records = failed_participants,
            migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE db_migration_log_id = p_db_migration_log_id;                         
    END IF;                         

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  -- can be NULL
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participants migration', 'error_details', error_details_json);
    RETURN result;
END;
$function$
;
-----------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_individual_participant_data(
    text, text, text, text, integer, text, text, text, text, text, text,
    double precision, double precision, text, text
);



CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_individual_participant_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_gender_id text, p_age integer, p_created_by text DEFAULT NULL::text, p_diagnosis_icd text DEFAULT NULL::text, p_med_rxnorm text DEFAULT NULL::text, p_treatment_modality text DEFAULT NULL::text, p_race_id text DEFAULT NULL::text, p_ethnicity_id text DEFAULT NULL::text, p_bmi double precision DEFAULT NULL::double precision, p_baseline_hba1c double precision DEFAULT NULL::double precision, p_diabetes_type text DEFAULT NULL::text, p_study_arm text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE    
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    v_research_subject_id TEXT;
    v_patient_id TEXT;
    bmi_loinc_code TEXT :='39156-5';--reference from drh_stateful_master.loinc_codes
    hba1c_loinc_code TEXT :='4548-4';--reference from drh_stateful_master.loinc_codes  
    next_participant_number INTEGER;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_individual_participant_data'; 
    current_query TEXT := pg_catalog.current_query();
    result jsonb;
    v_study_display_id text;
    v_org_party_id text;
    v_rec_status_id int;
    v_gender text;
    v_race text;
    v_ethnicity text;
    v_gender_id text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Start the transaction
    BEGIN
        -- Initialize result to failure by default in case of an error
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant creation');

        parameters_lst := jsonb_build_object(
            'p_study_id', p_study_id,
            'p_tenant_id', p_tenant_id,
            'p_participant_display_id', p_participant_display_id,
            'p_gender_id', p_gender_id,
            'p_age', p_age,
            'p_created_by', p_created_by,
            'p_diagnosis_icd', p_diagnosis_icd,
            'p_med_rxnorm', p_med_rxnorm,
            'p_treatment_modality', p_treatment_modality,
            'p_race_id', p_race_id,
            'p_ethnicity_id', p_ethnicity_id,
            'p_bmi', p_bmi,
            'p_baseline_hba1c', p_baseline_hba1c,
            'p_diabetes_type', p_diabetes_type,
            'p_study_arm', p_study_arm
        );

       
        SELECT 
            rs.organization_party_id,            
            rsv.study_display_id
        INTO v_org_party_id, v_study_display_id
        FROM drh_stateless_research_study.organization_party_view rs
        JOIN drh_stateless_research_study.research_study_view rsv
            ON rsv.study_id = p_study_id
        WHERE rs.organization_id = p_tenant_id
        LIMIT 1;
       
        RAISE NOTICE 'Found v_org_party_id: %, v_study_display_id: %', v_org_party_id, v_study_display_id;

       
        -- Validate mandatory fields
	    IF p_participant_display_id IS NULL OR TRIM(p_participant_display_id) = '' THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Participant display ID cannot be NULL or empty');
	        RETURN result;
	    END IF;
	
	    IF p_age IS NULL OR p_age < 0 THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Age must be a valid integer and cannot be NULL or negative');
	        RETURN result;
	    END IF;
	
	    IF p_gender_id IS NULL OR TRIM(p_gender_id) = '' THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Gender cannot be NULL or empty');
	        RETURN result;
	    END IF;
	
	           
       -- Check if participant_display_id is provided
       IF p_participant_display_id IS NOT NULL then
      
        IF p_age IS NULL OR p_gender_id IS NULL or p_created_by IS NULL or v_org_party_id IS NULL or p_study_id is null THEN         
           result := jsonb_build_object('status', 'failure', 'message', 'One of the mandatory fields are NULL');
           RETURN result;
        END IF;
        
       RAISE NOTICE 'Inserting participant with ID: %, Gender: %, Age: %', p_participant_display_id, p_gender_id, p_age;

       SELECT rs.value into v_rec_status_id FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1;

        -- Insert into patient table
        INSERT INTO drh_stateful_research_study.patient(
            id, identifier_system, identifier_value, name_use, name_family, 
            name_given, gender_type_id, birth_date, age, address_use, address_line1, 
            address_city, address_state, address_postal_code, address_country, 
            contact_relationship, contact_name_family, contact_name_given, contact_telecom_system, 
            contact_telecom_value, contact_telecom_use, tenant_id, org_party_id, rec_status_id, 
            created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES( 
            drh_stateless_util.get_unique_id(),  -- Generated patient ID
            NULL, -- Empty identifier_system
            NULL, -- Empty identifier_value
            NULL, -- Empty name_use
            NULL, -- Empty name_family
            NULL, -- Empty name_given
            p_gender_id,
            NULL, -- Empty birth_date
            COALESCE(p_age, 0),  -- Default age if empty
            NULL, -- Empty address_use
            NULL, -- Empty address_line1
            NULL, -- Empty address_city
            NULL, -- Empty address_state
            NULL, -- Empty address_postal_code
            NULL, -- Empty address_country
            NULL, -- Empty contact_relationship
            NULL, -- Empty contact_name_family
            NULL, -- Empty contact_name_given
            NULL, -- Empty contact_telecom_system
            NULL, -- Empty contact_telecom_value
            NULL, -- Empty contact_telecom_use
            p_tenant_id, -- Tenant ID
            v_org_party_id,  -- org_party_id
            v_rec_status_id,  -- rec_status_id
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
            p_created_by, -- created_by
            NULL, NULL, NULL, NULL  -- Null fields for updated and deleted
        ) RETURNING id INTO v_patient_id;
       
       RAISE NOTICE 'Patient ID inserted: %', v_patient_id;

        -- Insert into research_subject table
        INSERT INTO drh_stateful_research_study.research_subject (
            rsubject_id, participant_identifier, study_reference, individual_reference, 
            status_id, "group", diabetes_type, diagnosis_icd, med_rxnorm, treatment_modality, 
            race_type_id, ethnicity_type_id, tenant_id, rec_status_id, created_at, created_by, 
            updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),  -- rsubject_id
            p_participant_display_id,            -- participant_identifier
            p_study_id,                          -- study_reference
            v_patient_id,                        -- individual_reference
            (SELECT code FROM drh_stateful_master.research_subject_status_master WHERE display_name = 'on-study' LIMIT 1),
            p_study_arm,                         -- study group (e.g. arm)
            p_diabetes_type,                     -- diabetes type
            p_diagnosis_icd,                     -- diagnosis ICD
            p_med_rxnorm,                        -- medication code
            p_treatment_modality,                -- treatment modality
            p_race_id,  -- race type
            p_ethnicity_id, -- ethnicity type
            p_tenant_id,                         -- Tenant ID
            v_rec_status_id,                     -- rec_status_id
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- created_at
            p_created_by,                        -- created_by
            NULL, NULL, NULL, NULL               -- Updated and deleted info
        ) RETURNING rsubject_id INTO v_research_subject_id;
        
       RAISE NOTICE 'Research Subject ID inserted: %', v_research_subject_id;
      
        -- Insert BMI observation if p_bmi is provided
        IF p_bmi IS NOT NULL THEN
            INSERT INTO drh_stateful_research_study.subject_observation (
                id, research_subject_id, code, category, value, unit, effective_datetime, 
                tenant_id, rec_status_id, created_at, created_by
            ) 
            VALUES (
                drh_stateless_util.generate_unique_id(),
                v_research_subject_id,  -- research_subject_id
                bmi_loinc_code,         -- LOINC code for BMI
                'Clinical',             -- Category
                p_bmi,                  -- BMI value
                'kg/m^2',               -- Unit
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                p_tenant_id,            -- Tenant ID
                v_rec_status_id,        -- rec_status_id
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
                p_created_by            -- created_by
            );
           
           RAISE NOTICE 'inserted bmi: %', p_bmi;
        END IF;

       
      
        -- Insert Baseline HbA1c observation if p_baseline_hba1c is provided
        IF p_baseline_hba1c IS NOT NULL THEN
            INSERT INTO drh_stateful_research_study.subject_observation (
                id, research_subject_id, code, category, value, unit, effective_datetime, 
                tenant_id, rec_status_id, created_at, created_by
            )
            VALUES (
                drh_stateless_util.generate_unique_id(),
                v_research_subject_id,  -- research_subject_id
                hba1c_loinc_code,       -- LOINC code for HbA1c
                'Clinical',             -- Category
                p_baseline_hba1c,       -- HbA1c value
                '%',                    -- Unit for HbA1c
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                p_tenant_id,            -- Tenant ID
                v_rec_status_id,        -- rec_status_id
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
                p_created_by            -- created_by
            );
           
            RAISE NOTICE 'inserted baseline_hba1c: %', p_baseline_hba1c;
        END IF;

        -- Insert new records into participant_base
        PERFORM drh_stateless_raw_observation.save_participant_base(v_research_subject_id);
              
       
       -- Set the result to success only after all insertions are completed
           result := jsonb_build_object(
               'status', 'success',
               'message', 'Participant created successfully',
               'participant_display_id', p_participant_display_id,
               'participant_id', v_research_subject_id
              
           );
          
        end if;

        -- Return success result
        RETURN result;

    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Log the error details
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst  
            );

            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Prepare error JSON
            error_details_json := jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            );

            -- Return failure with the error details
            result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant data migration', 'error_details', error_details_json);
            RETURN result;
    END;
END;
$function$
;

-------------------------------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_cgm_observation(character varying, character varying);



CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_cgm_observation(p_cgm_raw_data_id character varying, p_created_by character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_map_field_of_cgm_date TEXT;
    v_map_field_of_cgm_value TEXT;    
    v_study_id TEXT;
    v_participant_sid TEXT;
    v_tenant_id TEXT;
    result JSONB;
    observation JSONB;
    v_cgm_date TEXT;
    v_cgm_value TEXT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_cgm_observation';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;


BEGIN
    -- Initialize result as success
    result := jsonb_build_object('status', 'success', 'message', 'CGM observation saved successfully');
    parameters_lst := jsonb_build_object(
        'p_cgm_raw_data_id', p_cgm_raw_data_id,
        'p_created_by', p_created_by
    );

    SELECT 
        file_meta_data->>'map_field_of_cgm_date', 
        file_meta_data->>'map_field_of_cgm_value',
        study_id,
        tenant_id,
        participant_sid
    INTO v_map_field_of_cgm_date, v_map_field_of_cgm_value, v_study_id, v_tenant_id, v_participant_sid
    FROM drh_stateful_raw_data.raw_cgm_extract_data
    WHERE cgm_raw_data_id = p_cgm_raw_data_id;

    -- Ensure mapping fields are not NULL or empty
    IF v_map_field_of_cgm_date IS NULL OR v_map_field_of_cgm_date = '' OR 
       v_map_field_of_cgm_value IS NULL OR v_map_field_of_cgm_value = '' THEN
        INSERT INTO drh_stateful_activity_audit.exception_log (
            function_name,
            error_code,
            error_message,
            error_detail,
            error_hint,
            error_context,
            query,
            parameters,
            occurred_at,
            resolved,
            resolved_at,
            resolver_comments
        )
        VALUES (
            function_name,
            NULL,
            'Mapping fields for CGM date or value are NULL',
            NULL,
            NULL,
            NULL,
            current_query,
            jsonb_build_object(
                'p_cgm_raw_data_id', p_cgm_raw_data_id,
                'p_created_by', p_created_by
            ),
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            'No',
            NULL,
            NULL
        );
        RETURN jsonb_build_object('status', 'failure', 'message', 'Mapping fields for CGM date or value are NULL');
    END IF;  
   
   

    -- Extract the observation data from JSON before entering the loop
    FOR observation IN
        SELECT value
        FROM jsonb_array_elements(
            (SELECT cgm_data 
             FROM drh_stateful_raw_data.raw_cgm_extract_data 
             WHERE cgm_raw_data_id = p_cgm_raw_data_id)
        )
    LOOP
        -- Extract values from the observation inside the loop
        v_cgm_date := observation->>v_map_field_of_cgm_date;
        v_cgm_value := observation->>v_map_field_of_cgm_value;

        -- Skip processing if date_time or cgm_value is NULL, empty, "NA", or non-numeric
			IF v_cgm_date IS NULL OR v_cgm_date = '' OR 
			   v_cgm_value IS NULL OR v_cgm_value = '' OR 
			   v_cgm_value ILIKE 'NA' OR 
			   NOT v_cgm_value ~ '^[0-9]+(\.[0-9]+)?$' THEN
			    CONTINUE;
			END IF;

        -- Create dynamic partition if not exists
        -- PERFORM drh_stateless_raw_observation.create_partitions_if_not_exists(v_tenant_id, v_study_id, v_participant_sid); 

        -- Insert observation into the table
        INSERT INTO drh_stateful_db_import_migration.cgm_observation_temp (
            id,
            study_id,
            research_subject_id,
            period,
            date_time,
            cgm_value,
            unit,
            tenant_id,
            rec_status_id,
            created_at,
            created_by,
            raw_cgm_extract_data_id
        ) VALUES (
            drh_stateless_util.get_unique_id()::TEXT,
            v_study_id,
            v_participant_sid, 
            'Baseline', 
            TO_TIMESTAMP(v_cgm_date, 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
            (v_cgm_value)::NUMERIC(8,3),
            'mg/dL', 
            v_tenant_id,
            (SELECT id FROM drh_stateful_party.record_status WHERE code = 'ACTIVE' LIMIT 1), 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_created_by,
            p_cgm_raw_data_id
        );
       
       END LOOP;
    
    -- Call the save_cgm_metrics function
    -- PERFORM drh_stateless_raw_observation.save_cgm_metrics(v_participant_sid);

    -- Set the final result message
    result := jsonb_build_object('status', 'success', 'message', 'CGM observation saved successfully','p_cgm_raw_data_id', p_cgm_raw_data_id,
            'p_created_by', p_created_by);

    -- Return success
    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;


    -- Log error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Return failure response
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during CGM observation save',
        'error_details', error_details_json
    );
END;
$function$
;

------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_individual_cgm_tracing(
    TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, JSONB, JSONB
);


CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_individual_cgm_tracing(
    p_database_id TEXT, 
    p_study_id TEXT, 
    p_tenant_id TEXT, 
    p_participant_display_id TEXT, 
    p_participant_id TEXT, 
    p_created_by TEXT, 
    file_meta_data JSONB, 
    cgm_data JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE    
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;    
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_individual_cgm_tracing'; 
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_study_display_id TEXT;
    v_org_party_id TEXT;
    v_rec_status_id INT;    
    p_cgm_raw_file_id TEXT;
    p_cgm_raw_data_id TEXT;
    p_cgm_obs_migrate_json JSONB;
    p_file_name TEXT;
    v_device_id TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Start the transaction
    BEGIN
        -- Validate input parameters
        IF p_database_id IS NULL OR p_study_id IS NULL OR p_tenant_id IS NULL 
           OR p_participant_display_id IS NULL OR p_participant_id IS NULL 
           OR p_created_by IS NULL OR file_meta_data IS NULL OR cgm_data IS NULL THEN
            err_message := 'One or more required parameters are NULL';
            INSERT INTO drh_stateful_activity_audit.exception_log (
                function_name,
                error_code,
                error_message,
                error_detail,
                error_hint,
                error_context,
                query,
                parameters,
                occurred_at,
                resolved,
                resolved_at,
                resolver_comments
            )
            VALUES (
                function_name,
                NULL,
                err_message,
                NULL,
                NULL,
                NULL,
                current_query,
                jsonb_build_object(
                    'file_meta_data', file_meta_data,
                    'p_created_by', p_created_by
                ),
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                'No',
                NULL,
                NULL
            );
            result := jsonb_build_object(
                'status', 'failure',
                'message', err_message,
                'file_meta_data', file_meta_data,
                'p_created_by', p_created_by
            );
        END IF;

        -- Initialize result to failure by default in case of an error
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during individual cgm tracing migration');
        
        -- Debugging: Print input parameters
        RAISE NOTICE 'Input Parameters: p_database_id=%, p_study_id=%, p_tenant_id=%, p_participant_display_id=%, p_participant_id=%, p_created_by=% file_meta_data=%', 
            p_database_id, p_study_id, p_tenant_id, p_participant_display_id, p_participant_id, p_created_by, file_meta_data;
        
        p_file_name := file_meta_data->>'file_name';
       
       -- Get active record status ID
        RAISE NOTICE 'Fetching active record status ID';
        SELECT rs.value INTO v_rec_status_id 
        FROM drh_stateful_party.record_status rs 
        WHERE rs.code = 'ACTIVE' 
        LIMIT 1;
        
        -- Insert into cgm_raw_upload_data
        INSERT INTO drh_stateful_raw_data.cgm_raw_upload_data (
            cgm_raw_file_id,
            file_name, 
            file_url, 
            zip_file_id, 
            cgm_raw_data_json, 
            upload_timestamp, 
            uploaded_by, 
            file_size, 
            is_processed, 
            processed_at,
            status, 
            file_metadata, 
            file_type, 
            study_id, 
            tenant_id, 
            rec_status_id, 
            created_at, 
            created_by, 
            updated_at,
            updated_by, 
            deleted_at, 
            deleted_by, 
            cgm_raw_data_csv, 
            cgm_raw_data_excel, 
            cgm_raw_data_xml, 
            cgm_raw_data_text, 
            database_id
        ) 
        VALUES (
            drh_stateless_util.get_unique_id(), 
            p_file_name, 
            NULL, 
            NULL, 
            NULL, 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
            p_created_by,
            NULL, 
            false,
            NULL, 
            (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'PENDING' LIMIT 1), 
            file_meta_data, 
            file_meta_data->>'file_format',
            p_study_id, 
            p_tenant_id, 
            v_rec_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
            p_created_by, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL,null,
            p_database_id
        ) 
        RETURNING cgm_raw_file_id INTO p_cgm_raw_file_id;

        -- Debugging: Print the value of cgm_raw_file_id
        RAISE NOTICE 'Inserted cgm_raw_file_id: %', p_cgm_raw_file_id;

        -- Fetch device_id from drh_stateful_research_study.device
        IF file_meta_data->>'device_name' IS NOT NULL THEN           

            SELECT id INTO v_device_id 
            FROM drh_stateful_research_study.device 
            WHERE LOWER(device_name) = LOWER(file_meta_data->>'devicename')
            LIMIT 1;

            -- RAISE NOTICE 'v_device_id: %', v_device_id;

            IF v_device_id IS NOT NULL THEN
                file_meta_data := jsonb_set(file_meta_data, '{device_id}', to_jsonb(v_device_id));
            END IF;

             -- RAISE NOTICE 'file_meta_data: %', file_meta_data;

        END IF;

        -- Insert into raw_cgm_extract_data
        INSERT INTO drh_stateful_raw_data.raw_cgm_extract_data (
            cgm_raw_data_id,
            raw_file_id,
            study_id,
            participant_sid, 
            cgm_raw_data_json,
            file_url, 
            file_meta_data, 
            cgm_data,
            tenant_id, 
            rec_status_id, 
            created_at, 
            created_by, 
            updated_at, 
            updated_by, 
            deleted_at, 
            deleted_by, 
            cgm_raw_data_csv, 
            cgm_raw_data_excel, 
            cgm_raw_data_text, 
            cgm_raw_data_xml
        ) 
        VALUES (
            drh_stateless_util.get_unique_id(),
            p_cgm_raw_file_id,
            p_study_id, 
            p_participant_id, 
            NULL, 
            NULL, 
            file_meta_data, 
            cgm_data, 
            p_tenant_id, 
            v_rec_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
            p_created_by, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL
        ) 
        RETURNING cgm_raw_data_id INTO p_cgm_raw_data_id;

        -- Debugging: Print the value of cgm_raw_data_id
        RAISE NOTICE 'Inserted cgm_raw_data_id: %', p_cgm_raw_data_id;

        -- Migrate CGM observations
        p_cgm_obs_migrate_json := drh_stateless_db_import_migration.migrate_cgm_observation(
            p_cgm_raw_data_id, 
            p_created_by
        );

        -- Debugging: Print migration status of CGM observations
        RAISE NOTICE 'CGM Observation Migration Result: %', p_cgm_obs_migrate_json;

        IF p_cgm_obs_migrate_json IS NOT NULL AND p_cgm_obs_migrate_json->>'status' = 'success' THEN
            UPDATE drh_stateful_raw_data.cgm_raw_upload_data 
            SET is_processed = true,
                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1)
            WHERE cgm_raw_file_id = p_cgm_raw_file_id;
            
            -- Debugging: Print success message
            RAISE NOTICE 'CGM data migrated successfully for cgm_raw_file_id: %', p_cgm_raw_file_id;
           
            result := jsonb_build_object(
                'status', 'success',
                'message', 'CGM record migrated successfully',
                'cgm_raw_file_id', p_cgm_raw_file_id,
                'cgm_raw_data_id', p_cgm_raw_data_id,
                'participant_id', p_participant_id
            );
        ELSE
            UPDATE drh_stateful_raw_data.cgm_raw_upload_data 
            SET is_processed = true,
                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            WHERE cgm_raw_file_id = p_cgm_raw_file_id;
            
            -- Debugging: Print error message
            RAISE NOTICE 'Error occurred during migration for cgm_raw_file_id: %', p_cgm_raw_file_id;
           
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Error occurred during migration',
                'cgm_raw_file_id', p_cgm_raw_file_id,
                'cgm_raw_data_id', p_cgm_raw_data_id,
                'participant_id', p_participant_id
            );
        END IF;

        -- Return success result
        RETURN result;

    EXCEPTION 
        WHEN OTHERS THEN
            -- Capture error details
            GET STACKED DIAGNOSTICS 
                err_context = PG_EXCEPTION_CONTEXT,
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT;


            parameters_lst := jsonb_build_object(
                    'p_database_id', p_database_id,
                    'p_study_id', p_study_id,
                    'p_tenant_id', p_tenant_id,
                    'p_participant_display_id', p_participant_display_id,
                    'p_participant_id', p_participant_id,
                    'p_created_by', p_created_by,
                    'file_meta_data', file_meta_data,
                    'cgm_data', cgm_data
            );


            -- Log the error details
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst  
            );

            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Return failure result
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Error during migration process',
                'error_details', jsonb_build_object(
                    'error_code', err_state,
                    'error_message', err_message,
                    'error_detail', err_detail,
                    'error_hint', err_hint
                )
            );
    END;
END;
$function$;

--------------------------------------------------------------------------------------------------------------------------------


DROP function if exists drh_stateless_db_import_migration.migrate_all_cgm_data(p_db_file_id text);
DROP function if exists drh_stateless_db_import_migration.migrate_all_cgm_data(p_db_file_id text, file_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_all_cgm_data(p_db_file_id text, file_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    cgm_record record;
    p_study_id text;
    p_tenant_id text;
    p_created_by text;
    p_file_name text;
    v_rec_status_id int;
    p_participant_id text;
    p_cgm_migrate_id text;
    p_cgm_migrate_json jsonb;
    successful_records text[] := '{}';
    failed_records text[] := '{}';
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_db_import_migration.migrate_all_cgm_data'; 
    current_query text := pg_catalog.current_query();
	file_interaction_result JSONB;
	inprogress_file_interaction_id TEXT;
	completed_file_interaction_id TEXT;
    result jsonb;
   	v_file_interaction_params JSONB;
    v_response text;
    v_error_response text;
    v_interaction_status  text;
    v_description text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting CGM data migration for db_file_id: %', p_db_file_id;
   
    result:= jsonb_build_object('status', 'failure',
                'message', 'DB CGM records migration failure','successfulrecords', '[]', 'failedrecords', '[]','db_id', p_db_file_id);

    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'file_interaction_id', file_interaction_id
    );


    IF p_db_file_id IS NOT NULL THEN
        -- Fetch study and tenant details
        RAISE NOTICE 'Fetching study and tenant details for db_file_id: %', p_db_file_id;
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;

        -- Get active record status ID
        RAISE NOTICE 'Fetching active record status ID';
        SELECT rs.value INTO v_rec_status_id 
        FROM drh_stateful_party.record_status rs 
        WHERE rs.code = 'ACTIVE' 
        LIMIT 1;

	   	-- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', file_interaction_id,
            'interaction_action_type', 'CGM MIGRATION',
            'interaction_status', 'IN PROGRESS',
            'description', 'CGM migration started',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
		
		-- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
			UPDATE drh_stateful_raw_data.cgm_raw_db set file_interaction_id = inprogress_file_interaction_id where db_file_id=p_db_file_id;
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;

        -- Iterate over CGM records for migration
        RAISE NOTICE 'Iterating over CGM records for migration';
        FOR cgm_record IN
            SELECT 
		    file_meta_id,
		    db_file_id,
		    participant_display_id,
		    file_meta_data,
		    cgm_data
            FROM drh_stateful_db_import_migration.file_meta_ingest_data fmid 
            WHERE fmid.db_file_id = p_db_file_id
        LOOP
            BEGIN
                RAISE NOTICE 'Processing participant with display ID: %', cgm_record.participant_display_id;               
               
                
                -- Check if participant exists
                IF EXISTS (
                    SELECT 1 
                    FROM drh_stateless_research_study.participant_data_view pdv  
                    WHERE pdv.participant_display_id = cgm_record.participant_display_id 
                    LIMIT 1
                ) THEN
                    BEGIN
                        -- Get participant ID
                        SELECT pdv.participant_id 
                        INTO p_participant_id 
                        FROM drh_stateless_research_study.participant_data_view pdv  
                        WHERE pdv.participant_display_id = cgm_record.participant_display_id 
                        LIMIT 1;

                        RAISE NOTICE 'Participant ID: %', p_participant_id;

                        -- Check if migration status already exists
                        IF NOT EXISTS (
                            SELECT 1
                            FROM drh_stateful_db_import_migration.cgm_data_migration_status cdms 
                            WHERE cdms.db_file_id = p_db_file_id
                            AND cdms.file_meta_id = cgm_record.file_meta_id
                        ) THEN
                            BEGIN
                                RAISE NOTICE 'Inserting migration status for participant display ID: %', cgm_record.participant_display_id;

                                -- Insert migration status
                                INSERT INTO drh_stateful_db_import_migration.cgm_data_migration_status (
                                    cgm_migrate_id, 
                                    cgm_raw_data_id, 
                                    db_file_id, 
                                    study_id, 
                                    participant_id,
                                    cgm_migration_status, 
                                    migration_start_time,
                                    migration_end_time, 
                                    last_updated_at, 
                                    participant_display_id,
                                    file_meta_id
                                ) 
                                VALUES (
                                    drh_stateless_util.get_unique_id(), 
                                    NULL, 
                                    p_db_file_id, 
                                    p_study_id, 
                                    p_participant_id,
                                    (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'PENDING' LIMIT 1),
                                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
                                    NULL, 
                                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                    cgm_record.participant_display_id,
                                    cgm_record.file_meta_id
                                ) 
                                RETURNING cgm_migrate_id INTO p_cgm_migrate_id;

                                RAISE NOTICE 'Migration status inserted for cgm_migrate_id: %', p_cgm_migrate_id;

                                -- Call individual migration function
                                RAISE NOTICE 'Calling individual migration function for participant: %', cgm_record.participant_display_id;
                                p_cgm_migrate_json := drh_stateless_db_import_migration.migrate_individual_cgm_tracing(
                                    p_db_file_id, 
                                    p_study_id, 
                                    p_tenant_id, 
                                    cgm_record.participant_display_id, 
                                    p_participant_id, 
                                    p_created_by, 
                                    cgm_record.file_meta_data::jsonb,  -- Cast to jsonb if it's text
                                    cgm_record.cgm_data::jsonb         -- Cast to jsonb if it's text
                                );
                                
                                -- Handle success or failure
                                IF p_cgm_migrate_json IS NOT NULL AND p_cgm_migrate_json->>'status' = 'success' THEN
                                    BEGIN
                                        RAISE NOTICE 'CGM migration success for file meta record: %', cgm_record.file_meta_id;

                                        UPDATE drh_stateful_db_import_migration.cgm_data_migration_status ps
                                        SET cgm_migration_status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1),
                                            migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                            cgm_raw_data_id = p_cgm_migrate_json->>'cgm_raw_data_id',
                                            last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                                        WHERE ps.db_file_id = p_db_file_id
                                        AND ps.file_meta_id = cgm_record.file_meta_id;

                                        successful_records := array_append(successful_records, cgm_record.file_meta_id);
                                    END;
                                ELSE
                                    BEGIN
                                        RAISE NOTICE 'CGM migration failed for file meta record: %', cgm_record.file_meta_id;

                                        UPDATE drh_stateful_db_import_migration.cgm_data_migration_status ms
                                        SET cgm_migration_status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
                                            migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                            last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                                        WHERE ms.db_file_id = p_db_file_id
                                        AND ms.file_meta_id = cgm_record.file_meta_id;

                                        failed_records := array_append(failed_records, cgm_record.file_meta_id);
                                    END;
                                END IF;
                            END;
                        END IF;
                    END;
                END IF;
            END;          
           
        END LOOP;
       
       -- Determine interaction status and description based on failures
			IF array_length(failed_records, 1) IS NOT NULL AND array_length(failed_records, 1) > 0 THEN
			    v_interaction_status := 'PARTIAL SUCCESS';
			    v_description := format('CGM migration completed with %s failed records.', array_length(failed_records, 1));
			    v_response := successful_records;
			    v_error_response := failed_records;
			ELSE
			    v_interaction_status := 'SUCCESS';
			    v_description := 'CGM migration completed';
			    v_response := successful_records;
			    v_error_response := NULL;
			END IF;
			
			-- Build JSONB parameters for save_file_interaction_log
			v_file_interaction_params := jsonb_build_object(
			    'last_file_interaction_id', inprogress_file_interaction_id,
			    'interaction_action_type', 'CGM MIGRATION',
			    'interaction_status', v_interaction_status,
			    'description', v_description,
			    'db_file_id', p_db_file_id,
			    'file_name', p_file_name,
			    'file_category', 'Database',
			    'created_by', p_created_by,
			    'response', v_response,
			    'error_response', v_error_response
			);
		
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
	   
	   -- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	       completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	       --Update file interaction completion id into cgm_raw_db table
	       UPDATE drh_stateful_raw_data.cgm_raw_db set file_interaction_id = completed_file_interaction_id where db_file_id=p_db_file_id;
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;		

        RAISE NOTICE 'Migration completed. Successful: %, Failed: %', array_length(successful_records, 1), array_length(failed_records, 1);
        result:= jsonb_build_object('status', 'success',
                'message', 'DB CGM records migrated successfully','successfulrecords', successful_records, 'failedrecords', failed_records,'db_id', p_db_file_id, 
                'file_interaction_id',completed_file_interaction_id);

    END IF;
return result;
EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'CGM MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'CGM migration failed',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', NULL
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    END IF;                          

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RAISE NOTICE 'Migration failed. Error: %', err_message;
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during CGM data migration', 'error_details', error_details_json);
END;
$function$
;


---------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_db_data();
--------------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_specific_db_data(p_db_file_id text);


CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_specific_db_data(p_db_file_id text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    db_record RECORD;
    db_entry_count INT;
    failed_migrations JSONB := '[]'::JSONB;
    success_migrations JSONB := '[]'::JSONB;
    db_participant_migrate_json JSONB;
    db_cgm_migrate_json JSONB;
    error_details_json JSONB;

    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_specific_db_data'; 
    current_query TEXT;
    p_db_migration_log_id TEXT;
    v_expected_study_display_id text;
    v_db_study_display_id text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Get the current query
    SELECT query INTO current_query FROM pg_stat_activity WHERE pid = pg_backend_pid();
    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id    
    );

    
   -- Get the record for the specified db_file_id
    SELECT * INTO db_record
    FROM drh_stateful_raw_data.cgm_raw_db
    WHERE is_processed = false AND db_file_id = p_db_file_id
    LIMIT 1; -- Only fetch a single record

    -- Check if the record exists
    IF FOUND THEN
        RAISE NOTICE 'Starting migration for db_file_id: %', db_record.db_file_id;
       
       -- Get the expected study_display_id from the research_study_view
        SELECT rsv.study_display_id INTO v_expected_study_display_id
        FROM drh_stateless_research_study.research_study_view rsv
        WHERE rsv.study_id = db_record.study_id
        LIMIT 1;

        -- Get the study_display_id from the attached database
        SELECT study_display_id INTO v_db_study_display_id
        FROM drh_stateful_db_import_migration.participant
        WHERE db_file_id = p_db_file_id
        LIMIT 1;

        -- Check if the study_display_id values match
        IF v_expected_study_display_id = v_db_study_display_id THEN
            -- Proceed with the migration

            BEGIN
                -- Insert migration log
                INSERT INTO drh_stateful_db_import_migration.db_migration_log (
                    db_migration_log_id, 
                    db_file_id, 
                    migration_status_id,
                    participant_success_records,
                    participant_failure_records, 
                    cgm_success_records, 
                    cgm_failure_records, 
                    migration_start_time, 
                    migration_end_time
                ) 
                VALUES(
                    drh_stateless_util.get_unique_id(),
                    db_record.db_file_id,
                    (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'IN_PROGRESS' LIMIT 1),
                    NULL, NULL, NULL, NULL, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', NULL
                ) 
                RETURNING db_migration_log_id INTO p_db_migration_log_id;
            END;

            -- Start the participant migration in its own transaction
            BEGIN
                RAISE NOTICE 'Calling migrate_all_participants for db_file_id: %', db_record.db_file_id;
                db_participant_migrate_json := drh_stateless_db_import_migration.migrate_all_participants(db_record.db_file_id);
                RAISE NOTICE 'Participant migration result: %', db_participant_migrate_json;

                -- Check if participant migration was successful
                IF db_participant_migrate_json IS NOT NULL 
                   AND db_participant_migrate_json->>'status' = 'success' 
                   AND db_participant_migrate_json->>'successful_participants' != '[]' THEN

                    BEGIN
                        RAISE NOTICE 'Calling migrate_all_cgm_data for db_file_id: %', db_record.db_file_id;
                        db_cgm_migrate_json := drh_stateless_db_import_migration.migrate_all_cgm_data(db_record.db_file_id);
                        RAISE NOTICE 'CGM migration result: %', db_cgm_migrate_json;

                        -- Check if CGM migration was successful
                        IF db_cgm_migrate_json IS NOT NULL 
                           AND db_cgm_migrate_json->>'status' = 'success' 
                           AND db_cgm_migrate_json->>'successfulrecords' != '[]' THEN

                            -- Migration success: Update the is_processed flag
                            UPDATE drh_stateful_raw_data.cgm_raw_db 
                            SET is_processed = true, 
                                process_status = (SELECT stage_name FROM drh_stateless_master.migration_status_view WHERE stage_id = 4 LIMIT 1),
                                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                            WHERE db_file_id = db_record.db_file_id;

                            -- Track successful migration
                            success_migrations := success_migrations || jsonb_build_object(
                                'db_file_id', db_record.db_file_id, 
                                'status', 'success',
                                'successful_participants', db_participant_migrate_json->>'successful_participants',
                                'failed_participants', db_participant_migrate_json->>'failed_participants',
                                'successful_cgm_records', db_cgm_migrate_json->>'successfulrecords',
                                'failed_cgm_records', db_cgm_migrate_json->>'failedrecords'
                            );

                            -- Update migration log
                            UPDATE drh_stateful_db_import_migration.db_migration_log 
                            SET 
                                migration_status_id = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1),
                                participant_success_records = db_participant_migrate_json->>'successful_participants', 
                                participant_failure_records = db_participant_migrate_json->>'failed_participants', 
                                cgm_success_records = db_cgm_migrate_json->>'successfulrecords', 
                                cgm_failure_records = db_cgm_migrate_json->>'failedrecords',
                                migration_end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC' 
                            WHERE db_migration_log_id = p_db_migration_log_id;

                            RAISE NOTICE 'Migration completed successfully for db_file_id: %', db_record.db_file_id;

                        ELSE
                            -- Handle CGM migration failure
                            UPDATE drh_stateful_db_import_migration.db_migration_log 
                            SET migration_status_id = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
                                cgm_failure_records = db_cgm_migrate_json->>'failedrecords'
                            WHERE db_migration_log_id = p_db_migration_log_id;

                            RAISE NOTICE 'CGM migration failed for db_file_id: %', db_record.db_file_id;
                        END IF;
                    END;

                ELSE
                    RAISE NOTICE 'Participant migration failed, skipping further steps for db_file_id: %', db_record.db_file_id;
                    
                    -- Update migration log with error
                    UPDATE drh_stateful_db_import_migration.db_migration_log 
                    SET migration_status_id = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1),
                        participant_failure_records = db_participant_migrate_json->>'failed_participants'
                    WHERE db_migration_log_id = p_db_migration_log_id;

                    RAISE NOTICE 'Participant migration failed for db_file_id: %', db_record.db_file_id;
                END IF; 
            END;       
        else
          RAISE NOTICE 'study display ids doesnt match';       
        end if;
    ELSE
        RAISE NOTICE 'No such database';       
    
    END IF;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );
    RAISE NOTICE 'Migration failed: %', error_details_json;
END;
$function$
;

------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_specific_db_data(text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.rollback_study_db_data(p_db_file_id text, p_initial_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    function_name TEXT := 'drh_stateless_db_import_migration.rollback_study_db_data';
    current_query TEXT := pg_catalog.current_query();
    parameters_lst JSONB := jsonb_build_object(
        'p_db_file_id', p_db_file_id,
        'p_initial_interaction_id', p_initial_interaction_id
    );

    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
   	p_study_id text;
   	p_tenant_id text;
  	p_created_by text;
 	p_file_name text;

    result JSONB;
    interaction_id TEXT := p_initial_interaction_id;
    error_list JSONB := '[]'::JSONB;    
begin
		SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

    -- Rollback Meal Data
    BEGIN
        result := drh_stateless_db_import_migration.rollback_all_meal_data(p_db_file_id, interaction_id);
        IF result ->> 'status' != 'success' THEN
            RAISE NOTICE 'Meal rollback failed: %', result;
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_meal_data', 'error', result);
        ELSE
            interaction_id := result ->> 'interaction_id';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_meal_data', 'error', err_message);
    END;

    -- Rollback Fitness Data
    BEGIN
        result := drh_stateless_db_import_migration.rollback_all_fitness_data(p_db_file_id, interaction_id);
        IF result ->> 'status' != 'success' THEN
            RAISE NOTICE 'Fitness rollback failed: %', result;
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_fitness_data', 'error', result);
        ELSE
            interaction_id := result ->> 'interaction_id';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_fitness_data', 'error', err_message);
    END;

    -- Revert CGM Data
    BEGIN
        result := drh_stateless_db_import_migration.revert_cgm_data_and_partitions(p_study_id, interaction_id, p_db_file_id);
        IF result ->> 'status' != 'success' THEN
            RAISE NOTICE 'CGM rollback failed: %', result;
            error_list := error_list || jsonb_build_object('function_name', 'revert_cgm_data_and_partitions', 'error', result);
        ELSE
            interaction_id := result ->> 'interaction_id';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            error_list := error_list || jsonb_build_object('function_name', 'revert_cgm_data_and_partitions', 'error', err_message);
    END;

    -- Rollback Participants
    BEGIN
        result := drh_stateless_db_import_migration.rollback_all_participants(p_db_file_id, interaction_id);
        IF result ->> 'status' != 'success' THEN
            RAISE NOTICE 'Participants rollback failed: %', result;
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_participants', 'error', result);
        ELSE
            interaction_id := result ->> 'interaction_id';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            error_list := error_list || jsonb_build_object('function_name', 'rollback_all_participants', 'error', err_message);
    END;

    -- Rollback Study Metadata
    BEGIN
        result := drh_stateless_db_import_migration.rollback_study_meta_data(p_db_file_id, interaction_id);
        IF result ->> 'status' != 'success' THEN
            RAISE NOTICE 'Study metadata rollback failed: %', result;
            error_list := error_list || jsonb_build_object('function_name', 'rollback_study_meta_data', 'error', result);
        ELSE
            interaction_id := result ->> 'interaction_id';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            error_list := error_list || jsonb_build_object('function_name', 'rollback_study_meta_data', 'error', err_message);
    END;

    -- Final Return
    IF jsonb_array_length(error_list) > 0 THEN
        RETURN jsonb_build_object(
            'status', 'partial_failure',
            'errors', error_list,
            'final_interaction_id', interaction_id
        );
    END IF;

    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'All study db rollback steps completed successfully',
        'final_interaction_id', interaction_id
    );

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS 
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT,
            err_context = PG_EXCEPTION_CONTEXT;

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        RETURN jsonb_build_object('status', 'failure', 'function_name', 'rollback_study_db_data', 'error', err_message);  
END;
$function$
;



----------------------------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_all_participants(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.rollback_all_participants(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    participant_record RECORD;
    p_tenant_id TEXT;
    p_study_id TEXT;
    p_created_by TEXT;
    successful_participants TEXT[] := '{}';
    failed_participants TEXT[] := '{}';
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.rollback_all_participants'; 
    current_query TEXT := pg_catalog.current_query(); 
    result JSONB;
    participant_count INT;
    patient_id TEXT;
    v_participant_id TEXT;
    revert_metrics_json JSONB;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    v_response TEXT;
    v_error_response TEXT;
    v_interaction_status TEXT;
    v_description TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting participant rollback for db_file_id: %', p_db_file_id;

    parameters_lst := jsonb_build_object(
        'p_db_file_id', p_db_file_id,
        'p_interaction_id', p_interaction_id
    );

    result := jsonb_build_object(
        'status', 'failure',
        'message', 'DB participant records rollback failure',        
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id,
        'interaction_id',p_interaction_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by 
        INTO p_study_id, p_tenant_id, p_created_by
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study: %, Tenant: %, Uploaded by: %', p_study_id, p_tenant_id, p_created_by;

        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', p_interaction_id,
            'interaction_action_type', 'PARTICIPANT ROLLBACK',
            'interaction_status', 'IN PROGRESS',
            'description', 'Participant rollback started',
            'db_file_id', p_db_file_id,
            'file_category', 'Database',
            'created_by', p_created_by
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        SELECT COUNT(*) INTO participant_count
        FROM (
            SELECT DISTINCT p ->> 'participant_display_id' AS participant_display_id
            FROM drh_stateful_db_import_migration.participant_migration_history pmh,
            LATERAL jsonb_array_elements(participant_json::jsonb) AS p
            WHERE pmh.db_file_id = p_db_file_id
        ) sub;

        RAISE NOTICE 'Total distinct participants to rollback: %', participant_count;

        IF participant_count > 0 THEN
            FOR participant_record IN 
            SELECT 
                pmh.db_file_id,                
                pmh.study_display_id,
                p ->> 'participant_display_id' AS participant_display_id
            FROM drh_stateful_db_import_migration.participant_migration_history pmh,
            LATERAL jsonb_array_elements(participant_json::jsonb) AS p
            WHERE pmh.db_file_id = p_db_file_id
            LOOP
                RAISE NOTICE 'Rolling back participant_display_id: %', participant_record.participant_display_id;
                BEGIN
                    IF EXISTS (
                        SELECT 1
                        FROM drh_stateful_db_import_migration.participant_migration_status pm
                        WHERE pm.db_file_id = p_db_file_id
                        AND pm.participant_display_id = participant_record.participant_display_id
                    ) THEN
                       

                        SELECT participant_id INTO v_participant_id 
                        FROM drh_stateful_db_import_migration.participant_migration_status
                        WHERE db_file_id = p_db_file_id AND participant_display_id = participant_record.participant_display_id
                        LIMIT 1;

                        IF v_participant_id IS NOT NULL THEN
                            RAISE NOTICE 'Found participant_id: %', v_participant_id;

                            IF NOT EXISTS (SELECT 1 FROM drh_stateful_research_study.cgm_device_info WHERE participant_sid = v_participant_id)
                               AND NOT EXISTS (SELECT 1 FROM drh_stateful_research_study.cgm_metrics WHERE participant_sid = v_participant_id)
                               AND NOT EXISTS (SELECT 1 FROM drh_stateful_research_study.nutrition_intake WHERE subject_id = v_participant_id)
                               AND NOT EXISTS (SELECT 1 FROM drh_stateful_research_study.observation_fitness WHERE subject_id = v_participant_id)
                               AND NOT EXISTS (SELECT 1 FROM drh_stateful_research_study.observation_fitness_component WHERE subject_id = v_participant_id) THEN

                                SELECT individual_reference INTO patient_id 
                                FROM drh_stateful_research_study.research_subject
                                WHERE rsubject_id = v_participant_id;

                                DELETE FROM drh_stateful_research_study.participant_base WHERE participant_id = v_participant_id;
                               
                                -- Soft delete from subject_observation
								UPDATE drh_stateful_research_study.subject_observation
								SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
								    deleted_by = p_created_by
								WHERE research_subject_id = v_participant_id;
								
								-- Soft delete from research_subject
								UPDATE drh_stateful_research_study.research_subject
								SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
								    deleted_by = p_created_by
								WHERE rsubject_id = v_participant_id;
								
								-- Soft delete from patient
								UPDATE drh_stateful_research_study.patient
								SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
								    deleted_by = p_created_by
								WHERE id = patient_id;
							
                                RAISE NOTICE 'Deleted participant_id: %, patient_id: %', v_participant_id, patient_id;                                

                                successful_participants := array_append(successful_participants, participant_record.participant_display_id);
                               	UPDATE drh_stateful_db_import_migration.participant_migration_status 
		                        SET migration_status = (SELECT stage_id FROM drh_stateless_master.migration_status_view WHERE stage_name = 'ROLLBACK' LIMIT 1),
		                            last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
		                        WHERE db_file_id = p_db_file_id AND participant_display_id = participant_record.participant_display_id;                               
                            ELSE
                                RAISE NOTICE 'CGM or other data still exists. Skipping deletion for participant_display_id: %', participant_record.participant_display_id;
                                failed_participants := array_append(failed_participants, participant_record.participant_display_id);
                               	UPDATE drh_stateful_db_import_migration.participant_migration_status 
		                        SET migration_status = (SELECT stage_id FROM drh_stateless_master.migration_status_view WHERE stage_name = 'ERROR' LIMIT 1),
		                            last_updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
		                        WHERE db_file_id = p_db_file_id AND participant_display_id = participant_record.participant_display_id;
                            END IF;
                        END IF;
                    END IF;

                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error rolling back participant_display_id: % - %', participant_record.participant_display_id, SQLERRM;
                    failed_participants := array_append(failed_participants, participant_record.participant_display_id);
                END;
            END LOOP;
        END IF;

        IF array_length(failed_participants, 1) IS NOT NULL AND array_length(failed_participants, 1) > 0 THEN
            v_interaction_status := 'PARTIAL SUCCESS';
            v_description := format('Participant rollback completed with %s failed records.', array_length(failed_participants, 1));
            v_response := successful_participants;
            v_error_response := failed_participants;
        ELSE
            v_interaction_status := 'SUCCESS';
            v_description := 'Participant rollback completed';
            v_response := successful_participants;
            v_error_response := NULL;
        END IF;

        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'PARTICIPANT ROLLBACK',
            'interaction_status', v_interaction_status,
            'description', v_description,
            'db_file_id', p_db_file_id,
            'file_category', 'Database',
            'created_by', p_created_by,
            'response', v_response,
            'error_response', v_error_response
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        RAISE NOTICE 'Participant rollback completed. Success: %, Fail: %', array_length(successful_participants, 1), array_length(failed_participants, 1);
        RAISE NOTICE 'Successful Participants: %', successful_participants;
        RAISE NOTICE 'Failed Participants: %', failed_participants;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'DB participant records rollbacked successfully',
            'db_id', p_db_file_id,
            'successful_records', successful_participants,
            'failed_records', failed_participants,
            'interaction_id', completed_file_interaction_id
        );
    ELSE
        RAISE NOTICE 'Invalid or missing db_file_id / interaction_id';
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid or missing db_file_id or interaction_id',            
            'successful_records', '[]',
        	'failed_records', '[]',
        	'db_id', COALESCE(p_db_file_id, 'NULL'),
            'interaction_id', COALESCE(p_interaction_id, 'NULL')
        );
       
RETURN result;

    END IF;
    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS 
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT,
            err_context = PG_EXCEPTION_CONTEXT;

        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'PARTICIPANT ROLLBACK',
            'interaction_status', 'FAILED',
            'description', 'Participant rollback failed',
            'db_file_id', p_db_file_id,
            'file_category', 'Database',
            'created_by', p_created_by
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during participant rollback',
            'error_details', error_details_json
        );
END;
$function$
;

-------------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_all_cgm_data(p_db_file_id text, p_interaction_id text);

---------------------------------------------------------------------------------------------------

DROP TABLE IF EXISTS drh_stateful_db_import_migration.cgm_observation_temp;
CREATE TABLE IF NOT EXISTS drh_stateful_db_import_migration.cgm_observation_temp AS TABLE drh_stateful_raw_observation.cgm_observation WITH NO DATA;

---------------------------------------------------------------------------------------------------------



DROP FUNCTION if exists drh_stateless_db_import_migration.migrate_study_meta_data(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_study_meta_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    metadata_record RECORD;
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    v_rec_status_id INT;
    v_expected_study_display_id text;
    org_party_id text;
    v_db_migration_log_id text;
    db_record record;
   
    v_associated_party_id TEXT;
    v_party_role_type_id TEXT;
    v_citation_id TEXT;
    
    v_publication_title TEXT;
    v_publication_doi TEXT;
    v_publication_date DATE;
   
    p_funding_source TEXT;
    funding_source_array TEXT[];
   
    party_name TEXT;
   
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_study_meta_data'; 
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
   
    v_db_study_display_id text;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    investigator_count INT;
    investigator_index INT := 0;
    investigator JSONB;
    investigator_json JSONB;
    v_inv_party_role_type_id TEXT;
    v_party_id TEXT;
    v_practitioner_party_id TEXT;
    v_practitioner_id TEXT;
    v_author_role TEXT;
    v_author_party_id TEXT;
    v_author_practitioner_party_id TEXT;
    v_author_practitioner_id TEXT;
    v_citation_count INT := 0;
    author JSONB;
    author_json JSONB;      
    
    fund_source TEXT;
    publications_count INT;
    authors_count  INT;
   
    p_citation_id text;
    existing_citation_count text;    
    err_completed_file_interaction_id text;
    citation_record record;    
   
   	v_file_interaction_params JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;

BEGIN   

    result := jsonb_build_object('status', 'failure',
                'message', 'DB study meta data extraction failure', 'db_id', p_db_file_id);
    
    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );
               
    IF p_db_file_id IS NOT NULL and p_interaction_id IS NOT NULL THEN
    
        -- Get the record for the specified db_file_id
        SELECT * INTO db_record
        FROM drh_stateful_raw_data.cgm_raw_db
        WHERE db_file_id = p_db_file_id
        LIMIT 1; -- Only fetch a single record

        -- Check if the record exists
        IF FOUND THEN
    
            RAISE NOTICE 'Starting study meta data extraction for db_file_id: %', p_db_file_id;
       
            -- Get the expected study_display_id from the research_study_view
            SELECT rsv.study_display_id INTO v_expected_study_display_id
            FROM drh_stateless_research_study.research_study_view rsv
            WHERE rsv.study_id = db_record.study_id
            LIMIT 1;

            -- Get the study_display_id from the attached database
            SELECT study_display_id INTO v_db_study_display_id
            FROM drh_stateful_db_import_migration.participant
            WHERE db_file_id = p_db_file_id
            LIMIT 1;
       
            -- Check if the study_display_id values match
            IF v_expected_study_display_id = v_db_study_display_id THEN      
            
            RAISE NOTICE 'Study display ID: %', v_db_study_display_id;       
           
           -- Fetch study and tenant details
                RAISE NOTICE 'Fetching study and tenant details for db_file_id: %', p_db_file_id;
               
                SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
                INTO p_study_id, p_tenant_id, p_created_by, p_file_name
                FROM drh_stateless_raw_data.cgm_raw_db_view crdv
                WHERE crdv.db_file_id = p_db_file_id
                LIMIT 1;
                            
               

				-- Call save_file_interaction_log to get the file interaction ID
		        v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', p_interaction_id,
		            'interaction_action_type', 'STUDY METADATA MIGRATION',
		            'interaction_status', 'IN PROGRESS',
		            'description', 'Study metadata migration started',
		            'db_file_id', p_db_file_id,
		            'file_name', p_file_name,
		            'file_category', 'Database',
		            'created_by', p_created_by
		        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
					
					-- Ensure file_interaction_result is not NULL before accessing JSON fields
					IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
					    inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';    					                      
					ELSE
					    result := jsonb_build_object(
					        'status', 'failure',
					        'message', 'Failed to insert file interaction',
					        'error_details', COALESCE(file_interaction_result ->> 'error_details', 'No details provided')
					    );
					END IF;

        
                -- Proceed with the migration
                -- Insert migration log if not exists
                IF NOT EXISTS (
                    SELECT 1 
                    FROM drh_stateful_db_import_migration.db_migration_log 
                    WHERE db_file_id = db_record.db_file_id
                ) THEN
                    INSERT INTO drh_stateful_db_import_migration.db_migration_log (
                        db_migration_log_id, 
                        db_file_id, 
                        migration_status_id,
                        participant_success_records,
                        participant_failure_records, 
                        cgm_success_records, 
                        cgm_failure_records, 
                        migration_start_time, 
                        migration_end_time
                    ) 
                    VALUES (
                        drh_stateless_util.get_unique_id(),
                        db_record.db_file_id,
                        (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'IN_PROGRESS' LIMIT 1),
                        NULL, NULL, NULL, NULL, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', NULL
                    ) 
                    RETURNING db_migration_log_id INTO v_db_migration_log_id;
                   RAISE NOTICE 'v_db_migration_log_id: %', v_db_migration_log_id;
                ELSE
                    SELECT d.db_migration_log_id INTO v_db_migration_log_id 
                    FROM drh_stateful_db_import_migration.db_migration_log d
                    WHERE db_file_id = db_record.db_file_id;
                    RAISE NOTICE 'updated v_db_migration_log_id: %', v_db_migration_log_id;
                END IF;

                
               
                SELECT opv.organization_party_id INTO org_party_id 
                FROM drh_stateless_research_study.organization_party_view opv  
                WHERE opv.organization_id = p_tenant_id;
        
                RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;
               
                -- Get active record status ID
                RAISE NOTICE 'Fetching active record status ID';
                SELECT rs.value INTO v_rec_status_id 
                FROM drh_stateful_party.record_status rs 
                WHERE rs.code = 'ACTIVE' 
                LIMIT 1;
        
                -- Retrieve the metadata record
                RAISE NOTICE 'Retrieving study meta data record for db_file_id: %', p_db_file_id;
                SELECT study_meta_id, tenant_id, study_display_id, study_name, start_date, end_date,
                       treatment_modalities, funding_source, nct_number, study_description,
                       investigators, publications, authors, institutions, labs, sites, elaboration 
                INTO metadata_record
                FROM drh_stateful_db_import_migration.study_meta_data fmid 
                WHERE fmid.db_file_id = p_db_file_id;
               
               RAISE NOTICE 'metadata: %', metadata_record;

               -- insert the data into a history table for auditing or any rollback in future               
               INSERT INTO drh_stateful_db_import_migration.study_metadata_history (
				  log_id,
				  db_file_id,
				  study_meta_id,
				  study_metadata,
				  captured_by,
				  captured_at,
				  study_id,
                  migration_status, updated_by, updated_at
				)
				SELECT
				  drh_stateless_util.get_unique_id(),
				  smd.db_file_id,
				  smd.study_meta_id,
				  json_build_object(
				    'tenant_id', smd.tenant_id,
				    'study_display_id', smd.study_display_id,
				    'study_name', smd.study_name,
				    'start_date', smd.start_date,
				    'end_date', smd.end_date,
				    'treatment_modalities', smd.treatment_modalities,
				    'funding_source', smd.funding_source,
				    'nct_number', smd.nct_number,
				    'study_description', smd.study_description,
				    'investigators', smd.investigators::json,
				    'publications', smd.publications::json,
				    'authors', smd.authors::json,
				    'institutions', smd.institutions::json,
				    'labs', smd.labs::json,
				    'sites', smd.sites::json,
				    'elaboration', smd.elaboration::json
				  ),
				  p_created_by,
				  CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
				  rs.study_id,
                  (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'IN_PROGRESS' LIMIT 1),
                  NULL,
                  NULL
				FROM drh_stateful_db_import_migration.study_meta_data smd
				LEFT JOIN drh_stateful_research_study.research_study rs
				  ON rs.study_display_id = smd.study_display_id
				WHERE smd.db_file_id = p_db_file_id
				LIMIT 1;
        
                -- Update research study table
                UPDATE drh_stateful_research_study.research_study rs 
					SET study_display_id = COALESCE(metadata_record.study_display_id, rs.study_display_id),
					    research_study_identifier = CASE 
					        WHEN COALESCE(metadata_record.nct_number, '') IS NOT NULL THEN 
					            jsonb_build_object('value', metadata_record.nct_number)::JSONB 
					        ELSE rs.research_study_identifier 
					    END,
					    title = COALESCE(metadata_record.study_name, rs.title),
					    description = COALESCE(metadata_record.study_description, rs.description),
					    start_date = COALESCE(NULLIF(metadata_record.start_date::TEXT, '')::DATE, rs.start_date),
					    end_date = COALESCE(NULLIF(metadata_record.end_date::TEXT, '')::DATE, rs.end_date),
					    treatment_modality = COALESCE(metadata_record.treatment_modalities, rs.treatment_modality),
					    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
					    updated_by = p_created_by
					WHERE rs.study_id = p_study_id;
				
				RAISE NOTICE 'basic data updated for db_file_id: %', p_db_file_id;
               
                SELECT study_party_role_id INTO v_party_role_type_id
                FROM drh_stateful_master.research_study_party_role
                WHERE code = 'sponsor';
               
                RAISE NOTICE 'v_party_role_type_id: %', v_party_role_type_id;
        
                -- Delete existing associated party records before inserting new values
                DELETE FROM drh_stateful_research_study.research_study_associated_party
                WHERE research_study_id = p_study_id;
        
                -- Insert funding source details
               
                RAISE NOTICE 'metadata_record.funding_source: %', metadata_record.funding_source;
                
				IF COALESCE(metadata_record.funding_source, '') IS NOT NULL THEN
			        funding_source_array := string_to_array(metadata_record.funding_source, ',');
			        FOREACH fund_source IN ARRAY funding_source_array LOOP
			            INSERT INTO drh_stateful_research_study.research_study_associated_party (
			                associated_party_id, research_study_id, party_role_type_id, 
			                party_name, classifier_id, period_start, period_end, 
			                created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
			            ) VALUES (
			                drh_stateless_util.get_unique_id(), p_study_id, v_party_role_type_id, 
			                TRIM(fund_source), NULL, NULL, NULL, 
			                CURRENT_TIMESTAMP AT TIME ZONE 'UTC', p_created_by, NULL, NULL, NULL, NULL
			            );
			        END LOOP;
			    END IF;
			
			   
			  
			    SELECT COALESCE(jsonb_array_length(metadata_record.investigators::JSONB), 0) INTO investigator_count;
			    RAISE NOTICE 'investigator_count: %', investigator_count;


				-- Iterate through investigators if not empty
				IF investigator_count > 0 THEN
				    FOR investigator IN 
				        SELECT * FROM jsonb_array_elements(metadata_record.investigators::JSONB)
				    LOOP
				        investigator_json := investigator::JSONB;
				        investigator_index := investigator_index + 1;
				
				        -- Determine investigator role based on position
				        SELECT study_party_role_id 
				        INTO v_inv_party_role_type_id
				        FROM drh_stateful_master.research_study_party_role
				        WHERE code = CASE 
				            WHEN investigator_index = 1 THEN 'primary-investigator'
				            WHEN investigator_index = 2 THEN 'nominated-principal-investigator'
				            WHEN investigator_index IN (3, 4) THEN 'co-investigator'
				            ELSE 'study-team'
				        END;
				
				       
				      				
				        -- Insert into research study associated party with determined role
				        INSERT INTO drh_stateful_research_study.research_study_associated_party (
				            associated_party_id, research_study_id, party_role_type_id, party_id,party_name, created_at, created_by
				        ) VALUES (
				            drh_stateless_util.get_unique_id(), p_study_id, v_inv_party_role_type_id,null ,investigator_json->>'investigator_name', CURRENT_TIMESTAMP, p_created_by
				        );
				
				    END LOOP;
				END IF;
					      
                 
                 
                   IF EXISTS (
				        SELECT 1 
				        FROM drh_stateful_research_study.citation c 
				        WHERE c.study_id = p_study_id
				    ) THEN	
				        -- Count citations
				        SELECT COUNT(*) INTO existing_citation_count 
				        FROM drh_stateful_research_study.citation c  
				        WHERE c.study_id = p_study_id;
				       
				        -- Loop through each citation
				        FOR citation_record IN 
				            SELECT id FROM drh_stateful_research_study.citation 
				            WHERE study_id = p_study_id
					        LOOP
					            -- Delete associated authors
					            DELETE FROM drh_stateful_research_study.citation_author  
					            WHERE citation_id = citation_record.id;
					            
					            -- Delete the citation
					            DELETE FROM drh_stateful_research_study.citation  
					            WHERE id = citation_record.id;
					        END LOOP;
				       
				        RAISE NOTICE 'Publications deleted';
				    END IF;
                
				   SELECT COALESCE(jsonb_array_length(metadata_record.publications::JSONB), 0) INTO publications_count;
				   RAISE NOTICE 'publications_count: %', publications_count;
					
				
				-- Insert citations from JSON array and capture only the first citation ID if publications JSON is not empty
				IF jsonb_array_length(COALESCE(metadata_record.publications::JSONB, '[]'::jsonb)) > 0 THEN
                    SELECT value->>'title', value->>'doi', (value->>'date')::DATE
					    INTO v_publication_title, v_publication_doi, v_publication_date
					    FROM jsonb_array_elements(metadata_record.publications::JSONB) AS value
					    LIMIT 1;
					    
					    -- Insert citation and capture ID
					    INSERT INTO drh_stateful_research_study.citation (
					        id, identifier_system, identifier_value, title, date, rec_status_id, 
					        study_id, created_at, created_by
					    ) 
					    VALUES (
					        drh_stateless_util.get_unique_id(),  -- Convert to TEXT
					        null, null, v_publication_title, 
					        v_publication_date, v_rec_status_id, p_study_id, 
					        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', p_created_by
					    ) 
					    RETURNING id INTO v_citation_id;	
					   
					   -- Insert into citation_identifier only if DOI is valid (not null or empty)
						IF v_publication_doi IS NOT NULL AND LENGTH(TRIM(v_publication_doi)) > 0 THEN
                        INSERT INTO drh_stateful_research_study.citation_identifier (
                            id,
                            citation_id,
                            identifier_system,
                            identifier_value,
                            rec_status_id,
                            created_at,
                            created_by,
                            updated_at,
                            updated_by,
                            deleted_at,
                            deleted_by
                        ) VALUES (
                            drh_stateless_util.get_unique_id(),
                            v_citation_id,
                            'DOI',
                            v_publication_doi,
                            v_rec_status_id,
                            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                            p_created_by,
                            NULL,
                            NULL,
                            NULL,
                            NULL
                        );
                    END IF;

				   
				   -- Determine author role
				    SELECT study_party_role_id 
				    INTO v_author_role
				    FROM drh_stateful_master.research_study_party_role
				    WHERE code = 'co-author';
				
				   SELECT COALESCE(jsonb_array_length(metadata_record.authors::JSONB), 0) INTO authors_count;
				   RAISE NOTICE 'authors_count: %', authors_count;    
				    
				    -- Insert authors linked to only the first citation ID if authors JSON is not empty
				    IF jsonb_array_length(COALESCE(metadata_record.authors::JSONB, '[]'::jsonb)) > 0 THEN
				        FOR author IN 
				            SELECT * FROM jsonb_array_elements(metadata_record.authors::JSONB) 
				        LOOP
				            author_json := author::JSONB;				            

				
				            INSERT INTO drh_stateful_research_study.citation_author (
				                id,
				                citation_id,
				                first_name,
				                role_id,
				                party_id,
				                rec_status_id,
				                created_at
				            ) VALUES (
				                drh_stateless_util.get_unique_id(),  -- Convert to TEXT
				                v_citation_id,  -- Always use the first (or only) citation ID
				                author_json->>'name',
				                v_author_role,
				                null,
				                v_rec_status_id,
				                CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
				            );
				        END LOOP;
				    END IF;
				END IF;
			
			
			   IF NOT EXISTS (
			        SELECT 1 
			        FROM drh_stateful_research_study.study_type_mapping st
			        WHERE st.study_id = p_study_id
			    ) THEN		
			        INSERT INTO drh_stateful_research_study.study_type_mapping (study_id, study_type)
			        VALUES (p_study_id, metadata_record.elaboration);
			    ELSE
			        UPDATE drh_stateful_research_study.study_type_mapping 
			        SET study_type = metadata_record.elaboration 
			        WHERE study_id = p_study_id;
			    END IF;
			   
			   -- Update the migration status to COMPLETED for one matching row
                UPDATE drh_stateful_db_import_migration.study_metadata_history smh
                SET migration_status = (
                    SELECT v1.stage_id 
                    FROM drh_stateless_master.migration_status_view v1 
                    WHERE v1.stage_name = 'COMPLETED'
                    LIMIT 1
                ),
                updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                updated_by = p_created_by
                WHERE smh.log_id = (
                    SELECT smd.log_id
                    FROM drh_stateful_db_import_migration.study_metadata_history smd
                    LEFT JOIN drh_stateful_research_study.research_study rs
                    ON rs.study_display_id = smd.study_meta_id
                    WHERE smd.db_file_id = p_db_file_id
                    LIMIT 1
                );

				                
                RAISE NOTICE 'Study meta data migration completed.';                

				-- Call save_file_interaction_log to get the file interaction ID
		        v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', inprogress_file_interaction_id,
		            'interaction_action_type', 'STUDY METADATA MIGRATION',
		            'interaction_status', 'SUCCESS',
		            'description', 'Study metadata migration completed',
		            'db_file_id', p_db_file_id,
		            'file_name', p_file_name,
		            'file_category', 'Database',
		            'created_by', p_created_by
		        );
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
					
					-- Ensure file_interaction_result is not NULL before accessing JSON fields
					IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
					    completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';    
					    result := jsonb_build_object(
					        'status', 'success', 
					        'message', 'Study metadata records migrated successfully', 
					        'db_id', p_db_file_id, 
					        'file_interaction_id', completed_file_interaction_id
					    );                   
					ELSE
					    result := jsonb_build_object(
					        'status', 'failure',
					        'message', 'Failed to insert file interaction',
					        'error_details', COALESCE(file_interaction_result ->> 'error_details', 'No details provided')
					    );
					END IF;

               
            END IF;
        END IF;
    END IF;

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RAISE NOTICE 'Migration failed. Error: %', err_message;

       -- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'STUDY METADATA MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'Study metadata migration failed',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );

        -- Handle the result of file interaction logging
        IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
            err_completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';    
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Study metadata records migration failure',
                'db_id', p_db_file_id,
                'file_interaction_id', err_completed_file_interaction_id
            );  
        ELSE
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result ->> 'error_details'
            );
        END IF;

        -- Return the final result
        RETURN result;
end;
$function$
;

------------------------------------------------------------------------------------------------

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.create_cgm_partition(
    v_participant_sid TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER AS
$$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.create_cgm_partition';
BEGIN
    -- Construct parameters list
    parameters_lst := jsonb_build_object(
        'v_participant_sid', v_participant_sid
    );

    EXECUTE format(
        $i$
        CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation."cgm_observation_research_subject_%1$s"
        (LIKE drh_stateful_raw_observation.cgm_observation INCLUDING DEFAULTS INCLUDING CONSTRAINTS);
        $i$, v_participant_sid);

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Build the error log JSON
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    -- Log the exception
    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Raise notice for debugging (optional)
    RAISE NOTICE 'Partition creation failed for SID %: %', v_participant_sid, err_message;
END;
$$;

---------------------------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.copy_cgm_partition(
    v_participant_sid TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER AS
$$
DECLARE
    sql_query TEXT;
    num_copied BIGINT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.copy_cgm_partition';
BEGIN
    -- Construct parameters list
    parameters_lst := jsonb_build_object(
        'v_participant_sid', v_participant_sid
    );

    -- Correct SQL formatting
    sql_query := format(
        $i$
        INSERT INTO drh_stateful_raw_observation."cgm_observation_research_subject_%1$s"
        SELECT * FROM drh_stateful_db_import_migration.cgm_observation_temp
        WHERE research_subject_id = %L;
        $i$, v_participant_sid, v_participant_sid);
    
    -- Print the generated SQL for debugging
    RAISE NOTICE 'Executing SQL: %', sql_query;
    
    -- Execute the query
    EXECUTE sql_query;

    -- Get affected rows
    GET DIAGNOSTICS num_copied = ROW_COUNT;
    
    -- Print the number of copied rows
    RAISE NOTICE 'Copied % rows to %', num_copied, 
        format('drh_stateful_raw_observation."cgm_observation_research_subject_%1$s"', v_participant_sid);
EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Build the error log JSON
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    -- Log the exception
    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    RAISE NOTICE 'CGM Partition copy failed for SID %: %', v_participant_sid, err_message;
    num_copied := -1;
END;
$$;

-------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.index_and_attach_cgm_partition(IN research_subject_id text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    success BOOLEAN;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.index_and_attach_cgm_partition';
BEGIN

    parameters_lst := jsonb_build_object(
    'research_subject_id', research_subject_id
    );

    
    BEGIN
        EXECUTE format(
        $i$

        -- Add Primary Key to the partition
        ALTER TABLE drh_stateful_raw_observation.%I 
        ADD PRIMARY KEY (id, tenant_id, study_id, research_subject_id);

        -- Add Check Constraint for partitioning
        ALTER TABLE drh_stateful_raw_observation.%I 
        ADD CONSTRAINT %I CHECK (research_subject_id = %L);

        -- Create necessary indexes
        CREATE INDEX %I ON drh_stateful_raw_observation.%I USING btree (study_id, research_subject_id, tenant_id, date_time);
        CREATE INDEX %I ON drh_stateful_raw_observation.%I USING btree (deleted_at) WHERE (deleted_at IS NULL);
        CREATE INDEX %I ON drh_stateful_raw_observation.%I USING btree (raw_cgm_extract_data_id);
        CREATE INDEX %I ON drh_stateful_raw_observation.%I USING btree (research_subject_id);
        CREATE INDEX %I ON drh_stateful_raw_observation.%I USING btree (study_id);

        -- Attach partition
        ALTER TABLE drh_stateful_raw_observation.cgm_observation 
        ATTACH PARTITION drh_stateful_raw_observation.%I FOR VALUES IN (%L);

        -- Attach indexes to parent table
        ALTER INDEX drh_stateful_raw_observation.idx_cgm_obs_study_participant_date 
        ATTACH PARTITION drh_stateful_raw_observation.%I;
        ALTER INDEX drh_stateful_raw_observation.idx_cgm_observation_deleted_at 
        ATTACH PARTITION drh_stateful_raw_observation.%I;
        ALTER INDEX drh_stateful_raw_observation.idx_cgm_observation_raw_data_id 
        ATTACH PARTITION drh_stateful_raw_observation.%I;
        ALTER INDEX drh_stateful_raw_observation.idx_cgm_observation_research_subject_id 
        ATTACH PARTITION drh_stateful_raw_observation.%I;
        ALTER INDEX drh_stateful_raw_observation.idx_cgm_observation_study_id 
        ATTACH PARTITION drh_stateful_raw_observation.%I;

        -- Drop Check Constraint (only if exists)
        DO $$ BEGIN
            ALTER TABLE drh_stateful_raw_observation.%I 
            DROP CONSTRAINT IF EXISTS %I;
        END $$;

        $i$,
        format('cgm_observation_research_subject_%s', research_subject_id), -- Table Name
        format('cgm_observation_research_subject_%s', research_subject_id), -- Table Name
        format('cgm_observation_partition_by_list_check_%s', research_subject_id), research_subject_id, -- Check Constraint

        format('idx_cgm_obs_study_participant_date_%s', research_subject_id), format('cgm_observation_research_subject_%s', research_subject_id),
        format('idx_cgm_observation_deleted_at_%s', research_subject_id), format('cgm_observation_research_subject_%s', research_subject_id),
        format('idx_cgm_observation_raw_data_id_%s', research_subject_id), format('cgm_observation_research_subject_%s', research_subject_id),
        format('idx_cgm_observation_research_subject_id_%s', research_subject_id), format('cgm_observation_research_subject_%s', research_subject_id),
        format('idx_cgm_observation_study_id_%s', research_subject_id), format('cgm_observation_research_subject_%s', research_subject_id),

        format('cgm_observation_research_subject_%s', research_subject_id), research_subject_id, -- Partition Attach

        format('idx_cgm_obs_study_participant_date_%s', research_subject_id),
        format('idx_cgm_observation_deleted_at_%s', research_subject_id),
        format('idx_cgm_observation_raw_data_id_%s', research_subject_id),
        format('idx_cgm_observation_research_subject_id_%s', research_subject_id),
        format('idx_cgm_observation_study_id_%s', research_subject_id),

        format('cgm_observation_research_subject_%s', research_subject_id), -- Drop Constraint Table Name
        format('cgm_observation_partition_by_list_check_%s', research_subject_id) -- Drop Constraint Name
        );

        success := TRUE;
    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Build the error log JSON
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    -- Log the exception
    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);    

    RAISE NOTICE 'Error in index_and_attach_cgm_partition for %: %', research_subject_id, err_message;
    success := FALSE;
    END;
END;
$procedure$
;

------------------------------------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_cgm_partition(research_subject_id TEXT)  
LANGUAGE plpgsql  
SECURITY DEFINER  
AS  
$$  
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.load_cgm_partition';
BEGIN  
    parameters_lst := jsonb_build_object(
    'research_subject_id', research_subject_id
    );
    BEGIN  
        CALL drh_stateless_db_import_migration.create_cgm_partition(research_subject_id);  
        CALL drh_stateless_db_import_migration.copy_cgm_partition(research_subject_id);  
        CALL drh_stateless_db_import_migration.index_and_attach_cgm_partition(research_subject_id);  
    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

        -- Build the error log JSON
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log the exception
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);    
        RAISE NOTICE 'Error in load_cgm_partition for %: %', research_subject_id, err_message;
    END;  
END;  
$$;

-----------------------------------------------------------------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_cgm_partitions(IN p_study_id text, IN p_interaction_id text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE  
    v_research_subject_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    error_details_json JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.load_cgm_partitions';
BEGIN  

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_interaction_id',p_interaction_id
    );

	-- Check if the study exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist', p_study_id;   
   ELSE
	    -- Initialize interaction log
	    v_file_interaction_params := jsonb_build_object(
	        'last_file_interaction_id', p_interaction_id,
	        'interaction_action_type', 'CGM PARTITION MIGRATION',
	        'interaction_status', 'IN PROGRESS',
	        'description', 'CGM partition migration started',
	        'study_id', p_study_id,
	        'file_category', 'Database',
	        'created_by', NULL
	    );
	
	    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
	
	    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
	        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	    ELSE
	        RAISE NOTICE 'Failed to insert file interaction log';
	        RETURN;
	    END IF;
	
	    -- Create partition
	    FOR v_research_subject_id IN  
	        SELECT DISTINCT research_subject_id  
	        FROM drh_stateful_db_import_migration.cgm_observation_temp  
	        WHERE study_id = p_study_id  
	    LOOP  
	        BEGIN
	            RAISE NOTICE 'Creating partition: %', v_research_subject_id;
	            CALL drh_stateless_db_import_migration.create_cgm_partition(v_research_subject_id);  
	        EXCEPTION  
	            WHEN OTHERS THEN  
	                RAISE NOTICE 'Error processing %: %', v_research_subject_id, SQLERRM;
	                -- ROLLBACK; -- Rollback only this iteration
	        END;
	    END LOOP;    
	    
	
	    -- Copy data
	    FOR v_research_subject_id IN  
	        SELECT DISTINCT research_subject_id  
	        FROM drh_stateful_db_import_migration.cgm_observation_temp  
	        WHERE study_id = p_study_id  
	    LOOP  
	        BEGIN
	            RAISE NOTICE 'Copying data: %', v_research_subject_id; 
	            CALL drh_stateless_db_import_migration.copy_cgm_partition(v_research_subject_id);  
	        EXCEPTION  
	            WHEN OTHERS THEN  
	                RAISE NOTICE 'Error processing %: %', v_research_subject_id, SQLERRM;
	                -- ROLLBACK; -- Rollback only this iteration
	        END;
	    END LOOP;    
	    
	
	    -- Attaching
	    FOR v_research_subject_id IN  
	        SELECT DISTINCT research_subject_id  
	        FROM drh_stateful_db_import_migration.cgm_observation_temp  
	        WHERE study_id = p_study_id  
	    LOOP  
	        BEGIN
	            RAISE NOTICE 'Attaching: %', v_research_subject_id; 
	            CALL drh_stateless_db_import_migration.index_and_attach_cgm_partition(v_research_subject_id); 
	        EXCEPTION  
	            WHEN OTHERS THEN  
	                RAISE NOTICE 'Error processing %: %', v_research_subject_id, SQLERRM;
	                -- ROLLBACK; -- Rollback only this iteration
	        END;
	    END LOOP;    
	   
	
	    -- Caching
	    FOR v_research_subject_id IN  
	        SELECT DISTINCT research_subject_id  
	        FROM drh_stateful_db_import_migration.cgm_observation_temp  
	        WHERE study_id = p_study_id  
	    LOOP  
	        BEGIN
	            RAISE NOTICE 'Caching: %', v_research_subject_id; 
	            PERFORM drh_stateless_raw_observation.save_cgm_metrics(v_research_subject_id); 
	        EXCEPTION  
	            WHEN OTHERS THEN  
	                RAISE NOTICE 'Error processing %: %', v_research_subject_id, SQLERRM;
	                -- ROLLBACK; -- Rollback only this iteration
	        END;
	    END LOOP;    
	    
	
	    DELETE FROM drh_stateful_db_import_migration.cgm_observation_temp WHERE study_id = p_study_id;
	    
	
	    -- Finalize interaction log
	    v_file_interaction_params := jsonb_build_object(
	        'last_file_interaction_id', inprogress_file_interaction_id,
	        'interaction_action_type', 'CGM PARTITION MIGRATION',
	        'interaction_status', 'SUCCESS',
	        'description', 'CGM partition migration completed',
	        'study_id', p_study_id,
	        'file_category', 'Database',
	        'created_by', NULL
	    );
	
	    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
	
	    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
	        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	    ELSE
	        RAISE NOTICE 'Failed to finalize file interaction log';
	    END IF;
 END IF;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN	     
	   -- Call save_file_interaction_log to get the file interaction ID
	    v_file_interaction_params := jsonb_build_object(
	        'last_file_interaction_id', inprogress_file_interaction_id,
	        'interaction_action_type', 'CGM PARTITION MIGRATION',
	        'interaction_status', 'FAILED',
	        'description', 'CGM partition Migration failed',
	        'study_id', p_study_id,
	        'file_category', 'Database',
	        'created_by', NULL
	    );
    	file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   END IF;



    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );
    RAISE NOTICE 'load_cgm_partitions failed: %', error_details_json;
END;
$procedure$
;


/*
Function: drh_stateless_db_import_migration.rollback_study_meta_data

Description:
This function performs a rollback of study metadata associated with a given database file ID (`p_db_file_id`) and interaction ID (`p_interaction_id`). It ensures that all related data, such as associated parties, citations, authors, and study type mappings, are removed from the database. The function also logs the progress and outcome of the rollback operation.

Parameters:
- p_db_file_id (TEXT): The unique identifier of the database file whose study metadata is to be rolled back.
- p_interaction_id (TEXT): The unique identifier of the interaction associated with the rollback operation.

Returns:
- JSONB: A JSON object containing the status (`success` or `failure`), a message, and additional details such as the database file ID and file interaction ID (if applicable).

Behavior:
1. Validates the input parameters (`p_db_file_id` and `p_interaction_id`).
2. Fetches study and tenant details associated with the provided database file ID.
3. Compares the `study_display_id` from the expected research study view with the one in the attached database.
4. If the `study_display_id` values match:
    - Logs the start of the rollback operation.
    - Deletes associated parties, citations, authors, and study type mappings related to the study.
    - Logs the successful completion of the rollback operation.
5. If the `study_display_id` values do not match, a notice is raised.
6. Handles exceptions by logging error details and marking the rollback operation as failed.

Exception Handling:
- Captures and logs any errors that occur during the rollback process, including the error message, details, hint, context, and SQL state.
- Ensures that the interaction log is finalized with a failure status in case of an error.

Dependencies:
- drh_stateless_raw_data.cgm_raw_db_view: Used to fetch study and tenant details.
- drh_stateless_research_study.research_study_view: Used to fetch the expected `study_display_id`.
- drh_stateful_db_import_migration.participant: Used to fetch the `study_display_id` from the attached database.
- drh_stateless_raw_observation.save_file_interaction_log: Used to log the progress and outcome of the rollback operation.
- drh_stateful_activity_audit.exception_log: Used to log exceptions.

Notes:
- The function is defined with `SECURITY DEFINER`, meaning it executes with the privileges of the user who created it.
- The function uses `pg_catalog.current_query()` to capture the current query for logging purposes.
*/

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_study_meta_data(text, text);
CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.rollback_study_meta_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    v_db_migration_log_id TEXT;
    v_expected_study_display_id TEXT;
    v_db_study_display_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.rollback_study_meta_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting rollback of study metadata for db_file_id: %, interaction_id: %', p_db_file_id, p_interaction_id;

    result := jsonb_build_object('status', 'failure', 'message', 'Study metadata rollback failed', 'db_id', p_db_file_id,
         'interaction_id',p_interaction_id);
    parameters_lst := jsonb_build_object(
        'p_db_file_id', p_db_file_id,
        'p_interaction_id', p_interaction_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        -- Fetch study details
        RAISE NOTICE 'Fetching study and tenant details...';
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Fetched study_id: %, tenant_id: %, created_by: %', p_study_id, p_tenant_id, p_created_by;

        -- Get expected study_display_id from current system
        SELECT rsv.study_display_id INTO v_expected_study_display_id
        FROM drh_stateless_research_study.research_study_view rsv
        WHERE rsv.study_id = p_study_id
        LIMIT 1;

        -- Get study_display_id from the attached DB
        SELECT smh.study_metadata::jsonb ->> 'study_display_id' INTO v_db_study_display_id
        FROM drh_stateful_db_import_migration.study_metadata_history smh 
        WHERE smh.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Expected study_display_id: %, DB study_display_id: %', v_expected_study_display_id, v_db_study_display_id;

        IF lower(trim(v_expected_study_display_id)) = lower(trim(v_db_study_display_id)) THEN

            RAISE NOTICE 'Study display IDs match. Proceeding with rollback.';

            -- Start interaction log
            v_file_interaction_params := jsonb_build_object(
                'last_file_interaction_id', p_interaction_id,
                'interaction_action_type', 'STUDY METADATA ROLLBACK',
                'interaction_status', 'IN PROGRESS',
                'description', 'Study metadata rollback started',
                'db_file_id', p_db_file_id,
                'file_name', p_file_name,
                'file_category', 'Database',
                'created_by', p_created_by
            );

            file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
            IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
                inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
                RAISE NOTICE 'Logged file interaction with ID: %', inprogress_file_interaction_id;
            ELSE
                RETURN jsonb_build_object(
                    'status', 'failure',
                    'message', 'Failed to insert file interaction',
                    'error_details', file_interaction_result -> 'error_details'
                );
            END IF;

            
            RAISE NOTICE 'Rolling back citation authors and citations...';
            DELETE FROM drh_stateful_research_study.citation_author
            WHERE citation_id IN (
                SELECT id FROM drh_stateful_research_study.citation WHERE study_id = p_study_id
            );

            -- Rollback citation identifiers
			RAISE NOTICE 'Rolling back citation identifiers...';
			DELETE FROM drh_stateful_research_study.citation_identifier
			WHERE citation_id IN (
			    SELECT id FROM drh_stateful_research_study.citation WHERE study_id = p_study_id
			);

            RAISE NOTICE 'Rolling back citation...';
            DELETE FROM drh_stateful_research_study.citation
            WHERE study_id = p_study_id;

            RAISE NOTICE 'Rolling back associated parties...sponsors and investigators';
            DELETE FROM drh_stateful_research_study.research_study_associated_party
            WHERE research_study_id = p_study_id;


            RAISE NOTICE 'Nullifying optional metadata in research_study...';
            UPDATE drh_stateful_research_study.research_study
            SET research_study_identifier = NULL,
                description = NULL,
                start_date = NULL,
                end_date = NULL,
                treatment_modality = NULL,
                updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                updated_by = p_created_by
            WHERE study_id = p_study_id;

            -- Update the migration status to ROLLBACK for one matching row
            UPDATE drh_stateful_db_import_migration.study_metadata_history smh
            SET migration_status = (
                SELECT v1.stage_id 
                FROM drh_stateless_master.migration_status_view v1 
                WHERE v1.stage_name = 'ROLLBACK'
                LIMIT 1
            ),
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            updated_by = p_created_by
            WHERE smh.log_id = (
                SELECT smd.log_id
                FROM drh_stateful_db_import_migration.study_metadata_history smd
                LEFT JOIN drh_stateful_research_study.research_study rs
                ON rs.study_display_id = smd.study_meta_id
                WHERE smd.db_file_id = p_db_file_id
                LIMIT 1
            );


            -- Finalize interaction log
            RAISE NOTICE 'Finalizing file interaction log...';
            v_file_interaction_params := jsonb_build_object(
                'last_file_interaction_id', inprogress_file_interaction_id,
                'interaction_action_type', 'STUDY METADATA ROLLBACK',
                'interaction_status', 'SUCCESS',
                'description', 'Study metadata rollback completed',
                'db_file_id', p_db_file_id,
                'file_name', p_file_name,
                'file_category', 'Database',
                'created_by', p_created_by
            );

            file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
            IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
                completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
                RAISE NOTICE 'Rollback completed successfully. Final log ID: %', completed_file_interaction_id;
                result := jsonb_build_object(
                    'status', 'success',
                    'message', 'Study metadata rollback completed successfully',
                    'db_id', p_db_file_id,
                    'interaction_id', completed_file_interaction_id
                );
            ELSE
                RETURN jsonb_build_object(
                    'status', 'failure',
                    'message', 'Failed to finalize file interaction',
                    'error_details', file_interaction_result -> 'error_details'
                );
            END IF;
        ELSE
            RAISE NOTICE 'Study display IDs do not match. Skipping rollback for db_file_id: %', p_db_file_id;
        END IF;
    ELSE
        RAISE NOTICE 'db_file_id or interaction_id is NULL. Exiting early.';
    END IF;

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Capture exception diagnostics
        GET STACKED DIAGNOSTICS 
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT,
            err_context = PG_EXCEPTION_CONTEXT;

        RAISE NOTICE 'Exception occurred: %', err_message;

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Finalize interaction log with failure
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'STUDY METADATA ROLLBACK',
            'interaction_status', 'FAILED',
            'description', 'Study metadata rollback failed',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during study metadata rollback',
            'error_details', error_details_json
        );
END;
$function$
;



/**
 * Function: drh_stateless_db_import_migration.cleanup_db_upload_entries
 * 
 * Description:
 * This function performs a cleanup operation for database upload entries associated with a given `db_file_id`.
 * It ensures that all related processes (study metadata, participant data, and CGM data rollbacks) have been 
 * successfully completed before deleting entries from various related tables. If any process has not completed 
 * successfully, the function returns a failure status with details. In case of an exception, the error details 
 * are logged, and a failure response is returned.
 * 
 * Parameters:
 * - p_db_file_id (TEXT): The unique identifier of the database file to be cleaned up.
 * 
 * Returns:
 * - JSONB: A JSON object containing the status of the cleanup operation. Possible keys include:
 *   - `status`: Indicates success or failure of the operation.
 *   - `message`: A descriptive message about the operation result.
 *   - `db_id`: The database file ID provided as input.
 *   - `study_metadata_status` (optional): Status of the study metadata rollback process.
 *   - `participant_data_status` (optional): Status of the participant data rollback process.
 *   - `cgm_data_status` (optional): Status of the CGM data rollback process.
 *   - `error_details` (optional): Detailed error information in case of an exception.
 * 
 * Behavior:
 * 1. Checks if the provided `db_file_id` exists in the `drh_stateful_raw_data.cgm_raw_db` table.
 * 2. Retrieves the statuses of the study metadata, participant data, and CGM data rollback processes.
 * 3. If all statuses are 'SUCCESS', deletes related entries from the following tables:
 *    - `drh_stateful_db_import_migration.file_meta_ingest_data`
 *    - `drh_stateful_db_import_migration.participant`
 *    - `drh_stateful_db_import_migration.participant_meal_fitness_data`
 *    - `drh_stateful_db_import_migration.study_meta_data`
 * 4. If any status is not 'SUCCESS', returns a failure response with the statuses of the processes.
 * 5. Handles exceptions by logging error details in the `drh_stateful_activity_audit.exception_log` table 
 *    and returning a failure response with error details.
 * 
 * Exceptions:
 * - Captures all exceptions and logs the error details, including the function name, error code, message, 
 *   context, and query parameters.
 * 
 * Security:
 * - The function is defined with `SECURITY DEFINER`, meaning it executes with the privileges of the function's owner.
 * 
 * Usage:
 * Call this function with a valid `db_file_id` to clean up related database entries after ensuring all 
 * rollback processes have completed successfully.
 */

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.cleanup_db_upload_entries(p_db_file_id TEXT); 
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.cleanup_db_upload_entries(p_db_file_id TEXT, p_interaction_id TEXT);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.cleanup_db_upload_entries(p_db_file_id TEXT, p_interaction_id TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE    
    study_metadata_status TEXT;
    participant_data_status TEXT;
    cgm_data_status TEXT;    
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.cleanup_db_upload_entries';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure
    result := jsonb_build_object('status', 'failure', 'message', 'Cleanup failed', 'db_id', p_db_file_id);
    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );

    -- Check if the DB file ID exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_raw_data.cgm_raw_db
        WHERE db_file_id = p_db_file_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'DB file ID not found',
            'db_id', p_db_file_id
        );
    END IF;

    -- Initialize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'DB CLEANUP',
        'interaction_status', 'IN PROGRESS',
        'description', 'Cleanup process started',
        'db_file_id', p_db_file_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Failed to insert file interaction',
            'error_details', file_interaction_result -> 'error_details'
        );
    END IF;

    -- Extract individual statuses
    study_metadata_status := (
        SELECT 'SUCCESS' 
        FROM drh_stateful_activity_audit.file_interaction 
        WHERE db_file_id = p_db_file_id AND interaction_action_type_id = (SELECT id FROM drh_stateful_master.interaction_action_type WHERE title = 'STUDY METADATA MIGRATION' LIMIT 1)
        AND interaction_status_id = (SELECT id FROM drh_stateful_master.interaction_status  WHERE title = 'SUCCESS' LIMIT 1)
        ORDER BY created_at DESC LIMIT 1
    );

    participant_data_status := (
        SELECT 'SUCCESS' 
        FROM drh_stateful_activity_audit.file_interaction 
        WHERE db_file_id = p_db_file_id AND interaction_action_type_id = (SELECT id FROM drh_stateful_master.interaction_action_type WHERE title = 'PARTICIPANT MIGRATION' LIMIT 1)
        AND interaction_status_id = (SELECT id FROM drh_stateful_master.interaction_status  WHERE title = 'SUCCESS' LIMIT 1)
        ORDER BY created_at DESC LIMIT 1
    );

    cgm_data_status := (
        SELECT 'SUCCESS' 
        FROM drh_stateful_activity_audit.file_interaction 
        WHERE db_file_id = p_db_file_id AND interaction_action_type_id = (SELECT id FROM drh_stateful_master.interaction_action_type WHERE title = 'CGM MIGRATION' LIMIT 1)
        AND interaction_status_id = (SELECT id FROM drh_stateful_master.interaction_status  WHERE title = 'SUCCESS' LIMIT 1)
        ORDER BY created_at DESC LIMIT 1
    );   

    -- Ensure all statuses are 'SUCCESS'
    IF study_metadata_status = 'SUCCESS' AND participant_data_status = 'SUCCESS' AND cgm_data_status = 'SUCCESS' THEN
        -- Delete entries from related tables
        DELETE FROM drh_stateful_db_import_migration.file_meta_ingest_data WHERE db_file_id = p_db_file_id;
        DELETE FROM drh_stateful_db_import_migration.participant WHERE db_file_id = p_db_file_id;
        DELETE FROM drh_stateful_db_import_migration.participant_meal_fitness_data WHERE db_file_id = p_db_file_id;
        DELETE FROM drh_stateful_db_import_migration.study_meta_data WHERE db_file_id = p_db_file_id;

        -- Finalize interaction log
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'DB CLEANUP',
            'interaction_status', 'SUCCESS',
            'description', 'Cleanup process completed',
            'db_file_id', p_db_file_id,
            'file_category', 'Database',
            'created_by', NULL
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
            result := jsonb_build_object(
                'status', 'success',
                'message', 'Cleanup completed successfully',
                'db_id', p_db_file_id,
                'file_interaction_id', completed_file_interaction_id
            );
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;
    ELSE
        -- Finalize interaction log with failure
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'DB CLEANUP',
            'interaction_status', 'FAILED',
            'description', 'Cleanup process failed',
            'db_file_id', p_db_file_id,
            'file_category', 'Database',
            'created_by', NULL
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        -- Return failure if any status is not 'SUCCESS'
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Not all processes completed successfully',
            'db_id', p_db_file_id,
            'study_metadata_status', study_metadata_status,
            'participant_data_status', participant_data_status,
            'cgm_data_status', cgm_data_status
        );
    END IF;

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Finalize interaction log with failure
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'DB CLEANUP',
        'interaction_status', 'FAILED',
        'description', 'Cleanup process failed',
        'db_file_id', p_db_file_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during cleanup', 'error_details', error_details_json);
END;
$function$;

--------------------------------------------------------------------------------------------------------------

DROP TABLE IF EXISTS drh_stateful_db_import_migration.nutrition_intake_temp;
CREATE TABLE IF NOT EXISTS drh_stateful_db_import_migration.nutrition_intake_temp AS TABLE drh_stateful_research_study.nutrition_intake WITH NO DATA;

DROP TABLE IF EXISTS drh_stateful_db_import_migration.observation_fitness_temp;
CREATE TABLE IF NOT EXISTS drh_stateful_db_import_migration.observation_fitness_temp AS TABLE drh_stateful_research_study.observation_fitness WITH NO DATA;

DROP TABLE IF EXISTS drh_stateful_db_import_migration.observation_fitness_component_temp;
CREATE TABLE IF NOT EXISTS drh_stateful_db_import_migration.observation_fitness_component_temp AS TABLE drh_stateful_research_study.observation_fitness_component WITH NO DATA;


--------------------------------------------------------------------------------------------------------------

/*
Function: drh_stateless_db_import_migration.migrate_individual_meal_data

Description:
This function migrates individual meal data for a participant into the database. It validates the input parameters, processes each meal entry in the provided JSONB array, and inserts the data into the `nutrition_intake` table. The function also handles errors gracefully by logging them into an exception log table and returning a detailed error response.

Parameters:
- p_study_id (TEXT): The study identifier.
- p_tenant_id (TEXT): The tenant identifier.
- p_participant_display_id (TEXT): The display ID of the participant.
- p_meal_data (JSONB): A JSONB array containing meal data entries.
- p_created_by (TEXT): The user who initiated the migration.

Returns:
- JSONB: A JSONB object indicating the status of the operation. It contains:
    - `status`: Either 'success' or 'failure'.
    - `message`: A message describing the outcome.
    - `participant_display_id`: The participant's display ID (on success).
    - `error_details`: Detailed error information (on failure).

Behavior:
1. Validates mandatory input parameters. If any are NULL, returns a failure response with details.
2. Fetches the participant ID using the provided `p_participant_display_id`.
3. Retrieves the active record status ID.
4. Iterates through the `p_meal_data` JSONB array:
     - Validates `meal_type_id` and `value_unit_id` for each meal entry.
     - Inserts valid meal data into the `nutrition_intake` table.
     - Logs notices for invalid meal types or units of measurement.
5. Handles exceptions during meal data insertion by logging the error and continuing with the next entry.
6. Returns a success response if all entries are processed without critical errors.

Error Handling:
- Captures and logs detailed error information, including the function name, error code, message, details, hint, context, query, and parameters.
- Returns a failure response with the error details in JSONB format.

Security:
- The function is defined with `SECURITY DEFINER`, meaning it executes with the privileges of the function owner.

Usage Notes:
- Ensure that the `p_meal_data` JSONB array contains valid meal entries with the required fields (`meal_type`, `meal_time`, `calories`, etc.).
- The function assumes that the `participant_data_view`, `meal_type`, `unit_of_measurement`, and other referenced tables are correctly populated and accessible.
- Proper error handling and logging mechanisms are in place to aid debugging and auditing.
*/

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_individual_meal_data(
    text, text, text, jsonb, text
);


CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_individual_meal_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_meal_data jsonb, p_created_by text,p_obs_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_individual_meal_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_participant_id TEXT;
    v_meal_id TEXT;
    v_rec_status_id INT;
    meal_entry JSONB;
    meal_entry_index INT := 0;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data migration');
    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_tenant_id', p_tenant_id,
    'p_participant_display_id', p_participant_display_id,
    'p_meal_data', p_meal_data,
    'p_created_by', p_created_by,
    'p_obs_id',p_obs_id
);


    -- Validate mandatory fields
    IF p_study_id IS NULL OR p_tenant_id IS NULL OR p_participant_display_id IS NULL OR p_meal_data IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'One or more required parameters are NULL',
            'details', jsonb_build_object(
                'p_study_id', p_study_id,
                'p_tenant_id', p_tenant_id,
                'p_participant_display_id', p_participant_display_id,
                'p_meal_data', p_meal_data
            )
        );
    END IF;

    -- Fetch participant ID
    SELECT participant_id INTO v_participant_id
    FROM drh_stateless_research_study.participant_data_view
    WHERE participant_display_id = p_participant_display_id AND study_id = p_study_id
    LIMIT 1;

    IF v_participant_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant not found',
            'participant_display_id', p_participant_display_id
        );
    END IF;

    -- Get active record status ID
    SELECT value INTO v_rec_status_id
    FROM drh_stateful_party.record_status
    WHERE code = 'ACTIVE'
    LIMIT 1;

    -- Iterate through meal data entries
    FOR meal_entry IN
        SELECT * FROM jsonb_array_elements(p_meal_data)
    LOOP
        BEGIN
            meal_entry_index := meal_entry_index + 1;

            -- Validate meal_type_id and value_unit_id
            IF (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid meal type for participant % at index %: %', p_participant_display_id, meal_entry_index, meal_entry->>'meal_type';
                CONTINUE;
            END IF;

            IF (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid unit of measurement for participant % at index %: calories', p_participant_display_id, meal_entry_index;
                CONTINUE;
            END IF;

            v_meal_id := drh_stateless_util.get_unique_id()::TEXT;

            -- Insert meal data
            INSERT INTO drh_stateful_db_import_migration.nutrition_intake_temp (
                id,
                status_code,
                study_id,
                subject_id,
                occurrence_time,
                meal_type_id,
                value_quantity,
                value_unit_id
            )
            VALUES (
                v_meal_id,
                (SELECT id FROM drh_stateful_master.nutrition_intake_status_code WHERE code = 'unknown' LIMIT 1),
                p_study_id,
                v_participant_id,
                TO_TIMESTAMP(meal_entry->>'meal_time', 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
                (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1),
                (meal_entry->>'calories')::FLOAT8,
                (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1)
            );


            INSERT INTO drh_stateful_research_study.nutritionintake_mapping (
		    id, 
		    nutrition_mapping_id, 
		    observation_mapping_id, 
		    r_subject_id, 
		    study_id, 
		    tenant_id,
		    rec_status_id, 
		    created_at, 
		    created_by, 
		    updated_at, 
		    updated_by, 
		    deleted_at,
		    deleted_by
			) 
			VALUES (
		    drh_stateless_util.get_unique_id(), 
		    v_meal_id, 
		    p_obs_id, 
		    v_participant_id, 
		    p_study_id, 
		    p_tenant_id, 
		    (
		        SELECT rs.value 
		        FROM drh_stateful_party.record_status rs 
		        WHERE rs.code = 'ACTIVE' 
		        LIMIT 1
		    ), 
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
		    p_created_by, 
		    NULL, 
		    NULL, 
		    NULL, 
		    NULL
			);

            RAISE NOTICE 'Inserted meal data for participant %: Meal ID: %, Index: %', p_participant_display_id, v_meal_id, meal_entry_index;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting meal data for participant % at index %: %', p_participant_display_id, meal_entry_index, SQLERRM;
        END;
    END LOOP;

    -- Set result to success
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Meal data migrated successfully',
        'participant_display_id', p_participant_display_id
    );

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data migration', 'error_details', error_details_json);
END;
$function$
;

/**
 * Function: drh_stateless_db_import_migration.migrate_all_meal_data
 * 
 * Description:
 * This function performs the migration of meal data from a source database to a target database.
 * It processes meal records associated with a given database file ID (`p_db_file_id`) and logs the 
 * migration progress and results. The function ensures idempotency and handles errors gracefully.
 * 
 * Parameters:
 * - `p_db_file_id` (TEXT): The unique identifier of the database file containing meal data to be migrated.
 * - `p_interaction_id` (TEXT): The identifier of the last file interaction, used for logging purposes.
 * 
 * Returns:
 * - JSONB: A JSON object containing the status of the migration, a message, lists of successful and failed records, 
 *   the database file ID, and the file interaction ID (if applicable).
 * 
 * Workflow:
 * 1. Validates the input parameters (`p_db_file_id` and `p_interaction_id`).
 * 2. Fetches study and tenant details associated with the database file ID.
 * 3. Logs the start of the migration process using `save_file_interaction_log`.
 * 4. Iterates over meal records from the source table (`participant_meal_fitness_data`) and processes each record:
 *    - Calls `migrate_individual_meal_data` to migrate individual meal data.
 *    - Tracks successful and failed migrations.
 * 5. Logs the completion of the migration process with a success or failure status.
 * 6. Handles exceptions and logs errors, including detailed diagnostic information.
 * 
 * Exceptions:
 * - Captures and logs any errors that occur during the migration process, including SQL errors and unexpected exceptions.
 * - Updates the file interaction log with a failure status in case of errors.
 * 
 * Notes:
 * - The function uses `SECURITY DEFINER` to execute with the privileges of the function owner.
 * - The migration process is idempotent, ensuring that records are not duplicated or corrupted.
 * - Diagnostic information is captured using `GET STACKED DIAGNOSTICS` for detailed error reporting.
 * 
 * Dependencies:
 * - `drh_stateless_raw_data.cgm_raw_db_view`: View used to fetch study and tenant details.
 * - `drh_stateless_raw_observation.save_file_interaction_log`: Function used to log file interactions.
 * - `drh_stateless_db_import_migration.migrate_individual_meal_data`: Function used to migrate individual meal data.
 * - `drh_stateful_db_import_migration.participant_meal_fitness_data`: Source table containing meal data to be migrated.
 */

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_all_meal_data(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_all_meal_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    meal_record RECORD;
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    successful_records TEXT[] := '{}';
    failed_records TEXT[] := '{}';
    no_data_records TEXT[] := '{}';
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_all_meal_data';
    current_query TEXT := pg_catalog.current_query();
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    result JSONB;
    v_file_interaction_params JSONB;
    meal_migration_result JSONB;
    p_participant_id text;
    v_obs_upload_id text;
    v_obs_extract_id text;
    v_response text;
    v_error_response text;
    v_interaction_status  text;
    v_description text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting meal data migration for db_file_id: %', p_db_file_id;
    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );

    result := jsonb_build_object(
        'status', 'failure',
        'message', 'DB meal records migration failure',
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        -- Fetch study and tenant details
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;

        -- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', p_interaction_id,
            'interaction_action_type', 'MEAL MIGRATION',
            'interaction_status', 'IN PROGRESS',
            'description', 'Meal data migration started',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        -- Extract file_interaction_id from result
        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        -- Iterate over meal records for migration
        FOR meal_record IN
		    SELECT participant_display_id, meal_data, meal_file_metadata 
		    FROM drh_stateful_db_import_migration.participant_meal_fitness_data
		    WHERE db_file_id = p_db_file_id
		LOOP
		    BEGIN
		        -- Check if meal_data is NOT NULL and NOT empty array
		        IF meal_record.meal_data IS NOT NULL AND meal_record.meal_data::text != '[]' THEN
		
		            SELECT rs.rsubject_id
		            INTO p_participant_id
		            FROM drh_stateful_research_study.research_subject rs
		            WHERE rs.participant_identifier = meal_record.participant_display_id
		            LIMIT 1;			
		
		            INSERT INTO drh_stateful_raw_data.subject_observation_upload_data (
		            id, 
				    file_name,
				    file_url,
				    zip_file_id,
				    file_content_type_id,
				    raw_data_json,
				    upload_timestamp,
				    uploaded_by,
				    file_size,
				    is_processed,
				    processed_at,
				    status,
				    file_metadata,
				    file_type,
				    study_id,
				    tenant_id,
				    database_id,
				    rec_status_id,
				    created_at,
				    created_by,
				    updated_at,
				    updated_by,
				    deleted_at,
				    deleted_by,
				    raw_data_csv,
				    raw_data_excel,
				    raw_data_xml,
				    raw_data_text,
				    file_interaction_id
		            ) VALUES (
		                drh_stateless_util.get_unique_id(),
		                (meal_record.meal_file_metadata::jsonb) -> 0 ->> 'file_name',
		                '',
		                '',
		                (SELECT id FROM drh_stateful_master.file_content_type t WHERE t.title = 'Meals' LIMIT 1),
		                (meal_record.meal_data::jsonb),
		                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		                p_created_by,
		                '',
		                FALSE,
		                NULL,
		                '',
		                (meal_record.meal_file_metadata::jsonb),
		                (meal_record.meal_file_metadata::jsonb) -> 0 ->> 'file_format',
		                p_study_id,
		                p_tenant_id,
		                p_db_file_id,
		                (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		                p_created_by,
		                NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
		            )
		            RETURNING id INTO v_obs_upload_id;
		
		            INSERT INTO drh_stateful_raw_data.subject_observation_extract_data (
		                id,
					    subject_observation_upload_id,
					    study_id,
					    participant_sid,
					    file_content_type_id,
					    subject_observation_data_json,
					    file_url,
					    file_meta_data,
					    subject_observation_data,
					    tenant_id,
					    rec_status_id,
					    created_at,
					    created_by,
					    updated_at,
					    updated_by,
					    deleted_at,
					    deleted_by,
					    raw_data_csv,
					    raw_data_excel,
					    raw_data_xml,
					    raw_data_text,
					    file_interaction_id
		            ) VALUES (
		                drh_stateless_util.get_unique_id(),
		                v_obs_upload_id,
		                p_study_id,
		                p_participant_id,
		                (SELECT id FROM drh_stateful_master.file_content_type t WHERE t.title = 'Meals' LIMIT 1),
		                (meal_record.meal_data::jsonb),
		                '',
		                (meal_record.meal_file_metadata::jsonb),
		                (meal_record.meal_data::jsonb),
		                p_tenant_id,
		                (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		                p_created_by,
		                NULL, NULL, NULL, NULL, NULL, NULL, NULL, null,NULL
		            )
		            RETURNING id INTO v_obs_extract_id;
		
		            -- Call individual meal data migration function
		            meal_migration_result := drh_stateless_db_import_migration.migrate_individual_meal_data(
		                p_study_id,
		                p_tenant_id,
		                meal_record.participant_display_id,
		                meal_record.meal_data::JSONB,
		                p_created_by,
                        v_obs_extract_id
		            );
		
		            IF meal_migration_result IS NOT NULL AND meal_migration_result->>'status' = 'success' THEN
		                successful_records := array_append(successful_records, meal_record.participant_display_id);
                        UPDATE drh_stateful_raw_data.subject_observation_upload_data 
			            SET is_processed = true,
			                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
			                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1)
			            WHERE id = v_obs_upload_id;
		            ELSE
		                failed_records := array_append(failed_records, meal_record.participant_display_id);
                        UPDATE drh_stateful_raw_data.subject_observation_upload_data 
			            SET is_processed = true,
			                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
			                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1)
			            WHERE id = v_obs_upload_id;
		            END IF;
		
		        ELSE
		            -- Meal data is missing or empty
		            RAISE NOTICE 'No meal data for participant %', meal_record.participant_display_id;
		            no_data_records := array_append(no_data_records, meal_record.participant_display_id);
		        END IF;
		
		    EXCEPTION WHEN OTHERS THEN
		        RAISE NOTICE 'Error processing meal data for participant %: %', meal_record.participant_display_id, SQLERRM;
		        failed_records := array_append(failed_records, meal_record.participant_display_id);
		    END;
		END LOOP;

	
		-- Determine interaction status and description based on failures
			IF array_length(failed_records, 1) IS NOT NULL AND array_length(failed_records, 1) > 0 THEN
			    v_interaction_status := 'PARTIAL SUCCESS';
			    v_description := format('Meal data migration completed with %s failed records.', array_length(failed_records, 1));
			    v_response := successful_records;
			    v_error_response := failed_records;
			ELSE
			    v_interaction_status := 'SUCCESS';
			    v_description := 'Meal data migration completed';
			    v_response := successful_records;
			    v_error_response := NULL;
			END IF;

        -- Finalize interaction log
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'MEAL MIGRATION',
            'interaction_status', v_interaction_status,
			'description', v_description,
			'db_file_id', p_db_file_id,
		    'file_name', p_file_name,
			'file_category', 'Database',
			'created_by', p_created_by,
			'response', v_response,
			'error_response', v_error_response
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        -- Extract file_interaction_id from result
        IF file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        RAISE NOTICE 'Meal data migration completed. Successful: %, Failed: %', array_length(successful_records, 1), array_length(failed_records, 1);

        result := jsonb_build_object(
            'status', 'success',
            'message', 'DB meal records migrated successfully',
            'successful_records', successful_records,
            'failed_records', failed_records,
            'no_data_records',no_data_records,
            'db_id', p_db_file_id,
            'file_interaction_id', completed_file_interaction_id
        );
    END IF;

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Finalize interaction log with failure
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'MEAL DATA MIGRATION',
        'interaction_status', 'FAILED',
        'description', 'Meal data migration failed',
        'db_file_id', p_db_file_id,
        'file_name', p_file_name,
        'file_category', 'Database',
        'created_by', NULL
    );
    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data migration', 'error_details', error_details_json);
END;
$function$
;



/**
 * Function: drh_stateless_db_import_migration.migrate_individual_fitness_data
 * 
 * Description:
 * This function migrates individual fitness data for a participant into the database. It validates the input parameters, 
 * fetches the participant ID, and iterates through the provided fitness data JSONB array to insert fitness observations 
 * and their components into the respective tables. The function also handles errors gracefully by logging them and 
 * returning a detailed error response.
 * 
 * Parameters:
 * - p_study_id (TEXT): The study ID associated with the fitness data.
 * - p_tenant_id (TEXT): The tenant ID associated with the fitness data.
 * - p_participant_display_id (TEXT): The display ID of the participant whose fitness data is being migrated.
 * - p_fitness_data (JSONB): A JSONB array containing fitness data entries. Each entry may include fields like 
 *   `activity_time`, `steps`, `exercise_minutes`, and `calories_burned`.
 * - p_created_by (TEXT): The user or system identifier responsible for creating the records.
 * 
 * Returns:
 * - JSONB: A JSONB object indicating the status of the migration. Possible statuses:
 *   - `success`: Indicates successful migration of fitness data.
 *   - `failure`: Indicates an error occurred during migration, with detailed error information.
 * 
 * Behavior:
 * 1. Validates that all mandatory parameters are provided.
 * 2. Fetches the participant ID using the provided `p_participant_display_id`.
 * 3. Retrieves the active record status ID.
 * 4. Iterates through the `p_fitness_data` array and performs the following for each entry:
 *    - Generates a unique observation ID.
 *    - Inserts a record into the `observation_fitness` table.
 *    - Inserts related fitness component data (e.g., steps, exercise minutes, calories burned) into the 
 *      `observation_fitness_component` table if the corresponding fields are present in the entry.
 * 5. Logs a notice for each successfully inserted fitness data entry.
 * 6. Handles errors during the insertion process by logging the error details and continuing with the next entry.
 * 7. Returns a success response if all entries are processed successfully, or a failure response with error details 
 *    if an exception occurs.
 * 
 * Error Handling:
 * - Captures and logs detailed error information, including the function name, error code, message, detail, hint, 
 *   context, query, and parameters, into the `exception_log` table.
 * - Returns a failure response with the error details in JSONB format.
 * 
 * Security:
 * - The function is defined with `SECURITY DEFINER` to execute with the privileges of the function owner.
 * 
 * Notes:
 * - The function assumes the existence of several supporting tables and utility functions, such as 
 *   `drh_stateless_util.get_unique_id`, and specific master data tables for observation status, categories, 
 *   activity types, component types, units of measurement, and observation methods.
 * - The function uses `pg_catalog.current_query()` to capture the current query for error logging purposes.
 */

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_individual_fitness_data(
    TEXT, TEXT, TEXT, JSONB, TEXT
);



CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_individual_fitness_data(
    p_study_id TEXT,
    p_tenant_id TEXT,
    p_participant_display_id TEXT,
    p_fitness_data JSONB,
    p_created_by TEXT,
    p_obs_id text
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_individual_fitness_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_participant_id TEXT;
    v_observation_id TEXT;
    v_rec_status_id INT;
    fitness_entry JSONB;
    fitness_entry_index INT := 0;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during fitness data migration');
    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_tenant_id', p_tenant_id,
    'p_participant_display_id', p_participant_display_id,
    'p_fitness_data', p_fitness_data,
    'p_created_by', p_created_by,
    'p_obs_id',p_obs_id
    );



    -- Validate mandatory fields
    IF p_study_id IS NULL OR p_tenant_id IS NULL OR p_participant_display_id IS NULL OR p_fitness_data IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'One or more required parameters are NULL',
            'details', jsonb_build_object(
                'p_study_id', p_study_id,
                'p_tenant_id', p_tenant_id,
                'p_participant_display_id', p_participant_display_id,
                'p_fitness_data', p_fitness_data
            )
        );
    END IF;

    -- Fetch participant ID
    SELECT participant_id INTO v_participant_id
    FROM drh_stateless_research_study.participant_data_view
    WHERE participant_display_id = p_participant_display_id AND study_id = p_study_id
    LIMIT 1;

    IF v_participant_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant not found',
            'participant_display_id', p_participant_display_id
        );
    END IF;

    -- Get active record status ID
    SELECT value INTO v_rec_status_id
    FROM drh_stateful_party.record_status
    WHERE code = 'ACTIVE'
    LIMIT 1;

    -- Iterate through fitness data entries
    FOR fitness_entry IN
        SELECT * FROM jsonb_array_elements(p_fitness_data)
    LOOP
        BEGIN
            fitness_entry_index := fitness_entry_index + 1;

            v_observation_id := drh_stateless_util.get_unique_id();

            -- Insert fitness data
            INSERT INTO drh_stateful_db_import_migration.observation_fitness_temp (
                observation_id,
                subject_id,
                study_id,
                effective_datetime,
                status_id,
                category_id,
                activity_type_id,                
                rec_status_id,
                created_at,
                created_by
            )
            VALUES (
                v_observation_id,
                v_participant_id,
                p_study_id,
                TO_TIMESTAMP(fitness_entry->>'date', 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
                (SELECT id FROM drh_stateful_master.observation_status WHERE code = 'unknown' LIMIT 1),
                (SELECT category_id FROM drh_stateful_master.observation_category WHERE LOWER(display) = 'activity' LIMIT 1),
                (SELECT activity_id FROM drh_stateful_master.activity_master WHERE LOWER(display) = 'physical activity' LIMIT 1),
                v_rec_status_id,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_created_by
            );

            -- Insert fitness component data for steps
            IF fitness_entry->>'steps' IS NOT NULL AND fitness_entry->>'steps' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Steps Count') LIMIT 1),
                    (fitness_entry->>'steps')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Steps') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Pedometer') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
                
            END IF;

            -- Insert fitness component data for exercise_minutes
            IF fitness_entry->>'exercise_minutes' IS NOT NULL AND fitness_entry->>'exercise_minutes' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Duration') LIMIT 1),
                    (fitness_entry->>'exercise_minutes')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Minutes') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Manual Entry') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;
            
            -- Insert fitness component data for calories_burned
            IF fitness_entry->>'calories_burned' IS NOT NULL AND fitness_entry->>'calories_burned' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Calories Burned') LIMIT 1),
                    (fitness_entry->>'calories_burned')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Kilocalories') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Heart Rate Sensor') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;

            -- Insert fitness component data for distance
            IF fitness_entry->>'distance' IS NOT NULL AND fitness_entry->>'distance' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Distance') LIMIT 1),
                    (fitness_entry->>'distance')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Kilometers') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Pedometer') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;

            -- Insert fitness component data for heart_rate
            IF fitness_entry->>'heart_rate' IS NOT NULL AND fitness_entry->>'heart_rate' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Heart Rate (BPM)') LIMIT 1),
                    (fitness_entry->>'heart_rate')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Beats per Minute') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Heart Rate Sensor') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;

            INSERT INTO drh_stateful_research_study.fitness_mapping (
		    id, 
		    fitness_mapping_id, 
		    observation_mapping_id, 
		    r_subject_id, 
		    study_id, 
		    tenant_id,
		    rec_status_id, 
		    created_at, 
		    created_by, 
		    updated_at, 
		    updated_by, 
		    deleted_at,
		    deleted_by
			) 
			VALUES (
		    drh_stateless_util.get_unique_id(), 
		    v_observation_id, 
		    p_obs_id, 
		    v_participant_id, 
		    p_study_id, 
		    p_tenant_id, 
		    (
		        SELECT rs.value 
		        FROM drh_stateful_party.record_status rs 
		        WHERE rs.code = 'ACTIVE' 
		        LIMIT 1
		    ), 
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
		    p_created_by, 
		    NULL, 
		    NULL, 
		    NULL, 
		    NULL
			);

            RAISE NOTICE 'Inserted fitness data for participant %: Index: %', p_participant_display_id, fitness_entry_index;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting fitness data for participant % at index %: %', p_participant_display_id, fitness_entry_index, SQLERRM;
        END;
    END LOOP;

    -- Set result to success
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Fitness data migrated successfully',
        'participant_display_id', p_participant_display_id
    );

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;
                
    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during fitness data migration', 'error_details', error_details_json);
END;
$function$;

/**
 * Function: drh_stateless_db_import_migration.migrate_all_fitness_data
 * 
 * Description:
 * This function performs the migration of fitness data from a database file to a target system.
 * It processes fitness records associated with a given database file ID (`p_db_file_id`) and logs
 * the migration progress and results. The function ensures idempotency and handles errors gracefully.
 * 
 * Parameters:
 * - `p_db_file_id` (TEXT): The unique identifier of the database file containing fitness data to be migrated.
 * - `p_interaction_id` (TEXT): The identifier of the last file interaction, used for logging purposes.
 * 
 * Returns:
 * - JSONB: A JSON object containing the migration status, message, successful records, failed records,
 *   database file ID, and file interaction ID (if applicable).
 * 
 * Workflow:
 * 1. Validates the input parameters (`p_db_file_id` and `p_interaction_id`).
 * 2. Fetches study, tenant, and file details associated with the given database file ID.
 * 3. Logs the start of the migration process using `save_file_interaction_log`.
 * 4. Iterates over fitness records in the source table and calls the `migrate_individual_fitness_data`
 *    function for each record.
 * 5. Tracks successful and failed migrations for each participant.
 * 6. Logs the completion of the migration process and updates the interaction log.
 * 7. Returns a JSON object summarizing the migration results.
 * 
 * Error Handling:
 * - Captures and logs errors during the migration process.
 * - Updates the interaction log with a failure status if an error occurs.
 * - Returns a JSON object with error details, including the error message, context, and SQL state.
 * 
 * Dependencies:
 * - `drh_stateless_raw_data.cgm_raw_db_view`: View used to fetch study and tenant details.
 * - `drh_stateless_raw_observation.save_file_interaction_log`: Function used to log file interactions.
 * - `drh_stateless_db_import_migration.migrate_individual_fitness_data`: Function used to migrate individual fitness data.
 * - `drh_stateful_db_import_migration.participant_meal_fitness_data`: Source table containing fitness records.
 * 
 * Notes:
 * - The function uses `SECURITY DEFINER` to execute with the privileges of the function owner.
 * - The function ensures idempotency by logging the migration progress and results.
 * - The function handles exceptions using the `WHEN OTHERS` clause to capture and log error details.
 */

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.migrate_all_fitness_data(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.migrate_all_fitness_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    fitness_record RECORD;
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    successful_records TEXT[] := '{}';
    failed_records TEXT[] := '{}';
    no_data_records TEXT[] := '{}';
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.migrate_all_fitness_data';
    current_query TEXT := pg_catalog.current_query();
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    result JSONB;
    v_file_interaction_params JSONB;
    fitness_migration_result JSONB;
    p_participant_id text;
    v_obs_upload_id text;
    v_obs_extract_id text;
    v_response text;
    v_error_response text;
    v_interaction_status  text;
    v_description text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting fitness data migration for db_file_id: %', p_db_file_id;
    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );


    result := jsonb_build_object(
        'status', 'failure',
        'message', 'DB fitness records migration failure',
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        -- Fetch study and tenant details
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;

        -- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', p_interaction_id,
            'interaction_action_type', 'FITNESS MIGRATION',
            'interaction_status', 'IN PROGRESS',
            'description', 'Fitness data migration started',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        -- Extract file_interaction_id from result
        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        -- Iterate over fitness records for migration
        FOR fitness_record IN
		    SELECT participant_display_id, fitness_data, fitness_file_metadata 
		    FROM drh_stateful_db_import_migration.participant_meal_fitness_data
		    WHERE db_file_id = p_db_file_id
		LOOP
		    begin
			    
			    
		        IF fitness_record.fitness_data IS NOT NULL AND fitness_record.fitness_data != '[]' THEN
			    
			    SELECT rs.rsubject_id
				INTO p_participant_id
				FROM drh_stateful_research_study.research_subject rs
				WHERE rs.participant_identifier = fitness_record.participant_display_id
				LIMIT 1;			
		
	               
            INSERT INTO drh_stateful_raw_data.subject_observation_upload_data (
				    id, 
				    file_name,
				    file_url,
				    zip_file_id,
				    file_content_type_id,
				    raw_data_json,
				    upload_timestamp,
				    uploaded_by,
				    file_size,
				    is_processed,
				    processed_at,
				    status,
				    file_metadata,
				    file_type,
				    study_id,
				    tenant_id,
				    database_id,
				    rec_status_id,
				    created_at,
				    created_by,
				    updated_at,
				    updated_by,
				    deleted_at,
				    deleted_by,
				    raw_data_csv,
				    raw_data_excel,
				    raw_data_xml,
				    raw_data_text,
				    file_interaction_id
				) VALUES (
				    drh_stateless_util.get_unique_id(),                             
				    (fitness_record.fitness_file_metadata::jsonb) -> 0 ->> 'file_name',
				    '',                             
				    '',                            
				    (select id from drh_stateful_master.file_content_type t where t.title ='Fitness' limit 1),  
				    fitness_record.fitness_data::jsonb,             
				    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',   
				    p_created_by,                             
				    '',                             
				    FALSE,                         
				    NULL,                          
				    '',                             
				    fitness_record.fitness_file_metadata::jsonb, 
				    (fitness_record.fitness_file_metadata::jsonb) -> 0 ->>'file_format',      
				    p_study_id,                     
				    p_tenant_id,                             
				    p_db_file_id,                             
				    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),                            
				    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',             
				    p_created_by,                
				    NULL,                          
				    NULL,                           
				    NULL,                          
				    NULL,                           
				    NULL,                              
				    NULL,                              
				    NULL,                             
				    NULL,                             
				    NULL                              
				)
				RETURNING id INTO v_obs_upload_id;


            -- Insert into extract table
            INSERT INTO drh_stateful_raw_data.subject_observation_extract_data (
			    id,
			    subject_observation_upload_id,
			    study_id,
			    participant_sid,
			    file_content_type_id,
			    subject_observation_data_json,
			    file_url,
			    file_meta_data,
			    subject_observation_data,
			    tenant_id,
			    rec_status_id,
			    created_at,
			    created_by,
			    updated_at,
			    updated_by,
			    deleted_at,
			    deleted_by,
			    raw_data_csv,
			    raw_data_excel,
			    raw_data_xml,
			    raw_data_text,
			    file_interaction_id
			) VALUES (
			    drh_stateless_util.get_unique_id(),                             
			    v_obs_upload_id,                            
			    p_study_id,                             
			    p_participant_id,                             
			    (select id from drh_stateful_master.file_content_type t where t.title ='Fitness' limit 1),                             
			    fitness_record.fitness_data::jsonb,                             
			    '',                            
			    fitness_record.fitness_file_metadata::jsonb,                            
			    fitness_record.fitness_data::jsonb,                            
			    p_tenant_id,                            
			   (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),                            
			    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
			    p_created_by,               
			    NULL,                           
			    NULL,                          
			    NULL,                          
			    NULL,                         
			    NULL,                              
			    NULL,                              
			    NULL,                           
			    NULL,                              
			    NULL                              
			)returning id into v_obs_extract_id;

		

                -- Call individual fitness data migration function
                fitness_migration_result := drh_stateless_db_import_migration.migrate_individual_fitness_data(
                    p_study_id,
                    p_tenant_id,
                    fitness_record.participant_display_id,
                    fitness_record.fitness_data::JSONB,
                    p_created_by,
                    v_obs_extract_id
                );

                -- Handle success or failure
                IF fitness_migration_result IS NOT NULL AND fitness_migration_result->>'status' = 'success' THEN
                    successful_records := array_append(successful_records, fitness_record.participant_display_id);
                    UPDATE drh_stateful_raw_data.subject_observation_upload_data 
			            SET is_processed = true,
			                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
			                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'COMPLETED' LIMIT 1)
			            WHERE id = v_obs_upload_id;
                ELSE
                    failed_records := array_append(failed_records, fitness_record.participant_display_id);
                    UPDATE drh_stateful_raw_data.subject_observation_upload_data 
			            SET is_processed = true,
			                processed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
			                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ERROR' LIMIT 1)
			            WHERE id = v_obs_upload_id;
                END IF;          
               
            ELSE
		           
		            RAISE NOTICE 'No fitness data for participant %', fitness_record.participant_display_id;
		            no_data_records := array_append(no_data_records, fitness_record.participant_display_id);
		    END IF;
		  
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Error processing fitness data for participant %: %', fitness_record.participant_display_id, SQLERRM;
                failed_records := array_append(failed_records, fitness_record.participant_display_id);
            END;           
        END LOOP;
       
       -- Determine interaction status and description based on failures
			IF array_length(failed_records, 1) IS NOT NULL AND array_length(failed_records, 1) > 0 THEN
			    v_interaction_status := 'PARTIAL SUCCESS';
			    v_description := format('Fitness data migration completed with %s failed records.', array_length(failed_records, 1));
			    v_response := successful_records;
			    v_error_response := failed_records;
			ELSE
			    v_interaction_status := 'SUCCESS';
			    v_description := 'Fitness data migration completed';
			    v_response := successful_records;
			    v_error_response := NULL;
			END IF;

        -- Finalize interaction log
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'FITNESS MIGRATION',
            'interaction_status', v_interaction_status,
			'description', v_description,
			'db_file_id', p_db_file_id,
			'file_name', p_file_name,
			'file_category', 'Database',
			'created_by', p_created_by,
			'response', v_response,
			'error_response', v_error_response
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        -- Extract file_interaction_id from result
        IF file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        RAISE NOTICE 'Fitness data migration completed. Successful: %, Failed: %', array_length(successful_records, 1), array_length(failed_records, 1);

        result := jsonb_build_object(
            'status', 'success',
            'message', 'DB fitness records migrated successfully',
            'successful_records', successful_records,
            'failed_records', failed_records,
            'no_data_records',no_data_records,
            'db_id', p_db_file_id,
            'file_interaction_id', completed_file_interaction_id
        );
       END IF;
    

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Finalize interaction log with failure
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'FITNESS DATA MIGRATION',
        'interaction_status', 'FAILED',
        'description', 'Fitness data migration failed',
        'db_file_id', p_db_file_id,
        'file_name', p_file_name,
        'file_category', 'Database',
        'created_by', NULL
    );
    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);


    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during fitness data migration', 'error_details', error_details_json);
END;
$function$
;




CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.create_meal_partition(IN v_participant_sid text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.create_meal_partition';
BEGIN
    EXECUTE format(
        $i$
        CREATE TABLE IF NOT EXISTS drh_stateful_research_study."nut_int_part_%1$s"
        (LIKE drh_stateful_research_study.nutrition_intake INCLUDING DEFAULTS INCLUDING CONSTRAINTS);
        $i$, v_participant_sid);
EXCEPTION 
    WHEN OTHERS THEN 
        -- Capture diagnostics
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Build parameter log
        parameters_lst := jsonb_build_object(
            'v_participant_sid', v_participant_sid
        );

        -- Build structured exception log
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log the exception
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        RAISE NOTICE 'Error creating partition for %', v_participant_sid;
END;
$procedure$
;

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.create_fitness_partition(IN v_participant_sid text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.create_fitness_partition';
BEGIN
    EXECUTE format(
        $i$
        CREATE TABLE IF NOT EXISTS drh_stateful_research_study."fit_part_%1$s"
        (LIKE drh_stateful_research_study.observation_fitness INCLUDING DEFAULTS INCLUDING CONSTRAINTS);
        $i$, v_participant_sid);

    EXECUTE format(
        $i$
        CREATE TABLE IF NOT EXISTS drh_stateful_research_study."fit_comp_part_%1$s"
        (LIKE drh_stateful_research_study.observation_fitness_component INCLUDING DEFAULTS INCLUDING CONSTRAINTS);
        $i$, v_participant_sid);
EXCEPTION 
    WHEN OTHERS THEN 
        -- Capture diagnostics
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Build parameter log
        parameters_lst := jsonb_build_object(
            'v_participant_sid', v_participant_sid
        );

        -- Build structured exception log
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log the exception
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        RAISE NOTICE 'Error creating partition for %', v_participant_sid;
END;
$procedure$
;



CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.copy_meal_partition(IN v_participant_sid text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    sql_query TEXT;
    num_copied BIGINT;    
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.copy_meal_partition';
BEGIN
    -- Correct SQL formatting
    sql_query := format(
        $i$
        INSERT INTO drh_stateful_research_study."nut_int_part_%1$s"
        SELECT * FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE subject_id = %L;
        $i$, v_participant_sid, v_participant_sid);
    
    -- Print the generated SQL for debugging
    RAISE NOTICE 'Executing SQL: %', sql_query;
    
    -- Execute the query
    EXECUTE sql_query;

    -- Get affected rows
    GET DIAGNOSTICS num_copied = ROW_COUNT;
    
    -- Print the number of copied rows
    RAISE NOTICE 'Copied % rows to %', num_copied, 
        format('drh_stateful_research_study."nut_int_part_%1$s"', v_participant_sid);
EXCEPTION 
    WHEN OTHERS THEN 
        -- Capture diagnostics
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Build parameter log
        parameters_lst := jsonb_build_object(
            'v_participant_sid', v_participant_sid
        );

        -- Build structured exception log
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log the exception
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        RAISE NOTICE 'Error: %', SQLERRM;
        num_copied := -1;
END;
$procedure$
;

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.copy_fitness_partition(IN v_participant_sid text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    sql_query TEXT;
    num_copied BIGINT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.copy_fitness_partition';
BEGIN
    -- Correct SQL formatting
    sql_query := format(
        $i$
        INSERT INTO drh_stateful_research_study."fit_part_%1$s"
        SELECT * FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE subject_id = %L;
        $i$, v_participant_sid, v_participant_sid);
    
    -- Print the generated SQL for debugging
    RAISE NOTICE 'Executing SQL: %', sql_query;
    
    -- Execute the query
    EXECUTE sql_query;

    -- Get affected rows
    GET DIAGNOSTICS num_copied = ROW_COUNT;
    
    -- Print the number of copied rows
    RAISE NOTICE 'Copied % rows to %', num_copied, 
        format('drh_stateful_research_study."fit_part_%1$s"', v_participant_sid);

    -- Correct SQL formatting
    sql_query := format(
        $i$
        INSERT INTO drh_stateful_research_study."fit_comp_part_%1$s"
        SELECT * FROM drh_stateful_db_import_migration.observation_fitness_component_temp
        WHERE subject_id = %L;
        $i$, v_participant_sid, v_participant_sid);
    
    -- Print the generated SQL for debugging
    RAISE NOTICE 'Executing SQL: %', sql_query;
    
    -- Execute the query
    EXECUTE sql_query;

    -- Get affected rows
    GET DIAGNOSTICS num_copied = ROW_COUNT;
    
    -- Print the number of copied rows
    RAISE NOTICE 'Copied % rows to %', num_copied, 
        format('drh_stateful_research_study."fit_comp_part_%1$s"', v_participant_sid);
EXCEPTION 
    WHEN OTHERS THEN
    -- Capture diagnostics
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Build parameter log
        parameters_lst := jsonb_build_object(
            'v_participant_sid', v_participant_sid
        );

        -- Build structured exception log
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log the exception
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        RAISE NOTICE 'Error: %', SQLERRM;
        num_copied := -1;
END;
$procedure$
;

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.index_and_attach_meal_partition(IN research_subject_id text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    success BOOLEAN;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.index_and_attach_meal_partition';
BEGIN
    BEGIN
        EXECUTE format(
        $i$        
        ALTER TABLE drh_stateful_research_study.nutrition_intake 
        ATTACH PARTITION drh_stateful_research_study."nut_int_part_%1$s" FOR VALUES IN (%L);
        $i$, research_subject_id, research_subject_id);

        success := TRUE;
    EXCEPTION        
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'research_subject_id', research_subject_id
            );

            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            RAISE NOTICE 'Error in index_and_attach_cgm_partition for %: %', research_subject_id, SQLERRM;
            success := FALSE;
    END;
END;
$procedure$
;

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.index_and_attach_fitness_partition(IN research_subject_id text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
    success BOOLEAN;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.index_and_attach_fitness_partition';
BEGIN
    BEGIN
        EXECUTE format(
        $i$        
        ALTER TABLE drh_stateful_research_study.observation_fitness 
        ATTACH PARTITION drh_stateful_research_study."fit_part_%1$s" FOR VALUES IN (%L);
        $i$, research_subject_id, research_subject_id);

        EXECUTE format(
        $i$        
        ALTER TABLE drh_stateful_research_study.observation_fitness_component 
        ATTACH PARTITION drh_stateful_research_study."fit_comp_part_%1$s" FOR VALUES IN (%L);
        $i$, research_subject_id, research_subject_id);

        success := TRUE;
    EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'research_subject_id', research_subject_id
            );

            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            RAISE NOTICE 'Error in index_and_attach_cgm_partition for %: %', research_subject_id, SQLERRM;
            success := FALSE;
    END;
END;
$procedure$
;



CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_meal_partitions(IN p_study_id TEXT, IN p_interaction_id TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
DECLARE
    v_research_subject_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.load_meal_partitions';
BEGIN
    -- Check if the study exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist', p_study_id;
        RETURN;
    END IF;

    -- Initialize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'MEAL PARTITION MIGRATION',
        'interaction_status', 'IN PROGRESS',
        'description', 'Meal partition migration started',
        'study_id', p_study_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to insert file interaction log';
        RETURN;
    END IF;

    -- Create partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Creating meal partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.create_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error creating meal partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Copy data
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Copying meal data: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.copy_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error copying meal data for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Attach partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Attaching meal partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.index_and_attach_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error attaching meal partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Finalize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'MEAL PARTITION MIGRATION',
        'interaction_status', 'SUCCESS',
        'description', 'Meal partition migration completed',
        'study_id', p_study_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to finalize file interaction log';
    END IF;

    -- Cleanup temporary data
    DELETE FROM drh_stateful_db_import_migration.nutrition_intake_temp WHERE study_id = p_study_id;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'p_study_id', p_study_id,
                'p_interaction_id', p_interaction_id
            );


            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'MEAL PARTITION MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'Meal Partition Migration failed',
            'study_id', p_study_id,
            'file_category', 'Database',
            'created_by', NULL
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    END IF;                          

END;
$procedure$;



CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_fitness_partitions(IN p_study_id TEXT, IN p_interaction_id TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
DECLARE
    v_research_subject_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.load_fitness_partitions';
BEGIN
    -- Check if the study exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist', p_study_id;
        RETURN;
    END IF;

    -- Initialize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'FITNESS PARTITION MIGRATION',
        'interaction_status', 'IN PROGRESS',
        'description', 'Fitness partition migration started',
        'study_id', p_study_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to insert file interaction log';
        RETURN;
    END IF;

    -- Create partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Creating fitness partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.create_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error creating fitness partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Copy data
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Copying fitness data: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.copy_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error copying fitness data for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Attach partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Attaching fitness partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.index_and_attach_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error attaching fitness partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Finalize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'FITNESS PARTITION MIGRATION',
        'interaction_status', 'SUCCESS',
        'description', 'Fitness partition migration completed',
        'study_id', p_study_id,
        'file_category', 'Database',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to finalize file interaction log';
    END IF;

    -- Cleanup temporary data
    DELETE FROM drh_stateful_db_import_migration.observation_fitness_temp WHERE study_id = p_study_id;
    DELETE FROM drh_stateful_db_import_migration.observation_fitness_component_temp WHERE study_id = p_study_id;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'p_study_id', p_study_id,
                'p_interaction_id', p_interaction_id
            );


            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'FITNESS PARTITION MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'Fitness partition migration failed',
            'study_id', p_study_id,
            'file_category', 'Database',
            'created_by', NULL
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    END IF;                          

END;
$procedure$;



CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_meal_partitions_file(IN p_study_id TEXT, IN p_interaction_id TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
DECLARE
    v_research_subject_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.load_meal_partitions_file';
BEGIN
    -- Check if the study exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist', p_study_id;
        RETURN;
    END IF;

    -- Initialize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'MEAL PARTITION MIGRATION',
        'interaction_status', 'IN PROGRESS',
        'description', 'Meal partition migration started',
        'study_id', p_study_id,
        'file_category', 'Meals',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to insert file interaction log';
        RETURN;
    END IF;

    -- Create partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Creating meal partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.create_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error creating meal partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Copy data
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Copying meal data: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.copy_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error copying meal data for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Attach partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.nutrition_intake_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Attaching meal partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.index_and_attach_meal_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error attaching meal partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Finalize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'MEAL PARTITION MIGRATION',
        'interaction_status', 'SUCCESS',
        'description', 'Meal partition migration completed',
        'study_id', p_study_id,
        'file_category', 'Meals',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to finalize file interaction log';
    END IF;

    -- Cleanup temporary data
    DELETE FROM drh_stateful_db_import_migration.nutrition_intake_temp WHERE study_id = p_study_id;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'p_study_id', p_study_id,
                'p_interaction_id', p_interaction_id
            );


            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'MEAL PARTITION MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'Meal partition migration failed',
            'study_id', p_study_id,
            'file_category', 'Meals',
            'created_by', NULL
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    END IF;                          

END;
$procedure$;

----------------------------------------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.load_fitness_partitions_file(IN p_study_id TEXT, IN p_interaction_id TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
DECLARE
    v_research_subject_id TEXT;
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    err_context TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();    
    function_name TEXT := 'drh_stateless_db_import_migration.load_fitness_partitions_file';
BEGIN
    -- Check if the study exists
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist', p_study_id;
        RETURN;
    END IF;

    -- Initialize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'FITNESS PARTITION MIGRATION',
        'interaction_status', 'IN PROGRESS',
        'description', 'Fitness partition migration started',
        'study_id', p_study_id,
        'file_category', 'Fitness',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to insert file interaction log';
        RETURN;
    END IF;

    -- Create partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Creating fitness partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.create_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error creating fitness partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Copy data
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Copying fitness data: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.copy_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error copying fitness data for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Attach partitions
    FOR v_research_subject_id IN
        SELECT DISTINCT subject_id
        FROM drh_stateful_db_import_migration.observation_fitness_temp
        WHERE study_id = p_study_id
    LOOP
        BEGIN
            RAISE NOTICE 'Attaching fitness partition: %', v_research_subject_id;
            CALL drh_stateless_db_import_migration.index_and_attach_fitness_partition(v_research_subject_id);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error attaching fitness partition for %: %', v_research_subject_id, SQLERRM;
        END;
    END LOOP;

    -- Finalize interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'FITNESS PARTITION MIGRATION',
        'interaction_status', 'SUCCESS',
        'description', 'Fitness partition migration completed',
        'study_id', p_study_id,
        'file_category', 'Fitness',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to finalize file interaction log';
    END IF;

    -- Cleanup temporary data
    DELETE FROM drh_stateful_db_import_migration.observation_fitness_temp WHERE study_id = p_study_id;
    DELETE FROM drh_stateful_db_import_migration.observation_fitness_component_temp WHERE study_id = p_study_id;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Prepare parameter list
            parameters_lst := jsonb_build_object(
                'p_study_id', p_study_id,
                'p_interaction_id', p_interaction_id
            );


            -- Prepare structured exception log
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Log the exception
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'FITNESS PARTITION MIGRATION',
            'interaction_status', 'FAILED',
            'description', 'Fitness partition migration failed',
            'study_id', p_study_id,
            'file_category', 'Fitness',
            'created_by', NULL
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
    END IF;                          

END;
$procedure$;



----------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_all_fitness_data(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.rollback_all_fitness_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    subj_record RECORD;
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    successful_records TEXT[] := '{}';
    failed_records TEXT[] := '{}';
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.rollback_all_fitness_data';
    current_query TEXT := pg_catalog.current_query();
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    result JSONB;
    v_file_interaction_params JSONB;
    exception_log_json JSONB;
    partition_table_name TEXT;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting fitness data rollback for db_file_id: %', p_db_file_id;

    result := jsonb_build_object(
        'status', 'failure',
        'message', 'DB fitness records rollback failure',
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id,
        'interaction_id',p_interaction_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        -- Fetch study and tenant details
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;

        -- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', p_interaction_id,
            'interaction_action_type', 'FITNESS ROLLBACK',
            'interaction_status', 'INPROGRESS',
            'description', 'Fitness data rollback started',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        -- Iterate over fitness participant IDs
        FOR subj_record IN
            SELECT pdv.participant_id
            FROM drh_stateless_research_study.participant_data_view pdv
            WHERE pdv.study_id = p_study_id
        LOOP
            BEGIN
                RAISE NOTICE 'Rolling back fitness data for participant: %', subj_record.participant_id;
                partition_table_name := format('drh_stateful_research_study.observation_fitness_part_%s', subj_record.participant_id);

                -- Step 1: Detach partition
                BEGIN
                    EXECUTE format('ALTER TABLE drh_stateful_research_study.observation_fitness DETACH PARTITION %I', partition_table_name);
                    RAISE NOTICE 'Detached partition %', partition_table_name;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Could not detach partition %, possibly not attached: %', partition_table_name, SQLERRM;
                END;

                -- Step 2: Drop partition table
                BEGIN
                    EXECUTE format('DROP TABLE IF EXISTS %I CASCADE', partition_table_name);
                    RAISE NOTICE 'Dropped partition table %', partition_table_name;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error dropping partition table %: %', partition_table_name, SQLERRM;
                END;

               
                -- Step 3: Delete from dependent table first
				BEGIN
				    EXECUTE format(
				        'DELETE FROM drh_stateful_research_study.observation_fitness_component WHERE subject_id = %L',
				        subj_record.participant_id
				    );
				    RAISE NOTICE 'Deleted observation_fitness_component rows for subject_id %', subj_record.participant_id;
				EXCEPTION WHEN OTHERS THEN
				    RAISE NOTICE 'Error deleting observation_fitness_component for subject_id %: %', subj_record.participant_id, SQLERRM;
				END;
				
				-- Step 4: Delete from observation_fitness table
				BEGIN
				    EXECUTE format(
				        'DELETE FROM drh_stateful_research_study.observation_fitness WHERE subject_id = %L',
				        subj_record.participant_id
				    );
                    RAISE NOTICE 'Deleted remaining rows for subject_id %', subj_record.participant_id;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error deleting rows for subject_id %: %', subj_record.participant_id, SQLERRM;
                END;


                -- Track success
                successful_records := array_append(successful_records, subj_record.participant_id);
                UPDATE drh_stateful_raw_data.subject_observation_upload_data sou
                SET 
                    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    status = (
                        SELECT v1.stage_id 
                        FROM drh_stateless_master.migration_status_view v1 
                        WHERE v1.stage_name = 'ROLLBACK' 
                        LIMIT 1
                    )
                FROM drh_stateful_raw_data.subject_observation_extract_data soe
                WHERE 
                    sou.id = soe.subject_observation_upload_id
                    AND soe.participant_sid = subj_record.participant_id
                    AND sou.database_id = p_db_file_id
                    AND sou.file_content_type_id = (
                        SELECT id 
                        FROM drh_stateful_master.file_content_type fct 
                        WHERE fct.title = 'Fitness' 
                        LIMIT 1
                    );

            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Rollback failed for participant %: %', subj_record.participant_id, SQLERRM;
                failed_records := array_append(failed_records, subj_record.participant_id);
            END;
        END LOOP;

        -- Finalize interaction log
		v_file_interaction_params := jsonb_build_object(
		    'last_file_interaction_id', inprogress_file_interaction_id,
		    'interaction_action_type', 'FITNESS ROLLBACK',
		    'interaction_status', CASE
		        WHEN array_length(failed_records, 1) > 0 THEN 'PARTIAL SUCCESS'
		        ELSE 'SUCCESS'
		    END,
		    'description', CASE
		        WHEN array_length(failed_records, 1) > 0 THEN 'Fitness data rollback completed with some failures'
		        ELSE 'Fitness data rollback completed successfully'
		    END,
		    'db_file_id', p_db_file_id,
		    'file_name', p_file_name,
		    'file_category', 'Database',
		    'created_by', p_created_by
		);
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'Fitness rollback completed',
            'successful_records', successful_records,
            'failed_records', failed_records,
            'db_id', p_db_file_id,
            'interaction_id', completed_file_interaction_id
        );
    END IF;

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'FITNESS ROLLBACK',
        'interaction_status', 'FAILED',
        'description', 'Fitness data rollback failed with exception',
        'db_file_id', p_db_file_id,
        'file_name', p_file_name,
        'file_category', 'Database',
        'created_by', p_created_by
    );
    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );


    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);



    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Exception during fitness data rollback',
        'error_details', error_details_json
    );
END;
$function$
;

-----------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.extract_file_meal_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_meal_data jsonb, p_created_by text);


CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.extract_file_meal_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_meal_data jsonb, p_created_by text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.extract_file_meal_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_participant_id TEXT;
    v_meal_id TEXT;
    v_rec_status_id INT;
    meal_entry JSONB;
    meal_entry_index INT := 0;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data extraction');

    -- Validate mandatory fields
    IF p_study_id IS NULL OR p_tenant_id IS NULL OR p_participant_display_id IS NULL OR p_meal_data IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'One or more required parameters are NULL',
            'details', jsonb_build_object(
                'p_study_id', p_study_id,
                'p_tenant_id', p_tenant_id,
                'p_participant_display_id', p_participant_display_id,
                'p_meal_data', p_meal_data
            )
        );
    END IF;

    -- Fetch participant ID
    SELECT participant_id INTO v_participant_id
    FROM drh_stateless_research_study.participant_data_view
    WHERE participant_display_id = p_participant_display_id AND study_id = p_study_id
    LIMIT 1;

    IF v_participant_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant not found',
            'participant_display_id', p_participant_display_id
        );
    END IF;

    -- Get active record status ID
    SELECT value INTO v_rec_status_id
    FROM drh_stateful_party.record_status
    WHERE code = 'ACTIVE'
    LIMIT 1;

    -- Iterate through meal data entries
    FOR meal_entry IN
        SELECT * FROM jsonb_array_elements(p_meal_data)
    LOOP
        BEGIN
            meal_entry_index := meal_entry_index + 1;

            -- Validate meal_type_id and value_unit_id
            IF (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid meal type for participant % at index %: %', p_participant_display_id, meal_entry_index, meal_entry->>'meal_type';
                CONTINUE;
            END IF;

            IF (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid unit of measurement for participant % at index %: calories', p_participant_display_id, meal_entry_index;
                CONTINUE;
            END IF;

            v_meal_id := drh_stateless_util.get_unique_id()::TEXT;

            -- Insert meal data
            INSERT INTO drh_stateful_db_import_migration.nutrition_intake_temp (
                id,
                status_code,
                study_id,
                subject_id,
                occurrence_time,
                meal_type_id,
                value_quantity,
                value_unit_id
            )
            VALUES (
                v_meal_id,
                (SELECT id FROM drh_stateful_master.nutrition_intake_status_code WHERE code = 'unknown' LIMIT 1),
                p_study_id,
                v_participant_id,
                TO_TIMESTAMP(meal_entry->>'meal_time', 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
                (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1),
                (meal_entry->>'calories')::FLOAT8,
                (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1)
            );

            RAISE NOTICE 'Inserted meal data for participant %: Meal ID: %, Index: %', p_participant_display_id, v_meal_id, meal_entry_index;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting meal data for participant % at index %: %', p_participant_display_id, meal_entry_index, SQLERRM;
        END;
    END LOOP;

    -- Set result to success
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Meal data extracted successfully',
        'participant_display_id', p_participant_display_id
    );

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_tenant_id', p_tenant_id,
        'p_participant_display_id', p_participant_display_id,
        'p_meal_data', p_meal_data,
        'p_created_by', p_created_by
    );

    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data extraction', 'error_details', error_details_json);
END;
$function$
;
-----------------------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.extract_file_meal_data(
    text, text, text, jsonb, text);

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.extract_file_meal_data(
    text, text, text, jsonb, text,text);


CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.extract_file_meal_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_meal_data jsonb, p_created_by text, p_obs_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.extract_file_meal_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_participant_id TEXT;
    v_meal_id TEXT;
    v_rec_status_id INT;
    meal_entry JSONB;
    meal_entry_index INT := 0;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data extraction');

    -- Validate mandatory fields
    IF p_study_id IS NULL OR p_tenant_id IS NULL OR p_participant_display_id IS NULL OR p_meal_data IS NULL or p_obs_id is null THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'One or more required parameters are NULL',
            'details', jsonb_build_object(
                'p_study_id', p_study_id,
                'p_tenant_id', p_tenant_id,
                'p_participant_display_id', p_participant_display_id,
                'p_meal_data', p_meal_data,
                'p_obs_id',p_obs_id
            )
        );
    END IF;

    -- Fetch participant ID
    SELECT participant_id INTO v_participant_id
    FROM drh_stateless_research_study.participant_data_view
    WHERE participant_display_id = p_participant_display_id AND study_id = p_study_id
    LIMIT 1;

    IF v_participant_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant not found',
            'participant_display_id', p_participant_display_id
        );
    END IF;

    -- Get active record status ID
    SELECT value INTO v_rec_status_id
    FROM drh_stateful_party.record_status
    WHERE code = 'ACTIVE'
    LIMIT 1;

    -- Iterate through meal data entries
    FOR meal_entry IN
        SELECT * FROM jsonb_array_elements(p_meal_data)
    LOOP
        BEGIN
            meal_entry_index := meal_entry_index + 1;

            -- Validate meal_type_id and value_unit_id
            IF (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid meal type for participant % at index %: %', p_participant_display_id, meal_entry_index, meal_entry->>'meal_type';
                CONTINUE;
            END IF;

            IF (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1) IS NULL THEN
                RAISE NOTICE 'Invalid unit of measurement for participant % at index %: calories', p_participant_display_id, meal_entry_index;
                CONTINUE;
            END IF;

            v_meal_id := drh_stateless_util.get_unique_id()::TEXT;

            -- Insert meal data
            INSERT INTO drh_stateful_db_import_migration.nutrition_intake_temp (
                id,
                status_code,
                study_id,
                subject_id,
                occurrence_time,
                meal_type_id,
                value_quantity,
                value_unit_id
            )
            VALUES (
                v_meal_id,
                (SELECT id FROM drh_stateful_master.nutrition_intake_status_code WHERE code = 'unknown' LIMIT 1),
                p_study_id,
                v_participant_id,
                TO_TIMESTAMP(meal_entry->>'meal_time', 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
                (SELECT meal_type_id FROM drh_stateful_master.meal_type WHERE LOWER(display) = LOWER(meal_entry->>'meal_type') LIMIT 1),
                (meal_entry->>'calories')::FLOAT8,
                (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = 'kilocalories' LIMIT 1)
            );
           
           INSERT INTO drh_stateful_research_study.nutritionintake_mapping (
		    id, 
		    nutrition_mapping_id, 
		    observation_mapping_id, 
		    r_subject_id, 
		    study_id, 
		    tenant_id,
		    rec_status_id, 
		    created_at, 
		    created_by, 
		    updated_at, 
		    updated_by, 
		    deleted_at,
		    deleted_by
			) 
			VALUES (
		    drh_stateless_util.get_unique_id(), 
		    v_meal_id, 
		    p_obs_id, 
		    v_participant_id, 
		    p_study_id, 
		    p_tenant_id, 
		    (
		        SELECT rs.value 
		        FROM drh_stateful_party.record_status rs 
		        WHERE rs.code = 'ACTIVE' 
		        LIMIT 1
		    ), 
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
		    p_created_by, 
		    NULL, 
		    NULL, 
		    NULL, 
		    NULL
			);
            RAISE NOTICE 'Inserted meal data for participant %: Meal ID: %, Index: %', p_participant_display_id, v_meal_id, meal_entry_index;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting meal data for participant % at index %: %', p_participant_display_id, meal_entry_index, SQLERRM;
        END;
    END LOOP;

    -- Set result to success
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Meal data extracted successfully',
        'participant_display_id', p_participant_display_id
    );

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_tenant_id', p_tenant_id,
        'p_participant_display_id', p_participant_display_id,
        'p_meal_data', p_meal_data,
        'p_created_by', p_created_by
    );

    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during meal data extraction', 'error_details', error_details_json);
END;
$function$
;


-------------------------------------------------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.extract_file_fitness_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_fitness_data jsonb, p_created_by text);
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.extract_file_fitness_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_fitness_data jsonb, p_created_by text,p_obs_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.extract_file_fitness_data(p_study_id text, p_tenant_id text, p_participant_display_id text, p_fitness_data jsonb, p_created_by text,p_obs_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.extract_file_fitness_data';
    current_query TEXT := pg_catalog.current_query();
    result JSONB;
    v_participant_id TEXT;
    v_observation_id TEXT;
    v_rec_status_id INT;
    fitness_entry JSONB;
    fitness_entry_index INT := 0;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result as failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during fitness data extraction');

    -- Validate mandatory fields
    IF p_study_id IS NULL OR p_tenant_id IS NULL OR p_participant_display_id IS NULL OR p_fitness_data IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'One or more required parameters are NULL',
            'details', jsonb_build_object(
                'p_study_id', p_study_id,
                'p_tenant_id', p_tenant_id,
                'p_participant_display_id', p_participant_display_id,
                'p_fitness_data', p_fitness_data,
                'p_obs_id',p_obs_id
            )
        );
    END IF;

    -- Fetch participant ID
    SELECT participant_id INTO v_participant_id
    FROM drh_stateless_research_study.participant_data_view
    WHERE participant_display_id = p_participant_display_id AND study_id = p_study_id
    LIMIT 1;

    IF v_participant_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant not found',
            'participant_display_id', p_participant_display_id
        );
    END IF;

    -- Get active record status ID
    SELECT value INTO v_rec_status_id
    FROM drh_stateful_party.record_status
    WHERE code = 'ACTIVE'
    LIMIT 1;

    -- Iterate through fitness data entries
    FOR fitness_entry IN
        SELECT * FROM jsonb_array_elements(p_fitness_data)
    LOOP
        BEGIN
            fitness_entry_index := fitness_entry_index + 1;

            v_observation_id := drh_stateless_util.get_unique_id();

            -- Insert fitness data
            INSERT INTO drh_stateful_db_import_migration.observation_fitness_temp (
                observation_id,
                subject_id,
                study_id,
                effective_datetime,
                status_id,
                category_id,
                activity_type_id,                
                rec_status_id,
                created_at,
                created_by
            )
            VALUES (
                v_observation_id,
                v_participant_id,
                p_study_id,
                TO_TIMESTAMP(fitness_entry->>'date', 'YYYY-MM-DD HH24:MI:SS')::TIMESTAMPTZ,
                (SELECT id FROM drh_stateful_master.observation_status WHERE code = 'unknown' LIMIT 1),
                (SELECT category_id FROM drh_stateful_master.observation_category WHERE LOWER(display) = 'activity' LIMIT 1),
                (SELECT activity_id FROM drh_stateful_master.activity_master WHERE LOWER(display) = 'physical activity' LIMIT 1),
                v_rec_status_id,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_created_by
            );

            -- Insert fitness component data for steps
            IF fitness_entry->>'steps' IS NOT NULL AND fitness_entry->>'steps' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Steps Count') LIMIT 1),
                    (fitness_entry->>'steps')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Steps') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Pedometer') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
                
            END IF;

            -- Insert fitness component data for exercise_minutes
            IF fitness_entry->>'exercise_minutes' IS NOT NULL AND fitness_entry->>'exercise_minutes' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Duration') LIMIT 1),
                    (fitness_entry->>'exercise_minutes')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Minutes') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Manual Entry') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;
            
            -- Insert fitness component data for calories_burned
            IF fitness_entry->>'calories_burned' IS NOT NULL AND fitness_entry->>'calories_burned' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Calories Burned') LIMIT 1),
                    (fitness_entry->>'calories_burned')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Kilocalories') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Heart Rate Sensor') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;

            -- Insert fitness component data for distance
            IF fitness_entry->>'distance' IS NOT NULL AND fitness_entry->>'distance' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Distance') LIMIT 1),
                    (fitness_entry->>'distance')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Kilometers') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Pedometer') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;

            -- Insert fitness component data for heart_rate
            IF fitness_entry->>'heart_rate' IS NOT NULL AND fitness_entry->>'heart_rate' <> '' THEN
                INSERT INTO drh_stateful_db_import_migration.observation_fitness_component_temp (
                    component_id,
                    observation_id,
                    subject_id,
                    study_id,
                    component_type_id,
                    value,
                    unit_code,
                    method_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    v_observation_id,
                    v_participant_id,
                    p_study_id,
                    (SELECT component_type_id FROM drh_stateful_master.activity_component_type WHERE LOWER(display) = LOWER('Heart Rate (BPM)') LIMIT 1),
                    (fitness_entry->>'heart_rate')::FLOAT8,
                    (SELECT unit_id FROM drh_stateful_master.unit_of_measurement WHERE LOWER(display) = LOWER('Beats per Minute') LIMIT 1),
                    (SELECT method_id FROM drh_stateful_master.observation_method WHERE LOWER(display) = LOWER('Heart Rate Sensor') LIMIT 1),
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_created_by
                );
            END IF;


            INSERT INTO drh_stateful_research_study.fitness_mapping (
		    id, 
		    fitness_mapping_id, 
		    observation_mapping_id, 
		    r_subject_id, 
		    study_id, 
		    tenant_id,
		    rec_status_id, 
		    created_at, 
		    created_by, 
		    updated_at, 
		    updated_by, 
		    deleted_at,
		    deleted_by
			) 
			VALUES (
		    drh_stateless_util.get_unique_id(), 
		    v_observation_id, 
		    p_obs_id, 
		    v_participant_id, 
		    p_study_id, 
		    p_tenant_id, 
		    (
		        SELECT rs.value 
		        FROM drh_stateful_party.record_status rs 
		        WHERE rs.code = 'ACTIVE' 
		        LIMIT 1
		    ), 
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
		    p_created_by, 
		    NULL, 
		    NULL, 
		    NULL, 
		    NULL
			);

            RAISE NOTICE 'Inserted fitness data for participant %: Index: %', p_participant_display_id, fitness_entry_index;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting fitness data for participant % at index %: %', p_participant_display_id, fitness_entry_index, SQLERRM;
        END;
    END LOOP;

    -- Set result to success
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Fitness data extracted successfully',
        'participant_display_id', p_participant_display_id
    );

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    -- Log the error details

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_tenant_id', p_tenant_id,
        'p_participant_display_id', p_participant_display_id,
        'p_fitness_data', p_fitness_data,
        'p_created_by', p_created_by
    );


    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    RETURN jsonb_build_object('status', 'failure', 'message', 'Error occurred during fitness data extraction', 'error_details', error_details_json);
END;
$function$
;


------------------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------- MEAL ROLLBACK---------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_db_import_migration.rollback_all_meal_data(p_db_file_id text, p_interaction_id text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.rollback_all_meal_data(p_db_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    subj_record RECORD;
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_created_by TEXT;
    p_file_name TEXT;
    successful_records TEXT[] := '{}';
    failed_records TEXT[] := '{}';
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_db_import_migration.rollback_all_meal_data';
    current_query TEXT := pg_catalog.current_query();
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    result JSONB;
    v_file_interaction_params JSONB;
    partition_table_name TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting meal data rollback for db_file_id: %', p_db_file_id;

    result := jsonb_build_object(
        'status', 'failure',
        'message', 'DB meal records rollback failure',
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id,
        'interaction_id',p_interaction_id
    );

    IF p_db_file_id IS NOT NULL AND p_interaction_id IS NOT NULL THEN
        -- Fetch study and tenant details
        SELECT crdv.study_id, crdv.tenant_id, crdv.uploaded_by, crdv.file_name
        INTO p_study_id, p_tenant_id, p_created_by, p_file_name
        FROM drh_stateless_raw_data.cgm_raw_db_view crdv
        WHERE crdv.db_file_id = p_db_file_id
        LIMIT 1;

        RAISE NOTICE 'Study ID: %, Tenant ID: %, Created By: %, File Name: %', p_study_id, p_tenant_id, p_created_by, p_file_name;

        -- Call save_file_interaction_log to get the file interaction ID
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', p_interaction_id,
            'interaction_action_type', 'MEAL ROLLBACK',
            'interaction_status', 'INPROGRESS',
            'description', 'Meal data rollback started',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to insert file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        -- Iterate over meal participant IDs
        FOR subj_record IN
            SELECT pdv.participant_id
            FROM drh_stateless_research_study.participant_data_view pdv
            WHERE pdv.study_id = p_study_id
        LOOP
            BEGIN
                RAISE NOTICE 'Rolling back meal data for participant: %', subj_record.participant_id;
                partition_table_name := format('drh_stateful_research_study.nutrition_intake_part_%s', subj_record.participant_id);

                -- Step 1: Detach partition
                BEGIN
                    EXECUTE format('ALTER TABLE drh_stateful_research_study.nutrition_intake DETACH PARTITION %I', partition_table_name);
                    RAISE NOTICE 'Detached partition %', partition_table_name;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Could not detach partition %, possibly not attached: %', partition_table_name, SQLERRM;
                END;

                -- Step 2: Drop partition table
                BEGIN
                    EXECUTE format('DROP TABLE IF EXISTS %I CASCADE', partition_table_name);
                    RAISE NOTICE 'Dropped partition table %', partition_table_name;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error dropping partition table %: %', partition_table_name, SQLERRM;
                END;

               
                -- Step 3: Delete from dependent table first
				BEGIN
				    EXECUTE format(
				        'DELETE FROM drh_stateful_research_study.nutrition_intake WHERE subject_id = %L',
				        subj_record.participant_id
				    );
				    RAISE NOTICE 'Deleted nutrition_intake rows for subject_id %', subj_record.participant_id;
				EXCEPTION WHEN OTHERS THEN
				    RAISE NOTICE 'Error deleting nutrition_intake for subject_id %: %', subj_record.participant_id, SQLERRM;
				END;
				
				
                -- Track success
                successful_records := array_append(successful_records, subj_record.participant_id);
                UPDATE drh_stateful_raw_data.subject_observation_upload_data sou
                SET 
                    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    status = (
                        SELECT v1.stage_id 
                        FROM drh_stateless_master.migration_status_view v1 
                        WHERE v1.stage_name = 'ROLLBACK' 
                        LIMIT 1
                    )
                FROM drh_stateful_raw_data.subject_observation_extract_data soe
                WHERE 
                    sou.id = soe.subject_observation_upload_id
                    AND soe.participant_sid = subj_record.participant_id
                    AND sou.database_id = p_db_file_id
                    AND sou.file_content_type_id = (
                        SELECT id 
                        FROM drh_stateful_master.file_content_type fct 
                        WHERE fct.title = 'Meals' 
                        LIMIT 1
                    );

            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Rollback failed for participant %: %', subj_record.participant_id, SQLERRM;
                failed_records := array_append(failed_records, subj_record.participant_id);
            END;
        END LOOP;

        -- Finalize interaction log
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'MEAL ROLLBACK',
            'interaction_status', CASE
		        WHEN array_length(failed_records, 1) > 0 THEN 'PARTIAL SUCCESS'
		        ELSE 'SUCCESS'
		    END,
		    'description', CASE
		        WHEN array_length(failed_records, 1) > 0 THEN 'Meal data rollback completed with some failures'
		        ELSE 'Meal data rollback completed successfully'
		    END,
            'description', 'Meal data rollback completed',
            'db_file_id', p_db_file_id,
            'file_name', p_file_name,
            'file_category', 'Database',
            'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Failed to finalize file interaction',
                'error_details', file_interaction_result -> 'error_details'
            );
        END IF;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'Meal rollback completed',
            'successful_records', successful_records,
            'failed_records', failed_records,
            'db_id', p_db_file_id,
            'interaction_id', completed_file_interaction_id
        );
    END IF;

    RETURN result;

EXCEPTION
        WHEN OTHERS THEN
            -- Capture exception diagnostics
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'MEAL ROLLBACK',
        'interaction_status', 'FAILED',
        'description', 'Meal data rollback failed with exception',
        'db_file_id', p_db_file_id,
        'file_name', p_file_name,
        'file_category', 'Database',
        'created_by', p_created_by
    );
    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    parameters_lst := jsonb_build_object(
    'p_db_file_id', p_db_file_id,
    'p_interaction_id', p_interaction_id
    );

    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Exception during meal data rollback',
        'error_details', error_details_json
    );
END;
$function$
;


-------------------------------------------------------------------------------------------------------------------------------
-- DROP PROCEDURE IF EXISTS drh_stateless_db_import_migration.revert_cgm_data_and_partitions(text, text, text);

CREATE OR REPLACE FUNCTION drh_stateless_db_import_migration.revert_cgm_data_and_partitions(p_study_id text, p_interaction_id text, p_db_file_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    -- Variable to store the JSONB result from save_file_interaction_log calls.
    file_interaction_result JSONB;
    -- Variable to store the file_interaction_id of the 'IN PROGRESS' log entry.
    inprogress_file_interaction_id TEXT;
    -- Variable to store the file_interaction_id of the 'SUCCESS' log entry (if applicable).
    completed_file_interaction_id TEXT;
    -- JSONB variable to hold parameters for the save_file_interaction_log function.
    v_file_interaction_params JSONB;
    -- Variables to capture PostgreSQL exception context.
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    -- JSONB variable to store formatted exception details for logging.
    exception_log_json JSONB;
    -- JSONB variable to store input parameters for auditing/logging.
    parameters_lst JSONB;
    -- JSONB variable to store formatted error details for the procedure's return.
    error_details_json JSONB;
    -- Captures the SQL query being executed when an error occurs.
    current_query TEXT := pg_catalog.current_query();
    -- Stores the name of the current procedure for logging.
    procedure_name TEXT := 'drh_stateless_db_import_migration.revert_cgm_partitions';
    -- Record variable to iterate through results of the participant query.
    d_record record;
   	successful_records text[] := '{}';
    failed_records text[] := '{}';
    p_created_by text;
    p_file_name text;
    v_interaction_status text;
    v_description text;
    v_response text;
    v_error_response text;
    result jsonb;
begin
	result := jsonb_build_object(
        'status', 'failure',
        'message', 'CGM data records rollback failure',
        'successful_records', '[]',
        'failed_records', '[]',
        'db_id', p_db_file_id,
        'interaction_id',p_interaction_id
    );
    -- Prepare a JSONB object of input parameters for consistent logging.
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_interaction_id', p_interaction_id,
        'p_db_file_id', p_db_file_id
    );

    -- Check if the provided study ID exists in the research_study table.
    -- If the study does not exist, raise a notice and exit the procedure.
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id
    ) THEN
        RAISE NOTICE 'Study with ID % does not exist, cannot revert partitions.', p_study_id;
        RETURN jsonb_build_object(
		    'status', 'failure',
		    'message', format('Study ID % not found', p_study_id),
		    'db_id', p_db_file_id
		);
    END IF;
   
   
    SELECT crdv.uploaded_by, crdv.file_name
    INTO p_created_by, p_file_name
    FROM drh_stateless_raw_data.cgm_raw_db_view crdv
    WHERE crdv.db_file_id = p_db_file_id
    LIMIT 1;

    -- Initialize a file interaction log entry to mark the start of the CGM partition reversal.
    -- Set interaction_status to 'IN PROGRESS' and action_type to 'CGM ROLLBACK'.
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'CGM ROLLBACK',
        'interaction_status', 'IN PROGRESS',
        'description', 'CGM Data Rollback started',
        'study_id', p_study_id,
        'db_file_id', p_db_file_id,
	    'file_name', p_file_name,
		'file_category', 'Database',		
        'created_by', p_created_by 
    );

    -- Call the external function to save the initial file interaction log.
    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    -- Check if the initial file interaction log was successfully inserted.
    -- If successful, store the new file_interaction_id; otherwise, log a notice.
    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RAISE NOTICE 'Failed to insert initial file interaction log for reversal. Reversal will proceed but logging might be incomplete.';
    END IF;

    -- Loop through each distinct participant_id and cgm_raw_data_id
    -- for which CGM records exist in both raw_cgm_extract_data and cgm_data_migration_status
    -- for the given study. This ensures we only attempt to revert data that was successfully migrated.
    FOR d_record IN
        SELECT DISTINCT cms.participant_id, cms.cgm_raw_data_id, rced.raw_file_id
        FROM drh_stateful_raw_data.raw_cgm_extract_data rced
        JOIN drh_stateful_db_import_migration.cgm_data_migration_status cms
            ON rced.cgm_raw_data_id = cms.cgm_raw_data_id
            AND rced.study_id = cms.study_id
        WHERE rced.study_id = p_study_id
    LOOP
        -- Begin an inner exception block to handle errors for each individual participant.
        -- This allows the procedure to continue processing other participants even if one fails.
        BEGIN
            RAISE NOTICE 'Starting cgm rollback for participant_id: % (cgm_raw_data_id: %)', d_record.participant_id, d_record.cgm_raw_data_id;

            -- Step 1: Revert CGM metrics.
            -- This calls a separate function to delete associated records from stateful tables
            -- (participant_base, cgm_metrics, cgm_device_info), effectively undoing save_cgm_metrics.
            RAISE NOTICE ' Calling drh_stateless_raw_observation.revert_cgm_metrics for participant: %', d_record.participant_id;
            PERFORM drh_stateless_raw_observation.revert_cgm_metrics(d_record.participant_id);

            -- Step 2: Unindex and detach the partition and drop the physical partition table
            -- This calls a procedure to remove indexes and detach the partition table
            -- from the main cgm_observation table, undoing index_and_attach_cgm_partition.
            -- drops the physical partition  table also.
            RAISE NOTICE ' Calling drh_stateless_db_import_migration.unindex_and_detach_cgm_partition for participant: %', d_record.participant_id;
            CALL drh_stateless_db_import_migration.unindex_and_detach_cgm_partition(d_record.participant_id);
            

            -- Update the status in the cgm_data_migration_status table for the processed raw data ID.
            -- Set the status to 'ROLLBACK' and update the timestamp.
            UPDATE drh_stateful_db_import_migration.cgm_data_migration_status
            SET
                cgm_migration_status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ROLLBACK' LIMIT 1),
                migration_start_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            WHERE db_file_id = p_db_file_id AND cgm_raw_data_id = d_record.cgm_raw_data_id;

            -- Update the status in the cgm_raw_upload_data table for the corresponding raw file.
            UPDATE drh_stateful_raw_data.cgm_raw_upload_data 
            SET updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                status = (SELECT v1.stage_id FROM drh_stateless_master.migration_status_view v1 WHERE v1.stage_name = 'ROLLBACK' LIMIT 1)
            WHERE cgm_raw_file_id = d_record.raw_file_id; -- Using d_record.raw_file_id from the join
            
                       
            RAISE NOTICE 'Completed cgm rollback for participant_id: % (cgm_raw_data_id: %)', d_record.participant_id, d_record.cgm_raw_data_id;
           
            successful_records := array_append(successful_records, d_record.cgm_raw_data_id);

        EXCEPTION WHEN OTHERS THEN
            -- Handle exceptions that occur during the processing of an individual participant.
            failed_records := array_append(failed_records, d_record.cgm_raw_data_id);
            RAISE NOTICE 'Error processing participant % (raw_data_id: %) during revert: %', d_record.participant_id, d_record.cgm_raw_data_id, SQLERRM;
            -- Capture detailed error diagnostics.
            GET STACKED DIAGNOSTICS
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            -- Build a JSONB object with error details for logging.
            exception_log_json := jsonb_build_object(
                'function_name', procedure_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );
            -- Log the exception using the activity audit function.
            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            -- The loop continues to the next participant despite this error.
        END;
    END LOOP;

    RAISE NOTICE 'CGM Data Rollback completed for study: %', p_study_id;
   
   
   			-- Determine interaction status and description based on failures
			IF array_length(failed_records, 1) IS NOT NULL AND array_length(failed_records, 1) > 0 THEN
			    v_interaction_status := 'PARTIAL SUCCESS';
			    v_description := format('CGM Data Rollback  completed with %s failed records.', array_length(failed_records, 1));
			    v_response := successful_records;
			    v_error_response := failed_records;
			ELSE
			    v_interaction_status := 'SUCCESS';
			    v_description := 'CGM Data Rollback  completed';
			    v_response := successful_records;
			    v_error_response := NULL;
			END IF;

    -- Finalize the interaction log with 'SUCCESS' status if no unhandled errors occurred.
    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'CGM ROLLBACK',
            'interaction_status', v_interaction_status,
			'description', v_description,
			'db_file_id', p_db_file_id,
			'file_name', p_file_name,
			'file_category', 'Database',
			'created_by', p_created_by,
			'response', v_response,
			'error_response', v_error_response
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
            result := jsonb_build_object(
            'status', 'success',
            'message', 'CGM data rolledback successfully',            
            'successful_records', successful_records,
            'failed_records', failed_records,
            'db_id', p_db_file_id,
            'interaction_id',completed_file_interaction_id
   		 );
        ELSE
            RAISE NOTICE 'Failed to finalize file interaction log as SUCCESS.';
        END IF;
    END IF;
   
   return result;

EXCEPTION WHEN OTHERS THEN
    -- This is the outer exception handler, catching any errors that escape the inner loop
    -- or occur outside the loop (e.g., initial setup, final logging).
    -- Capture general exception diagnostics for the entire procedure.
    GET STACKED DIAGNOSTICS
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT,
        err_context = PG_EXCEPTION_CONTEXT;

    -- Log the main error details for the entire procedure's failure.
    exception_log_json := jsonb_build_object(
        'function_name', procedure_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );
    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Log failure in the file interaction log, if an initial entry was created.
    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'CGM ROLLBACK',
            'interaction_status', 'FAILED',
            'description', 'CGM Data Rollback failed due to an error',
            'study_id', p_study_id,
            'file_category', 'Database',
            'db_file_id', p_db_file_id,
			'file_name', p_file_name,			
			'created_by', p_created_by
        );
        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
    END IF;

    -- Raise a notice for the failure to the caller or console.
    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );
    RAISE NOTICE 'CGM Data Rollback failed: %', error_details_json;
   -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during cgm  rollback', 'error_details', error_details_json);
    RETURN result;
END;
$function$
;



--------------------------------------------------------------------------------------------------------------------------

CREATE OR REPLACE PROCEDURE drh_stateless_db_import_migration.unindex_and_detach_cgm_partition(
    IN p_research_subject_id TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
DECLARE
    success BOOLEAN;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    current_query TEXT := pg_catalog.current_query();
    function_name TEXT := 'drh_stateless_db_import_migration.unindex_and_detach_cgm_partition';
    partition_table TEXT := format('cgm_observation_research_subject_%s', p_research_subject_id);
BEGIN
    parameters_lst := jsonb_build_object(
        'research_subject_id', p_research_subject_id
    );

    BEGIN
        RAISE NOTICE 'Starting unindex and detach for %', p_research_subject_id;

        -- Detach the partition
        RAISE NOTICE 'Detaching partition from parent table...';
        EXECUTE format(
            'ALTER TABLE drh_stateful_raw_observation.cgm_observation DETACH PARTITION drh_stateful_raw_observation.%I',
            partition_table
        );

        -- Drop indexes (these are partition-level indexes)
        RAISE NOTICE 'Dropping indexes...';
        EXECUTE format('DROP INDEX IF EXISTS drh_stateful_raw_observation.%I;', format('idx_cgm_obs_study_participant_date_%s', p_research_subject_id));
        EXECUTE format('DROP INDEX IF EXISTS drh_stateful_raw_observation.%I;', format('idx_cgm_observation_deleted_at_%s', p_research_subject_id));
        EXECUTE format('DROP INDEX IF EXISTS drh_stateful_raw_observation.%I;', format('idx_cgm_observation_raw_data_id_%s', p_research_subject_id));
        EXECUTE format('DROP INDEX IF EXISTS drh_stateful_raw_observation.%I;', format('idx_cgm_observation_research_subject_id_%s', p_research_subject_id));
        EXECUTE format('DROP INDEX IF EXISTS drh_stateful_raw_observation.%I;', format('idx_cgm_observation_study_id_%s', p_research_subject_id));

        -- Drop check constraint
        RAISE NOTICE 'Dropping check constraint...';
        EXECUTE format(
            'ALTER TABLE drh_stateful_raw_observation.%I DROP CONSTRAINT IF EXISTS %I;',
            partition_table,
            format('cgm_observation_partition_by_list_check_%s', p_research_subject_id)
        );

        -- Drop primary key (drops associated index too)
        RAISE NOTICE 'Dropping primary key constraint...';
        EXECUTE format(
            'ALTER TABLE drh_stateful_raw_observation.%I DROP CONSTRAINT IF EXISTS %I;',
            partition_table,
            format('%s_pkey', partition_table)
        );

        RAISE NOTICE 'Successfully completed unindex and detach for %', p_research_subject_id;

        -- Drop the partition table itself
        RAISE NOTICE 'Dropping partition table...';
        EXECUTE format('DROP TABLE IF EXISTS drh_stateful_raw_observation.%I;', format('cgm_observation_research_subject_%s', p_research_subject_id));

        success := TRUE;

    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS 
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT,
                err_context = PG_EXCEPTION_CONTEXT;

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst
            );

            -- Optional: truncate fields before insert if you hit varchar(255) limits
            BEGIN
                PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error logging exception: %', err_message;
            END;

            RAISE NOTICE 'Exception in %: %', function_name, err_message;
            success := FALSE;
    END;
END;
$procedure$;

---------------------------------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION drh_stateless_research_study.insert_missing_fitness_mapping()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    extract_record RECORD;
    fitness_entry JSONB;
    component RECORD;
    rsubject_id TEXT;
    fitness_date DATE;
    fitness_id TEXT;
    matched_obs_id TEXT;
    matched_component_count INTEGER;
    total_component_count INTEGER;
    mapping_exists BOOLEAN;
BEGIN
    FOR extract_record IN
        SELECT * 
        FROM drh_stateful_raw_data.subject_observation_extract_data d
        WHERE d.subject_observation_data_json IS NOT NULL
          AND d.file_content_type_id = (
              SELECT fct.id 
              FROM drh_stateful_master.file_content_type fct  
              WHERE fct.title = 'Fitness' 
              LIMIT 1
          )
    LOOP
        rsubject_id := extract_record.participant_sid;
        RAISE NOTICE 'Processing extract record ID: %, Participant SID: %, Study ID: %',
            extract_record.id, rsubject_id, extract_record.study_id;

        FOR fitness_entry IN
            SELECT * FROM jsonb_array_elements(extract_record.subject_observation_data_json)
        LOOP
            fitness_id := fitness_entry->>'fitness_id';
            fitness_date := (fitness_entry->>'date')::DATE;
           

            IF fitness_id IS NULL OR fitness_date IS NULL THEN
                RAISE NOTICE 'Skipping record with NULL fitness_id or date for participant %', rsubject_id;
                CONTINUE;
            END IF;

            RAISE NOTICE 'Processing fitness_id: %, Date: %, Participant: %',
                fitness_id, fitness_date, rsubject_id;

            total_component_count := 0;
            matched_component_count := 0;
            matched_obs_id := NULL;

            FOR component IN
                SELECT 'steps' AS field, 'Steps Count' AS component_type UNION ALL
                SELECT 'distance', 'Distance' UNION ALL
                SELECT 'calories_burned', 'Calories Burned' UNION ALL
                SELECT 'heart_rate', 'Heart Rate (BPM)' UNION ALL
                SELECT 'exercise_minutes', 'Duration'
            LOOP
                IF NULLIF(TRIM(fitness_entry->>component.field), '') IS NOT NULL THEN
                    total_component_count := total_component_count + 1;
                    RAISE NOTICE 'Checking component: %, Value: %',
                        component.component_type, fitness_entry->>component.field;

                    SELECT ofc.observation_id into matched_obs_id
						FROM drh_stateful_research_study.observation_fitness_component ofc
						JOIN drh_stateful_research_study.observation_fitness ofd
						  ON ofc.observation_id = ofd.observation_id
						WHERE ofc.subject_id = rsubject_id
                      AND ofc.study_id = extract_record.study_id
                      AND ofc.component_type_id = (
					      SELECT act.component_type_id
					      FROM drh_stateful_master.activity_component_type act
					      WHERE LOWER(act.display) = LOWER(component.component_type)
					      LIMIT 1
					  )
					  AND ofc.value =(fitness_entry->>component.field)::FLOAT AND DATE(ofd.effective_datetime) = fitness_date  
						LIMIT 1;

                    IF FOUND THEN
                        matched_component_count := matched_component_count + 1;
                        RAISE NOTICE 'Matched: % -> Observation ID: %',
                            component.component_type, matched_obs_id;
                    ELSE
                        RAISE NOTICE 'No match for % with value %',
                            component.component_type, fitness_entry->>component.field;
                    END IF;
                END IF;
            END LOOP;

            IF matched_obs_id IS NOT NULL AND matched_component_count > 0 THEN
                SELECT EXISTS (
                    SELECT 1
                    FROM drh_stateful_research_study.fitness_mapping fm
                    WHERE fm.observation_mapping_id = extract_record.id
                      AND fm.fitness_mapping_id = matched_obs_id
                ) INTO mapping_exists;

                IF NOT mapping_exists THEN
                    INSERT INTO drh_stateful_research_study.fitness_mapping (id, fitness_mapping_id, observation_mapping_id, r_subject_id, study_id, tenant_id, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by) 
                    VALUES (
                        drh_stateless_util.generate_unique_id(),
                        matched_obs_id,
                        extract_record.id,
                        rsubject_id,
                        extract_record.study_id,
                        extract_record.tenant_id,
                        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                        extract_record.created_by,null,null,null,NULL
                    );

                    RAISE NOTICE '✅ Inserted mapping: Subject %, Date %, Observation ID %',
                        rsubject_id, fitness_date, matched_obs_id;
                ELSE
                    RAISE NOTICE 'ℹ Mapping already exists: Subject %, Date %, Observation ID %',
                        rsubject_id, fitness_date, matched_obs_id;
                END IF;
            ELSE
                RAISE NOTICE '⚠ No sufficient matching components to insert mapping: Subject %, Date %',
                    rsubject_id, fitness_date;
            END IF;
        END LOOP;
    END LOOP;
   RAISE NOTICE 'Completed fitness -file mapping process.';
END;
$function$
;

--------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_research_study.insert_missing_nutritionintake_mappings();

CREATE OR REPLACE FUNCTION drh_stateless_research_study.insert_missing_nutritionintake_mappings()
RETURNS void
LANGUAGE plpgsql
AS $function$
DECLARE
    extract_record RECORD;
    meal_entry JSONB;
    matching_nutrition RECORD;
    possible_match RECORD;
    rsubject_id TEXT;
    meal_time TIMESTAMPTZ;
    meal_type TEXT;
    meal_type_id TEXT;
    calories FLOAT8;
    mapping_exists BOOLEAN;
    status_active_id INT;

    total_processed INT := 0;
    matched_count INT := 0;
    unmatched_count INT := 0;
BEGIN
    -- Get the ACTIVE record status ID
    SELECT rs.value INTO status_active_id
    FROM drh_stateful_party.record_status rs
    WHERE rs.code = 'ACTIVE'
    LIMIT 1;

    IF status_active_id IS NULL THEN
        RAISE NOTICE '❌ ACTIVE status not found. Aborting.';
        RETURN;
    END IF;

    FOR extract_record IN
        SELECT * 
        FROM drh_stateful_raw_data.subject_observation_extract_data d
        WHERE d.subject_observation_data_json IS NOT NULL
          AND d.file_content_type_id = (
              SELECT fct.id 
              FROM drh_stateful_master.file_content_type fct  
              WHERE fct.title = 'Meals' 
              LIMIT 1
          )
    LOOP
        rsubject_id := extract_record.participant_sid;

        FOR meal_entry IN
            SELECT * FROM jsonb_array_elements(extract_record.subject_observation_data_json)
        LOOP
            total_processed := total_processed + 1;

            BEGIN
                -- Robust UTC-safe parsing
                BEGIN
                    meal_time := (meal_entry->>'meal_time')::timestamptz;
                EXCEPTION WHEN OTHERS THEN
                    meal_time := (meal_entry->>'meal_time')::timestamp AT TIME ZONE 'UTC';
                END;

                meal_type := meal_entry->>'meal_type';
                calories := (meal_entry->>'calories')::float8;

                RAISE NOTICE '🍽️ Meal Entry ID: %', meal_entry->>'meal_id';
                RAISE NOTICE '   Parsed Time: %, Type: %, Calories: %',
                    meal_time, meal_type, calories;

                -- Get meal_type_id
                SELECT mt.meal_type_id INTO meal_type_id
                FROM drh_stateful_master.meal_type mt
                WHERE LOWER(mt.display) = LOWER(meal_type)
                LIMIT 1;

                IF meal_type_id IS NULL THEN
                    RAISE NOTICE '❌ No meal_type_id found for type: %', meal_type;
                    unmatched_count := unmatched_count + 1;
                    RAISE NOTICE '------------------------------------------------------------';
                    CONTINUE;
                END IF;

                -- Try to find exact match
                SELECT ni.*
                INTO matching_nutrition
                FROM drh_stateful_research_study.nutrition_intake ni
                JOIN drh_stateful_master.meal_type mt 
                    ON mt.meal_type_id = ni.meal_type_id 
                WHERE ni.subject_id = rsubject_id
				  AND date_trunc('second', ni.occurrence_time AT TIME ZONE 'UTC') = date_trunc('second', meal_time)                
                  AND ni.value_quantity = calories
                  AND ni.study_id = extract_record.study_id
                  AND LOWER(mt.display) = LOWER(meal_type)
                LIMIT 1;

                IF FOUND THEN
                    matched_count := matched_count + 1;

                    -- Check if mapping already exists
                    SELECT EXISTS (
                        SELECT 1
                        FROM drh_stateful_research_study.nutritionintake_mapping nm
                        WHERE nm.nutrition_mapping_id = matching_nutrition.id
                          AND nm.observation_mapping_id = extract_record.id
                    ) INTO mapping_exists;

                    IF NOT mapping_exists THEN
                        -- You can uncomment below insert if needed
                        INSERT INTO drh_stateful_research_study.nutritionintake_mapping (
                                    id,
                                    nutrition_mapping_id,
                                    observation_mapping_id,
                                    r_subject_id,
                                    study_id,
                                    tenant_id,
                                    rec_status_id,
                                    created_at,
                                    created_by
                                )
                                VALUES (
                                    drh_stateless_util.generate_unique_id(),
                                    matching_nutrition.id,
                                    extract_record.id,
                                    rsubject_id,
                                    extract_record.study_id,
                                    extract_record.tenant_id,
                                    status_active_id,
                                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                    extract_record.created_by
                                );
                        RAISE NOTICE '✅ Mapped: nutrition_id=%, observation_id=%',
                            matching_nutrition.id, extract_record.id;
                    ELSE
                        RAISE NOTICE 'ℹ️ Already mapped: nutrition_id=%, observation_id=%',
                            matching_nutrition.id, extract_record.id;
                    END IF;

                                ELSE
                    unmatched_count := unmatched_count + 1;
                    RAISE NOTICE '❌ No exact match found for subject=%, time=%, type=%, cal=%',
                        rsubject_id, meal_time, meal_type, calories;

                    -- Try to find closest match and treat first as valid match
                    FOR possible_match IN
                        SELECT ni.id, ni.occurrence_time, ni.value_quantity, mt.display AS type
                        FROM drh_stateful_research_study.nutrition_intake ni
                        JOIN drh_stateful_master.meal_type mt 
                          ON mt.meal_type_id = ni.meal_type_id
                        WHERE ni.subject_id = rsubject_id
                          AND ni.study_id = extract_record.study_id
                          AND DATE(ni.occurrence_time AT TIME ZONE 'UTC') = DATE(meal_time)
                        ORDER BY ABS(EXTRACT(EPOCH FROM (ni.occurrence_time - meal_time)))
                        LIMIT 5
                    LOOP
                        IF matching_nutrition IS NULL THEN
                            -- Use the first match as fallback mapping
                            matching_nutrition := possible_match;

                            -- Check for existing mapping
                            SELECT EXISTS (
                                SELECT 1
                                FROM drh_stateful_research_study.nutritionintake_mapping nm
                                WHERE nm.nutrition_mapping_id = matching_nutrition.id
                                  AND nm.observation_mapping_id = extract_record.id
                            ) INTO mapping_exists;

                            IF NOT mapping_exists THEN
                                -- INSERT into mapping table
                                INSERT INTO drh_stateful_research_study.nutritionintake_mapping (
                                    id,
                                    nutrition_mapping_id,
                                    observation_mapping_id,
                                    r_subject_id,
                                    study_id,
                                    tenant_id,
                                    rec_status_id,
                                    created_at,
                                    created_by
                                )
                                VALUES (
                                    drh_stateless_util.generate_unique_id(),
                                    matching_nutrition.id,
                                    extract_record.id,
                                    rsubject_id,
                                    extract_record.study_id,
                                    extract_record.tenant_id,
                                    status_active_id,
                                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                                    extract_record.created_by
                                );

                                matched_count := matched_count + 1;
                                unmatched_count := unmatched_count - 1;

                                RAISE NOTICE '✅ Fallback match inserted: nutrition_id=%, observation_id=%',
                                    matching_nutrition.id, extract_record.id;
                            ELSE
                                RAISE NOTICE 'ℹ️ Fallback match already mapped: nutrition_id=%, observation_id=%',
                                    matching_nutrition.id, extract_record.id;
                            END IF;
                        END IF;

                        -- Print all top 5 matches
                        RAISE NOTICE '   🔸 Close Match => ID: %, Time: %, Type: %, Calories: %',
                            possible_match.id, possible_match.occurrence_time, possible_match.type, possible_match.value_quantity;
                    END LOOP;

                END IF;

            EXCEPTION WHEN OTHERS THEN
                unmatched_count := unmatched_count + 1;
                RAISE NOTICE '⚠️ Error processing meal ID %: %', meal_entry->>'meal_id', SQLERRM;
            END;

            RAISE NOTICE '------------------------------------------------------------';
        END LOOP;
    END LOOP;

    RAISE NOTICE '🏁 Meal mapping process completed.';
    RAISE NOTICE '✅ Total Processed: % | 🟢 Matched: % | 🔴 Unmatched: %',
        total_processed, matched_count, unmatched_count;
END;
$function$;










