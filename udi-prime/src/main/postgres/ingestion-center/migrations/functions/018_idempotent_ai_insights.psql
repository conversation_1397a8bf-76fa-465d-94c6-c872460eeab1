DROP VIEW IF EXISTS drh_stateless_ai_insights.ai_conversation_log_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_ai_insights.ai_conversation_log_view 
WITH (security_invoker = true) AS
SELECT
    id,
    message_json,
    auth_provider_id,
    user_party_id,
    context_section,
    rec_status_id,
    created_at,
    created_by
FROM drh_stateful_ai_insights.ai_conversation_log
WHERE deleted_at IS NULL;


DROP FUNCTION IF EXISTS drh_stateless_ai_insights.save_ai_conversation_log(jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_ai_insights.save_ai_conversation_log(p_input_json jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_id TEXT := drh_stateless_util.get_unique_id();
    v_now TIMESTAMPTZ := CURRENT_TIMESTAMP AT TIME ZONE 'UTC';
    v_message_json JSONB;
    v_auth_provider_id TEXT;
    v_user_party_id VARCHAR(255);
    v_context_section TEXT;
    v_rec_status_id INT := 1;
    v_created_by TEXT;
    v_updated_by TEXT := NULL;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    exception_log_json JSONB;
    function_name TEXT := 'save_ai_conversation_log';
    current_query TEXT := pg_catalog.current_query();
BEGIN
    -- Extract fields from input JSON
    v_message_json    := p_input_json->'message_json';
    v_auth_provider_id := p_input_json->>'auth_provider_id';
    v_user_party_id    := p_input_json->>'user_party_id';
    v_context_section  := p_input_json->>'context_section';
    v_created_by       := COALESCE(p_input_json->>'current_user_party_id', 'UNKNOWN');
    

    INSERT INTO drh_stateful_ai_insights.ai_conversation_log (
        id,
        message_json,
        auth_provider_id,
        user_party_id,
        context_section,
        rec_status_id,
        created_at,
        created_by
    ) VALUES (
        v_id,
        v_message_json,
        v_auth_provider_id,
        v_user_party_id,
        v_context_section,
        v_rec_status_id,
        v_now,
        v_created_by
    );

    result := jsonb_build_object(
        'status', 'success',
        'message', 'AI conversation log saved successfully',
        'id', v_id
    );
    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred while saving AI conversation log', 'error_details', error_details_json);
    RETURN result;
END;
$function$
;
