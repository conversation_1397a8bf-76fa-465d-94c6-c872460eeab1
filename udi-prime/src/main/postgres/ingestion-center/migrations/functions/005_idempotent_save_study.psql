-----------------------research study view----------------------------------------------
DROP VIEW IF EXISTS drh_stateless_research_study.research_study_view;

CREATE OR REPLACE VIEW drh_stateless_research_study.research_study_view
WITH(security_invoker=true)
AS SELECT DISTINCT 
    rs.study_id,
    rs.study_display_id,
    rs.title,
    rs.description,
    rs.created_by,
    rs.start_date,
    rs.end_date,
    rs.research_study_identifier ->> 'value'::text AS nct_number,
    rs.visibility,
    rs.archive_status,
    lo.name AS study_location,
    rs.treatment_modality AS treatment_modalities,
    string_agg(asp.party_name::text, ', '::text) AS funding_source,
    rs.site_id,
    opv.organization_party_id,
    rs.tenant_id AS organization_id,
    pt.party_id AS practitioner_party_id,
    rec_status.code AS rec_status_code
FROM drh_stateful_research_study.research_study rs
    LEFT JOIN drh_stateful_research_study.location lo ON rs.site_id = lo.id
    LEFT JOIN drh_stateful_research_study.research_study_associated_party asp ON rs.study_id = asp.research_study_id 
        AND asp.party_role_type_id = (
            SELECT research_study_party_role.study_party_role_id
            FROM drh_stateful_master.research_study_party_role
            WHERE research_study_party_role.code::text = 'sponsor'::text
        )
    LEFT JOIN drh_stateless_research_study.organization_party_view opv ON rs.tenant_id = opv.organization_id
    LEFT JOIN drh_stateful_party.party pt ON rs.created_by = pt.party_id
    JOIN drh_stateful_party.record_status rec_status ON rs.rec_status_id = rec_status.id
WHERE rs.deleted_by IS NULL
GROUP BY rs.study_id, rs.study_display_id, rs.title, rs.description, rs.created_by, 
         rs.start_date, rs.end_date, rs.research_study_identifier, rs.visibility,
         lo.name, rs.treatment_modality, opv.organization_party_id, rs.tenant_id,
         pt.party_id, rec_status.code;

----------------------------------------------------------------------------------------------------------
------------------------RESEARCH STUDY CITATION DETAILS---------------------------------------------------
----------------------------------------------------------------------------------------------------------
-- Citation view
DROP VIEW IF EXISTS drh_stateless_research_study.research_study_citation_view;

CREATE OR REPLACE VIEW drh_stateless_research_study.research_study_citation_view
WITH(security_invoker=true)
AS SELECT 
    ci.study_id,
    ci.id AS citation_id,
    ci.title AS publication_title,
    ci.date AS publication_date,
    ci_doi.identifier_value as publication_doi,
    ci_pubmed.identifier_value AS pubmed_id,
    COALESCE(json_agg(ca.first_name) FILTER (WHERE ca.first_name IS NOT NULL), '[]'::json) AS citation_authors,
    ci.created_at,
    ci.citation_data_source
FROM drh_stateful_research_study.citation ci
LEFT JOIN drh_stateful_research_study.citation_identifier ci_pubmed 
    ON ci.id = ci_pubmed.citation_id 
    AND ci_pubmed.identifier_system = 'pubmed_id'::text AND ci_pubmed.deleted_at IS NULL
LEFT JOIN drh_stateful_research_study.citation_identifier ci_doi 
    ON ci.id = ci_doi.citation_id 
    AND ci_doi.identifier_system = 'DOI'::text  AND ci_doi.deleted_at IS NULL    
LEFT JOIN drh_stateful_research_study.citation_author ca
    ON ci.id = ca.citation_id
    AND ca.deleted_at IS NULL
WHERE ci.deleted_by IS NULL
GROUP BY 
    ci.study_id,
    ci.id,
    ci.title,
    ci.date,
    ci.identifier_value,
    ci_pubmed.identifier_value,
    ci_doi.identifier_value;

----------------------------------------------------------------------------------

DROP VIEW IF EXISTS drh_stateless_research_study.all_investigators_or_authors_view;

CREATE OR REPLACE VIEW drh_stateless_research_study.all_investigators_or_authors_view
WITH (security_invoker = true) AS
SELECT p.id,
    p.name,
    pt.party_id AS practitioner_party_id --investigator or author party id
FROM drh_stateful_research_study.practitioner p
JOIN drh_stateful_party.party pt ON pt.party_name = p.name::text
WHERE p.deleted_at IS NULL AND p.rec_status_id = 1;

----------------------------------------------------------------------------------
-----------------LIST INVESTIGATORS AND AUTHORS-----------------------------------
----------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_research_study.existing_team_collabrators_view;

CREATE OR REPLACE VIEW drh_stateless_research_study.existing_team_collabrators_view
WITH (security_invoker = true) AS 
-- Get citation authors (for authors/co-authors)
SELECT 
    ca.id,
    ca.first_name as name,
    p_role.display AS role,
    p_role.code AS role_code,
    ct.study_id
FROM drh_stateful_research_study.citation_author ca
JOIN drh_stateful_research_study.citation ct ON ct.id = ca.citation_id
JOIN drh_stateful_master.research_study_party_role p_role ON p_role.study_party_role_id = ca.role_id
WHERE ca.deleted_at IS NULL 
AND ca.rec_status_id = 1

UNION

-- Get associated party members (for investigators and study team)
SELECT 
    ap.associated_party_id as id,
    ap.party_name as name,
    p_role.display AS role,
    p_role.code AS role_code,
    ap.research_study_id AS study_id
FROM drh_stateful_research_study.research_study_associated_party ap
JOIN drh_stateful_master.research_study_party_role p_role ON p_role.study_party_role_id = ap.party_role_type_id
WHERE ap.deleted_at IS NULL;

DROP FUNCTION IF EXISTS drh_stateless_activity_audit.insert_activity_log_by_session(text, JSONB);

CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_activity_log_by_session(
    p_current_user_id text DEFAULT NULL,
    p_input_json JSONB DEFAULT NULL
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_activity_log jsonb;
    v_result jsonb;
    v_session_data record;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.insert_activity_log_by_session';
    current_query text := pg_catalog.current_query();
   	v_session_id text;
BEGIN
	v_session_id := p_input_json->>'session_id';
	-- Get the most recent activity log record for the session
    SELECT 
        suim.session_unique_id as session_unique_id,
        activity_name,
        activity_type, 
        activity_description,
        activity_hierarchy,
        hierarchy_path,
        request_url,
        organization_party_id,
        user_name,
        app_version,
        ip_address,
        jsonb_build_object(
		    'provenance', 'org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLogService.doFilterInternal',
		    'initiator', NULL,
		    'initiatorHost', (activity_data::jsonb)->>'initiatorHost',
		    'ipAddress', ip_address,
		    'userAgent', (activity_data::jsonb)->>'userAgent',
		    'httpMethod', (activity_data::jsonb)->>'httpMethod',
		    'statusCode', COALESCE(((activity_data::jsonb)->>'statusCode')::int, 200)
		) as activity_data,
        activity_log_level
    INTO v_session_data
    FROM drh_stateful_activity_audit.activity_log al
    left join drh_stateful_activity_audit.session_unique_id_mapping suim on suim.id =al.session_unique_id 
    WHERE al.session_id = v_session_id
    ORDER BY created_at DESC
    LIMIT 1;

    -- Build activity log JSON
    v_activity_log := jsonb_build_object(
        'session_unique_id', COALESCE(v_session_data.session_unique_id, drh_stateless_util.get_unique_id()),
        'activity_name', COALESCE(p_input_json->>'activity_name', NULL),
        'activity_type', v_session_data.activity_type,
        'activity_type_id', p_input_json->>'activity_type_id',
        'activity_description', p_input_json->>'activity_description',
        'activity_hierarchy', p_input_json->>'activity_hierarchy',
        'hierarchy_path', p_input_json->>'hierarchy_path',
        'request_url', p_input_json->>'request_url',
        'organization_party_id', v_session_data.organization_party_id,
        'user_name', v_session_data.user_name,
        'app_version', v_session_data.app_version,
        'session_id', v_session_id,
        'ip_address', v_session_data.ip_address,
        'activity_data', v_session_data.activity_data,
        'activity_log_level', COALESCE(p_input_json->>'activity_log_level', 'Level 6'),
        'created_by', p_current_user_id,
        'updated_by', p_current_user_id,
        'activity_level_id', p_input_json->>'activity_level_id'
    );   
	
    -- Call insert_activity_log with the constructed JSON
    v_result := drh_stateless_activity_audit.insert_activity_log(v_activity_log);
    
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Activity log inserted successfully',
        'data', v_result
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    ) VALUES (
        function_name,
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        NULL,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'No',
        NULL,
        NULL
    );

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during activity log insertion',
        'error_details', error_details_json
    );
END;
$function$;
-------------------------------------save study--------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study(varchar, text, varchar, varchar, text, int4);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study(varchar, text, varchar, varchar, text, int4, JSONB);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_research_study(p_study_display_id character varying DEFAULT NULL::character varying, p_org_party_id text DEFAULT NULL::text, p_title character varying DEFAULT NULL::character varying, p_description character varying DEFAULT NULL::character varying, p_created_by text DEFAULT 'UNKNOWN'::text, p_visibility integer DEFAULT NULL::integer, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql SECURITY DEFINER
AS $function$
DECLARE
    v_study_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
	v_collab_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_research_study.save_research_study';
    current_query TEXT := pg_catalog.current_query();
    duplicate_display_id_count INTEGER;
    v_tenant_id TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_display_id', p_study_display_id,
        'p_org_party_id', p_org_party_id,
        'p_title', p_title,
        'p_description', p_description,
        'p_created_by', p_created_by,
        'p_visibility', p_visibility
    );

    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Research study saved successfully');

    -- Check if the study_display_id is unique
    SELECT COUNT(rs.*) 
    INTO duplicate_display_id_count
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_research_study.organization o ON o.id=rs.tenant_id
    WHERE rs.study_display_id = p_study_display_id 
    AND rs.deleted_at IS NULL
   	AND o.party_id = p_org_party_id;

    IF duplicate_display_id_count > 0 THEN
        -- If duplicate found, return failure with an error message
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Duplicate study display ID found',
            'study_display_id', p_study_display_id
        );
        RETURN result;
    END IF;

    
    SELECT organization_id INTO v_tenant_id FROM drh_stateless_research_study.organization_party_view WHERE organization_party_id = p_org_party_id LIMIT 1;

        /*EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_tenant_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation
            FOR VALUES IN (%L)
            PARTITION BY LIST (study_id)',
            v_tenant_id, v_tenant_id
        );

        -- Create study partition if not exists
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_study_id_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation_tenant_%s
            FOR VALUES IN (%L)
            PARTITION BY LIST (research_subject_id)',
            v_study_id, v_tenant_id, v_study_id
        );*/

    -- Attempt to insert the new research study
    BEGIN
        INSERT INTO drh_stateful_research_study.research_study (
            study_id,
            research_study_identifier,
            study_display_id,
            title,
            description,    
            status_id,    
            tenant_id,   
            progress_status,   
            rec_status_id,  
            created_at,
            created_by,        
            visibility,
            archive_status
        ) VALUES (
            v_study_id, 
            '{"value": ""}',
            p_study_display_id,          
            p_title,
            p_description,  
            1,      
            v_tenant_id, -- Tenant ID
            1,  
            (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  -- rec_status_id 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_created_by,        
            p_visibility,
            false
        );
		
		-- Insert into study_collaboration table
	    INSERT INTO drh_stateful_research_study.study_collaboration (
	        collab_id,
	        study_id,
	        user_id,
	        access_level,
	        shared_at,
	        rec_status_id,
	        created_at,
	        created_by
	    ) VALUES (
	        v_collab_id,
	        v_study_id,
	        p_created_by,
	        'admin', -- Assuming 'owner' as the default access level
	        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
	        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  -- rec_status_id 
	        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
	        p_created_by
	    ); 

        -- Return success with the study ID
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Research study saved successfully',
            'study_id', v_study_id
        );
        IF p_activity_json ? 'session_id' THEN
            --Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='CREATE_STUDY' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Save Research Study',
                'activity_description', format('%s research study created successfully', p_title)
            );

            --Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_created_by,v_activity_log_json);
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during research study save',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;

-----------------------------update study settings--------------------------------------------
-- DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study_settings(varchar, varchar, varchar, varchar, varchar, varchar, varchar, date, date, varchar, date, varchar, varchar);

-- CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_research_study_settings(p_study_id character varying, p_study_title character varying, p_description character varying, p_location_id character varying, p_treatment_modalities character varying, p_funding_source character varying, p_nct_number character varying, p_start_date date, p_end_date date, p_publication_title character varying, p_publication_date date, p_publication_doi character varying, p_user_id character varying)
--  RETURNS jsonb
--  LANGUAGE plpgsql SECURITY DEFINER
-- AS $function$
-- DECLARE
--     result JSONB;
--     err_context TEXT;
--     err_state TEXT;
--     err_message TEXT;
--     err_detail TEXT;
--     err_hint TEXT;
--     error_details_json JSONB;
--     function_name TEXT := 'save_research_study_settings';
--     current_query TEXT := pg_catalog.current_query();
--     v_citation_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
--     funding_source_array TEXT[];
--     v_party_role_type_id TEXT;
--     v_associated_party_id TEXT;
--     party_name TEXT;

--     study_created_by TEXT;
--    current_visibility TEXT;
--    is_archived BOOLEAN;
-- BEGIN
--     -- Initialize result to success by default
--     result := jsonb_build_object('status', 'success', 'message', 'Research study settings updated successfully');

--     -- Get the study's created_by and current visibility
--     SELECT rs.created_by, sv.visibility_name, rs.archive_status 
--     INTO study_created_by, current_visibility, is_archived
--     FROM drh_stateful_research_study.research_study rs
--     JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
--     WHERE rs.study_id = p_study_id;

--     -- Check visibility restriction policy
--     IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
--         IF study_created_by != p_user_id THEN
--             result := jsonb_build_object(
--                 'status', 'failure',
--                 'message', 'Permission denied. Only the study creator can update the details.'
--             );
--             RETURN result;
--         END IF;
--     END IF;

--     -- Get the party_role_type_id for 'sponsor'
--     SELECT study_party_role_id INTO v_party_role_type_id
--     FROM drh_stateful_master.research_study_party_role
--     WHERE code = 'sponsor';

--     -- Ensure p_nct_number is at most 11 digits
-- 	IF p_nct_number IS NOT NULL AND length(p_nct_number) > 11 THEN
-- 	    result := jsonb_build_object(
-- 	        'status', 'failure',
-- 	        'message', 'NCT number exceeds maximum length of 11 digits',
-- 	        'nct_number', p_nct_number
-- 	    );
-- 	    RETURN result;
-- 	END IF;

--     -- Update the research study details if p_study_id is provided
--     IF p_study_id IS NOT NULL THEN
--         UPDATE drh_stateful_research_study.research_study
--         SET title = COALESCE(p_study_title, title),
--             description = COALESCE(p_description, description),
--             site_id = COALESCE(p_location_id, site_id),
--             treatment_modality = p_treatment_modalities,
--             research_study_identifier = CASE 
--                     WHEN p_nct_number IS NOT NULL THEN 
--                         jsonb_build_object('value', p_nct_number)::JSONB 
--                     ELSE 
--                         NULL 
--                 END,
--             start_date = p_start_date,
--             end_date = p_end_date,
--             updated_by = COALESCE(p_user_id, updated_by),
--             updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
--         WHERE study_id = p_study_id;

--         -- Check if a citation record already exists for the given study_id
--         IF EXISTS (SELECT 1 FROM drh_stateful_research_study.citation WHERE study_id = p_study_id) THEN
--             -- Update the existing citation record
--             UPDATE drh_stateful_research_study.citation
--             SET identifier_value = p_publication_doi,
--                 title = p_publication_title,
--                 date = p_publication_date,
--                 updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
--                 updated_by = p_user_id
--             WHERE study_id = p_study_id;
--         ELSE
--             -- Insert citation details if p_publication_title or p_publication_doi is provided
--             IF p_publication_title IS NOT NULL OR p_publication_doi IS NOT NULL THEN
--                 INSERT INTO drh_stateful_research_study.citation (
--                 id,
--                 identifier_system,
--                 identifier_value,
--                 title,
--                 date,
--                 rec_status_id,
--                 study_id,
--                 created_at,
--                 created_by
--                 ) VALUES (
--                 v_citation_id,
--                 'DOI',
--                 p_publication_doi,
--                 p_publication_title,
--                 p_publication_date,
--                 1,
--                 p_study_id,
--                 CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
--                 p_user_id
--                 );
--             END IF;
--         END IF;

--         -- Delete existing associated party records before inserting new values
--         DELETE FROM drh_stateful_research_study.research_study_associated_party
--         WHERE research_study_id = p_study_id and party_role_type_id=v_party_role_type_id;

--         -- Insert funding source details if p_funding_source is provided
--         IF p_funding_source IS NOT NULL THEN
--             funding_source_array := string_to_array(p_funding_source, ',');

--             FOREACH party_name IN ARRAY funding_source_array LOOP
--                 v_associated_party_id := drh_stateless_util.get_unique_id()::TEXT;

--                 INSERT INTO drh_stateful_research_study.research_study_associated_party (
--                     associated_party_id,
--                     research_study_id,
--                     party_role_type_id,
--                     party_name,
--                     classifier_id,
--                     period_start,
--                     period_end,
--                     created_at,
--                     created_by,
--                     updated_at,
--                     updated_by,
--                     deleted_at,
--                     deleted_by
--                 ) VALUES (
--                     v_associated_party_id,
--                     p_study_id,
--                     v_party_role_type_id,
--                     TRIM(party_name),
--                     NULL,
--                     NULL,
--                     NULL,
--                     CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
--                     p_user_id,
--                     NULL,
--                     NULL,
--                     NULL,
--                     NULL
--                 );
--             END LOOP;
--         END IF;
--     END IF;

--     -- Return success with the study ID
--     result := jsonb_build_object(
--         'status', 'success',
--         'message', 'Research study settings updated successfully',
--         'study_id', p_study_id
--     );

--     -- Return the final result
--     RETURN result;
-- EXCEPTION
--     WHEN OTHERS THEN
--         -- Capture error details
--         GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
--                                  err_state = RETURNED_SQLSTATE,
--                                  err_message = MESSAGE_TEXT,
--                                  err_detail = PG_EXCEPTION_DETAIL,
--                                  err_hint = PG_EXCEPTION_HINT;

--         -- Log the error details
--         INSERT INTO drh_stateful_activity_audit.exception_log (
--             function_name,
--             error_code,
--             error_message,
--             error_detail,
--             error_hint,
--             error_context,
--             query,
--             parameters,
--             occurred_at,
--             resolved,
--             resolved_at,
--             resolver_comments
--         ) VALUES (
--             function_name,
--             err_state,
--             err_message,
--             err_detail,
--             err_hint,
--             err_context,
--             current_query,
--             NULL,
--             CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
--             'No',
--             NULL,
--             NULL
--         );

--         -- Prepare error JSON
--         error_details_json := jsonb_build_object(
--             'error', err_message,
--             'detail', err_detail,
--             'hint', err_hint,
--             'context', err_context,
--             'state', err_state
--         );

--         -- Return failure with the error details
--         result := jsonb_build_object(
--             'status', 'failure',
--             'message', 'Error occurred during research study settings update',
--             'error_details', error_details_json
--         );

--         RETURN result;
-- END;
-- $function$
-- ;


-----------------------------------------------------------------------------------------------------------
----------------------------------------------SAVE PUBLICATION---------------------------------------------
-----------------------------------------------------------------------------------------------------------
--remove old function
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_study_citation_v1(varchar, varchar, date, varchar, varchar, varchar, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_study_citation(varchar, varchar, date, varchar, varchar, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_study_citation(varchar, varchar, date, varchar, varchar, varchar, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_study_citation(varchar, varchar, date, varchar, varchar, varchar, varchar, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_study_citation(p_study_id character varying, p_publication_title character varying, p_publication_date date, p_publication_doi character varying, p_user_id character varying, p_pubmed_id character varying, p_source character varying, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    -- Result variables
    result JSONB;
    
    -- Generated IDs
    v_citation_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    
    -- Study information
    v_study_created_by TEXT;
    v_current_visibility TEXT;
    v_is_archived BOOLEAN;
    
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_study_citation.save_study_citation';
	current_query TEXT := pg_catalog.current_query();
    
    -- Flag for DOI identifier
    v_has_doi BOOLEAN := FALSE;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    
        parameters_lst := jsonb_build_object(
            'p_study_id', p_study_id,
            'p_publication_title', p_publication_title,
            'p_publication_date', p_publication_date,
            'p_publication_doi', p_publication_doi,
            'p_user_id', p_user_id,
            'p_pubmed_id', p_pubmed_id,
            'p_source', p_source
        );

    -- Input validation
    IF p_study_id IS NULL OR TRIM(p_study_id) = '' THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Study ID cannot be NULL or empty'
        );
    END IF;
    
    -- Determine if we have enough data to proceed
    IF p_publication_title IS NULL AND p_publication_doi IS NULL AND p_pubmed_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'At least one of publication title, DOI, or PubMed ID must be provided'
        );
    END IF;
    

    -- Get the study's created_by and current visibility
    SELECT 
        rs.created_by, 
        sv.visibility_name, 
        rs.archive_status 
    INTO 
        v_study_created_by, 
        v_current_visibility, 
        v_is_archived
    FROM 
        drh_stateful_research_study.research_study rs
        JOIN drh_stateful_master.study_visibility sv ON rs.visibility = sv.visibility_id
    WHERE 
        rs.study_id = p_study_id;
        
    -- Check if study exists
    IF v_study_created_by IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Study not found'
        );
    END IF;

    -- Check visibility restriction policy
    IF (v_current_visibility = 'Private' OR v_is_archived IS TRUE) 
       AND v_study_created_by != p_user_id THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Permission denied. Only the study creator can update citation details for private or archived studies.'
        );
    END IF;

    -- Check for existing DOI
    IF p_publication_doi IS NOT NULL AND TRIM(p_publication_doi) != '' THEN
        IF EXISTS (
            SELECT 1
            FROM drh_stateful_research_study.citation_identifier ci
            JOIN drh_stateful_research_study.citation c ON ci.citation_id = c.id
            WHERE 
                c.study_id = p_study_id
                AND ci.identifier_system = 'DOI'
                AND ci.identifier_value = p_publication_doi
                AND c.deleted_at IS NULL 
                AND ci.deleted_at IS NULL
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'DOI already exists for this study'
            );
        END IF;
        v_has_doi := TRUE;
    END IF;

    -- Check for existing PubMed ID
    IF p_pubmed_id IS NOT NULL AND TRIM(p_pubmed_id) != '' THEN
        IF EXISTS (
            SELECT 1
            FROM drh_stateful_research_study.citation_identifier ci
            JOIN drh_stateful_research_study.citation c ON ci.citation_id = c.id
            WHERE 
                c.study_id = p_study_id
                AND ci.identifier_system = 'pubmed_id'
                AND ci.identifier_value = p_pubmed_id
                AND c.deleted_at IS NULL 
                AND ci.deleted_at IS NULL
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'PubMed ID already exists for this study'
            );
        END IF;
    END IF;

    -- Begin transaction
    BEGIN
        -- Insert new citation
        INSERT INTO drh_stateful_research_study.citation (
            id,
            identifier_system,
            identifier_value,
            title,
            date,
            rec_status_id,
            study_id,
            created_at,
            created_by,
            citation_data_source
        ) VALUES (
            v_citation_id,
            'DOI',
            '',
            p_publication_title,
            p_publication_date,
            1,
            p_study_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_user_id,
            p_source
        );

        -- Insert DOI if provided
        IF v_has_doi THEN
            INSERT INTO drh_stateful_research_study.citation_identifier (
                id,
                citation_id,
                identifier_system,
                identifier_value,
                rec_status_id,
                created_at,
                created_by
            ) VALUES (
                drh_stateless_util.get_unique_id()::TEXT,
                v_citation_id,
                'DOI',
                p_publication_doi,
                1,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_user_id
            );
        END IF;

        -- Insert PubMed ID if provided
        IF p_pubmed_id IS NOT NULL AND TRIM(p_pubmed_id) != '' THEN
            INSERT INTO drh_stateful_research_study.citation_identifier (
                id,
                citation_id,
                identifier_system,
                identifier_value,
                rec_status_id,
                created_at,
                created_by
            ) VALUES (
                drh_stateless_util.get_unique_id()::TEXT,
                v_citation_id,
                'pubmed_id',
                p_pubmed_id,
                1,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_user_id
            );
        END IF;
    END;

    -- Log activity if p_activity_json is provided
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_STUDY_CITATION' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Save Study Citation',
            'activity_description', 'Citation details added successfully'
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_user_id, v_activity_log_json);
    END IF;

    -- Return success result
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Citation details added successfully',
        'citation_id', v_citation_id
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details

        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving citation details',
            'error_details', error_details_json
        );

        RETURN result;
END;
$function$
;

------------------------------------------------------------------------------------------------------------------------------
-----------------------------------------------------SAVE RESEARCH STUDY SETTINGS---------------------------------------------
------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study_settings(varchar, varchar, varchar, varchar, varchar, varchar, varchar, date, date, varchar, date, varchar, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study_settings(varchar, varchar,varchar, varchar,varchar, varchar,varchar, date, date, varchar);
--DROP FUNCTION IF EXISTS drh_stateless_research_study.save_research_study_settings(varchar, varchar,varchar, varchar,varchar, varchar,varchar, date, date, varchar, jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_research_study_settings(
    p_study_id character varying,
    p_study_title character varying,
    p_description character varying,
    p_location_id character varying,
    p_treatment_modalities character varying,
    p_funding_source character varying,
    p_nct_number character varying,
    p_start_date date,
    p_end_date date,
    p_user_id character varying,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS jsonb
LANGUAGE plpgsql SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_research_study_settings';
    current_query TEXT := pg_catalog.current_query();
    funding_source_array TEXT[];
    v_party_role_type_id TEXT;
    v_associated_party_id TEXT;
    party_name TEXT;
    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Research study settings updated successfully');

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_study_title', p_study_title,
    'p_description', p_description,
    'p_location_id', p_location_id,
    'p_treatment_modalities', p_treatment_modalities,
    'p_funding_source', p_funding_source,
    'p_nct_number', p_nct_number,
    'p_start_date', p_start_date,
    'p_end_date', p_end_date,
    'p_user_id', p_user_id
    );

    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR is_archived IS TRUE THEN
        IF study_created_by != p_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;

    -- Get the party_role_type_id for 'sponsor'
    SELECT study_party_role_id INTO v_party_role_type_id
    FROM drh_stateful_master.research_study_party_role
    WHERE code = 'sponsor';

    -- Ensure p_nct_number is at most 11 digits
    IF p_nct_number IS NOT NULL AND length(p_nct_number) > 11 THEN
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'NCT number exceeds maximum length of 11 digits',
            'nct_number', p_nct_number
        );
        RETURN result;
    END IF;

    -- Update the research study details if p_study_id is provided
    IF p_study_id IS NOT NULL THEN
        UPDATE drh_stateful_research_study.research_study
        SET title = COALESCE(p_study_title, title),
            description = COALESCE(p_description, description),
            site_id = COALESCE(p_location_id, site_id),
            treatment_modality = p_treatment_modalities,
            research_study_identifier = CASE 
                WHEN p_nct_number IS NOT NULL THEN 
                    jsonb_build_object('value', p_nct_number)::JSONB 
                ELSE 
                    NULL 
                END,
            start_date = p_start_date,
            end_date = p_end_date,
            updated_by = COALESCE(p_user_id, updated_by),
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE study_id = p_study_id;

        -- Delete existing associated party records before inserting new values
        DELETE FROM drh_stateful_research_study.research_study_associated_party
        WHERE research_study_id = p_study_id and party_role_type_id = v_party_role_type_id;

        -- Insert funding source details if p_funding_source is provided
        IF p_funding_source IS NOT NULL THEN
            funding_source_array := string_to_array(p_funding_source, ',');

            FOREACH party_name IN ARRAY funding_source_array LOOP
                v_associated_party_id := drh_stateless_util.get_unique_id()::TEXT;

                INSERT INTO drh_stateful_research_study.research_study_associated_party (
                    associated_party_id,
                    research_study_id,
                    party_role_type_id,
                    party_name,
                    classifier_id,
                    period_start,
                    period_end,
                    created_at,
                    created_by,
                    updated_at,
                    updated_by,
                    deleted_at,
                    deleted_by
                ) VALUES (
                    v_associated_party_id,
                    p_study_id,
                    v_party_role_type_id,
                    TRIM(party_name),
                    NULL,
                    NULL,
                    NULL,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_user_id,
                    NULL,
                    NULL,
                    NULL,
                    NULL
                );
            END LOOP;
        END IF;
    END IF;

    -- Log activity if p_activity_json is provided
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_RESEARCH_STUDY_SETTINGS' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Update Research Study Settings',
            'activity_description', 'Research study settings updated successfully'
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_user_id, v_activity_log_json);
    END IF;

    -- Return success with the study ID
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Research study settings updated successfully',
        'study_id', p_study_id
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during research study settings update',
            'error_details', error_details_json
        );

        RETURN result;
END;
$function$;


-----------------------------update inline study--------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_inline(varchar, jsonb, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_inline(varchar, jsonb, varchar, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_research_study_inline(p_study_id character varying, json_input jsonb, p_user_id character varying, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql SECURITY DEFINER
AS $function$
DECLARE
    key TEXT;
    value TEXT;
    study_location TEXT;
    update_query TEXT;
    rows_affected INTEGER;
    valid_fields TEXT[] := ARRAY['title', 'start_date', 'end_date', 'nct_number', 'description', 'treatment_modality', 'study_location','funding_source'];
    invalid_keys TEXT[];
	funding_source_array TEXT[];
    v_associated_party_id TEXT;
	v_party_role_type_id TEXT;
	party_name TEXT;
    nct_number TEXT;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_research_study_inline';
    current_query TEXT := pg_catalog.current_query();

    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;

    --Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Research study updated successfully');

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'json_input', json_input,
        'p_user_id', p_user_id
    );
	
    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;
    
	-- Get the party_role_type_id for 'sponsor'
    SELECT study_party_role_id INTO v_party_role_type_id
    FROM drh_stateful_master.research_study_party_role
    WHERE code = 'sponsor';

    -- Initialize an array for invalid keys
    invalid_keys := ARRAY[]::TEXT[];

    -- Check each key in the JSON input
    FOR key IN SELECT jsonb_object_keys(json_input)
    LOOP
        IF NOT (key = ANY(valid_fields)) THEN
            invalid_keys := array_append(invalid_keys, key);
        END IF;
    END LOOP;

    -- If invalid keys are found, return an error message
    IF array_length(invalid_keys, 1) IS NOT NULL THEN
        result := jsonb_build_object('status', 'failure', 'message', format('Invalid fields: %s', array_to_string(invalid_keys, ', ')));
        RETURN result;
    END IF;

    -- Initialize the dynamic update query    
    update_query := 'UPDATE drh_stateful_research_study.research_study SET updated_by = ' || quote_literal(p_user_id) || ', updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'', ';

    -- Check if study_location is present in the JSON input
    IF json_input ? 'study_location' THEN
        -- Extract the study_location value
        study_location := json_input->>'study_location';

        -- Dynamically construct the update query to set site_id
        update_query := update_query || format('site_id = %L, ', study_location);
    END IF;

    -- Check if funding_source is present in the JSON input
    IF json_input ? 'funding_source' THEN
        -- Extract the funding_source value
        funding_source_array := string_to_array(json_input->>'funding_source', ',');

        -- Delete existing associated party records for the funding source
        DELETE FROM drh_stateful_research_study.research_study_associated_party
        WHERE research_study_id = p_study_id
          AND party_role_type_id = v_party_role_type_id;

        -- Insert new funding source details
        FOREACH party_name IN ARRAY funding_source_array LOOP
            v_associated_party_id := drh_stateless_util.get_unique_id()::TEXT;

            INSERT INTO drh_stateful_research_study.research_study_associated_party (
                associated_party_id,
                research_study_id,
                party_role_type_id,
                party_name,
                classifier_id,
                period_start,
                period_end,
                created_at,
                created_by,
                updated_at,
                updated_by,
                deleted_at,
                deleted_by
            ) VALUES (
                v_associated_party_id,
                p_study_id,
                v_party_role_type_id,
                TRIM(party_name),
                NULL,
                NULL,
                NULL,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_user_id,
                NULL,
                NULL,
                NULL,
                NULL
            );
        END LOOP;
    END IF;

    -- Check if nct_number is present in the JSON input
    IF json_input ? 'nct_number' THEN
        -- Extract the nct_number value
        nct_number := json_input->>'nct_number';

        -- Ensure p_nct_number is at most 11 digits
        IF length(nct_number) > 11 THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'NCT number exceeds maximum length of 11 digits',
                'nct_number', nct_number
            );
            RETURN result;
        END IF;

        -- Dynamically construct the update query to set research_study_identifier
        -- Ensure the research_study_identifier is not null before updating
        update_query := update_query || format(
            'research_study_identifier = jsonb_set(
                COALESCE(research_study_identifier, ''{}''::jsonb),
                ''{value}'', 
                ''"%s"''::jsonb
            ), ', 
            nct_number
        );

    END IF;

    -- Loop through each key-value pair in the JSON object for other fields
    FOR key, value IN 
        SELECT * FROM jsonb_each_text(json_input)
    LOOP
        -- Skip the nct_number and study_location field as it's already handled above
        IF key = 'nct_number' OR key = 'study_location' OR key = 'funding_source' THEN
            CONTINUE;
        END IF;

        -- Dynamically construct the SET part of the query for other fields
        update_query := update_query || format('%I = %L, ', key, value);
    END LOOP;

    -- Remove the trailing comma and space
    update_query := left(update_query, length(update_query) - 2);

    -- Append the WHERE clause
    update_query := update_query || format(' WHERE study_id = %L', p_study_id);

    -- Execute the dynamic query
    BEGIN
        EXECUTE update_query;
        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- Return success with the number of rows updated
        result := jsonb_build_object(
            'status', 'success',
            'message', format('Update successful: %s row(s) affected.', rows_affected),
            'rows_affected', rows_affected
        );

        --Activity log insertion
        IF p_activity_json ? 'session_id' THEN 
            --Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_RESEARCH_STUDY' AND deleted_at IS NULL;
    
            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Research Study Updated',
                'activity_description', 'Research study updated successfully.'
            );
    
            --Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_user_id,v_activity_log_json);   
        END IF;      
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        
        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during research study update',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;

----------------------------------------------------------------------------------------------------------------------------
--------------------------------------------SUB FUNCTION TO HANDLE IDENTIFIER UPDATE----------------------------------------
----------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.handle_identifier_update(varchar, varchar, varchar, varchar);
CREATE OR REPLACE FUNCTION drh_stateless_research_study.handle_identifier_update(
    p_citation_id character varying,
    p_identifier_system character varying,
    p_identifier_value character varying,
    p_user_id character varying
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.handle_identifier_update';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_citation_id', p_citation_id,
        'p_identifier_system', p_identifier_system,
        'p_identifier_value', p_identifier_value,
        'p_user_id', p_user_id
    );

    IF p_citation_id IS NOT NULL AND (p_identifier_value IS NULL OR p_identifier_value='') THEN
        -- Soft delete existing identifier
        UPDATE drh_stateful_research_study.citation_identifier
        SET 
            deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            deleted_by = p_user_id
        WHERE 
            citation_id = p_citation_id 
            AND identifier_system = p_identifier_system 
            AND deleted_at IS NULL;

    ELSIF EXISTS (
        SELECT 1 FROM drh_stateful_research_study.citation_identifier
        WHERE citation_id = p_citation_id 
          AND identifier_system = p_identifier_system
    ) THEN
        -- Update existing identifier
        UPDATE drh_stateful_research_study.citation_identifier
        SET 
            identifier_value = p_identifier_value,
            updated_by = p_user_id,
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE 
            citation_id = p_citation_id 
            AND identifier_system = p_identifier_system;

    ELSE
        -- Insert new identifier
        INSERT INTO drh_stateful_research_study.citation_identifier (
            id, citation_id, identifier_system, identifier_value, 
            rec_status_id, created_by, created_at
        ) VALUES (
            drh_stateless_util.get_unique_id(),
            p_citation_id,
            p_identifier_system,
            p_identifier_value,
            1,
            p_user_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            err_state   = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail  = PG_EXCEPTION_DETAIL,
            err_hint    = PG_EXCEPTION_HINT,
            err_context = PG_EXCEPTION_CONTEXT;

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        RAISE;

END;
$function$;

----------------------------------------------------------------------------------------------------------------------------
--------------------------------------------update inline publcation--------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.check_identifier_exists(varchar, varchar, varchar, varchar);
CREATE OR REPLACE FUNCTION drh_stateless_research_study.check_identifier_exists(
    p_study_id character varying,
    p_identifier_system character varying,
    p_identifier_value character varying,
    p_exclude_citation_id character varying DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    exists_flag BOOLEAN;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.check_identifier_exists';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_identifier_system', p_identifier_system,
        'p_identifier_value', p_identifier_value,    
        'p_exclude_citation_id',p_exclude_citation_id
    );

    SELECT EXISTS (
        SELECT 1
        FROM drh_stateful_research_study.citation_identifier ci
        JOIN drh_stateful_research_study.citation c ON ci.citation_id = c.id
        WHERE 
            c.study_id = p_study_id
            AND ci.identifier_system = p_identifier_system
            AND ci.identifier_value = p_identifier_value
            AND c.deleted_at IS NULL 
            AND ci.deleted_at IS NULL
            AND ci.identifier_value !=''
            AND (p_exclude_citation_id IS NULL OR ci.citation_id != p_exclude_citation_id)
    ) INTO exists_flag;
    
    RETURN exists_flag;
EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            err_state   = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail  = PG_EXCEPTION_DETAIL,
            err_hint    = PG_EXCEPTION_HINT,
            err_context = PG_EXCEPTION_CONTEXT;

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

END;
$function$;
----------------------------------------------------------------------------------------------------------------------------
--------------------------------------------update inline publcation--------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_publicaton_inline(varchar, jsonb, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_publicaton_inline(varchar, varchar, jsonb, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_publication_inline(varchar, varchar, jsonb, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_publication_inline(varchar, varchar, jsonb, varchar, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_publication_inline(
    p_study_id character varying, 
    p_citation_id character varying, 
    json_input jsonb, 
    p_user_id character varying,
    p_activity_json JSONB DEFAULT NULL::JSONB
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    key TEXT;
    value TEXT;
    v_citation_id TEXT;
    v_identifier_id TEXT;
    update_query TEXT;
    rows_affected INTEGER;
    valid_fields TEXT[] := ARRAY['publication_title', 'publication_date', 'publication_doi', 'pubmed_id', 'citation_data_source'];
    invalid_keys TEXT[] := ARRAY[]::TEXT[];
    result JSONB := jsonb_build_object('status', 'success', 'message', 'Publication updated successfully');
    -- Error handling variables
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_publication_inline';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    
    parameters_lst := jsonb_build_object(
                'p_study_id', p_study_id,
                'p_citation_id', p_citation_id,
                'json_input', json_input,
                'p_user_id', p_user_id
    );

    -- Validate input fields
    FOR key IN SELECT jsonb_object_keys(json_input) LOOP
        IF NOT (key = ANY(valid_fields)) THEN
            invalid_keys := array_append(invalid_keys, key);
        END IF;
    END LOOP;

    -- Return early if invalid keys are found
    IF array_length(invalid_keys, 1) IS NOT NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure', 
            'message', format('Invalid fields: %s', array_to_string(invalid_keys, ', '))
        );
    END IF;

    -- Check if DOI already exists for this study
    IF json_input ? 'publication_doi' AND 
       drh_stateless_research_study.check_identifier_exists(
           p_study_id, 'DOI', json_input->>'publication_doi', p_citation_id
       ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'DOI already exists for this study'
        );
    END IF;

    -- Check if PubMed ID already exists for this study
    IF json_input ? 'pubmed_id' AND 
       drh_stateless_research_study.check_identifier_exists(
           p_study_id, 'pubmed_id', json_input->>'pubmed_id', p_citation_id
       ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'PubMed ID already exists for this study'
        );
    END IF;

    -- Check if the citation record exists
    SELECT id INTO v_citation_id
    FROM drh_stateful_research_study.citation
    WHERE id = p_citation_id;

    -- Handle record creation or update
    IF v_citation_id IS NULL THEN
        -- Create new citation if title is provided
        IF json_input ? 'publication_title' THEN
            -- Insert new citation record
            INSERT INTO drh_stateful_research_study.citation (
                id, study_id, title, rec_status_id, created_by, created_at
            ) VALUES (
                drh_stateless_util.get_unique_id(), 
                p_study_id, 
                json_input->>'publication_title', 
                1, 
                p_user_id, 
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            ) RETURNING id INTO v_citation_id;
            
            -- Insert or update additional identifiers
            PERFORM drh_stateless_research_study.handle_identifier_update(v_citation_id, 'pubmed_id', json_input->>'pubmed_id', p_user_id);
            PERFORM drh_stateless_research_study.handle_identifier_update(v_citation_id, 'DOI', json_input->>'publication_doi', p_user_id);
        ELSE
            RETURN jsonb_build_object(
                'status', 'failure', 
                'message', 'Record not found and publication_title is missing'
            );
        END IF;
    ELSE
        -- Update existing identifiers
        PERFORM drh_stateless_research_study.handle_identifier_update(v_citation_id, 'pubmed_id', json_input->>'pubmed_id', p_user_id);
        PERFORM drh_stateless_research_study.handle_identifier_update(v_citation_id, 'DOI', json_input->>'publication_doi', p_user_id);
        
        -- Build and execute update query for citation record
        update_query := 'UPDATE drh_stateful_research_study.citation SET updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC''';
        
        -- Add user ID if provided
        IF p_user_id IS NOT NULL AND p_user_id != '' AND p_user_id != 'null' THEN
            update_query := update_query || format(', updated_by = %L', p_user_id);
        END IF;

        -- Handle source field separately
        IF json_input ? 'citation_data_source' THEN
            update_query := update_query || format(', citation_data_source = %L', COALESCE(json_input->>'citation_data_source', ''));
        END IF;

        -- Add title and date fields if provided
        IF json_input ? 'publication_title' THEN
            update_query := update_query || format(', title = %L', json_input->>'publication_title');
        END IF;
        
        IF json_input ? 'publication_date' THEN
            update_query := update_query || format(', date = %L', json_input->>'publication_date');
        END IF;

        -- Complete the query with WHERE clause
        update_query := update_query || format(' WHERE id = %L', p_citation_id);

        -- Execute the update query
        BEGIN
            EXECUTE update_query;
            GET DIAGNOSTICS rows_affected = ROW_COUNT;
            
            -- Return success with number of rows updated
            result := jsonb_build_object(
                'status', 'success',
                'message', format('Update successful: %s row(s) affected.', rows_affected),
                'rows_affected', rows_affected
            );
        EXCEPTION WHEN OTHERS THEN
            -- Capture error details
            GET STACKED DIAGNOSTICS 
                err_context = PG_EXCEPTION_CONTEXT,
                err_state = RETURNED_SQLSTATE,
                err_message = MESSAGE_TEXT,
                err_detail = PG_EXCEPTION_DETAIL,
                err_hint = PG_EXCEPTION_HINT;

            -- Log the error details           

            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst  
            );

            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Prepare error JSON
            error_details_json := jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            );

            -- Return failure with the error details
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Error occurred during Publication update',
                'error_details', error_details_json
            );
            RETURN result;
        END;
    END IF;

    -- Log activity if p_activity_json is not null
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_PUBLICATION' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Update Publication',
            'activity_description', 'Publication updated successfully'
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_user_id, v_activity_log_json);
    END IF;
    -- Return the final result
    RETURN result;
END;
$function$;

DROP FUNCTION IF EXISTS drh_stateless_research_study.update_publication_inline_v1(varchar, varchar, jsonb, varchar);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_publication_inline_v1(p_study_id character varying, p_citation_id character varying, json_input jsonb, p_user_id character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    key TEXT;
    value TEXT;
	v_citation_id TEXT;
    update_query TEXT;
    rows_affected INTEGER;
    valid_fields TEXT[] := ARRAY['publication_title', 'publication_date', 'publication_doi','pubmed_id', 'source'];
    invalid_keys TEXT[];
    record_exists INTEGER;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_publication_inline_v1';
    current_query TEXT := pg_catalog.current_query();
  	v_identifier_id TEXT;
  	v_current_source TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
begin
	
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Publication updated successfully');  

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_citation_id', p_citation_id,
    'json_input', json_input,
    'p_user_id', p_user_id
    );

    -- Initialize an array for invalid keys
    invalid_keys := ARRAY[]::TEXT[];

    -- Check each key in the JSON input
    FOR key IN SELECT jsonb_object_keys(json_input)
    LOOP
        IF NOT (key = ANY(valid_fields)) THEN
            invalid_keys := array_append(invalid_keys, key);
        END IF;
    END LOOP;

    -- If invalid keys are found, return an error message
    IF array_length(invalid_keys, 1) IS NOT NULL THEN
        result := jsonb_build_object('status', 'failure', 'message', format('Invalid fields: %s', array_to_string(invalid_keys, ', ')));
        RETURN result;
    END IF;

   -- Check if the record exists for the given study_id
    SELECT id INTO v_citation_id
    FROM drh_stateful_research_study.citation
    WHERE id = p_citation_id;

    -- If no record is found, check if publication_title is provided and insert a new record
    IF v_citation_id IS NULL THEN  

        IF json_input ? 'publication_title' THEN
            -- v_citation_id := drh_stateless_util.get_unique_id ()::TEXT;
            INSERT INTO drh_stateful_research_study.citation (id,identifier_system,identifier_value,study_id, title, rec_status_id,created_by, created_at)
            VALUES (drh_stateless_util.get_unique_id (),'DOI','',p_study_id, json_input->>'publication_title',  1,p_user_id, CURRENT_TIMESTAMP AT TIME ZONE 'UTC') returning id into v_citation_id ;
           	
            -- Handle PubMed ID if provided
            IF json_input ? 'pubmed_id' THEN
                v_identifier_id := drh_stateless_util.get_unique_id()::TEXT;
                INSERT INTO drh_stateful_research_study.citation_identifier (
                    id, citation_id, identifier_system, identifier_value, rec_status_id, created_by, created_at
                )
                VALUES (
                    drh_stateless_util.get_unique_id (), v_citation_id, 'pubmed_id', json_input->>'pubmed_id',
                    1, p_user_id, CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                )returning id into v_citation_id;
            END IF;

            -- Handle DOI if provided
            IF json_input ? 'publication_doi' THEN
                -- v_identifier_id := drh_stateless_util.get_unique_id()::TEXT;
                INSERT INTO drh_stateful_research_study.citation_identifier (
                    id, citation_id, identifier_system, identifier_value, rec_status_id, created_by, created_at
                )
                VALUES (
                    drh_stateless_util.get_unique_id (), v_citation_id, 'DOI', json_input->>'publication_doi',
                    1, p_user_id, CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                )returning id into v_identifier_id;
            END IF;
        ELSE
            result := jsonb_build_object('status', 'failure', 'message', 'Record not found and publication_title is missing');
            RETURN result;
        END IF;
    ELSE
        -- If record exists, handle PubMed ID and DOI updates
        IF json_input ? 'pubmed_id' THEN
            -- Update existing PubMed ID if present
            IF EXISTS (
                SELECT 1 FROM drh_stateful_research_study.citation_identifier
                WHERE citation_id = v_citation_id AND identifier_system = 'pubmed_id'
            ) THEN
                UPDATE drh_stateful_research_study.citation_identifier
                SET identifier_value = json_input->>'pubmed_id',
                    updated_by = p_user_id,
                    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                WHERE citation_id = v_citation_id AND identifier_system = 'pubmed_id';
            ELSE
                -- Insert new PubMed ID if it does not exist
                -- v_identifier_id := drh_stateless_util.get_unique_id()::TEXT;
                INSERT INTO drh_stateful_research_study.citation_identifier (
                    id, citation_id, identifier_system, identifier_value, rec_status_id, created_by, created_at
                )
                VALUES (
                    drh_stateless_util.get_unique_id(), v_citation_id, 'pubmed_id', json_input->>'pubmed_id',
                    1, p_user_id, CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                )returning id into v_identifier_id;
            END IF;
        END IF;

        IF json_input ? 'publication_doi' THEN
            -- Update existing DOI if present
            IF EXISTS (
                SELECT 1 FROM drh_stateful_research_study.citation_identifier
                WHERE citation_id = v_citation_id AND identifier_system = 'DOI'
            ) THEN
                UPDATE drh_stateful_research_study.citation_identifier
                SET identifier_value = json_input->>'publication_doi',
                    updated_by = p_user_id,
                    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                WHERE citation_id = v_citation_id AND identifier_system = 'DOI';
            ELSE
                -- Insert new DOI if it does not exist
                v_identifier_id := drh_stateless_util.get_unique_id()::TEXT;
                INSERT INTO drh_stateful_research_study.citation_identifier (
                    id, citation_id, identifier_system, identifier_value, rec_status_id, created_by, created_at
                )
                VALUES (
                    drh_stateless_util.get_unique_id(), v_citation_id, 'DOI', json_input->>'publication_doi',
                    1, p_user_id, CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                )returning id into v_identifier_id;
            END IF;
        END IF;
    END IF;

    -- Update the citation record itself (title, source, etc.)
    update_query := 'UPDATE drh_stateful_research_study.citation SET updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'', ';
    IF p_user_id IS NOT NULL AND p_user_id != '' AND p_user_id != 'null' THEN
        update_query := update_query || format('updated_by = %L, ', p_user_id);
    END IF;

    -- Update source (and only update source if changed)
    IF json_input ? 'source' then
     RAISE NOTICE 'Updating citation_data_source with value: %', json_input->>'source';
        update_query := update_query || format('citation_data_source = %L, ',
            COALESCE(json_input->>'source', ''));
    END IF;

    -- Add other fields (publication title, publication date) to the update query
    FOR key, value IN SELECT * FROM jsonb_each_text(json_input)
    LOOP
        IF key = 'pubmed_id' OR key = 'publication_doi' OR key = 'source' THEN
            CONTINUE;
        END IF;

        -- Handle title and date updates
        CASE key
            WHEN 'publication_title' THEN
                update_query := update_query || format('title = %L, ', value);
            WHEN 'publication_date' THEN
                update_query := update_query || format('publication_date = %L, ', value);
            ELSE
                RAISE EXCEPTION 'Unexpected key: %', key;
        END CASE;
    END LOOP;

    -- Remove the trailing comma
    update_query := left(update_query, length(update_query) - 2);

    -- Append WHERE clause
    update_query := update_query || format(' WHERE id = %L', p_citation_id);

    -- Execute the dynamic query
    BEGIN
        EXECUTE update_query;
        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- Return success with number of rows updated
        result := jsonb_build_object(
            'status', 'success',
            'message', format('Update successful: %s row(s) affected.', rows_affected),
            'rows_affected', rows_affected
        );
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during Publication update',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;

-- ************************************************************************************************
-- Function Name: delete_research_study
-- Purpose:
-- This function performs a soft delete operation for a research study by updating the `deleted_at` 
-- and `deleted_by` fields in the `research_study` table instead of permanently deleting the record. 
-- It identifies the research study to be deleted using the provided `research_study_id`.
-- Parameters:
--  - research_study_id: The unique identifier for the research study.
--  - current_user_id: The ID of the user performing the soft delete operation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

DROP FUNCTION IF EXISTS drh_stateless_research_study.delete_research_study(varchar, text);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.delete_research_study(
    research_study_id CHARACTER VARYING, -- The ID of the research study to be soft-deleted
    deleted_by_user_id TEXT DEFAULT 'UNKNOWN' -- The user or process performing the deletion
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB; -- Holds the result of the operation
    err_context TEXT; -- Error context for exception handling
    err_state TEXT; -- SQLSTATE error code
    err_message TEXT; -- Error message
    err_detail TEXT; -- Detailed error information
    err_hint TEXT; -- Hint for resolving the error
    error_details_json JSONB; -- JSON object for error details
    function_name TEXT := 'drh_stateless_research_study.delete_research_study'; -- The function name for logging
    current_query TEXT := pg_catalog.current_query(); -- Captures the current query
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize the result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Research study deleted successfully');

    parameters_lst := jsonb_build_object(
    'research_study_id', research_study_id,
    'deleted_by_user_id', deleted_by_user_id
    );


    -- Perform the soft delete
    BEGIN
        UPDATE drh_stateful_research_study.research_study
        SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Record the deletion timestamp
            deleted_by = deleted_by_user_id, -- Record the user or process performing the deletion
            updated_by = deleted_by_user_id,
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE study_id = research_study_id;

        -- Check if any row was updated
        IF NOT FOUND THEN
            result := jsonb_build_object('status', 'failure', 'message', 'No matching research study found for deletion');
            RETURN result;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during research study deletion',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$;

-- ************************************************************************************************
-- Function Name: update_research_study_visibility
-- Purpose:
-- This function updates the `visibility` of a research study in the `research_study` table based 
-- on the provided `research_study_id` and `study_visibility_id`. It also updates the `updated_by`
-- and `updated_at` fields with the details of the user performing the operation and the current
-- timestamp, respectively.
-- Parameters:
--  - research_study_id: The unique identifier for the research study.
--  - study_visibility_id: The visibility level to be applied to the research study.
--  - current_user_id: The ID of the user performing the operation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_visibility(varchar, int4, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_visibility(varchar, int4, varchar, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_research_study_visibility(
    research_study_id CHARACTER VARYING,
    study_visibility_id INTEGER,
    current_user_id CHARACTER VARYING,
    p_activity_json JSONB DEFAULT NULL::JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    rows_affected INTEGER;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_research_study_visibility';
    current_query TEXT := pg_catalog.current_query();
    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Visibility updated successfully');

    parameters_lst := jsonb_build_object(
    'research_study_id', research_study_id,
    'study_visibility_id', study_visibility_id,
    'current_user_id', current_user_id
    );

    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = research_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != current_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only study creator can update visibility.'
            );
            RETURN result;
        END IF;
    END IF;

    -- Update the visibility column, updated_by, and updated_at for the given study ID
    BEGIN
        UPDATE drh_stateful_research_study.research_study
        SET visibility = study_visibility_id,
            updated_by = current_user_id,
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE study_id = research_study_id;

        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- If no rows are updated, return a failure result
        IF rows_affected = 0 THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'No rows updated. Invalid research study ID.'
            );
            RETURN result;
        END IF;

        -- Activity log feature
        IF p_activity_json IS NOT NULL THEN
            -- Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_VISIBILITY' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Update Research Study Visibility',
                'activity_description', format('Visibility updated to %s for study %s', study_visibility_id, research_study_id)
            );

            -- Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(current_user_id, v_activity_log_json);
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while updating visibility',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$;

-- ************************************************************************************************
-- Function Name: update_research_study_archive_status
-- Purpose:
-- This function updates the `archive_status` of a research study in the `research_study` table 
-- based on the provided `research_study_id` and `archive_status` (boolean). It also updates the 
-- `updated_by` and `updated_at` fields with the details of the user performing the operation and 
-- the current timestamp, respectively.
-- Parameters:
--  - research_study_id: The unique identifier for the research study.
--  - archive_status: A boolean value indicating whether the research study is archived (`TRUE`) 
--    or not (`FALSE`).
--  - current_user_id: The ID of the user performing the operation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_archive_status(varchar, bool, varchar);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_research_study_archive_status(varchar, bool, varchar,jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_research_study_archive_status(
    research_study_id CHARACTER VARYING,
    v_is_archived BOOLEAN,
    current_user_id CHARACTER VARYING,
    p_activity_json JSONB DEFAULT NULL::JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    rows_affected INTEGER;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_research_study_archive_status';
    current_query TEXT := pg_catalog.current_query();

    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Archive status updated successfully');

    parameters_lst := jsonb_build_object(
    'research_study_id', research_study_id,
    'v_is_archived', v_is_archived,
    'current_user_id', current_user_id
    );


    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = research_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != current_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;


    -- Update the archive_status, updated_by, and updated_at for the given study ID
    BEGIN
        UPDATE drh_stateful_research_study.research_study
        SET archive_status = v_is_archived,
            updated_by = current_user_id,
            updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE study_id = research_study_id;

        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- If no rows are updated, return a failure result
        IF rows_affected = 0 THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'No rows updated. Invalid research study ID.'
            );
            RETURN result;
        END IF;

        -- Activity log feature
        IF p_activity_json IS NOT NULL THEN
            -- Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_ARCHIVE_STATUS' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Update Archive Status',
                'activity_description', format('Archive status set to %s for study %s', v_is_archived, research_study_id)
            );

            -- Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(current_user_id, v_activity_log_json);
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while updating archive status',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$;

-- ***************************************************************************************************
-- Create a view to fetch all public research studies
-- This function retrieves study details from the research study table for studies marked as "Public" and "Private," where the private studies are created by the current user.
-- and not deleted. Only studies with a specific record status (rec_status_id = 1) are included.

DROP FUNCTION IF EXISTS drh_stateless_research_study.get_all_studies(varchar);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.get_all_studies(current_user_id character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result jsonb;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_research_study.get_my_studies';
    current_query text := pg_catalog.current_query();
    v_tenant_id text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
            'current_user_id', current_user_id
    );

    BEGIN

        --fetch current user tenant id
       	SELECT  p.tenant_id into v_tenant_id
        FROM drh_stateful_research_study.practitioner p
        JOIN drh_stateful_party.party pt ON p.practitioner_party_id = pt.party_id
        WHERE pt.party_id = current_user_id
        LIMIT 1;

        -- Directly build the JSON array of studies without intermediate variables
        SELECT COALESCE(
            jsonb_build_object(
                'status', 'success',
                'message', 'Studies details fetched successfully',
                'data', COALESCE(
                    jsonb_agg(
                        jsonb_build_object(
                            'id', rs.study_id,
                            'title', rs.title,
                            'description', rs.description,
                            'updated_at', rs.updated_at,
                            'created_at', rs.created_at,
                            'visibility_name', sv.visibility_name
                        )
                        ORDER BY rs.created_at DESC
                    ),
                    '[]'::jsonb
                )
            ),
            jsonb_build_object(
                'status', 'success',
                'message', 'No studies found for the user'
            )
        )
        INTO result
        FROM drh_stateful_research_study.research_study rs
        INNER JOIN drh_stateful_master.study_visibility sv ON rs.visibility = sv.visibility_id
        WHERE (sv.visibility_name = 'Public'::text OR rs.tenant_id = v_tenant_id OR (sv.visibility_name = 'Private'::text AND rs.created_by = current_user_id) )
        AND rs.deleted_at IS NULL 
        AND rs.rec_status_id = 1
        AND (sv.visibility_name != 'Private'::text OR rs.created_by = current_user_id);

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                err_state = RETURNED_SQLSTATE,
                                err_message = MESSAGE_TEXT,
                                err_detail = PG_EXCEPTION_DETAIL,
                                err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during fetching study details', 'error_details', error_details_json);
    
    END;
    
    RETURN result;
END;
$function$
;




-- ***************************************************************************************************
-- Function to fetch all research studies created by a specific user
-- This function accepts the user's id as input and returns the studies as a JSONB object.
-- It handles error logging in case of exceptions and ensures a well-structured error response.

DROP FUNCTION IF EXISTS drh_stateless_research_study.get_my_studies(varchar);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.get_my_studies(current_user_id character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result jsonb;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_research_study.get_my_studies';
    current_query text := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
            'current_user_id', current_user_id
        );

    BEGIN
        -- Directly build the JSON array of studies without intermediate variables
        SELECT COALESCE(
            jsonb_build_object(
                'status', 'success',
                'message', 'Studies details fetched successfully',
                'data', COALESCE(
                    jsonb_agg(
                        jsonb_build_object(
                            'id', rs.study_id,
                            'title', rs.title,
                            'description', rs.description,
                            'updated_at', rs.updated_at,
                            'created_at', rs.created_at,
                            'visibility_name', sv.visibility_name
                        )
                        ORDER BY rs.created_at DESC
                    ),
                    '[]'::jsonb
                )
            ),
            jsonb_build_object(
                'status', 'success',
                'message', 'No studies found for the user'
            )
        )
        INTO result
        FROM drh_stateful_research_study.research_study rs
        INNER JOIN drh_stateful_master.study_visibility sv ON rs.visibility = sv.visibility_id
        WHERE rs.created_by = current_user_id
          AND rs.deleted_at IS NULL
          AND rs.rec_status_id = 1;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                err_state = RETURNED_SQLSTATE,
                                err_message = MESSAGE_TEXT,
                                err_detail = PG_EXCEPTION_DETAIL,
                                err_hint = PG_EXCEPTION_HINT;

        -- Log the error details       

        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred while fetching studies', 'error_details', error_details_json);
    END;
    
    RETURN result;
END;
$function$
;



----------------------------------------Save study collaboration and team details, ensuring data is also saved to the practitioner table-----------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_to_collab_teams_with_practitioner(varchar, drh_stateless_research_study."collab_team_type", varchar, varchar, bool) CASCADE;

DROP FUNCTION IF EXISTS drh_stateless_research_study.save_to_collab_teams(varchar, _text, drh_stateless_research_study."collab_team_type", bool) CASCADE;
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_to_collab_teams(varchar, _text, drh_stateless_research_study."collab_team_type", bool) CASCADE;
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_to_collab_teams(p_study_id character varying, p_practitioner_names text[], p_type drh_stateless_research_study.collab_team_type) CASCADE;
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_to_collab_teams(p_study_id character varying, p_practitioner_names text[], p_type drh_stateless_research_study.collab_team_type,jsonb) CASCADE;
DROP TYPE IF EXISTS drh_stateless_research_study.collab_team_type CASCADE;


CREATE TYPE drh_stateless_research_study."collab_team_type" AS ENUM (
	'principal_investigator',
	'nominated_principal_investigator',
	'co_investigator',
	'study_team',
	'author');

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_to_collab_teams_with_practitioner(p_study_id character varying, p_type drh_stateless_research_study.collab_team_type, name character varying, p_created_by character varying, is_principal boolean DEFAULT false)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_investigator_study_id TEXT := drh_stateless_util.get_unique_id()::TEXT; 
    v_practitioner_id TEXT := drh_stateless_util.get_unique_id()::TEXT;     
    v_mapping_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    v_author_id TEXT := drh_stateless_util.get_unique_id()::TEXT; 
    v_tenant_id TEXT;
    v_party_type_id TEXT;
    v_role Int;
	v_citation_id TEXT;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_to_collab_teams_with_practitioner';
    current_query TEXT := pg_catalog.current_query();

    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Study Collaboration saved successfully');

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_type', p_type,
        'name', name,
        'p_created_by', p_created_by,
        'is_principal', is_principal
    );

    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_created_by THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;

    -- Determine the role based on p_type and is_principal
    IF p_type = 'investigator' THEN
        v_role := CASE WHEN is_principal THEN 1 ELSE 3 END;
    ELSIF p_type = 'author' THEN
        v_role := CASE WHEN is_principal THEN 2 ELSE 4 END;
    ELSE
        RAISE EXCEPTION 'Invalid p_type value: %', p_type;
    END IF;

    -- Attempt to insert the new research study
    BEGIN
        -- Fetch party_type_id for the 'PERSON' type
        SELECT party_type_id
        INTO v_party_type_id
        FROM drh_stateful_party.party_type
        WHERE code = 'PERSON';

        -- Fetch tenant_id from the drh_stateful_research_study.research_study table using p_study_id
        SELECT tenant_id
        INTO v_tenant_id
        FROM drh_stateful_research_study.research_study
        WHERE study_id = p_study_id;

        
        -- Insert into the drh_stateful_research_study.practitioner table
        INSERT INTO drh_stateful_research_study.practitioner (
            id, 
            "name", 
            tenant_id, 
            rec_status_id, 
            created_at, 
            created_by
        )
        VALUES (
            v_practitioner_id, 
            name, 
            v_tenant_id,  
            (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  -- rec_status_id                 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
            p_created_by
        );

        -- Insert into the drh_stateful_research_study.investigator_study table
        INSERT INTO drh_stateful_research_study.investigator_study (
            mapping_id,
            investigator_id,
            study_id,
            "role",            
            created_at
        )
        VALUES (
            v_mapping_id,
            v_practitioner_id,  
            p_study_id,  
            v_role,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        -- Conditionally insert into citation_author table if p_type = 'author'
        IF p_type = 'author' THEN

			SELECT id 
		    INTO v_citation_id
		    FROM drh_stateful_research_study.citation
		    WHERE study_id = p_study_id;

            INSERT INTO drh_stateful_research_study.citation_author (
                id, 
                citation_id, 
                first_name, 
                rec_status_id, 
                created_at, 
                created_by
            )
            VALUES (
                v_author_id,
                v_citation_id, 
                name, -- Extract first name                
                1, -- Active status
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_created_by
            );
        END IF;

        -- Return success with the study ID
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Study Collaboration saved successfully',
            'practitioner_id', v_practitioner_id
        );
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during study collaboration save',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;

----------------------------------------Save study collaboration and team details-----------------------------------------


CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_to_collab_teams(p_study_id character varying, p_practitioner_names text[], p_type drh_stateless_research_study.collab_team_type,p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_role TEXT;
    v_mapping_id TEXT;
    v_practitioner_names TEXT[] := COALESCE(p_practitioner_names, ARRAY[]::TEXT[]);
    v_practitioner_name TEXT;
    result JSONB := jsonb_build_object('status', 'success', 'message', 'Study Collaboration saved successfully');
    v_existing_names TEXT[] := ARRAY[]::TEXT[];
    v_citation_id TEXT;
    v_author_id TEXT;
    deleted_status_id INT;
    active_status_id INT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_to_collab_teams';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_practitioner_names', p_practitioner_names,
        'p_type', p_type
    );
    -- Fetch status IDs
    SELECT 
        MAX(CASE WHEN code = 'DELETED' THEN id END),
        MAX(CASE WHEN code = 'ACTIVE' THEN id END)
    INTO deleted_status_id, active_status_id
    FROM drh_stateful_party.record_status;

    -- Updated role determination logic
    SELECT study_party_role_id 
    INTO v_role
    FROM drh_stateful_master.research_study_party_role
    WHERE code = CASE 
        WHEN p_type = 'principal_investigator' THEN 'primary-investigator'
        WHEN p_type = 'nominated_principal_investigator' THEN 'nominated-principal-investigator'
        WHEN p_type = 'co_investigator' THEN 'co-investigator'
        WHEN p_type = 'study_team' THEN 'study-team'
        WHEN p_type = 'author' THEN 'co-author'
        ELSE NULL
    END;

    IF v_role IS NULL THEN
        RAISE EXCEPTION 'Invalid p_type value: %', p_type;
    END IF;

    -- Fetch existing members by name
    IF p_type IN ('principal_investigator', 'nominated_principal_investigator', 'co_investigator', 'study_team') THEN
        SELECT ARRAY_AGG(party_name)
        INTO v_existing_names
        FROM drh_stateful_research_study.research_study_associated_party
        WHERE research_study_id = p_study_id::TEXT 
        AND party_role_type_id = v_role::TEXT 
        AND deleted_at IS NULL;
    ELSE
        SELECT ARRAY_AGG(first_name)
        INTO v_existing_names
        FROM drh_stateful_research_study.citation_author ca
        JOIN drh_stateful_research_study.citation c ON ca.citation_id = c.id
        WHERE c.study_id = p_study_id::TEXT 
        AND ca.role_id = v_role::TEXT 
        AND ca.deleted_at IS NULL;
    END IF;

    -- Process team members
    IF p_type IN ('principal_investigator', 'nominated_principal_investigator', 'co_investigator', 'study_team') THEN
        -- Soft delete unmatched members
        UPDATE drh_stateful_research_study.research_study_associated_party
        SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            deleted_by = 'SYSTEM'
        WHERE research_study_id = p_study_id::TEXT 
        AND party_role_type_id = v_role::TEXT
        AND party_name <> ALL(v_practitioner_names)
        AND deleted_at IS NULL;

        -- Insert new team members
        FOREACH v_practitioner_name IN ARRAY v_practitioner_names LOOP
            IF v_existing_names IS NULL OR v_practitioner_name <> ALL(v_existing_names) THEN
                v_mapping_id := drh_stateless_util.get_unique_id()::TEXT;

                -- Insert new team member directly with name
                INSERT INTO drh_stateful_research_study.research_study_associated_party (
                    associated_party_id,
                    research_study_id,
                    party_role_type_id,
                    party_name,
                    created_at,
                    created_by
                ) VALUES (
                    v_mapping_id,
                    p_study_id,
                    v_role,
                    v_practitioner_name,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    'SYSTEM'
                );
            END IF;
        END LOOP;
    ELSE
        -- Handle authors
        SELECT id INTO v_citation_id
        FROM drh_stateful_research_study.citation
        WHERE study_id = p_study_id;

        IF v_citation_id IS NULL THEN
            RETURN jsonb_build_object('status', 'failure', 'message', 'Citation not found for the provided study ID');
        END IF;

        -- Soft delete unmatched authors
        UPDATE drh_stateful_research_study.citation_author
        SET rec_status_id = deleted_status_id, deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC', deleted_by = 'SYSTEM'
        WHERE citation_id = v_citation_id
        AND role_id = v_role::TEXT
        AND first_name <> ALL(p_practitioner_names);

        -- Insert new authors
        FOREACH v_practitioner_name IN ARRAY v_practitioner_names LOOP
            IF v_existing_names IS NULL OR v_practitioner_name <> ALL(v_existing_names) THEN
                v_author_id := drh_stateless_util.get_unique_id()::TEXT;
            
                INSERT INTO drh_stateful_research_study.citation_author (
                    id,
                    citation_id,
                    first_name,
                    role_id,
                    rec_status_id,
                    created_at
                ) VALUES (
                    v_author_id,
                    v_citation_id,
                    v_practitioner_name,
                    v_role,
                    active_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                );
            END IF;
        END LOOP;
    END IF;

    -- Log activity if p_activity_json is not null
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_COLLAB_TEAM' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Save Collaboration Team',
            'activity_description', 'Collaboration team/author saved successfully'
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(NULL, v_activity_log_json);
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );
    
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during study collaboration save',
        'error_details', error_details_json
    );
END;
$function$
;

------------------------------------------------------------------------------------------------------------
---------------------------------------SAVE CITATION AUTHORS------------------------------------------------
------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_citation_authors(
    p_study_id character varying,
    p_author_names text[],
    p_citation_id text);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_citation_authors(character varying, text[], text,jsonb);    

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_citation_authors(
    p_study_id character varying,
    p_author_names text[],
    p_citation_id text,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_role TEXT;
    v_author_id TEXT;
    v_citation_id TEXT := p_citation_id;
    v_author_names TEXT[] := COALESCE(p_author_names, ARRAY[]::TEXT[]);
    v_author_name TEXT;
    result JSONB := jsonb_build_object('status', 'success', 'message', 'Citation authors saved successfully');
    v_existing_names TEXT[] := ARRAY[]::TEXT[];
    deleted_status_id INT;
    active_status_id INT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_citation_authors';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;

    --Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_author_names', p_author_names,
    'p_citation_id', p_citation_id
    );

    -- Fetch status IDs
    SELECT 
        MAX(CASE WHEN code = 'DELETED' THEN id END),
        MAX(CASE WHEN code = 'ACTIVE' THEN id END)
    INTO deleted_status_id, active_status_id
    FROM drh_stateful_party.record_status;

    -- Get author role
    SELECT study_party_role_id 
    INTO v_role
    FROM drh_stateful_master.research_study_party_role
    WHERE code = 'co-author';

    -- Get or create citation ID if not provided

    IF v_citation_id IS NULL THEN
        RETURN jsonb_build_object('status', 'failure', 'message', 'Citation details not provided');
    ELSE 
        SELECT id 
        INTO v_citation_id
        FROM drh_stateful_research_study.citation
        WHERE id = v_citation_id 
        AND deleted_at IS NULL;
        
        IF v_citation_id IS NULL THEN
            RETURN jsonb_build_object('status', 'failure', 'message', 'Citation not found for the provided citation ID');
        END IF;   
    END IF;

    -- Fetch existing authors
    SELECT ARRAY_AGG(first_name)
    INTO v_existing_names
    FROM drh_stateful_research_study.citation_author ca
    WHERE ca.citation_id = v_citation_id 
    AND ca.role_id = v_role::TEXT 
    AND ca.deleted_at IS NULL;

    -- Soft delete unmatched authors
    UPDATE drh_stateful_research_study.citation_author
    SET rec_status_id = deleted_status_id,
        deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        deleted_by = 'SYSTEM'
    WHERE citation_id = v_citation_id
    AND role_id = v_role::TEXT
    AND first_name <> ALL(p_author_names);

    -- Insert new authors
    FOREACH v_author_name IN ARRAY v_author_names LOOP
        IF v_existing_names IS NULL OR v_author_name <> ALL(v_existing_names) THEN
            v_author_id := drh_stateless_util.get_unique_id()::TEXT;
        
            INSERT INTO drh_stateful_research_study.citation_author (
                id,
                citation_id,
                first_name,
                role_id,
                rec_status_id,
                created_at
            ) VALUES (
                v_author_id,
                v_citation_id,
                v_author_name,
                v_role,
                active_status_id,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            );
        END IF;
    END LOOP;

    -- Log activity
    IF p_activity_json IS NOT NULL THEN
        --Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_CITATION_AUTHORS' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Save Citation Authors',
            'activity_description', 'Citation authors saved successfully'
        );

        --Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(NULL,v_activity_log_json);
    END IF;
    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log error
    exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );
    
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during citation authors save',
        'error_details', error_details_json
    );
END;
$function$;

---------------------------------------------------------------------------------------------------------------
---------------------------------------STUDY INVESTIGATORS SAVING----------------------------------------------
---------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_citation_authors(
    p_study_id character varying,
    p_practitioner_names text[],
    p_type drh_stateless_research_study.collab_team_type,
    jsonb
    );
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_citation_authors(
    p_study_id character varying,
    p_practitioner_names text[],
    p_type drh_stateless_research_study.collab_team_type,
    jsonb
    );    

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_study_team_members(
    p_study_id character varying,
    p_practitioner_names text[],
    p_type drh_stateless_research_study.collab_team_type,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_role TEXT;
    v_mapping_id TEXT;
    v_practitioner_names TEXT[] := COALESCE(p_practitioner_names, ARRAY[]::TEXT[]);
    v_practitioner_name TEXT;
    result JSONB := jsonb_build_object('status', 'success', 'message', 'Study team members saved successfully');
    v_existing_names TEXT[] := ARRAY[]::TEXT[];
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_study_team_members';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_practitioner_names', p_practitioner_names,
    'p_type', p_type
    );

    -- Get role ID based on type
    SELECT study_party_role_id 
    INTO v_role
    FROM drh_stateful_master.research_study_party_role
    WHERE code = CASE 
        WHEN p_type = 'principal_investigator' THEN 'primary-investigator'
        WHEN p_type = 'nominated_principal_investigator' THEN 'nominated-principal-investigator'
        WHEN p_type = 'co_investigator' THEN 'co-investigator'
        WHEN p_type = 'study_team' THEN 'study-team'
        ELSE NULL
    END;

    IF v_role IS NULL THEN
        RAISE EXCEPTION 'Invalid p_type value: %', p_type;
    END IF;

    -- Fetch existing team members
    SELECT ARRAY_AGG(party_name)
    INTO v_existing_names
    FROM drh_stateful_research_study.research_study_associated_party
    WHERE research_study_id = p_study_id::TEXT 
    AND party_role_type_id = v_role::TEXT 
    AND deleted_at IS NULL;

    -- Soft delete unmatched members
    UPDATE drh_stateful_research_study.research_study_associated_party
    SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        deleted_by = 'SYSTEM'
    WHERE research_study_id = p_study_id::TEXT 
    AND party_role_type_id = v_role::TEXT
    AND party_name <> ALL(v_practitioner_names)
    AND deleted_at IS NULL;

    -- Insert new team members
    FOREACH v_practitioner_name IN ARRAY v_practitioner_names LOOP
        IF v_existing_names IS NULL OR v_practitioner_name <> ALL(v_existing_names) THEN
            v_mapping_id := drh_stateless_util.get_unique_id()::TEXT;

            INSERT INTO drh_stateful_research_study.research_study_associated_party (
                associated_party_id,
                research_study_id,
                party_role_type_id,
                party_name,
                created_at,
                created_by
            ) VALUES (
                v_mapping_id,
                p_study_id,
                v_role,
                v_practitioner_name,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                'SYSTEM'
            );
        END IF;
    END LOOP;

    -- Log activity if p_activity_json is not null
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_STUDY_TEAM_MEMBERS' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Save Study Team Members',
            'activity_description', 'Study team members saved successfully'
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(NULL, v_activity_log_json);
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log error
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );
    
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during study team members save',
        'error_details', error_details_json
    );
END;
$function$;



--******************************************************************************************************************************
--*******************************DASHBOARD AND POPULATION PERCENTAGE STUDY LIST WITH FILTER*************************************************
--******************************************************************************************************************************
CREATE OR REPLACE FUNCTION drh_stateless_research_study.get_filtered_study_metrics(filters jsonb, start_row integer, end_row integer, current_user_id character varying, sort jsonb DEFAULT NULL::jsonb)
 RETURNS TABLE(study_id text, tenant_id text, study_display_id character varying, title character varying, description text, visibility integer, visibility_name text, created_by text, rec_status_id integer, deleted_at timestamp with time zone, nct_number text, start_date date, end_date date, organization_party_id text, age numeric, tir numeric, tbr numeric, tar numeric, wear_time numeric, gri numeric, gmi numeric, days_wear numeric, hba1c numeric, gender numeric)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_tenant_id text;
    v_order_by text;
    v_sql text;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.get_filtered_study_metrics';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'filters', filters,
        'start_row', start_row,
        'end_row', end_row,
        'current_user_id', current_user_id,
        'sort', sort
    );
    -- Get tenant_id
    SELECT p.tenant_id INTO v_tenant_id
    FROM drh_stateful_research_study.practitioner p
    JOIN drh_stateful_party.party pt ON p.practitioner_party_id = pt.party_id
    WHERE pt.party_id = current_user_id
    LIMIT 1;

    -- Set default order by if sort is null
    IF sort IS NULL OR sort->>'column' IS NULL THEN
        v_order_by := 'visibility ASC NULLS LAST';
    ELSE
        -- Validate and construct order by clause
        IF sort->>'column' IN ('study_display_id', 'title', 'visibility', 'start_date', 'end_date','age','rec_status_id','nct_number') THEN
            v_order_by := quote_ident(sort->>'column') || ' ' ||
                CASE WHEN UPPER(sort->>'order') IN ('ASC', 'DESC') 
                    THEN UPPER(sort->>'order') 
                    ELSE 'ASC' 
                END || ' NULLS LAST';
        ELSE
            v_order_by := 'visibility ASC NULLS LAST';
        END IF;
    END IF;
	
    -- Construct the main query
    v_sql := FORMAT('
        WITH study_base AS (
            SELECT 
                s.study_id,
                s.tenant_id,
                s.study_display_id,
                s.title,
                s.description,
                s.visibility,
                sv.visibility_name,
                s.created_by,
                s.rec_status_id,
                s.deleted_at,
                s.research_study_identifier ->> ''value''::text AS nct_number,
                s.start_date,
                s.end_date,
                o.party_id AS organization_party_id
            FROM drh_stateful_research_study.research_study s
            JOIN drh_stateful_master.study_visibility sv ON sv.visibility_id = s.visibility
            LEFT JOIN drh_stateful_research_study.organization o ON s.tenant_id = o.id
            WHERE (sv.visibility_name = ''Public''::text 
                OR s.tenant_id = $1 
                OR (sv.visibility_name = ''Private''::text AND s.created_by = $2))
            AND s.deleted_at IS NULL 
            AND s.rec_status_id = 1
            AND (sv.visibility_name != ''Private''::text OR s.created_by = $2)
        ),
        participant_metrics AS (
            SELECT 
            pdm.study_id,
            COUNT(DISTINCT pdm.participant_id) AS total_participants,
            -- Age filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''age_operator'')
                    WHEN ''lessThan'' THEN pdv.participant_age::numeric < ($5->>''age_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdv.participant_age::numeric <= ($5->>''age_value'')::numeric
                    WHEN ''greaterThan'' THEN pdv.participant_age::numeric > ($5->>''age_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdv.participant_age::numeric >= ($5->>''age_value'')::numeric
                    WHEN ''equals'' THEN pdv.participant_age::numeric = ($5->>''age_value'')::numeric
                    WHEN ''between'' THEN pdv.participant_age::numeric BETWEEN 
                        ($5->>''age_min'')::numeric AND ($5->>''age_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS age_filter_count,
            -- TIR filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''tir_operator'')
                    WHEN ''lessThan'' THEN pdm.tir < ($5->>''tir_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.tir <= ($5->>''tir_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.tir > ($5->>''tir_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.tir >= ($5->>''tir_value'')::numeric
                    WHEN ''equals'' THEN pdm.tir = ($5->>''tir_value'')::numeric
                    WHEN ''between'' THEN pdm.tir BETWEEN 
                        ($5->>''tir_min'')::numeric AND ($5->>''tir_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS tir_filter_count,
            -- TAR filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''tar_operator'')
                    WHEN ''lessThan'' THEN pdm.tar < ($5->>''tar_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.tar <= ($5->>''tar_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.tar > ($5->>''tar_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.tar >= ($5->>''tar_value'')::numeric
                    WHEN ''equals'' THEN pdm.tar = ($5->>''tar_value'')::numeric
                    WHEN ''between'' THEN pdm.tar BETWEEN 
                        ($5->>''tar_min'')::numeric AND ($5->>''tar_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS tar_filter_count,
            -- TBR filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''tbr_operator'')
                    WHEN ''lessThan'' THEN pdm.tbr < ($5->>''tbr_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.tbr <= ($5->>''tbr_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.tbr > ($5->>''tbr_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.tbr >= ($5->>''tbr_value'')::numeric
                    WHEN ''equals'' THEN pdm.tbr = ($5->>''tbr_value'')::numeric
                    WHEN ''between'' THEN pdm.tbr BETWEEN 
                        ($5->>''tbr_min'')::numeric AND ($5->>''tbr_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS tbr_filter_count,
            -- GRI filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''gri_operator'')
                    WHEN ''lessThan'' THEN pdm.gri < ($5->>''gri_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.gri <= ($5->>''gri_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.gri > ($5->>''gri_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.gri >= ($5->>''gri_value'')::numeric
                    WHEN ''equals'' THEN pdm.gri = ($5->>''gri_value'')::numeric
                    WHEN ''between'' THEN pdm.gri BETWEEN 
                        ($5->>''gri_min'')::numeric AND ($5->>''gri_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS gri_filter_count,
            -- GMI filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''gmi_operator'')
                    WHEN ''lessThan'' THEN pdm.gmi < ($5->>''gmi_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.gmi <= ($5->>''gmi_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.gmi > ($5->>''gmi_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.gmi >= ($5->>''gmi_value'')::numeric
                    WHEN ''equals'' THEN pdm.gmi = ($5->>''gmi_value'')::numeric
                    WHEN ''between'' THEN pdm.gmi BETWEEN 
                        ($5->>''gmi_min'')::numeric AND ($5->>''gmi_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS gmi_filter_count,
            -- Days of wear filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''days_wear_operator'')
                    WHEN ''lessThan'' THEN pdm.days_of_wear < ($5->>''days_wear_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.days_of_wear <= ($5->>''days_wear_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.days_of_wear > ($5->>''days_wear_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.days_of_wear >= ($5->>''days_wear_value'')::numeric
                    WHEN ''equals'' THEN pdm.days_of_wear = ($5->>''days_wear_value'')::numeric
                    WHEN ''between'' THEN pdm.days_of_wear BETWEEN 
                        ($5->>''days_wear_min'')::numeric AND ($5->>''days_wear_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS days_wear_filter_count,
            -- Wear time percentage filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''wear_time_operator'')
                    WHEN ''lessThan'' THEN pdm.wear_time_percentage < ($5->>''wear_time_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdm.wear_time_percentage <= ($5->>''wear_time_value'')::numeric
                    WHEN ''greaterThan'' THEN pdm.wear_time_percentage > ($5->>''wear_time_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdm.wear_time_percentage >= ($5->>''wear_time_value'')::numeric
                    WHEN ''equals'' THEN pdm.wear_time_percentage = ($5->>''wear_time_value'')::numeric
                    WHEN ''between'' THEN pdm.wear_time_percentage BETWEEN 
                        ($5->>''wear_time_min'')::numeric AND ($5->>''wear_time_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS wear_time_filter_count,
            -- HbA1c filter
            COUNT(DISTINCT CASE WHEN 
                CASE ($5->>''hba1c_operator'')
                    WHEN ''lessThan'' THEN pdv.baseline_hba1c::numeric < ($5->>''hba1c_value'')::numeric
                    WHEN ''lessOrEqual'' THEN pdv.baseline_hba1c::numeric <= ($5->>''hba1c_value'')::numeric
                    WHEN ''greaterThan'' THEN pdv.baseline_hba1c::numeric > ($5->>''hba1c_value'')::numeric
                    WHEN ''greatersOrEqual'' THEN pdv.baseline_hba1c::numeric >= ($5->>''hba1c_value'')::numeric
                    WHEN ''equals'' THEN pdv.baseline_hba1c::numeric = ($5->>''hba1c_value'')::numeric
                    WHEN ''between'' THEN pdv.baseline_hba1c::numeric BETWEEN 
                        ($5->>''hba1c_min'')::numeric AND ($5->>''hba1c_max'')::numeric
                    ELSE NULL
                END THEN pdm.participant_id END) AS hba1c_filter_count,
            -- Gender filter (exact match only)
            COUNT(DISTINCT CASE WHEN 
                CASE 
                    WHEN $5->>''gender_value'' IS NOT NULL THEN 
                        pdv.participant_gender = $5->>''gender_value''
                    ELSE NULL
                END THEN pdm.participant_id END) AS gender_filter_count
        FROM drh_stateless_research_study.study_participant_dashboard_metrics_view pdm
        JOIN drh_stateless_research_study.participant_data_view pdv 
            ON pdm.participant_id = pdv.participant_id
        GROUP BY pdm.study_id
        )
        SELECT 
            sb.*,
            ROUND((pm.age_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS age,
            ROUND((pm.tir_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS tir,
            ROUND((pm.tbr_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS tbr,
            ROUND((pm.tar_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS tar,
            ROUND((pm.wear_time_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS wear_time,
            ROUND((pm.gri_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS gri,
            ROUND((pm.gmi_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS gmi,
            ROUND((pm.days_wear_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS days_wear,
            ROUND((pm.hba1c_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS hba1c,
            ROUND((pm.gender_filter_count::numeric / NULLIF(pm.total_participants, 0) * 100), 2) AS gender
        FROM study_base sb
        LEFT JOIN participant_metrics pm ON pm.study_id = sb.study_id
        ORDER BY %s
        OFFSET $3 ROWS
        FETCH NEXT $4 ROWS ONLY',
        v_order_by
    );
    -- Execute the dynamic query
    RETURN QUERY EXECUTE v_sql 
    USING v_tenant_id, current_user_id, start_row, end_row, filters;  -- Add filters as the fifth parameter

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;   


    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    -- Log exception (assuming your logging function)
    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    RAISE;  -- re-throw the error so caller knows it failed
END;
$function$
;



--******************************************************************************************************************************
--*******************************DASHBOARD COUNT API***************************************************************************
--******************************************************************************************************************************
    CREATE OR REPLACE VIEW drh_stateless_research_study.dashboard_metrics_view WITH(security_invoker=true)
AS WITH participant_stats AS (
         SELECT rs.study_reference,
            count(DISTINCT rs.rsubject_id) AS total_participants,
            avg(p.age::double precision) AS average_age,
            count(
                CASE
                    WHEN gt.code = 'FEMALE'::text THEN 1
                    ELSE NULL::integer
                END)::numeric * 100.0 / NULLIF(count(rs.rsubject_id), 0)::numeric AS total_female_percentage
           FROM drh_stateful_research_study.research_subject rs
             LEFT JOIN drh_stateful_research_study.patient p ON rs.individual_reference = p.id
             LEFT JOIN drh_stateful_party.gender_type gt ON p.gender_type_id = gt.gender_type_id
            WHERE rs.deleted_at IS NULL
     			AND p.deleted_at IS NULL
          GROUP BY rs.study_reference
        )
 SELECT 
    s.visibility,
    sv.visibility_name,
    s.created_by,
    s.rec_status_id,
    s.deleted_at,
    o.party_id as organization_party_id,
    COALESCE(ps.total_participants, 0::bigint) AS total_participants,
    round(COALESCE(ps.average_age, 0::double precision)::numeric, 2) AS average_age,
    round(COALESCE(ps.total_female_percentage, 0::numeric), 2) AS total_female_percentage     
   FROM drh_stateful_research_study.research_study s
     JOIN drh_stateful_master.study_visibility sv ON sv.visibility_id = s.visibility
     LEFT JOIN participant_stats ps ON s.study_id = ps.study_reference
     LEFT JOIN drh_stateful_research_study.organization o ON s.tenant_id = o.id;

 
--******************************************************************************************************************************
--*******************************POPULATION COUNT API***************************************************************************
--******************************************************************************************************************************
CREATE OR REPLACE VIEW drh_stateless_research_study.population_percentage_metrics_view WITH(security_invoker=true) AS
WITH data_stats AS (
    -- Calculate data points from cgm_observation with index usage
    SELECT
        rs.study_reference AS study_id,
        count(DISTINCT(cm.participant_sid )) AS participants_with_data,
        sum(cm.total_readings) AS raw_data_points
    FROM drh_stateful_research_study.cgm_metrics cm  
    JOIN drh_stateful_research_study.research_subject rs  on rs.rsubject_id =cm.participant_sid 
    GROUP BY rs.study_reference
),
file_stats AS (
    -- Calculate total CGM files uploaded using indexes
    SELECT 
        study_id,
        COUNT(*) as total_cgm_files
    FROM drh_stateful_raw_data.raw_cgm_extract_data rced
    WHERE rced.deleted_at IS NULL
    GROUP BY study_id
),
participant_counts AS (
    -- Use index on research_subject for better performance
    SELECT 
        study_reference as study_id,
        COUNT(DISTINCT rsubject_id) as total_participants
    FROM drh_stateful_research_study.research_subject rs
    WHERE rs.deleted_at IS NULL
    GROUP BY study_reference
)
SELECT 
    s.study_id,
    s.tenant_id,
    s.study_display_id,
    s.title,
    s.description,
    sv.visibility_name,
    s.created_by,
    s.rec_status_id,
    s.deleted_at,
    s.research_study_identifier ->> 'value'::text AS nct_number,
    s.start_date,
    s.end_date,
    o.party_id as organization_party_id,
    COALESCE(pc.total_participants, 0) as total_participants,
    COALESCE(ds.participants_with_data, 0) as total_participants_with_data,
    COALESCE(fs.total_cgm_files, 0) as total_cgm_files,
    ds.raw_data_points as data_points
FROM drh_stateful_research_study.research_study s
    INNER JOIN drh_stateful_master.study_visibility sv ON sv.visibility_id = s.visibility
    LEFT JOIN drh_stateful_research_study.organization o ON s.tenant_id = o.id
    LEFT JOIN data_stats ds ON s.study_id = ds.study_id
    LEFT JOIN file_stats fs ON s.study_id = fs.study_id
    LEFT JOIN participant_counts pc ON s.study_id = pc.study_id;
    
--******************************************************************************************************************************
--*******************************DASHBOARD DISPLAY ALL STUDY DETAILS*************************************************
--******************************************************************************************************************************

DROP VIEW IF EXISTS drh_stateless_research_study.dashboard_all_research_study_view;
CREATE OR REPLACE VIEW drh_stateless_research_study.dashboard_all_research_study_view
WITH(security_invoker=true) AS SELECT s.study_id,
    s.tenant_id,
    s.study_display_id,
    s.title,
    s.description,
    s.visibility,
    sv.visibility_name,
    s.created_by,
    s.rec_status_id,
    s.deleted_at,
    s.research_study_identifier ->> 'value'::text AS nct_number,
    s.start_date,
    s.end_date,
    o.party_id AS organization_party_id,
    o.name AS organization_name
   FROM drh_stateful_research_study.research_study s
     JOIN drh_stateful_master.study_visibility sv ON sv.visibility_id = s.visibility
     LEFT JOIN drh_stateful_research_study.organization o ON s.tenant_id = o.id;