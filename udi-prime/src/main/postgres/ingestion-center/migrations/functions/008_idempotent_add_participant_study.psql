
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_individual_participant_data(
    text, -- p_study_id
    text, -- p_org_party_id
    text, -- p_gender_id
    integer, -- p_age
    text, -- p_created_by
    text, -- p_diagnosis_icd
    text, -- p_med_rxnorm
    text, -- p_treatment_modality
    text, -- p_race_id
    text, -- p_ethnicity_id
    double precision, -- p_bmi
    double precision, -- p_baseline_hba1c
    text, -- p_diabetes_type
    text  -- p_study_arm
);
DROP FUNCTION IF EXISTS drh_stateless_research_study.save_individual_participant_data(
    text, -- p_study_id
    text, -- p_org_party_id
    text, -- p_gender_id
    integer, -- p_age
    text, -- p_created_by
    text, -- p_diagnosis_icd
    text, -- p_med_rxnorm
    text, -- p_treatment_modality
    text, -- p_race_id
    text, -- p_ethnicity_id
    double precision, -- p_bmi
    double precision, -- p_baseline_hba1c
    text, -- p_diabetes_type
    text,  -- p_study_arm
    jsonb
);

CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_individual_participant_data(p_study_id text, p_org_party_id text, p_participant_display_id text, p_gender_id text, p_age integer, p_created_by text DEFAULT NULL::text, p_diagnosis_icd text DEFAULT NULL::text, p_med_rxnorm text DEFAULT NULL::text, p_treatment_modality text DEFAULT NULL::text, p_race_id text DEFAULT NULL::text, p_ethnicity_id text DEFAULT NULL::text, p_bmi double precision DEFAULT NULL::double precision, p_baseline_hba1c double precision DEFAULT NULL::double precision, p_diabetes_type text DEFAULT NULL::text, p_study_arm text DEFAULT NULL::text, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE    
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    v_research_subject_id TEXT;
    v_patient_id TEXT;
    bmi_loinc_code TEXT :='39156-5';--reference from drh_stateful_master.loinc_codes
    hba1c_loinc_code TEXT :='4548-4';--reference from drh_stateful_master.loinc_codes  
    next_participant_number INTEGER;
    function_name TEXT := 'drh_stateless_research_study.save_individual_participant_data'; 
    current_query TEXT := pg_catalog.current_query();
    result jsonb;
    v_study_display_id text;
    v_tenant_id text;
    v_rec_status_id int;
    v_gender text;
    v_race text;
    v_ethnicity text;
    v_gender_id text;

    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;
   
   	--Activity log variables
   	v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_org_party_id', p_org_party_id,
    'p_participant_display_id', p_participant_display_id,
    'p_gender_id', p_gender_id,
    'p_age', p_age,
    'p_created_by', p_created_by,
    'p_diagnosis_icd', p_diagnosis_icd,
    'p_med_rxnorm', p_med_rxnorm,
    'p_treatment_modality', p_treatment_modality,
    'p_race_id', p_race_id,
    'p_ethnicity_id', p_ethnicity_id,
    'p_bmi', p_bmi,
    'p_baseline_hba1c', p_baseline_hba1c,
    'p_diabetes_type', p_diabetes_type,
    'p_study_arm', p_study_arm
    );


   
    -- Start the transaction
    BEGIN
        -- Initialize result to failure by default in case of an error
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant creation');
       
       -- Get the study's created_by and current visibility
        SELECT rs.created_by, sv.visibility_name, rs.archive_status 
        INTO study_created_by, current_visibility, is_archived
        FROM drh_stateful_research_study.research_study rs
        JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
        WHERE rs.study_id = p_study_id;

        -- Check visibility restriction policy
        IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
            IF study_created_by != p_created_by THEN
                result := jsonb_build_object(
                    'status', 'failure',
                    'message', 'Permission denied. Only the study creator can update the details.'
                );
                RETURN result;
            END IF;
        END IF;

        -- Validate mandatory fields
	    IF p_participant_display_id IS NULL OR TRIM(p_participant_display_id) = '' THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Participant display ID cannot be NULL or empty');
	        RETURN result;
	    END IF;
	
	    IF p_age IS NULL OR p_age < 0 THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Age must be a valid integer and cannot be NULL or negative');
	        RETURN result;
	    END IF;
	
	    IF p_gender_id IS NULL OR TRIM(p_gender_id) = '' THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Gender cannot be NULL or empty');
	        RETURN result;
	    END IF;
	
	    -- Check if gender exists in the master table
	    SELECT gtv.gender_type_id INTO v_gender_id
	    FROM drh_stateless_master.gender_type_view gtv 
	    WHERE gtv.gender_type_id = p_gender_id;
	
	    IF v_gender_id IS NULL THEN
	        result := jsonb_build_object('status', 'failure', 'message', 'Invalid gender. Gender does not exist in the master table');
	        RETURN result;
	    END IF;

        IF p_created_by IS NULL or p_org_party_id IS NULL or p_study_id is null THEN         
           result := jsonb_build_object('status', 'failure', 'message', 'One of the mandatory fields are NULL');
           RETURN result;
        END IF;

        
        -- Retrieve common data for tenant_id and study_display_id
        SELECT 
            rs.organization_id,            
            rsv.study_display_id
        INTO v_tenant_id, v_study_display_id
        FROM drh_stateless_research_study.organization_party_view rs
        JOIN drh_stateless_research_study.research_study_view rsv
            ON rsv.study_id = p_study_id
        WHERE rs.organization_party_id = p_org_party_id
        LIMIT 1;
            
	    if v_tenant_id is null then
		      result := jsonb_build_object('status', 'failure', 'message', 'Tenant Id cannot be NULL');
		      return result;
	    end if;
       
	    if v_study_display_id is null then
	      result := jsonb_build_object('status', 'failure', 'message', 'study_display_id cannot be NULL');
	      return result;
	    end if;
    	 
		if p_race_id is not null then 
		   select rtv.code into v_race from drh_stateless_master.race_type_view rtv where rtv.race_type_id = p_race_id limit 1;
		    if v_race is null then
		     result := jsonb_build_object('status', 'failure', 'message', 'Race code for this id not available');
		     return result;
		    end if;
		   raise notice 'Race:%',v_race;		  
		end if;
		 
		 
		if p_ethnicity_id is not null then 
		   select etv.code into v_ethnicity from drh_stateless_master.ethnicity_type_view etv where etv.ethnicity_type_id = p_ethnicity_id limit 1;
		    if v_ethnicity is null then
		     result := jsonb_build_object('status', 'failure', 'message', 'Ethnicity code for this id not available');
		     return result;
		    end if;
		   raise notice 'Ethnicity:%',v_ethnicity;		  
		end if;
       
       SELECT rs.value into v_rec_status_id FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1;

        -- Insert into patient table
        INSERT INTO drh_stateful_research_study.patient(
            id, identifier_system, identifier_value, name_use, name_family, 
            name_given, gender_type_id, birth_date, age, address_use, address_line1, 
            address_city, address_state, address_postal_code, address_country, 
            contact_relationship, contact_name_family, contact_name_given, contact_telecom_system, 
            contact_telecom_value, contact_telecom_use, tenant_id, org_party_id, rec_status_id, 
            created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES( 
            drh_stateless_util.get_unique_id(),  -- Generated patient ID
            NULL, -- Empty identifier_system
            NULL, -- Empty identifier_value
            NULL, -- Empty name_use
            NULL, -- Empty name_family
            NULL, -- Empty name_given
            p_gender_id,
            NULL, -- Empty birth_date
            COALESCE(p_age, 0),  -- Default age if empty
            NULL, -- Empty address_use
            NULL, -- Empty address_line1
            NULL, -- Empty address_city
            NULL, -- Empty address_state
            NULL, -- Empty address_postal_code
            NULL, -- Empty address_country
            NULL, -- Empty contact_relationship
            NULL, -- Empty contact_name_family
            NULL, -- Empty contact_name_given
            NULL, -- Empty contact_telecom_system
            NULL, -- Empty contact_telecom_value
            NULL, -- Empty contact_telecom_use
            v_tenant_id, -- Tenant ID
            p_org_party_id,  -- org_party_id
            v_rec_status_id,  -- rec_status_id
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
            p_created_by, -- created_by
            NULL, NULL, NULL, NULL  -- Null fields for updated and deleted
        ) RETURNING id INTO v_patient_id;

        -- Insert into research_subject table
        INSERT INTO drh_stateful_research_study.research_subject (
            rsubject_id, participant_identifier, study_reference, individual_reference, 
            status_id, "group", diabetes_type, diagnosis_icd, med_rxnorm, treatment_modality, 
            race_type_id, ethnicity_type_id, tenant_id, rec_status_id, created_at, created_by, 
            updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),  -- rsubject_id
            p_participant_display_id,            -- participant_identifier
            p_study_id,                          -- study_reference
            v_patient_id,                        -- individual_reference
            (SELECT code FROM drh_stateful_master.research_subject_status_master WHERE display_name = 'on-study' LIMIT 1),
            p_study_arm,                         -- study group (e.g. arm)
            p_diabetes_type,                     -- diabetes type
            p_diagnosis_icd,                     -- diagnosis ICD
            p_med_rxnorm,                        -- medication code
            p_treatment_modality,                -- treatment modality
            p_race_id,  -- race type
            p_ethnicity_id, -- ethnicity type
            v_tenant_id,                         -- Tenant ID
            v_rec_status_id,                     -- rec_status_id
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- created_at
            p_created_by,                        -- created_by
            NULL, NULL, NULL, NULL               -- Updated and deleted info
        ) RETURNING rsubject_id INTO v_research_subject_id;
        
        -- Insert BMI observation if p_bmi is provided
        IF p_bmi IS NOT NULL THEN
            INSERT INTO drh_stateful_research_study.subject_observation (
                id, research_subject_id, code, category, value, unit, effective_datetime, 
                tenant_id, rec_status_id, created_at, created_by
            ) 
            VALUES (
                drh_stateless_util.generate_unique_id(),
                v_research_subject_id,  -- research_subject_id
                bmi_loinc_code,         -- LOINC code for BMI
                'Clinical',             -- Category
                p_bmi,                  -- BMI value
                'kg/m^2',               -- Unit
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                v_tenant_id,            -- Tenant ID
                v_rec_status_id,        -- rec_status_id
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
                p_created_by            -- created_by
            );
        END IF;

        -- Insert Baseline HbA1c observation if p_baseline_hba1c is provided
        IF p_baseline_hba1c IS NOT NULL THEN
            INSERT INTO drh_stateful_research_study.subject_observation (
                id, research_subject_id, code, category, value, unit, effective_datetime, 
                tenant_id, rec_status_id, created_at, created_by
            )
            VALUES (
                drh_stateless_util.generate_unique_id(),
                v_research_subject_id,  -- research_subject_id
                hba1c_loinc_code,       -- LOINC code for HbA1c
                'Clinical',             -- Category
                p_baseline_hba1c,       -- HbA1c value
                '%',                    -- Unit for HbA1c
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                v_tenant_id,            -- Tenant ID
                v_rec_status_id,        -- rec_status_id
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
                p_created_by            -- created_by
            );
        END IF;
       
	    -- Insert new records into participant_base
	    PERFORM drh_stateless_raw_observation.save_participant_base(v_research_subject_id);
       
        IF p_activity_json ? 'session_id' THEN 
            --Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='ADD_NEW_PARTICIPANT' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Add New Participant',
                'activity_description', 'New participant added.'
            );

            --Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_created_by,v_activity_log_json);
        END IF;

       	-- Set the result to success only after all insertions are completed
       result := jsonb_build_object(
           'status', 'success',
           'message', 'Participant created successfully',
           'participant_display_id', p_participant_display_id,
           'participant_id', v_research_subject_id
          
       );
          
        -- Return success result
        RETURN result;

    EXCEPTION 
        WHEN OTHERS THEN
            -- Capture error details
            GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

            -- Log the error details
            exception_log_json := jsonb_build_object(
                'function_name', function_name,
                'error_code', err_state,
                'error_message', err_message,
                'error_detail', err_detail,
                'error_hint', err_hint,
                'error_context', err_context,
                'query', current_query,
                'parameters', parameters_lst  
            );

            PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

            -- Prepare error JSON
            error_details_json := jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            );

            -- Return failure with the error details
            result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant data save', 'error_details', error_details_json);
            RETURN result;
    END;
END;
$function$
;



---------------------------------------------------------------------------------
-- INLINE UPDATE INDIVIDUAL PARTICIPANT
---------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_individual_participant_data_inline(text, jsonb, text);
DROP FUNCTION IF EXISTS drh_stateless_research_study.update_individual_participant_data_inline(text, jsonb, text, jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_research_study.update_individual_participant_data_inline(p_research_subject_id text, json_input jsonb,current_user_id text, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    key TEXT;
    value TEXT;
    update_query TEXT;
    rows_affected INTEGER;
    valid_fields TEXT[] := ARRAY[
        'diagnosis_icd', 'med_rxnorm', 'treatment_modality', 'gender_type_id', 
        'age', 'bmi', 'baseline_hba1c', 
        'diabetes_type', 'study_arm','race_type_id', 'ethnicity_type_id'
    ];
    invalid_keys TEXT[];
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.update_individual_participant_data_inline';
    current_query TEXT := pg_catalog.current_query();
    v_patient_id TEXT;
    bmi_loinc_code TEXT :='39156-5';--reference from drh_stateful_master.loinc_codes
    hba1c_loinc_code TEXT :='4548-4';--reference from drh_stateful_master.loinc_codes
    v_rec_status_id int;
    v_tenant_id text;
    exception_log_json JSONB;
    parameters_lst JSONB;

    --Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Participant data updated successfully');

    parameters_lst := jsonb_build_object(
    'p_research_subject_id', p_research_subject_id,
    'json_input', json_input,
    'current_user_id', current_user_id
    );


    SELECT rs.value into v_rec_status_id FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1;

    -- Initialize an array for invalid keys
    invalid_keys := ARRAY[]::TEXT[];

    -- Check each key in the JSON input
    FOR key IN SELECT jsonb_object_keys(json_input)
    LOOP
        IF NOT (key = ANY(valid_fields)) THEN
            invalid_keys := array_append(invalid_keys, key);
        END IF;
    END LOOP;

    -- If invalid keys are found, return an error message
    IF array_length(invalid_keys, 1) IS NOT NULL THEN
        result := jsonb_build_object(
            'status', 'failure', 
            'message', format('Invalid fields: %s', array_to_string(invalid_keys, ', '))
        );
        RETURN result;
    END IF;

    -- Get the patient_id from research_subject
    SELECT individual_reference,tenant_id  INTO v_patient_id,v_tenant_id
    FROM drh_stateful_research_study.research_subject
    WHERE rsubject_id = p_research_subject_id;
   
    -- Begin updates based on the provided fields
    BEGIN
        -- Update patient table for gender and age if provided
        IF (json_input ? 'gender_type_id') OR (json_input ? 'age') THEN
            update_query := 'UPDATE drh_stateful_research_study.patient SET ';
            
            IF json_input ? 'gender_type_id' THEN
                update_query := update_query || format('gender_type_id = %L, ', json_input->>'gender_type_id');
            END IF;
            
            IF json_input ? 'age' THEN
                update_query := update_query || format('age = %L, ', CAST(json_input->>'age' AS int4));
            END IF;

            -- Remove trailing comma and space
            update_query := left(update_query, length(update_query) - 2);
            update_query := update_query || format(' WHERE id = %L', v_patient_id);
                     
            EXECUTE update_query;
        END IF;

        -- Update research_subject table for relevant fields
        update_query := 'UPDATE drh_stateful_research_study.research_subject SET ';
        
        FOR key, value IN SELECT * FROM jsonb_each_text(json_input)
        LOOP
            IF key = ANY(ARRAY['diagnosis_icd', 'med_rxnorm', 'treatment_modality', 
                               'diabetes_type', 'study_arm', 'race_type_id', 'ethnicity_type_id']) AND value IS NOT NULL AND value != 'null' then
               IF key = 'study_arm' THEN
                    update_query := update_query || format('%I = %L, ', 'group', value);
                ELSE
                    update_query := update_query || format('%I = %L, ', key, value);
                END IF;
            END IF;
        END LOOP;

        -- Only execute if there are fields to update
        IF update_query != 'UPDATE drh_stateful_research_study.research_subject SET ' THEN
            -- Remove trailing comma and space
            update_query := left(update_query, length(update_query) - 2);
            update_query := update_query || format(' WHERE rsubject_id = %L', p_research_subject_id);
            
            EXECUTE update_query;            
        END IF;

        -- Handle BMI and HbA1c updates in subject_observation table
        IF json_input ? 'bmi' OR json_input ? 'baseline_hba1c' THEN            
            -- Update HbA1c if provided
            IF json_input ? 'bmi' THEN
                WITH upsert AS (
                        UPDATE drh_stateful_research_study.subject_observation
                        SET value = CAST(json_input->>'bmi' AS float8),
                        updated_by =current_user_id,
                        updated_at =CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                        WHERE research_subject_id = p_research_subject_id
                        AND code = bmi_loinc_code
                        RETURNING 1
                    )
                    INSERT INTO drh_stateful_research_study.subject_observation (
                        id,
                        research_subject_id,
                        code,
                        category,
                        value,
                        unit,
                        effective_datetime,
                        tenant_id,
                        rec_status_id,
                        created_at,
                        created_by
                    )
                    SELECT 
                        drh_stateless_util.get_unique_id(),
                        p_research_subject_id,
                        bmi_loinc_code,
                        'Clinical',             -- Category
                        CAST(json_input->>'bmi' AS float8),
                        'kg/m^2',               -- Unit                    -- Unit for HbA1c
                        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                        v_tenant_id,--tenant id
                        v_rec_status_id, 
                        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                        current_user_id
                    WHERE NOT EXISTS (
                        SELECT 1 FROM upsert
                    );
                
            end if;
            -- Update HbA1c if provided
            IF json_input ? 'baseline_hba1c' THEN
                WITH upsert AS (
                    UPDATE drh_stateful_research_study.subject_observation 
                    SET value = CAST(json_input->>'baseline_hba1c' AS float8),
                    updated_by =current_user_id,
                    updated_at =CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                    WHERE research_subject_id = p_research_subject_id
                    AND code = hba1c_loinc_code
                    RETURNING 1
                )
                INSERT INTO drh_stateful_research_study.subject_observation (
                    id,
                    research_subject_id,
                    code,
                    category,
                    value,
                    unit,
                    effective_datetime,
                    tenant_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                SELECT 
                    drh_stateless_util.get_unique_id(),
                    p_research_subject_id,
                    hba1c_loinc_code,
                    'Clinical',             -- Category
                    CAST(json_input->>'baseline_hba1c' AS float8),
                    '%',                    -- Unit for HbA1c
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
                    v_tenant_id,--tenant id
                    v_rec_status_id, 
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    current_user_id
                WHERE NOT EXISTS (
                    SELECT 1 FROM upsert
                );
            END IF;
        END IF;

        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- Insert new records into participant_base
        PERFORM drh_stateless_raw_observation.save_participant_base(p_research_subject_id);

        -- Return success with the number of rows updated
        result := jsonb_build_object(
            'status', 'success',
            'message', format('Update successful: %s row(s) affected.', rows_affected),
            'rows_affected', rows_affected
        );

        IF p_activity_json ? 'session_id' THEN 
            --Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='EDIT_PARTICIPANT' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Edit Participant',
                'activity_description', 'Participant details updated'
            );

            --Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(current_user_id,v_activity_log_json); 
        END IF;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during participant data update',
            'error_details', error_details_json
        );
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;


---------------------------------------------------------------------------------

-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_research_study.participant_data_view;

-- Create the view (idempotent)
CREATE OR REPLACE VIEW drh_stateless_research_study.participant_data_view
WITH (security_invoker = true)
AS SELECT 
    DISTINCT rs.rsubject_id AS participant_id,
    opv.organization_party_id,
    upv.practitioner_party_id,
    rsv.study_id,
    rsv.study_display_id,
    rs.participant_identifier AS participant_display_id,
    p.age AS participant_age,
    gp.value AS participant_gender,
    rt.display AS participant_race,
    et.display AS participant_ethnicity,
    rs.diabetes_type,
    rs."group" AS study_arm,
    bmi.value AS bmi,
    hba1c.value AS baseline_hba1c,    
    rs.diagnosis_icd ,
    rs.med_rxnorm ,
    rs.treatment_modality 
   FROM drh_stateful_research_study.research_subject rs
     JOIN drh_stateless_research_study.research_study_view rsv ON rsv.study_id = rs.study_reference
     JOIN drh_stateless_research_study.organization_party_view opv ON opv.organization_id = rs.tenant_id
     JOIN drh_stateless_authentication.user_profile_view upv ON upv.practitioner_party_id = rs.created_by
     JOIN drh_stateful_research_study.patient p ON rs.individual_reference = p.id
     LEFT JOIN drh_stateful_party.gender_type gp ON gp.gender_type_id = p.gender_type_id
     LEFT JOIN drh_stateless_master.race_type_view rt ON rt.race_type_id = rs.race_type_id
     LEFT JOIN drh_stateless_master.ethnicity_type_view et ON et.ethnicity_type_id = rs.ethnicity_type_id
     LEFT JOIN drh_stateful_research_study.subject_observation bmi ON rs.rsubject_id = bmi.research_subject_id AND bmi.code::text = (( SELECT loinc_codes.loinc_code
           FROM drh_stateful_master.loinc_codes
          WHERE loinc_codes.loinc_description = 'Body mass index (BMI) [Ratio] by Calculation'::text
         LIMIT 1))
     LEFT JOIN drh_stateful_research_study.subject_observation hba1c ON rs.rsubject_id = hba1c.research_subject_id AND hba1c.code::text = (( SELECT loinc_codes.loinc_code
           FROM drh_stateful_master.loinc_codes
          WHERE loinc_codes.loinc_description = 'Hemoglobin A1c/Hemoglobin.total in Blood'::text
         LIMIT 1))
  WHERE rs.rec_status_id = (( SELECT record_status.value
           FROM drh_stateful_party.record_status
          WHERE record_status.code::text = 'ACTIVE'::text
         LIMIT 1)) and rs.deleted_at is null and p.deleted_at is NULL;
