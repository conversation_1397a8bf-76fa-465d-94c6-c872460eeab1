---------------------------------------------------------------------------------------
-----------------------ROLE-PERMISSIONS-VIEW-------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_authentication.role_permission_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_authentication.role_permission_view 
WITH (security_invoker = true) AS
SELECT 
    rp.role_permission_id,
    r.role_name,
    p.permission_name,
    p.resource_type,
    p.action_type,
    rp.rec_status_id,
    r.role_id,
    p.permission_id
FROM drh_stateful_authentication.role_permission rp
JOIN drh_stateful_master.role r ON r.role_id = rp.role_id
JOIN drh_stateful_master.permission p ON p.permission_id = rp.permission_id
ORDER BY r.role_name, p.resource_type, p.permission_name;

---------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_authentication.update_user_roles(current_user_id TEXT, p_user_id TEXT, p_role_ids JSONB);
DROP FUNCTION IF EXISTS drh_stateless_authentication.update_user_roles(current_user_id TEXT, p_user_id TEXT, p_role_ids JSONB, jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_authentication.update_user_roles(
    current_user_id TEXT,
    p_user_id TEXT,
    p_role_ids JSONB,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS JSONB AS $$
DECLARE 
    v_role_id TEXT;
    v_existing_roles TEXT[];
    v_new_roles TEXT[];
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.update_user_roles';
    current_query TEXT := pg_catalog.current_query();
    v_user_role text;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    parameters_lst := jsonb_build_object(
        'current_user_id', current_user_id,
        'p_user_id', p_user_id,
        'p_role_ids', p_role_ids
    );
    -- Input validation
    IF p_user_id IS NULL THEN
        RETURN jsonb_build_object('status', 'failure', 'message', 'Error: User ID cannot be null');
    END IF;

    IF p_role_ids IS NULL OR p_role_ids = '[]'::jsonb THEN
        RETURN jsonb_build_object('status', 'failure', 'message', 'Error: Role IDs cannot be empty');
    END IF;

    -- Get existing roles for the user
    SELECT array_agg(role_id) 
    INTO v_existing_roles
    FROM drh_stateful_authentication.user_role
    WHERE user_id = p_user_id 
    AND deleted_at IS NULL;

    -- Convert JSON array to text array
    SELECT array_agg(value::text)
    INTO v_new_roles
    FROM jsonb_array_elements_text(p_role_ids);

    -- Remove roles that are not in the new role list
    UPDATE drh_stateful_authentication.user_role
    SET 
        deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        deleted_by = current_user_id,
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        updated_by = current_user_id
    WHERE user_id = p_user_id
    AND role_id = ANY(v_existing_roles)
    AND role_id <> ALL(v_new_roles)
    AND deleted_at IS NULL;

    -- Insert new roles that don't exist
    FOR v_role_id IN SELECT jsonb_array_elements_text(p_role_ids)
    LOOP
        IF NOT EXISTS (
            SELECT 1 
            FROM drh_stateful_authentication.user_role 
            WHERE user_id = p_user_id 
            AND role_id = v_role_id 
            AND deleted_at IS NULL
        ) then
            v_user_role :=drh_stateless_util.get_unique_id();
            INSERT INTO drh_stateful_authentication.user_role (
                user_role_id,
                role_id,
                user_id,
                rec_status_id,
                created_by,
                created_at
            ) VALUES (
                v_user_role,
                v_role_id,
                p_user_id,
                1,
                current_user_id,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            );
        END IF;
    END LOOP;

    -- Activity log feature
    IF p_activity_json IS NOT NULL THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_USER_ROLES' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Update User Roles',
            'activity_description', format('User roles updated for user_id %s', p_user_id)
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(current_user_id, v_activity_log_json);
    END IF;

    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'User roles updated successfully'
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred while updating user roles',
        'error_details', error_details_json
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;


----------------------------------------------------------------------------------------------------------------------------------
------------------------------------------GET ROLES AND PERMISSIONS BY USER ID----------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_authentication.get_user_roles_and_permissions(p_user_id text);
CREATE OR REPLACE FUNCTION drh_stateless_authentication.get_user_roles_and_permissions(
    p_user_id text
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.get_user_roles_and_permissions';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_user_id', p_user_id
        );


    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success');

    -- Get roles and permissions
    WITH user_roles AS (
        SELECT DISTINCT
            r.role_id,
            r.role_name,
            r.description as role_description
        FROM drh_stateful_master.role r
        JOIN drh_stateful_authentication.user_role ur ON r.role_id = ur.role_id
        WHERE ur.user_id = p_user_id 
        AND ur.rec_status_id = 1  -- Active records only
    ),
    role_permissions AS (
        SELECT 
            ur.role_id,
            ur.role_name,
            ur.role_description,
            jsonb_agg(
                jsonb_build_object(
                    'permission_id', p.permission_id,
                    'permission_name', p.permission_name,
                    'code', p.code,
                    'resource_type', p.resource_type,
                    'action_type', p.action_type
                ) ORDER BY p.resource_type, p.permission_name
            ) as permissions
        FROM user_roles ur
        JOIN drh_stateful_authentication.role_permission rp ON ur.role_id = rp.role_id
        JOIN drh_stateful_master.permission p ON rp.permission_id = p.permission_id
        WHERE rp.rec_status_id = 1  -- Active permissions only
        GROUP BY ur.role_id, ur.role_name, ur.role_description
    )
    SELECT jsonb_build_object(
        'user_id', p_user_id::text,
        'roles', COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'role_id', role_id,
                    'role_name', role_name,
                    'role_description', role_description,
                    'permissions', COALESCE(permissions, '[]'::jsonb)
                )
            ), '[]'::jsonb
        )
    )
    INTO result
    FROM role_permissions;

    -- Return empty array if no roles found
    IF result IS NULL 
       OR NOT result ? 'roles'
       OR jsonb_array_length(result -> 'roles') = 0 THEN
        result := jsonb_build_object(
            'status', 'success',
            'message', 'User roles not found',  -- Added missing comma here
            'user_id', p_user_id::text,
            'roles', '[]'::jsonb
        );
    ELSE
    -- Add status field to non-empty result
    result := result || jsonb_build_object('status', 'success', 'message', 'User roles fetched successfully');
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error getting user roles and permissions',
        'error_details', error_details_json
    );
END;
$function$
;


-------------------------------------------

/**
 * Function: drh_stateless_authentication.create_super_admin_account
 * 
 * Description:
 * This function creates a Super Admin account in the system. It performs various operations such as 
 * validating input parameters, checking for duplicate email addresses, creating associated records 
 * in multiple tables, and assigning the Super Admin role to the user. The function ensures that all 
 * operations are performed within a transaction and includes error handling to log and return 
 * detailed error information in case of failure.
 * 
 * Parameters:
 * - p_fullname (TEXT): The full name of the Super Admin user.
 * - p_email (TEXT): The email address of the Super Admin user. This is required and must be unique.
 * - p_organization_party_id (TEXT): The ID of the organization to which the Super Admin belongs.
 * - p_password (TEXT): The password for the Super Admin account. Can be plain text or encrypted.
 * - p_is_pass_encrypted (BOOLEAN): Indicates whether the provided password is already encrypted.
 * 
 * Returns:
 * - JSONB: A JSON object containing the status of the operation. On success, it includes details 
 *   such as the party ID, user account ID, and organization party ID. On failure, it includes 
 *   error details such as the error message, context, and SQL state.
 * 
 * Behavior:
 * 1. Validates the email address to ensure it is not null or empty.
 * 2. Checks if the email address already exists in the `user_account` table.
 * 3. Fetches the organization ID based on the provided organization party ID.
 * 4. Retrieves the active status ID from the `record_status` table.
 * 5. Inserts records into the following tables:
 *    - `party`: Creates a new party record for the Super Admin.
 *    - `user_account`: Creates a user account for the Super Admin.
 *    - `user_credentials`: Stores the user's credentials, encrypting the password if necessary.
 *    - `auth_mappings`: Adds external authentication mappings for the user.
 *    - `telecom`: Adds the user's email address to the telecom table.
 *    - `user_role`: Assigns the Super Admin role to the user.
 * 6. Returns a success response with relevant details if all operations succeed.
 * 7. Handles exceptions by logging the error details in the `exception_log` table and returning 
 *    a failure response with detailed error information.
 * 
 * Notes:
 * - The function uses the `drh_stateless_util.get_unique_id()` utility to generate unique IDs for 
 *   various records.
 * - The function is defined with `SECURITY DEFINER` to allow execution with the privileges of the 
 *   function owner.
 * - The function ensures transactional integrity by rolling back changes in case of any errors.
 */

CREATE OR REPLACE FUNCTION drh_stateless_authentication.create_super_admin_account(
    p_fullname TEXT,
    p_email TEXT,
    p_organization_party_id TEXT,
    p_password TEXT,
    p_is_pass_encrypted BOOLEAN
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    user_account_id TEXT;
    v_practitioner_party_id TEXT := drh_stateless_util.get_unique_id();
    p_createdby TEXT;
    org_id TEXT;
    v_active_status_id INT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.create_super_admin_account';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_fullname', p_fullname,
    'p_email', p_email,
    'p_organization_party_id', p_organization_party_id,    
    'p_is_pass_encrypted', p_is_pass_encrypted
    );


    -- Validate email
    IF p_email IS NULL OR p_email = '' THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Email address is required.'
        );
    END IF;

    -- Check if email already exists
    IF EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account 
        WHERE email = p_email
    ) THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Email already exists.'
        );
    END IF;

    -- Fetch organization ID
    SELECT organization_id INTO org_id
    FROM drh_stateless_research_study.organization_party_view
    WHERE organization_party_id = p_organization_party_id;

    IF org_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Organization not found.'
        );
    END IF;

    -- Get active status ID
    SELECT rs.value INTO v_active_status_id 
    FROM drh_stateful_party.record_status rs 
    WHERE rs.code = 'ACTIVE';

    -- Begin transaction
    BEGIN
        -- Insert party record
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, 
            created_at
        )
        VALUES (
            v_practitioner_party_id,
            (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
            p_fullname,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        p_createdby := v_practitioner_party_id;

        -- Insert user account
        INSERT INTO drh_stateful_authentication.user_account (
            user_id, username, email, first_name, 
            profile_status, rec_status_id, created_at, created_by,
            party_id,
            org_party_id
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            p_email,
            p_email,
            p_fullname,
            (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            v_practitioner_party_id,
            p_organization_party_id
        )
        RETURNING user_id INTO user_account_id;

        -- Insert into user_credentials table
        INSERT INTO drh_stateful_authentication.user_credentials (
            id, user_id, password_hash, password_salt, password_updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            CASE
            WHEN p_is_pass_encrypted THEN p_password
            ELSE drh_stateless_util.crypt(p_password, drh_stateless_util.gen_salt('bf'))
            END,
            'bcrypt',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        -- Insert external auth mappings
        INSERT INTO drh_stateful_authentication.auth_mappings (
            id, user_id, auth_provider, provider_user_id, access_token,
            refresh_token, status, rec_status_id, created_at, updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            'Local',
            p_email,
            '', '', 'ACTIVE',
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        -- Insert email into the telecom table
        INSERT INTO drh_stateful_research_study.telecom (
            id, party_id, telecom_type, telecom_value, contact_point_system_id, contact_point_use_type_id, tenant_id, rec_status_id, created_at, created_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(), 
            v_practitioner_party_id,
            'email', 
            p_email,
            (SELECT id FROM drh_stateless_master.contact_point_system_view WHERE code = 'email' LIMIT 1), 
            (SELECT id FROM drh_stateless_master.contact_point_use_view WHERE code = 'work' LIMIT 1),
            org_id, 
            v_active_status_id,  
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
            p_createdby
        );

        -- Assign Super Admin role
        INSERT INTO drh_stateful_authentication.user_role (
            user_role_id, user_id, role_id, rec_status_id,
            created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            (SELECT role_id FROM drh_stateful_master.role WHERE role_name = 'Super Admin' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            NULL, NULL, NULL, NULL
        );

        -- Return success response
        RETURN jsonb_build_object(
            'status', 'success',
            'message', 'Super Admin profile successfully created.',
            'party_id', v_practitioner_party_id,
            'user_account_id', user_account_id,
            'organization_party_id', p_organization_party_id
        );
    END;

EXCEPTION WHEN OTHERS THEN
    -- Error handling
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return error response
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during Super Admin profile creation',
        'error_details', error_details_json
    );
END;
$function$;

----------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------SUPER ADMIN VIEW----------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_authentication.super_admin_view;

CREATE OR REPLACE VIEW drh_stateless_authentication.super_admin_view
WITH (security_invoker = true)
AS 
SELECT 
    ua.email,
    uc.password_hash as password,   
    ua.org_party_id as organization_party_id,
    r.role_id, 
    r.role_name,
    CASE 
        WHEN ua.last_name IS NULL OR TRIM(ua.last_name) = '' 
        THEN ua.first_name
        ELSE ua.first_name || ' ' || ua.last_name
    END AS full_name,
    ua.party_id,
    o.id as tenant_id
FROM drh_stateful_authentication.user_account ua
LEFT JOIN drh_stateful_authentication.user_credentials uc 
    ON uc.user_id = ua.user_id
LEFT JOIN drh_stateful_research_study.practitioner p 
    ON p.practitioner_party_id = ua.party_id
LEFT JOIN drh_stateful_research_study.organization o 
    ON o.party_id = ua.org_party_id      
JOIN drh_stateful_authentication.user_role ur 
    ON ur.user_id = ua.user_id 
    AND ur.deleted_at IS NULL
JOIN drh_stateful_master.role r 
    ON r.role_id = ur.role_id
WHERE r.role_name = 'Super Admin';

----------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------REMOVE ADMIN ROLE----------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_authentication.remove_admin_privilege;

CREATE OR REPLACE FUNCTION drh_stateless_authentication.remove_admin_privilege(
    p_current_user_id TEXT,
    p_user_party_id TEXT
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_is_super_admin BOOLEAN;
    v_admin_role_id TEXT;
    v_user_id TEXT;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.remove_admin_privilege';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_current_user_id', p_current_user_id,
        'p_user_party_id', p_user_party_id        
    );

    -- Initialize result
    result := jsonb_build_object('status', 'success');

    -- Check if current user is a super admin
    SELECT EXISTS (
        SELECT 1 
        FROM drh_stateful_authentication.user_role ur
        JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
        join drh_stateful_authentication.user_account ua on ua.user_id =ur.user_id 
        WHERE ua.party_id  = p_current_user_id 
        AND r.role_name = 'Super Admin'
        AND ur.deleted_at IS NULL
    ) INTO v_is_super_admin;

    IF NOT v_is_super_admin THEN
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Only super admin can remove admin privileges'
        );
        RETURN result;
    END IF;

    -- Get admin role id
    SELECT role_id INTO v_admin_role_id
    FROM drh_stateful_master.role
    WHERE role_name = 'Admin'
    AND deleted_at IS NULL;

    -- Get user_id from party_id
    SELECT user_id INTO v_user_id
    FROM drh_stateful_authentication.user_account
    WHERE party_id = p_user_party_id
    AND deleted_at IS NULL;

    IF v_user_id IS NULL THEN
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'User not found'
        );
        RETURN result;
    END IF;

    -- Soft delete admin role from user_role table
    UPDATE drh_stateful_authentication.user_role
    SET deleted_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        deleted_by = p_current_user_id,
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        updated_by = p_current_user_id
    WHERE user_id = v_user_id
    AND role_id = v_admin_role_id
    AND deleted_at IS NULL;

    result := jsonb_build_object(
        'status', 'success',
        'message', 'Admin privileges removed successfully'
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
        -- Error handling
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Return error response
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Failed to remove admin privileges',
            'error_details', error_details_json
        );
    END;
$function$;

----------------------------------------------------------------------------------------------------------------------------------
------------------------------------------UPDATE USER PROFILE DETAILS-------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_authentication.update_profile_details(jsonb, text);
DROP FUNCTION IF EXISTS drh_stateless_authentication.update_profile_details(jsonb, text, jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_authentication.update_profile_details(json_input jsonb, current_user_id text, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    key TEXT;
    value TEXT;
    update_query TEXT;
    rows_affected INTEGER;
    valid_fields TEXT[] := ARRAY[
        'name', 'birth_date',   -- practitioner fields
        'line1', 'line2', 'city', 'state', 'country', 'postal_code', -- address fields
        'telecom', 'tenant_id'  -- new fields
    ];
    invalid_keys TEXT[];
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'update_profile_details';
    current_query TEXT := pg_catalog.current_query();
    v_rec_status_id int;
    v_address_exists boolean;
   	v_contact_point_address_use TEXT;
    v_contact_point_address_type TEXT;
    v_tenant_id TEXT;
    telecom_item jsonb;
    v_contact_point_system_id TEXT;
    v_contact_point_use_type_id TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;

    --Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Person details updated successfully');

    parameters_lst := jsonb_build_object(
        'json_input', json_input,
        'current_user_id', current_user_id
    );    

    SELECT rs.value into v_rec_status_id 
    FROM drh_stateful_party.record_status rs 
    WHERE rs.code = 'ACTIVE' LIMIT 1;

    -- Initialize an array for invalid keys
    invalid_keys := ARRAY[]::TEXT[];

    -- Check each key in the JSON input
    FOR key IN SELECT jsonb_object_keys(json_input)
    LOOP
        IF NOT (key = ANY(valid_fields)) THEN
            invalid_keys := array_append(invalid_keys, key);
        END IF;
    END LOOP;

    -- If invalid keys are found, return an error message
    IF array_length(invalid_keys, 1) IS NOT NULL THEN
        result := jsonb_build_object(
            'status', 'failure', 
            'message', format('Invalid fields: %s', array_to_string(invalid_keys, ', '))
        );
        RETURN result;
    END IF;
   
	-- Get tenant_id from practitioner if not provided in input
    IF json_input ? 'tenant_id' THEN
        v_tenant_id := json_input->>'tenant_id';
    ELSE
        SELECT tenant_id INTO v_tenant_id
        FROM drh_stateful_research_study.practitioner
        WHERE practitioner_party_id = current_user_id;
    END IF;
   
    BEGIN
        -- Update user_account table for name if provided
        IF json_input ? 'name' THEN
            update_query := 'UPDATE drh_stateful_authentication.user_account SET ';
            update_query := update_query || format('first_name = %L, ', json_input->>'name');
            update_query := update_query || format('updated_by = %L, ', current_user_id);
            update_query := update_query || format('updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'' ');
            update_query := update_query || format('WHERE party_id = %L', current_user_id);
            
            EXECUTE update_query;

            update_query := 'UPDATE drh_stateful_party.party SET ';
            update_query := update_query || format('party_name = %L, ', json_input->>'name');
            update_query := update_query || format('updated_by = %L, ', current_user_id);
            update_query := update_query || format('updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'' ');
            update_query := update_query || format('WHERE party_id = %L', current_user_id);
            
            EXECUTE update_query;
        END IF;

        -- Update practitioner table for name and birth_date if provided
        IF (json_input ? 'name') OR (json_input ? 'birth_date') THEN
            update_query := 'UPDATE drh_stateful_research_study.practitioner SET ';
            
            IF json_input ? 'name' THEN
                update_query := update_query || format('name = %L, ', json_input->>'name');
            END IF;
            
            IF json_input ? 'birth_date' THEN
                update_query := update_query || format('birth_date = %L, ', json_input->>'birth_date');
            END IF;

            update_query := update_query || format('updated_by = %L, ', current_user_id);
            update_query := update_query || format('updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'', ');
            
            -- Remove trailing comma and space
            update_query := left(update_query, length(update_query) - 2);
            update_query := update_query || format(' WHERE practitioner_party_id = %L', current_user_id);
            
            EXECUTE update_query;
        END IF;

        -- Check if address exists for the user
        SELECT EXISTS (
            SELECT 1 
            FROM drh_stateful_research_study.address 
            WHERE party_id = current_user_id
        ) INTO v_address_exists;

        -- Handle address fields
        IF (json_input ? 'line1') OR (json_input ? 'line2') OR 
           (json_input ? 'city') OR (json_input ? 'state') OR 
           (json_input ? 'country')  OR (json_input ? 'postal_code')THEN
            
            IF v_address_exists THEN
              
            -- Update existing address
                update_query := 'UPDATE drh_stateful_research_study.address SET ';
                
                FOR key, value IN SELECT * FROM jsonb_each_text(json_input)
                LOOP
                    IF key = ANY(ARRAY['line1', 'line2', 'city', 'state', 'country', 'postal_code']) 
                       AND value IS NOT NULL AND value != 'null' THEN
                        update_query := update_query || format('%I = %L, ', key, value);
                    END IF;
                END LOOP;

                update_query := update_query || format('updated_by = %L, ', current_user_id);
                update_query := update_query || format('updated_at = CURRENT_TIMESTAMP AT TIME ZONE ''UTC'', ');
                
                -- Remove trailing comma and space
                update_query := left(update_query, length(update_query) - 2);
                update_query := update_query || format(' WHERE party_id = %L', current_user_id);
                
                EXECUTE update_query;
            ELSE
                
            	SELECT id into v_contact_point_address_type FROM drh_stateful_master.contact_point_address_type WHERE code='postal' AND deleted_at IS NULL;
            
            	SELECT id into v_contact_point_address_use FROM drh_stateful_master.contact_point_address_use WHERE code='work' AND deleted_at IS NULL;
            
            	-- Insert new address record
                INSERT INTO drh_stateful_research_study.address (
                    id,
                    party_id,
                    line1,
                    line2,
                    city,
                    state,
                    country,
                    postal_code,
                    rec_status_id,
                    created_at,
                    created_by,
                    address_type,
                    contact_point_address_type_id,
					contact_point_address_use_type_id
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    current_user_id,
                    json_input->>'line1',
                    json_input->>'line2',
                    json_input->>'city',
                    json_input->>'state',
                    json_input->>'country',
                    json_input->>'postal_code',
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    current_user_id,
                    v_contact_point_address_use,
                    v_contact_point_address_type,
                    v_contact_point_address_use
                    
                );
            END IF;
        END IF;

        -- Handle telecom data
        IF json_input ? 'telecom' AND jsonb_typeof(json_input->'telecom') = 'array' THEN
            FOR telecom_item IN SELECT * FROM jsonb_array_elements(json_input->'telecom')
            LOOP
                -- Get contact_point_system_id
                SELECT id INTO v_contact_point_system_id
                FROM drh_stateful_master.contact_point_system
                WHERE code = telecom_item->>'contact_point_system_value'
                AND deleted_at IS NULL;

                -- Get contact_point_use_type_id
                SELECT id INTO v_contact_point_use_type_id
                FROM drh_stateful_master.contact_point_use
                WHERE code = telecom_item->>'contact_point_use_type_value'
                AND deleted_at IS NULL;

                -- Update or insert telecom record
                INSERT INTO drh_stateful_research_study.telecom (
                    id,
                    party_id,
                    telecom_type,
                    telecom_value,
                    contact_point_system_id,
                    contact_point_use_type_id,
                    tenant_id,
                    rec_status_id,
                    created_at,
                    created_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    current_user_id,
                    telecom_item->>'contact_point_system_value',
                    telecom_item->>'telecom_value',
                    v_contact_point_system_id,
                    v_contact_point_use_type_id,
                    v_tenant_id,
                    v_rec_status_id,
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    current_user_id
                )
                ON CONFLICT (party_id, contact_point_system_id, contact_point_use_type_id)
                DO UPDATE SET
                    telecom_value = EXCLUDED.telecom_value,
                    updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    updated_by = current_user_id;
            END LOOP;
        END IF;
        
        GET DIAGNOSTICS rows_affected = ROW_COUNT;

        -- Return success with the number of rows updated
        result := jsonb_build_object(
            'status', 'success',
            'message', format('Profile updated successfully: %s row(s) affected.', rows_affected),
            'rows_affected', rows_affected
        );

        -- Only perform activity logging if p_activity_json contains session_id
        IF p_activity_json ? 'session_id' THEN
            --Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPDATE_PROFILE_DATA' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Update Profile Details',
                'activity_description', 'Profile details updated successfully'
            );

            --Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(current_user_id,v_activity_log_json); 
        END IF;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during profile update',
            'error_details', error_details_json
        );
    END;

    -- Return the final result
    RETURN result;
END;
$function$
;


--------------------------------------------------------------------------------------------------------
-----------------------------VIEW FOR EDIT PROFILE DETAILS----------------------------------------------
--------------------------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_authentication.profile_details_view CASCADE;

CREATE OR REPLACE VIEW drh_stateless_authentication.profile_details_view
WITH (security_invoker = true) AS
SELECT ua.party_id,
    ua.first_name AS name,
    ua.email AS user_account_primary_email,
    p.birth_date,
    a.line1,
    a.line2,
    a.city,
    a.state,
    a.country,
    a.postal_code,
    p.tenant_id,
    p.practitioner_party_id,
    COALESCE(o1.party_id, o2.party_id) AS organization_party_id,
    COALESCE(o1.name, o2.name) AS organization_name,
    ( SELECT jsonb_agg(jsonb_build_object('contact_point_system_value', t.telecom_type, 'telecom_value', t.telecom_value, 'contact_point_use_type_value', cpu.code)) AS jsonb_agg
           FROM drh_stateful_research_study.telecom t
             LEFT JOIN drh_stateful_master.contact_point_use cpu ON cpu.id = t.contact_point_use_type_id
          WHERE t.party_id = ua.party_id AND t.deleted_at IS NULL) AS telecom       
   FROM drh_stateful_authentication.user_account ua
    LEFT JOIN drh_stateful_research_study.practitioner p ON p.practitioner_party_id = ua.party_id
    LEFT JOIN drh_stateful_research_study.address a ON a.party_id = ua.party_id
    LEFT JOIN drh_stateful_research_study.organization o1 ON o1.id = p.tenant_id
    LEFT JOIN drh_stateful_research_study.organization o2 ON o2.party_id = ua.org_party_id
  WHERE ua.deleted_at IS NULL;

--------------------------------------------------------------------------------------------------------
-----------------------------USER VERIFICATION LOG VIEW-------------------------------------------------
--------------------------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_authentication.user_account_verification_log_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_authentication.user_account_verification_log_view AS
SELECT 
    uavl.id,
    uavl.email,
    uavl.provider_id,
    uavl.verification_token,
    uavl.token_expires_at,
    uavl.otp_code,
    uavl.otp_expires_at,
    uavl.attempt_count,
    uavl.last_attempt_at,
    uavl.locked_until,
    uavl.is_verified,    
    -- Join with user_verification_status
    uavl.verification_status_id,
    uvs.code AS verification_status_code,
    uvs.title AS verification_status_title,    
    -- Join with record_status
    uavl.rec_status_id,
    -- Audit fields
    uavl.created_at,
    uavl.created_by,
    uavl.updated_at 
FROM 
    drh_stateful_authentication.user_account_verification_log uavl
JOIN 
    drh_stateful_master.user_verification_status uvs
    ON uavl.verification_status_id = uvs.id
WHERE uavl.deleted_at IS NULL;

--------------------------------------------------------------------------------------------------------
-----------------------------UPSERT VERIFICATION LOG----------------------------------------------------
--------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_authentication.upsert_user_verification_log(jsonb);
DROP FUNCTION IF EXISTS drh_stateless_authentication.upsert_user_verification_log(jsonb,jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_authentication.upsert_user_verification_log(
    p_input_json JSONB,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_id TEXT;
    v_verification_status_id TEXT;
    v_now TIMESTAMPTZ := CURRENT_TIMESTAMP AT TIME ZONE 'UTC';
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'upsert_user_verification_log';
    current_query TEXT := pg_catalog.current_query();

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
    v_actor TEXT;
BEGIN
    -- Validate required input parameters
    IF p_input_json->>'email' IS NULL OR p_input_json->>'provider_id' IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Email and provider_id are required parameters'
        );
    END IF;

    -- Get PENDING verification status id for new records
    SELECT id INTO v_verification_status_id 
    FROM drh_stateful_master.user_verification_status 
    WHERE code = 'PENDING'
    LIMIT 1;

    -- Check if record exists
    SELECT id INTO v_id
    FROM drh_stateful_authentication.user_account_verification_log
    WHERE email = (p_input_json->>'email')
    AND provider_id = (p_input_json->>'provider_id')
    AND deleted_at IS NULL;

    IF v_id IS NULL THEN
        -- Insert new record
        v_id := drh_stateless_util.get_unique_id();
        
        INSERT INTO drh_stateful_authentication.user_account_verification_log (
            id,
            email,
            provider_id,
            verification_token,
            token_expires_at,
            otp_code,
            otp_expires_at,
            verification_status_id,
            rec_status_id,
            created_at,
            updated_at,
            created_by
        ) VALUES (
            v_id,
            p_input_json->>'email',
            p_input_json->>'provider_id',
            p_input_json->>'verification_token',
            CASE 
                WHEN p_input_json->>'verification_token' IS NOT NULL 
                THEN v_now + INTERVAL '1 week'
                ELSE NULL
            END,
            p_input_json->>'otp_code',
            CASE 
                WHEN p_input_json->>'otp_code' IS NOT NULL 
                THEN v_now + INTERVAL '5 minutes'
                ELSE NULL
            END,
            COALESCE(p_input_json->>'verification_status_id', v_verification_status_id),
            1,
            v_now,
            v_now,
            COALESCE(p_input_json->>'created_by', 'UNKNOWN')
        );

        result := jsonb_build_object(
            'status', 'success',
            'message', 'User verification log created successfully',
            'id', v_id
        );

    ELSE
        -- Update existing record
        UPDATE drh_stateful_authentication.user_account_verification_log
        SET 
            verification_token = COALESCE(p_input_json->>'verification_token', verification_token),
            token_expires_at = CASE 
                WHEN p_input_json->>'verification_token' IS NOT NULL 
                THEN v_now + INTERVAL '1 week'
                ELSE token_expires_at
            END,
            otp_code = COALESCE(p_input_json->>'otp_code', otp_code),
            otp_expires_at = CASE 
                WHEN p_input_json->>'otp_code' IS NOT NULL 
                THEN v_now + INTERVAL '5 minutes'
                ELSE otp_expires_at
            END,
            verification_status_id = COALESCE(p_input_json->>'verification_status_id', verification_status_id),
            rec_status_id = COALESCE((p_input_json->>'rec_status_id')::integer, rec_status_id),
            updated_at = v_now,
            updated_by = COALESCE(p_input_json->>'updated_by', 'UNKNOWN')
        WHERE id = v_id;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'User verification log updated successfully',
            'id', v_id
        );
    END IF;

    -- Activity log feature
    v_actor := COALESCE(p_input_json->>'updated_by', p_input_json->>'created_by', 'UNKNOWN');
    IF p_activity_json IS NOT NULL AND p_activity_json ? 'session_id' THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPSERT_USER_VERIFICATION_LOG' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Upsert User Verification Log',
            'activity_description', format('User verification log upserted for email %s, provider %s', p_input_json->>'email', p_input_json->>'provider_id')
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(v_actor, v_activity_log_json);
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    ) VALUES (
        function_name,
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        p_input_json,
        v_now,
        'No',
        NULL,
        NULL
    );

    -- Prepare error response
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during user verification log operation',
        'error_details', error_details_json
    );

END;
$function$;

--------------------------------------------------------------------------------------------------------
-----------------------------UPSERT VERIFICATION LOG----------------------------------------------------
--------------------------------------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_authentication.link_external_auth_provider(jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_authentication.link_external_auth_provider(p_input_params jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_email text;
    v_auth_provider text;
    v_provider_user_id text;
    v_user_id text;
    v_status text := 'ACTIVE';
    v_rec_status_id integer := 1;
    v_mapping_id text;
    result jsonb;
    -- Error handling variables
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'save_auth_mapping';
    current_query text := pg_catalog.current_query();
BEGIN
    -- Extract parameters from input JSON
    v_email := p_input_params->>'email';
    v_auth_provider := p_input_params->>'auth_provider';
    v_provider_user_id := p_input_params->>'provider_user_id';

    -- Validate required parameters
    IF v_email IS NULL OR v_auth_provider IS NULL OR v_provider_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Missing required parameters',
            'details', jsonb_build_object(
                'email', v_email,
                'auth_provider', v_auth_provider,
                'provider_user_id', v_provider_user_id
            )
        );
    END IF;

    -- Check if email exists in user_account
    SELECT ua.user_id 
    INTO v_user_id
    FROM drh_stateful_authentication.user_account ua
    JOIN drh_stateful_party.party pt ON pt.party_id = ua.party_id
    WHERE ua.deleted_at IS NULL 
    AND ua.deleted_by IS NULL 
    AND ua.email = v_email;

    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'The entered email ID is not registered in our system.',
            'details', jsonb_build_object('email', v_email)
        );
    END IF;

    -- Check if mapping already exists
    IF EXISTS (
        SELECT 1 
        FROM drh_stateful_authentication.auth_mappings 
        WHERE user_id = v_user_id 
        AND auth_provider = v_auth_provider
        AND provider_user_id = v_provider_user_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Auth mapping already exists for this user and provider',
            'details', jsonb_build_object(
                'user_id', v_user_id,
                'auth_provider', v_auth_provider
            )
        );
    END IF;

    -- Generate new mapping ID
    v_mapping_id := drh_stateless_util.get_unique_id();

    -- Insert new auth mapping
    INSERT INTO drh_stateful_authentication.auth_mappings (
        id,
        user_id,
        auth_provider,
        provider_user_id,
        access_token,
        refresh_token,
        status,
        rec_status_id,
        created_at
    ) VALUES (
        v_mapping_id,
        v_user_id,
        v_auth_provider,
        v_provider_user_id,  
        ' ',
		' ',      
        v_status,
        v_rec_status_id,
        CURRENT_TIMESTAMP
    );

    -- Return success response
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Auth mapping created successfully',
        'details', jsonb_build_object(
            'mapping_id', v_mapping_id,
            'user_id', v_user_id,
            'auth_provider', v_auth_provider
        )
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    ) VALUES (
        function_name,
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        p_input_params,
        CURRENT_TIMESTAMP,
        'No',
        NULL,
        NULL
    );

    -- Prepare error response
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred while creating auth mapping',
        'error_details', error_details_json
    );
END;
$function$;