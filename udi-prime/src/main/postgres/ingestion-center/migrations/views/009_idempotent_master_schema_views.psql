-- Create or replace views in the drh_stateless_master schema

-- Citation Status Master View
CREATE OR REPLACE VIEW drh_stateless_master.citation_status_master_view 
WITH (security_invoker = true) AS
SELECT 
    citation_status_master.code,
    citation_status_master.display_name,
    citation_status_master.definition,
    citation_status_master.system_url
FROM drh_stateful_master.citation_status_master;

-- Contact Point Address Type View
CREATE OR REPLACE VIEW drh_stateless_master.contact_point_address_type_view 
WITH (security_invoker = true) AS
SELECT 
    contact_point_address_type.id,
    contact_point_address_type.code,
    contact_point_address_type.system,
    contact_point_address_type.value,
    contact_point_address_type.description,
    contact_point_address_type.created_at,
    contact_point_address_type.created_by,
    contact_point_address_type.updated_at,
    contact_point_address_type.updated_by,
    contact_point_address_type.deleted_at,
    contact_point_address_type.deleted_by
FROM drh_stateful_master.contact_point_address_type;

-- Contact Point Address Use View
CREATE OR REPLACE VIEW drh_stateless_master.contact_point_address_use_view 
WITH (security_invoker = true) AS
SELECT 
    contact_point_address_use.id,
    contact_point_address_use.code,
    contact_point_address_use.system,
    contact_point_address_use.value,
    contact_point_address_use.description,
    contact_point_address_use.created_at,
    contact_point_address_use.created_by,
    contact_point_address_use.updated_at,
    contact_point_address_use.updated_by,
    contact_point_address_use.deleted_at,
    contact_point_address_use.deleted_by
FROM drh_stateful_master.contact_point_address_use;

-- Contact Point System View
CREATE OR REPLACE VIEW drh_stateless_master.contact_point_system_view 
WITH (security_invoker = true) AS
SELECT 
    contact_point_system.id,
    contact_point_system.code,
    contact_point_system.system,
    contact_point_system.value,
    contact_point_system.description,
    contact_point_system.created_at,
    contact_point_system.created_by,
    contact_point_system.updated_at,
    contact_point_system.updated_by,
    contact_point_system.deleted_at,
    contact_point_system.deleted_by
FROM drh_stateful_master.contact_point_system;

-- Contact Point Use View
CREATE OR REPLACE VIEW drh_stateless_master.contact_point_use_view 
WITH (security_invoker = true) AS
SELECT 
    contact_point_use.id,
    contact_point_use.code,
    contact_point_use.system,
    contact_point_use.value,
    contact_point_use.description,
    contact_point_use.created_at,
    contact_point_use.created_by,
    contact_point_use.updated_at,
    contact_point_use.updated_by,
    contact_point_use.deleted_at,
    contact_point_use.deleted_by
FROM drh_stateful_master.contact_point_use;

-- Ethnicity Type View
CREATE OR REPLACE VIEW drh_stateless_master.ethnicity_type_view 
WITH (security_invoker = true) AS
SELECT 
    ethnicity_type.ethnicity_type_id,
    ethnicity_type.code,
    ethnicity_type.system_uri,
    ethnicity_type.system_oid,
    ethnicity_type.display,
    ethnicity_type.ethnicity_text,
    ethnicity_type.created_at,
    ethnicity_type.created_by,
    ethnicity_type.updated_at,
    ethnicity_type.updated_by,
    ethnicity_type.deleted_at,
    ethnicity_type.deleted_by
FROM drh_stateful_master.ethnicity_type;

-- Investigator Study Role View
CREATE OR REPLACE VIEW drh_stateless_master.investigator_study_role_view 
WITH (security_invoker = true) AS
SELECT 
    investigator_study_role.code,
    investigator_study_role.role
FROM drh_stateful_master.investigator_study_role;

-- LOINC Codes View
CREATE OR REPLACE VIEW drh_stateless_master.loinc_codes_view 
WITH (security_invoker = true) AS
SELECT 
    loinc_codes.loinc_code_id,
    loinc_codes.loinc_code,
    loinc_codes.loinc_description,
    loinc_codes.loinc_class,
    loinc_codes.loinc_type,
    loinc_codes.rec_status_id,
    loinc_codes.created_at,
    loinc_codes.created_by,
    loinc_codes.updated_at,
    loinc_codes.updated_by,
    loinc_codes.deleted_at,
    loinc_codes.deleted_by
FROM drh_stateful_master.loinc_codes;

-- Metric Definitions View
CREATE OR REPLACE VIEW drh_stateless_master.metric_definitions_view 
WITH (security_invoker = true) AS
SELECT 
    metric_definitions.metric_id,
    metric_definitions.metric_name,
    metric_definitions.metric_info
FROM drh_stateful_master.metric_definitions;

-- Organization Type View
CREATE OR REPLACE VIEW drh_stateless_master.organization_type_view 
WITH (security_invoker = true) AS
SELECT 
    organization_type.organization_type_id,
    organization_type.code,
    organization_type.system_uri,
    organization_type.display,
    organization_type.description,
    organization_type.rec_status_id,
    organization_type.created_at,
    organization_type.created_by,
    organization_type.updated_at,
    organization_type.updated_by,
    organization_type.deleted_at,
    organization_type.deleted_by
FROM drh_stateful_master.organization_type;

-- Profile Status Type View
CREATE OR REPLACE VIEW drh_stateless_master.profile_status_type_view 
WITH (security_invoker = true) AS
SELECT 
    profile_status_type.profile_status_type_id,
    profile_status_type.code,
    profile_status_type.description
FROM drh_stateful_master.profile_status_type;

-- Race Type View
CREATE OR REPLACE VIEW drh_stateless_master.race_type_view 
WITH (security_invoker = true) AS
SELECT 
    race_type.race_type_id,
    race_type.code,
    race_type.system_uri,
    race_type.system_oid,
    race_type.display,
    race_type.race_text,
    race_type.created_at,
    race_type.created_by,
    race_type.updated_at,
    race_type.updated_by,
    race_type.deleted_at,
    race_type.deleted_by
FROM drh_stateful_master.race_type;

-- Research Study Condition View
CREATE OR REPLACE VIEW drh_stateless_master.research_study_condition_view 
WITH (security_invoker = true) AS
SELECT 
    research_study_condition.id,
    research_study_condition.coding_system,
    research_study_condition.code,
    research_study_condition.display,
    research_study_condition.rec_status_id,
    research_study_condition.created_at,
    research_study_condition.created_by,
    research_study_condition.updated_at,
    research_study_condition.updated_by,
    research_study_condition.deleted_at,
    research_study_condition.deleted_by
FROM drh_stateful_master.research_study_condition;

-- Research Study Focus View
CREATE OR REPLACE VIEW drh_stateless_master.research_study_focus_view 
WITH (security_invoker = true) AS
SELECT 
    research_study_focus.id,
    research_study_focus.coding_system,
    research_study_focus.code,
    research_study_focus.display,
    research_study_focus.rec_status_id,
    research_study_focus.created_at,
    research_study_focus.created_by,
    research_study_focus.updated_at,
    research_study_focus.updated_by,
    research_study_focus.deleted_at,
    research_study_focus.deleted_by
FROM drh_stateful_master.research_study_focus;

-- Research Study Party Role View
CREATE OR REPLACE VIEW drh_stateless_master.research_study_party_role_view 
WITH (security_invoker = true) AS
SELECT 
    research_study_party_role.study_party_role_id,
    research_study_party_role.code,
    research_study_party_role.system,
    research_study_party_role.display,
    research_study_party_role.description,
    research_study_party_role.rec_status_id,
    research_study_party_role.created_at,
    research_study_party_role.created_by,
    research_study_party_role.updated_at,
    research_study_party_role.updated_by,
    research_study_party_role.deleted_at,
    research_study_party_role.deleted_by
FROM drh_stateful_master.research_study_party_role;

-- Research Subject Status Master View
CREATE OR REPLACE VIEW drh_stateless_master.research_subject_status_master_view 
WITH (security_invoker = true) AS
SELECT 
    research_subject_status_master.code,
    research_subject_status_master.display_name,
    research_subject_status_master.definition,
    research_subject_status_master.system_url
FROM drh_stateful_master.research_subject_status_master;

-- Study Status Master View
CREATE OR REPLACE VIEW drh_stateless_master.study_status_master_view 
WITH (security_invoker = true) AS
SELECT 
    study_status_master.code,
    study_status_master.display_name,
    study_status_master.definition,
    study_status_master.system_url
FROM drh_stateful_master.study_status_master;

-- Study Visibility View
CREATE OR REPLACE VIEW drh_stateless_master.study_visibility_view 
WITH (security_invoker = true) AS
SELECT 
    study_visibility.visibility_id,
    study_visibility.visibility_name,
    study_visibility.visibility_description,
    study_visibility.rec_status_id,
    study_visibility.created_at,
    study_visibility.created_by,
    study_visibility.updated_at,
    study_visibility.updated_by,
    study_visibility.deleted_at,
    study_visibility.deleted_by
FROM drh_stateful_master.study_visibility;


CREATE OR REPLACE VIEW drh_stateless_master.gender_type_view 
WITH (security_invoker = true) AS
SELECT 
    gender_type_id,
    code,
    value,
    created_at,
    created_by,
    updated_at,
    updated_by,
    deleted_at,
    deleted_by,
    activity_log
FROM drh_stateful_party.gender_type
WHERE deleted_at IS NULL;


DROP VIEW IF EXISTS drh_stateless_master.interaction_action_type_view;

CREATE OR REPLACE VIEW drh_stateless_master.interaction_action_type_view 
WITH (security_invoker = true) AS
SELECT 
    id,
    title
FROM drh_stateful_master.interaction_action_type;

DROP VIEW IF EXISTS drh_stateless_master.interaction_status_view;

CREATE OR REPLACE VIEW drh_stateless_master.interaction_status_view 
WITH (security_invoker = true) AS
SELECT 
    id,
    title
FROM drh_stateful_master.interaction_status;


-- Accepted File Formats View
CREATE OR REPLACE VIEW drh_stateless_master.accepted_file_formats_view 
WITH (security_invoker = true) AS
SELECT 
    accepted_file_format_id,
    title
FROM drh_stateful_master.accepted_file_formats;

-- Device View
CREATE OR REPLACE VIEW drh_stateless_raw_observation.device_view 
WITH (security_invoker = true) AS
SELECT 
    device.id,
    device.identifier,
    device.manufacturer,
    device.serial_number,
    device.device_name,
    device.status,
    device.device_type,
    device.udi_carrier,
    device.manufacture_date,
    device.expiration_date,
    device.lot_number
FROM drh_stateful_research_study.device
WHERE deleted_at IS NULL AND rec_status_id=1;


-- migration_status_view

CREATE OR REPLACE VIEW drh_stateless_master.migration_status_view
WITH (security_invoker = true) AS  
    SELECT migration_status.stage_id,
    migration_status.stage_name,
    migration_status.stage_description,
    migration_status.created_at,
    migration_status.updated_at
   FROM drh_stateful_master.migration_status;


---------------------------------------------------------------------------------------
---------------------------- ROLES-----------------------------------------------------
---------------------------------------------------------------------------------------
--Drop view user_roles_view which is not using now
DROP VIEW IF EXISTS drh_stateless_master.user_roles_view CASCADE;

DROP VIEW IF EXISTS drh_stateless_master.roles_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.roles_view 
WITH (security_invoker = true) AS
SELECT 
    r.role_id,
    r.role_type_id,
    r.org_party_id,
    r.role_name,
    r.is_system_role,
    rt.code AS role_type_code,
    rt.display AS role_type_display,
    r.rec_status_id
FROM drh_stateful_master.role r
LEFT JOIN drh_stateful_master.role_type rt ON r.role_type_id = rt.role_type_id
WHERE r.deleted_at IS NULL AND r.rec_status_id=1;  

---------------------------------------------------------------------------------------
-----------------------PERMISSIONS-----------------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_master.permissions_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.permissions_view 
WITH (security_invoker = true) AS
SELECT 
    permission_id,
    code,
    permission_name,
    resource_type,
    action_type,
    rec_status_id
FROM drh_stateful_master.permission
ORDER BY resource_type, permission_name;

---------------------------------------------------------------------------------------
-----------------------FILE CONTENT TYPES VIEW-----------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_master.file_content_type_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.file_content_type_view 
WITH (security_invoker = true) AS
SELECT 
    id,
    title,
    description,
    created_at,
    created_by,
    updated_at,
    updated_by,
    deleted_at,
    deleted_by,
    rec_status_id
FROM drh_stateful_master.file_content_type
WHERE deleted_at IS NULL;

---------------------------------------------------------------------------------------
-----------------------ACTIVITY TYPES VIEW---------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_master.activity_type_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.activity_type_view
WITH (security_invoker = true) AS
SELECT 
    id,
    code,
    title,
    description
FROM drh_stateful_master.activity_type
WHERE rec_status_id=1;
---------------------------------------------------------------------------------------
-----------------------ACTIVITY LEVEL VIEW---------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_master.activity_level_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.activity_level_view
WITH (security_invoker = true) AS
SELECT 
    al.id as level_id,
    al.title,
    al.level,
    al.rec_status_id
FROM drh_stateful_master.activity_level al 
WHERE al.deleted_at IS NULL
    AND al.deleted_at IS NULL;

---------------------------------------------------------------------------------------
-----------------------USER VERIFICATION STATUS VIEW---------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_master.user_verification_status_view CASCADE;
CREATE OR REPLACE VIEW drh_stateless_master.user_verification_status_view 
WITH (security_invoker = true) AS
SELECT 
    uvs.id,
    uvs.code,
    uvs.title,
    uvs.description,
    uvs.rec_status_id
FROM 
    drh_stateful_master.user_verification_status uvs
WHERE uvs.deleted_at IS NULL
    AND uvs.deleted_at IS NULL;    