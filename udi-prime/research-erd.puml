@startuml IE
  hide circle
  skinparam linetype ortho
  skinparam roundcorner 20
  skinparam class {
    BackgroundColor White
    ArrowColor Silver
    BorderColor Silver
    FontColor Black
    FontSize 12
  }

  entity "record_status" as record_status {
      **id**: SERIAL
    --
    * code: VARCHAR(25)
    * value: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "organization" as organization {
    * **id**: TEXT
    --
    * party_id: TEXT
      identifier_system_value: JSONB
    * name: VARCHA<PERSON>(255)
      alias: VA<PERSON><PERSON><PERSON>(255)
      type_code: VARCHAR(100)
      type_display: VARCHAR(255)
      address_text: VARCHAR(255)
      address_line: VARCHAR(255)
      city: VARCHAR(100)
      state: VARCHAR(100)
      postal_code: VARCHAR(20)
      country: VARCHAR(100)
      phone: VARCHAR(20)
      email: VARCHAR(255)
      website_url: TEXT
      parent_organization_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMES<PERSON>MPTZ
      created_by: TEXT
      updated_at: TIMES<PERSON>MPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "location" as location {
    * **id**: TEXT
    --
      name: VARCHAR(255)
      alias: VARCHAR(255)
      description: VARCHAR(4096)
      type_code: VARCHAR(50)
      type_display: VARCHAR(255)
      address_line: VARCHAR(255)
      city: VARCHAR(100)
      state: VARCHAR(100)
      postal_code: VARCHAR(20)
      country: VARCHAR(100)
      longitude: FLOAT
      latitude: FLOAT
      managing_org_id: TEXT
      part_of_id: TEXT
      characteristic_code: VARCHAR(50)
      characteristic_display: VARCHAR(255)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "practitioner" as practitioner {
    * **id**: TEXT
    --
      system_identifier: VARCHAR(255)
    * name: VARCHAR(255)
      gender_type_id: TEXT
      birth_date: DATE
      photo_url: VARCHAR(255)
    * org_party_id: TEXT
    * tenant_id: TEXT
      practitioner_party_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "patient" as patient {
    * **id**: TEXT
    --
      identifier_system: VARCHAR(255)
      identifier_value: VARCHAR(255)
      name_use: VARCHAR(50)
      name_family: VARCHAR(100)
      name_given: VARCHAR(100)
      gender_type_id: TEXT
      birth_date: DATE
      age: INTEGER
      address_use: VARCHAR(4096)
      address_line1: VARCHAR(4096)
      address_city: VARCHAR(4096)
      address_state: VARCHAR(4096)
      address_postal_code: VARCHAR(4096)
      address_country: VARCHAR(4096)
      contact_relationship: VARCHAR(50)
      contact_name_family: VARCHAR(100)
      contact_name_given: VARCHAR(100)
      contact_telecom_system: VARCHAR(50)
      contact_telecom_value: VARCHAR(50)
      contact_telecom_use: VARCHAR(50)
    * tenant_id: TEXT
    * org_party_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "qualification" as qualification {
    * **id**: TEXT
    --
    * org_party_id: TEXT
      qualification_code: VARCHAR(50)
      issuer_name: VARCHAR(255)
      start_date: DATE
      end_date: DATE
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "telecom" as telecom {
    * **id**: TEXT
    --
    * party_id: TEXT
    * telecom_type: VARCHAR(50)
    * telecom_value: VARCHAR(255)
      telecom_use: VARCHAR(50)
    * contact_point_system_id: TEXT
    * contact_point_use_type_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "address" as address {
    * **id**: TEXT
    --
    * org_party_id: TEXT
    * address_type: VARCHAR(50)
      line1: VARCHAR(255)
      line2: VARCHAR(255)
      city: VARCHAR(100)
      state: VARCHAR(100)
      postal_code: VARCHAR(20)
      country: VARCHAR(100)
    * tenant_id: TEXT
    * contact_point_address_type_id: TEXT
    * contact_point_address_use_type_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "communication" as communication {
    * **id**: TEXT
    --
      language_code: VARCHAR(50)
      preferred: VARCHAR(50)
    * org_party_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "associated_party_type" as associated_party_type {
    * **id**: TEXT
    --
    * name: VARCHAR(100)
      description: VARCHAR(4096)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "laboratory" as laboratory {
    * **id**: TEXT
    --
      system: VARCHAR(100)
    * name: VARCHAR(255)
      type: VARCHAR(50)
      address_line: VARCHAR(255)
      city: VARCHAR(100)
      state: VARCHAR(50)
      postal_code: VARCHAR(20)
      country: VARCHAR(50)
      phone: VARCHAR(20)
      email: VARCHAR(255)
      contact_name: VARCHAR(255)
      contact_phone: VARCHAR(20)
      parent_org_id: VARCHAR(255)
    * org_party_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "research_study_focus" as research_study_focus {
      **id**: SERIAL
    --
    * coding_system: VARCHAR(255)
    * code: TEXT
    * display: VARCHAR(255)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "research_study_condition" as research_study_condition {
      **id**: SERIAL
    --
    * coding_system: VARCHAR(255)
    * code: TEXT
    * display: VARCHAR(255)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "device" as device {
    * **id**: TEXT
    --
      identifier: VARCHAR(255)
    * manufacturer: VARCHAR(255)
      serial_number: VARCHAR(255)
    * device_name: VARCHAR(255)
    * status: VARCHAR(50)
      device_type: VARCHAR(100)
      udi_carrier: VARCHAR(255)
      manufacture_date: DATE
      expiration_date: DATE
      lot_number: VARCHAR(255)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "loinc_codes" as loinc_codes {
      **loinc_code_id**: SERIAL
    --
    * loinc_code: TEXT
    * loinc_description: TEXT
    * loinc_class: VARCHAR(100)
    * loinc_type: VARCHAR(100)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "study_visibility" as study_visibility {
      **visibility_id**: SERIAL
    --
    * visibility_name: TEXT
    * visibility_description: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "citation_identifier" as citation_identifier {
    * **id**: TEXT
    --
    * citation_id: INTEGER
    * identifier_system: TEXT
    * identifier_value: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "metric_definitions" as metric_definitions {
    * metric_id: TEXT
    * metric_name: TEXT
    * metric_info: TEXT
  }

  entity "research_study" as research_study {
    * **study_id**: TEXT
    --
      research_study_identifier: JSONB
    * study_display_id: VARCHAR(10)
    * title: VARCHAR(255)
      description: TEXT
    * status_id: INTEGER
      start_date: DATE
      end_date: DATE
      focus_id: VARCHAR(255)
      condition_code: VARCHAR(255)
      region_code: VARCHAR(255)
      site_id: TEXT
      treatment_modality: TEXT
      protocol_reference_id: TEXT
      phase: VARCHAR(50)
      primary_purpose_type: VARCHAR(50)
      keywords: VARCHAR(250)
      progress_status: VARCHAR(50)
      why_stopped: VARCHAR(255)
      eligibility_criteria: TEXT
      target_enrollment: INTEGER
      actual_enrollment: INTEGER
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
    * visibility: INTEGER
    * archive_status: BOOLEAN
  }

  entity "research_subject" as research_subject {
    * **rsubject_id**: TEXT
    --
    * participant_identifier: VARCHAR(255)
    * study_reference: TEXT
      individual_reference: TEXT
    * status_id: INTEGER
      group: VARCHAR(255)
      diabetes_type: VARCHAR(50)
      diagnosis_icd: VARCHAR(255)
      med_rxnorm: VARCHAR(255)
      treatment_modality: TEXT
      race_type_id: TEXT
      ethnicity_type_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "investigator_study" as investigator_study {
    * **mapping_id**: TEXT
    --
    * investigator_id: TEXT
    * study_id: TEXT
      role: INTEGER
      start_date: DATE
      end_date: DATE
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "plan_definition" as plan_definition {
    * **id**: TEXT
    --
    * url: VARCHAR(255)
    * version: VARCHAR(50)
    * name: VARCHAR(255)
    * title: VARCHAR(255)
    * status_id: INTEGER
      experimental: VARCHAR(50)
    * description: VARCHAR(255)
    * purpose: VARCHAR(255)
    * usage: VARCHAR(255)
    * copyright: VARCHAR(255)
    * publisher: VARCHAR(255)
    * jurisdiction_code: VARCHAR(100)
    * related_artifact: VARCHAR(255)
    * org_party_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
    * study_id: TEXT
  }

  entity "goal" as goal {
    * **id**: TEXT
    --
    * plan_definition_id: TEXT
    * identifier: VARCHAR(255)
    * category: VARCHAR(100)
    * description: VARCHAR(255)
    * priority: VARCHAR(50)
    * start: VARCHAR(100)
    * conditions_addressed: VARCHAR(255)
    * outcome_code: VARCHAR(255)
    * status_id: INTEGER
    * status_reason: VARCHAR(255)
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "activity_definition" as activity_definition {
    * **id**: TEXT
    --
    * plan_definition_id: TEXT
    * identifier: VARCHAR(255)
    * status_id: VARCHAR(50)
    * name: VARCHAR(255)
    * description: VARCHAR(255)
    * timing: VARCHAR(255)
    * location: TEXT
    * participant: TEXT
    * type: VARCHAR(100)
    * reason_code: VARCHAR(255)
      goal_id: TEXT
    * output: VARCHAR(255)
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "subject_observation" as subject_observation {
    * **id**: TEXT
    --
    * research_subject_id: TEXT
    * code: VARCHAR(50)
    * category: VARCHAR(50)
      value: FLOAT
    * unit: VARCHAR(50)
      effective_datetime: TIMESTAMPTZ
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "citation" as citation {
    * **id**: TEXT
    --
      url: TEXT
    * identifier_system: TEXT
    * identifier_value: TEXT
    * title: TEXT
      status: TEXT
      date: DATE
      publisher: TEXT
      abstract: TEXT
      journal_title: TEXT
      journal_volume: TEXT
      journal_issue: TEXT
      journal_page: TEXT
      publication_date: DATE
    * rec_status_id: INTEGER
    * study_id: TEXT
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "citation_author" as citation_author {
    * **id**: TEXT
    --
    * citation_id: TEXT
    * first_name: TEXT
    * last_name: TEXT
      middle_name: TEXT
      affiliation: TEXT
      orcid: TEXT
      email: TEXT
      role_id: TEXT
      party_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "study_collaboration" as study_collaboration {
    * **collab_id**: TEXT
    --
    * study_id: TEXT
      user_id: TEXT
    * access_level: VARCHAR(20)
    * shared_at: TIMESTAMPTZ
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "external_auth_mappings" as external_auth_mappings {
    * **id**: TEXT
    --
    * user_id: TEXT
    * auth_provider: VARCHAR(50)
    * provider_user_id: VARCHAR(255)
    * access_token: VARCHAR(255)
    * refresh_token: VARCHAR(255)
    * status: VARCHAR(50)
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "site" as site {
    * **site_id**: TEXT
    --
    * site_name: TEXT
      site_description: TEXT
    * organization_id: TEXT
      address: TEXT
      telecom: TEXT
      latitude: FLOAT
      longitude: FLOAT
    * status: VARCHAR(50)
    * study_id: TEXT
    * participant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
      created_by: TEXT
      updated_by: TEXT
  }

  entity "cgm_observation" as cgm_observation {
    * id: TEXT
    * study_id: TEXT
    * research_subject_id: TEXT
    * period: VARCHAR(50)
    * date_time: TIMESTAMPTZ
    * cgm_value: DOUBLE PRECISION
    * unit: VARCHAR(10)
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "activity_log" as activity_log {
    * **activity_id**: TEXT
    --
    * activity_name: TEXT
    * activity_type: TEXT
    * activity_description: TEXT
    * root_id: INTEGER
    * parent_id: INTEGER
    * activity_hierarchy: TEXT
    * hierarchy_path: TEXT
    * request_url: TEXT
    * tenant_id: INTEGER
    * platform: TEXT
    * environment: TEXT
    * created_by: TEXT
    * user_name: TEXT
    * created_at: TEXT
    * app_version: TEXT
    * test_case: TEXT
    * session_id: TEXT
    * linkage_id: TEXT
    * ip_address: TEXT
    * location_latitude: TEXT
    * location_longitude: TEXT
    * activity_data: TEXT
    * activity_log_level: TEXT
    * session_unique_id: TEXT
  }

  entity "filter_interaction" as filter_interaction {
    * **filter_interaction_id**: TEXT
    --
    * filter_name: TEXT
    * filter_type: TEXT
    * filter_description: TEXT
    * view_mode: TEXT
    * created_by: TEXT
    * updated_by: TEXT
    * created_at: TEXT
    * updated_at: TEXT
    * filter: TEXT
  }

  entity "vanna_ai_request_response" as vanna_ai_request_response {
    * id: TEXT
    * question: TEXT
    * sql_query: TEXT
    * results: TEXT
    * json_result: TEXT
    * created_at: TIMESTAMPTZ
    * updated_at: TIMESTAMPTZ
    * created_by: TEXT
  }

  entity "cgm_raw_zip_data" as cgm_raw_zip_data {
    * **zip_file_id**: TEXT
    --
    * tenant_id: TEXT
    * study_id: TEXT
    * file_name: VARCHAR(255)
      file_url: TEXT
    * file_format: VARCHAR(50)
    * upload_timestamp: TIMESTAMPTZ
      uploaded_by: TEXT
      file_size: VARCHAR(100)
    * is_processed: BOOLEAN
      processed_at: TIMESTAMPTZ
      status: VARCHAR(50)
      file_metadata: JSONB
    * file_content: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "cgm_raw_upload_data" as cgm_raw_upload_data {
    * **cgm_raw_file_id**: TEXT
    --
    * file_name: VARCHAR(255)
      file_url: TEXT
      zip_file_id: VARCHAR(255)
      cgm_raw_data_json: JSONB
    * upload_timestamp: TIMESTAMPTZ
      uploaded_by: VARCHAR(255)
      file_size: VARCHAR(50)
    * is_processed: BOOLEAN
      processed_at: TIMESTAMPTZ
      status: VARCHAR(50)
      file_metadata: JSONB
    * file_type: VARCHAR(50)
    * study_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "raw_cgm_extract_data" as raw_cgm_extract_data {
    * **cgm_raw_data_id**: TEXT
    --
    * raw_file_id: TEXT
    * study_id: TEXT
    * participant_sid: TEXT
      cgm_raw_data_json: JSONB
      file_url: TEXT
      file_meta_data: JSONB
      cgm_data: JSONB
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "cgm_raw_db" as cgm_raw_db {
    * **db_file_id**: TEXT
    --
    * file_name: VARCHAR(255)
      file_url: TEXT
    * upload_timestamp: TIMESTAMPTZ
      uploaded_by: VARCHAR(255)
      file_size: VARCHAR(50)
    * is_processed: BOOLEAN
      processed_at: TIMESTAMPTZ
      process_status: VARCHAR(50)
      db_file_metadata: JSONB
    * db_type: VARCHAR(50)
    * study_id: TEXT
    * tenant_id: TEXT
    * rec_status_id: INTEGER
      created_at: TIMESTAMPTZ
      created_by: TEXT
      updated_at: TIMESTAMPTZ
      updated_by: TEXT
      deleted_at: TIMESTAMPTZ
      deleted_by: TEXT
  }

  entity "file_meta_ingest_data" as file_meta_ingest_data {
    * file_meta_id: TEXT
    * db_file_id: TEXT
    * participant_sid: TEXT
      file_meta_data: TEXT
      cgm_data: TEXT
  }

  entity "participant" as participant {
      db_file_id: TEXT
      tenant_id: TEXT
      study_id: TEXT
      participant_id: TEXT
      site_id: TEXT
      diagnosis_icd: TEXT
      med_rxnorm: TEXT
      treatment_modality: TEXT
      gender: TEXT
      race_ethnicity: TEXT
      age: TEXT
      bmi: TEXT
      baseline_hba1c: TEXT
      diabetes_type: TEXT
      study_arm: TEXT
  }

  entity "migration_status" as migration_status {
      **stage_id**: SERIAL
    --
    * stage_name: TEXT
      stage_description: TEXT
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "participant_migration_status" as participant_migration_status {
    * db_file_id: TEXT
    * study_id: TEXT
    * participant_display_id: TEXT
      participant_id: TEXT
    * migration_status: INTEGER
      migration_start_time: TIMESTAMPTZ
      migration_end_time: TIMESTAMPTZ
      last_updated_at: TIMESTAMPTZ
  }

  entity "cgm_data_migration_status" as cgm_data_migration_status {
    * cgm_migrate_id: TEXT
      cgm_raw_data_id: TEXT
    * db_file_id: TEXT
    * study_id: TEXT
    * participant_id: TEXT
    * cgm_migration_status: INTEGER
      migration_start_time: TIMESTAMPTZ
      migration_end_time: TIMESTAMPTZ
      last_updated_at: TIMESTAMPTZ
    * participant_display_id: TEXT
    * file_meta_id: TEXT
  }

  entity "file_interaction" as file_interaction {
    * file_interaction_id: TEXT
      hub_interaction_id: TEXT
      study_id: TEXT
      organization_party_id: TEXT
      participant_id: TEXT
      uri: TEXT
      description: TEXT
      request: JSONB
      response: JSONB
      db_file_id: TEXT
      file_location: TEXT
      file_name: TEXT
      file_content_type: TEXT
      file_content_json: JSONB
      file_category: TEXT
      file_upload_status: TEXT
      file_processing_initiated_at: TIMESTAMPTZ
      file_processing_completed_at: TIMESTAMPTZ
      file_processing_status: TEXT
      response_code: INTEGER
      error_response: TEXT
      created_by: TEXT
      updated_by: TEXT
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "hub_interaction" as hub_interaction {
    * hub_interaction_id: TEXT
      study_id: TEXT
      organization_party_id: TEXT
      created_by: TEXT
      updated_by: TEXT
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "db_migration_log" as db_migration_log {
    * db_migration_log_id: TEXT
    * db_file_id: TEXT
    * migration_status_id: INTEGER
      participant_success_records: TEXT
      participant_failure_records: TEXT
      cgm_success_records: TEXT
      cgm_failure_records: TEXT
      migration_start_time: TIMESTAMPTZ
      migration_end_time: TIMESTAMPTZ
  }

  entity "study_interaction" as study_interaction {
    * study_interaction_id: TEXT
      hub_interaction_id: TEXT
      study_id: TEXT
      organization_party_id: TEXT
      uri: TEXT
      interaction_type: TEXT
      description: TEXT
      request: JSONB
      response: JSONB
      from_state: TEXT
      to_state: TEXT
      status: TEXT
      response_code: INTEGER
      error_response: TEXT
      created_by: TEXT
      updated_by: TEXT
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "study_participant_interaction" as study_participant_interaction {
    * participant_interaction_id: TEXT
      hub_interaction_id: TEXT
      study_id: TEXT
      organization_party_id: TEXT
      participant_id: TEXT
      uri: TEXT
      interaction_type: TEXT
      description: TEXT
      request: JSONB
      response: JSONB
      from_state: TEXT
      to_state: TEXT
      status: TEXT
      response_code: INTEGER
      error_response: TEXT
      created_by: TEXT
      updated_by: TEXT
      created_at: TIMESTAMPTZ
      updated_at: TIMESTAMPTZ
  }

  entity "cgm_device_info" as cgm_device_info {
    * **participant_sid**: TEXT
    --
      cgm_devices: TEXT
      cgm_files: TEXT
  }

  entity "participant_base" as participant_base {
      organization_party_id: TEXT
      organization_id: TEXT
      study_id: TEXT
      study_display_id: VARCHAR(10)
      participant_id: TEXT
      participant_display_id: VARCHAR(255)
      gender: TEXT
      age: INTEGER
      Study Arm: VARCHAR(255)
      Baseline HbA1C: FLOAT
  }

  entity "cgm_metrics" as cgm_metrics {
    * **participant_sid**: TEXT
    --
      total_readings: INTEGER
      days_of_wear: INTEGER
      data_start_date: DATE
      data_end_date: DATE
      avg_glucose: FLOAT
      glucose_stddev: FLOAT
      in_range_count: INTEGER
      very_high_count: INTEGER
      high_count: INTEGER
      low_count: INTEGER
      very_low_count: INTEGER
      above_range_count: INTEGER
      below_range_count: INTEGER
  }

  record_status |o..o{ organization
  record_status |o..o{ location
  organization |o..o{ practitioner
  record_status |o..o{ practitioner
  organization |o..o{ patient
  record_status |o..o{ patient
  organization |o..o{ qualification
  record_status |o..o{ qualification
  organization |o..o{ telecom
  record_status |o..o{ telecom
  organization |o..o{ address
  record_status |o..o{ address
  organization |o..o{ communication
  record_status |o..o{ communication
  record_status |o..o{ associated_party_type
  organization |o..o{ laboratory
  record_status |o..o{ laboratory
  record_status |o..o{ research_study_focus
  record_status |o..o{ research_study_condition
  record_status |o..o{ device
  record_status |o..o{ loinc_codes
  record_status |o..o{ study_visibility
  record_status |o..o{ citation_identifier
  organization |o..o{ research_study
  record_status |o..o{ research_study
  study_visibility |o..o{ research_study
  research_study |o..o{ research_subject
  organization |o..o{ research_subject
  record_status |o..o{ research_subject
  practitioner |o..o{ investigator_study
  research_study |o..o{ investigator_study
  organization |o..o{ plan_definition
  record_status |o..o{ plan_definition
  plan_definition |o..o{ goal
  organization |o..o{ goal
  record_status |o..o{ goal
  plan_definition |o..o{ activity_definition
  location |o..o{ activity_definition
  patient |o..o{ activity_definition
  organization |o..o{ activity_definition
  record_status |o..o{ activity_definition
  research_subject |o..o{ subject_observation
  organization |o..o{ subject_observation
  record_status |o..o{ subject_observation
  record_status |o..o{ citation
  research_study |o..o{ citation
  citation |o..o{ citation_author
  record_status |o..o{ citation_author
  research_study |o..o{ study_collaboration
  record_status |o..o{ study_collaboration
  record_status |o..o{ external_auth_mappings
  organization |o..o{ site
  research_study |o..o{ site
  research_subject |o..o{ site
  record_status |o..o{ site
  record_status |o..o{ cgm_observation
  organization |o..o{ cgm_raw_zip_data
  research_study |o..o{ cgm_raw_zip_data
  record_status |o..o{ cgm_raw_zip_data
  research_study |o..o{ cgm_raw_upload_data
  organization |o..o{ cgm_raw_upload_data
  record_status |o..o{ cgm_raw_upload_data
  cgm_raw_upload_data |o..o{ raw_cgm_extract_data
  research_study |o..o{ raw_cgm_extract_data
  research_subject |o..o{ raw_cgm_extract_data
  organization |o..o{ raw_cgm_extract_data
  record_status |o..o{ raw_cgm_extract_data
  research_study |o..o{ cgm_raw_db
  organization |o..o{ cgm_raw_db
  record_status |o..o{ cgm_raw_db
  migration_status |o..o{ participant_migration_status
  migration_status |o..o{ cgm_data_migration_status
  migration_status |o..o{ db_migration_log
@enduml