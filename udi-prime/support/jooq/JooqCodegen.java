import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.jar.Attributes;
import java.util.jar.JarEntry;
import java.util.jar.JarOutputStream;
import java.util.jar.Manifest;
import org.jooq.meta.jaxb.SchemaMappingType;

import javax.tools.ToolProvider;

import org.jooq.codegen.GenerationTool;
import org.jooq.meta.jaxb.Configuration;
import org.jooq.meta.jaxb.Database;
import org.jooq.meta.jaxb.Generator;
import org.jooq.meta.jaxb.Jdbc;
import org.jooq.meta.jaxb.Strategy;
import org.jooq.meta.jaxb.Target;

public class JooqCodegen {
    private record CliArguments(String jdbcUrl, String schema, String packageName, String directory, String jarName,
            String dialect) {
        String safeJdbcUrl() {
            return removeCredentials(jdbcUrl);
        }
    }

    public static void main(String[] args) {
        final var cliArgs = new CliArguments(args[0], args[1], args[2], args[3], args[4], args[5]);
        // Test database connection
        try (Connection conn = DriverManager.getConnection(cliArgs.jdbcUrl())) {
            if (conn != null) {
                System.out.println("Connected to the database!");
            } else {
                System.out.println("Failed to make connection!");
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        if (args.length < 5) {
            throw new IllegalArgumentException(
                    "Expected 6 arguments: <JDBC_URL> <SCHEMA> <PACKAGE_NAME> <DIRECTORY> <JAR_NAME> <DIALECT>");
        }

        // final var cliArgs = new CliArguments(args[0], args[1], args[2], args[3],
        // args[4]);
        System.out.println("Present Working directory" + Paths.get(".").toAbsolutePath().normalize().toString());
        System.out.println("cliArgs.schema():" + cliArgs.schema());
        System.out.println("cliArgs.packageName():" + cliArgs.packageName());
        System.out.println("cliArgs.directory():" + cliArgs.directory());
        System.out.println("cliArgs.jdbcUrl():" + cliArgs.jdbcUrl());
        System.out.println("cliArgs.dialect():" + cliArgs.dialect());
        final var dialect = cliArgs.dialect();
        String metaDB = "org.jooq.meta.postgres.PostgresDatabase";
        if ("org.sqlite.JDBC".equals(dialect)) {
            metaDB = "org.jooq.meta.sqlite.SQLiteDatabase";
        }
        List<String> drhSchemas = new ArrayList<>();
        if (metaDB.equals("org.jooq.meta.postgres.PostgresDatabase")) {
            System.out.println("Using Postgres Database");
            try (Connection connection = DriverManager.getConnection(cliArgs.jdbcUrl())) {

                Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(
                        "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT LIKE 'pg_%' AND schema_name != 'information_schema'");

                while (rs.next()) {
                    if (!rs.getString("schema_name").equals("drh_udi_assurance"))
                        drhSchemas.add(rs.getString("schema_name"));
                }
                for (String schema : drhSchemas) {
                    System.out.println("Detected schemas: " + schema);
                }
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
        }
        Database database = new Database()
                .withName("org.jooq.meta.postgres.PostgresDatabase")
                .withIncludes(".*")
                .withExcludes("");

        drhSchemas.forEach(database::withInputSchema);

        Configuration configuration = new Configuration()
                .withJdbc(new Jdbc()
                        .withDriver(dialect != null ? dialect : "org.postgresql.Driver")
                        .withUrl(cliArgs.jdbcUrl()))
                .withGenerator(new Generator()
                        .withDatabase(database)
                        .withTarget(new Target()
                                .withPackageName(cliArgs.packageName())
                                .withDirectory(cliArgs.directory())));

        try {
            GenerationTool.generate(configuration);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {

            // Ensure the target directory exists
            createDirectories(Paths.get(cliArgs.directory()));

            // Compile generated code
            compileGeneratedCode(cliArgs.directory());

            // Create JAR file
            createJarFile(cliArgs);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void compileGeneratedCode(final String directory) throws IOException {
        final var compiler = ToolProvider.getSystemJavaCompiler();
        System.out.println("Compiler:" + compiler);
        if (compiler == null) {
            throw new IllegalStateException(
                    "Cannot find the system Java compiler. Check that your class path includes tools.jar");
        }
        System.out.println("fileManager:" + compiler.getStandardFileManager(null, null, null));
        try (final var fileManager = compiler.getStandardFileManager(null, null, null)) {
            System.out.println("Java Files:");
            System.out.println("Files.walk(Paths.get(directory):" + Files.walk(Paths.get(directory)));
            final var javaFiles = Files.walk(Paths.get(directory))
                    .filter(path -> path.toString().endsWith(".java"))
                    .map(Path::toFile)
                    .toList();

            System.out.println("Java Files:");
            javaFiles.forEach(System.out::println);

            final var compilationUnits = fileManager.getJavaFileObjectsFromFiles(javaFiles);
            System.out.println("compilationUnits:" + compilationUnits);

            final var options = List.of("-d", directory);
            System.out.println("options:" + options);

            final var task = compiler.getTask(null, fileManager, null, options, null,
                    compilationUnits);
            System.out.println("task:" + task);
            // System.out.println("task.call():"+task.call());

            if (!task.call()) {
                throw new RuntimeException("Compilation failed");
            }

        }
        System.out.println("Compilation completed");
    }

    private static void createJarFile(final CliArguments cliArgs) throws IOException {
        final var dirPath = Paths.get(cliArgs.directory());

        final var jarPath = Paths.get(cliArgs.jarName());
        Files.createDirectories(jarPath.getParent());
        // Create a manifest with metadata
        final var manifest = new Manifest();
        final var attributes = manifest.getMainAttributes();
        attributes.put(Attributes.Name.MANIFEST_VERSION, "1.0");
        attributes.put(new Attributes.Name("Generated-By"), JooqCodegen.class.getName());
        attributes.put(new Attributes.Name("Generated-By-Java-Version"), System.getProperty("java.version"));
        attributes.put(new Attributes.Name("Generated-At"), new Date().toString());
        attributes.put(new Attributes.Name("Generated-On-Host-Name"), getHostName());
        attributes.put(new Attributes.Name("Generated-On-Host-IP"), getHostIP());
        attributes.put(new Attributes.Name("Generated-On-OS-Name"), System.getProperty("os.name"));
        attributes.put(new Attributes.Name("Generated-On-OS-Version"), System.getProperty("os.version"));
        attributes.put(new Attributes.Name("Generated-From-Schema"), cliArgs.schema());
        final var dialect = cliArgs.dialect();
        String jdbcUrl;

        if ("org.sqlite.JDBC".equals(dialect)) {
            jdbcUrl = cliArgs.jdbcUrl();
        } else {
            jdbcUrl = cliArgs.safeJdbcUrl();
        }

        attributes.put(new Attributes.Name("Generated-From-JDBC-URL"), jdbcUrl);
        attributes.put(new Attributes.Name("Package-Name"), cliArgs.packageName());
        attributes.put(new Attributes.Name("Build-Dir"), cliArgs.directory());

        try (final var jos = new JarOutputStream(
                Files.newOutputStream(Paths.get((cliArgs.jarName()))), manifest)) {
            Files.walk(dirPath)
                    .filter(path -> path.toString().endsWith(".class") || path.toString().endsWith(".java"))
                    .forEach(path -> {
                        final var entryName = dirPath.relativize(path).toString().replace("\\", "/");
                        try {
                            jos.putNextEntry(new JarEntry(entryName));
                            Files.copy(path, jos);
                            jos.closeEntry();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
        }

        System.out.println("%s:META-INF/MANIFEST.MF".formatted(cliArgs.jarName()));
        try (final var jar = new java.util.zip.ZipFile(cliArgs.jarName())) {
            jar.stream()
                    .filter(e -> e.getName().equals("META-INF/MANIFEST.MF"))
                    .findFirst()
                    .ifPresent(e -> {
                        try (final var is = jar.getInputStream(e)) {
                            is.transferTo(System.out);
                        } catch (java.io.IOException ex) {
                            ex.printStackTrace();
                        }
                    });
        } catch (java.io.IOException ex) {
            ex.printStackTrace();
        }
    }

    private static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (IOException e) {
            return "Unknown";
        }
    }

    private static String getHostIP() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (IOException e) {
            return "Unknown";
        }
    }

    private static String removeCredentials(final String jdbcUrl) {
        try {
            final var uri = new URI(jdbcUrl.replaceFirst("^jdbc:", ""));
            // remove the userInfo and query parts of the URI since those will include
            // credentials
            final var safeUri = new URI(uri.getScheme(), null, uri.getHost(), uri.getPort(), uri.getPath(), null, null);
            return "jdbc:" + safeUri.toString();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Invalid JDBC URL", e);
        }
    }

    private static void createDirectories(Path path) throws IOException {
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
    }

}