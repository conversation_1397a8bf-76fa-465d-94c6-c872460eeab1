
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Package Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
            font-weight: bold;
        }
        h3, h4 {
            margin-top: 30px;
            color: #333;
        }
        p {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h2>Data File (CSV) Specification</h2>
    <p>We leverage the <a href="https://specs.frictionlessdata.io/data-package/" target="_blank">Frictionless Data Package framework</a> to collect and unify data from various disparate sources, providing a structured overview of the CSV files. Frictionless Data, an open standard, simplifies working with data by ensuring it is well-organized, consistent, and accessible. The Data Package acts as a lightweight container, bundling data files with their metadata to serve as a comprehensive 'guidebook' describing the structure, contents, and quality of the data. This specification report offers detailed insights into the structure of each file, including its format and data types, to support the quality and reliability of the information.</p>


    <h2>Package Metadata</h2>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
<tr><td>name</td><td>study-data</td></tr></table>
    <h2>Resources</h2>

    <h3>Resource: cgm_file_metadata</h3>
    <p><strong>Description:</strong> This resource tracks metadata for CGM files, including information like the device used, file details, and associated study identifiers.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>cgm_file_metadata</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>This resource tracks metadata for CGM files, including information like the device used, file details, and associated study identifiers.</td></tr><tr><td>path</td><td>research-study-sample/cgm_file_metadata.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>recommended</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>metadata_id</td>
                <td>string</td>
                <td>A unique identifier for the record</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>devicename</td>
                <td>string</td>
                <td>Name of the device</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>device_id</td>
                <td>string</td>
                <td>Unique identifier for the device</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>source_platform</td>
                <td>string</td>
                <td>Platform or system from which data originated</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>patient_id</td>
                <td>string</td>
                <td>Unique identifier for the patient</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>file_name</td>
                <td>string</td>
                <td>Name of the uploaded file</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>file_format</td>
                <td>string</td>
                <td>Format of the uploaded file (e.g., CSV, excel)</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>file_upload_date</td>
                <td>date</td>
                <td>Date when the file was uploaded</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>data_start_date</td>
                <td>date</td>
                <td>Start date of the data period covered by the file</td>
                <td>None</td>
            </tr>
            
            <tr>
                <td>data_end_date</td>
                <td>date</td>
                <td>End date of the data period covered by the file</td>
                <td>None</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study associated with the data</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: participant</h3>
    <p><strong>Description:</strong> Information about the participants/patients in the study, including identifiers, demographic data, medical details, and the treatment group assigned to them.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>participant</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Information about the participants/patients in the study, including identifiers, demographic data, medical details, and the treatment group assigned to them.</td></tr><tr><td>path</td><td>research-study-sample/participant.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>required</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>participant_id</td>
                <td>string</td>
                <td>Unique identifier for the participant/patient</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>site_id</td>
                <td>string</td>
                <td>Identifier for the site where participant is enrolled</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>diagnosis_icd</td>
                <td>string</td>
                <td>Diagnosis code based on International Classification of Diseases (ICD) system</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>med_rxnorm</td>
                <td>string</td>
                <td>Medication code based on RxNorm system</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>treatment_modality</td>
                <td>string</td>
                <td>Modality of treatment for the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>gender</td>
                <td>string</td>
                <td>Gender of the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>race_ethnicity</td>
                <td>string</td>
                <td>Race and ethnicity of the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>age</td>
                <td>integer</td>
                <td>Age of the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>bmi</td>
                <td>number</td>
                <td>Body Mass Index (BMI) of the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>baseline_hba1c</td>
                <td>number</td>
                <td>Baseline Hemoglobin A1c level of the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>diabetes_type</td>
                <td>string</td>
                <td>Type of diabetes diagnosed for the participant</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_arm</td>
                <td>string</td>
                <td>Arm or group to which the participant is assigned in the study</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: site</h3>
    <p><strong>Description:</strong> Describes the study sites where participants are enrolled, including site identifiers, names, and types.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>site</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Describes the study sites where participants are enrolled, including site identifiers, names, and types.</td></tr><tr><td>path</td><td>research-study-sample/site.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>optional</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>site_id</td>
                <td>string</td>
                <td>Unique identifier for the site</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>site_name</td>
                <td>string</td>
                <td>Name of the site</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>site_type</td>
                <td>string</td>
                <td>Type or category of the site (e.g., hospital, clinic)</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: study</h3>
    <p><strong>Description:</strong> Central information about each study, such as the study name, start and end dates, funding sources, and treatment modalities.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>study</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Central information about each study, such as the study name, start and end dates, funding sources, and treatment modalities.</td></tr><tr><td>path</td><td>research-study-sample/study.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>recommended</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_name</td>
                <td>string</td>
                <td>Name or title of the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>start_date</td>
                <td>date</td>
                <td>Date when the study commences</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>end_date</td>
                <td>date</td>
                <td>Date when the study concludes</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>treatment_modalities</td>
                <td>string</td>
                <td>Different modalities or interventions used in the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>funding_source</td>
                <td>string</td>
                <td>Source(s) of funding for the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>nct_number</td>
                <td>string</td>
                <td>ClinicalTrials.gov identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_description</td>
                <td>string</td>
                <td>Description about Study</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: investigator</h3>
    <p><strong>Description:</strong> Details about researchers involved in the studies, including their names, institutions, and study affiliations.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>investigator</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Details about researchers involved in the studies, including their names, institutions, and study affiliations.</td></tr><tr><td>path</td><td>research-study-sample/investigator.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>recommended</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>investigator_id</td>
                <td>string</td>
                <td>The ID of the investigator / researcher</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>investigator_name</td>
                <td>string</td>
                <td>Name of the Researcher</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Researcher email</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>institution_id</td>
                <td>string</td>
                <td>Unique identifier for the institution</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>ID for the study associated with the researcher</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: institution</h3>
    <p><strong>Description:</strong> Information about the institutions where labs or research activities are conducted, including identifiers, names, and locations.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>institution</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Information about the institutions where labs or research activities are conducted, including identifiers, names, and locations.</td></tr><tr><td>path</td><td>research-study-sample/institution.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>optional</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>institution_id</td>
                <td>string</td>
                <td>Unique identifier for the institution</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>institution_name</td>
                <td>string</td>
                <td>Name of the institution</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>city</td>
                <td>string</td>
                <td>City where the institution is located</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>state</td>
                <td>string</td>
                <td>State where the institution is located</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>country</td>
                <td>string</td>
                <td>Country where the institution is located</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: lab</h3>
    <p><strong>Description:</strong> Data on the labs involved in the studies, including lab names, principal investigators, and associated institutions.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>lab</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Data on the labs involved in the studies, including lab names, principal investigators, and associated institutions.</td></tr><tr><td>path</td><td>research-study-sample/lab.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>optional</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>lab_id</td>
                <td>string</td>
                <td>Unique identifier for the laboratory</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>lab_name</td>
                <td>string</td>
                <td>Name of the laboratory</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>lab_pi</td>
                <td>string</td>
                <td>Principal investigator associated with the lab</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>institution_id</td>
                <td>string</td>
                <td>Unique identifier of the institution the lab belongs to</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: author</h3>
    <p><strong>Description:</strong> Information about authors related to the research studies, typically researchers who are affiliated with specific studies and investigators.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>author</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Information about authors related to the research studies, typically researchers who are affiliated with specific studies and investigators.</td></tr><tr><td>path</td><td>research-study-sample/author.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>optional</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>author_id</td>
                <td>string</td>
                <td>Unique identifier for the author</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>name</td>
                <td>string</td>
                <td>Name of the author</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Email of the author</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>investigator_id</td>
                <td>string</td>
                <td>Unique identifier of the investigator the author is associated with</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            </table>
    <h3>Resource: publication</h3>
    <p><strong>Description:</strong> Details about the publications associated with the studies, including titles, digital object identifiers, and the publishing site.</p>
    <table>
        <tr><th>Field</th><th>Value</th></tr>
    <tr><td>name</td><td>publication</td></tr><tr><td>type</td><td>table</td></tr><tr><td>description</td><td>Details about the publications associated with the studies, including titles, digital object identifiers, and the publishing site.</td></tr><tr><td>path</td><td>research-study-sample/publication.csv</td></tr><tr><td>scheme</td><td>file</td></tr><tr><td>format</td><td>csv</td></tr><tr><td>mediatype</td><td>text/csv</td></tr><tr><td>encoding</td><td>utf-8</td></tr><tr><td>status</td><td>optional</td></tr></table>
        <h4>Schema Fields</h4>
        <table>
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Constraints</th>
            </tr>
        
            <tr>
                <td>publication_id</td>
                <td>string</td>
                <td>Unique identifier for the publication</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>publication_title</td>
                <td>string</td>
                <td>Title of the publication</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>digital_object_identifier</td>
                <td>string</td>
                <td>Identifier for the digital object associated with the publication</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>publication_site</td>
                <td>string</td>
                <td>Publishing site</td>
                <td>{'required': True}</td>
            </tr>
            
            <tr>
                <td>study_id</td>
                <td>string</td>
                <td>Unique identifier for the study</td>
                <td>{'required': True}</td>
            </tr>
            </table>
</body>
</html>
