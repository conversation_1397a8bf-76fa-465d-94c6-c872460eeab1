# `study-data`
## `cgm_file_metadata`
  - `path` research-study-sample/cgm_file_metadata.csv
  - `schema`
      - `primaryKey` ['metadata_id']
    - `foreignKeys`
      - [1]
        - `fields` ['study_id']
        - `reference`
          - `resource` study
          - `fields` ['study_id']
    - `dialect`
      - `delimiter` ,
### `metadata_id`
  - `type` string
  - `constraints`:
    - `required` True
### `devicename`
  - `type` string
  - `constraints`:
    - `required` True
### `device_id`
  - `type` string
  - `constraints`:
    - `required` True
### `source_platform`
  - `type` string
  - `constraints`:
    - `required` True
### `patient_id`
  - `type` string
  - `constraints`:
    - `required` True
### `file_name`
  - `type` string
  - `constraints`:
    - `required` True
### `file_format`
  - `type` string
  - `constraints`:
    - `required` True
### `file_upload_date`
  - `type` date
  - `constraints`:
    - `required` True
### `data_start_date`
  - `type` date
### `data_end_date`
  - `type` date
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
## `participant`
  - `path` research-study-sample/participant.csv
  - `schema`
      - `primaryKey` ['participant_id']
    - `foreignKeys`
      - [1]
        - `fields` ['study_id']
        - `reference`
          - `resource` study
          - `fields` ['study_id']
      - [2]
        - `fields` ['site_id']
        - `reference`
          - `resource` site
          - `fields` ['site_id']
    - `dialect`
      - `delimiter` ,
### `participant_id`
  - `type` string
  - `constraints`:
    - `required` True
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
### `site_id`
  - `type` string
  - `constraints`:
    - `required` True
### `diagnosis_icd`
  - `type` string
  - `constraints`:
    - `required` True
### `med_rxnorm`
  - `type` string
  - `constraints`:
    - `required` True
### `treatment_modality`
  - `type` string
  - `constraints`:
    - `required` True
### `gender`
  - `type` string
  - `constraints`:
    - `required` True
### `race_ethnicity`
  - `type` string
  - `constraints`:
    - `required` True
### `age`
  - `type` integer
  - `constraints`:
    - `required` True
### `bmi`
  - `type` number
  - `constraints`:
    - `required` True
### `baseline_hba1c`
  - `type` number
  - `constraints`:
    - `required` True
### `diabetes_type`
  - `type` string
  - `constraints`:
    - `required` True
### `study_arm`
  - `type` string
  - `constraints`:
    - `required` True
## `site`
  - `path` research-study-sample/site.csv
  - `schema`
      - `primaryKey` ['site_id']
    - `foreignKeys` []
    - `dialect`
      - `delimiter` ,
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
### `site_id`
  - `type` string
  - `constraints`:
    - `required` True
### `site_name`
  - `type` string
  - `constraints`:
    - `required` True
### `site_type`
  - `type` string
  - `constraints`:
    - `required` True
## `study`
  - `path` research-study-sample/study.csv
  - `schema`
      - `primaryKey` ['study_id']
    - `foreignKeys` []
    - `dialect`
      - `delimiter` ,
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
### `study_name`
  - `type` string
  - `constraints`:
    - `required` True
### `start_date`
  - `type` date
  - `constraints`:
    - `required` True
### `end_date`
  - `type` date
  - `constraints`:
    - `required` True
### `treatment_modalities`
  - `type` string
  - `constraints`:
    - `required` True
### `funding_source`
  - `type` string
  - `constraints`:
    - `required` True
### `nct_number`
  - `type` string
  - `constraints`:
    - `required` True
### `study_description`
  - `type` string
  - `constraints`:
    - `required` True
## `investigator`
  - `path` research-study-sample/investigator.csv
  - `schema`
      - `primaryKey` ['investigator_id']
    - `foreignKeys`
      - [1]
        - `fields` ['study_id']
        - `reference`
          - `resource` study
          - `fields` ['study_id']
    - `dialect`
      - `delimiter` ,
### `investigator_id`
  - `type` string
  - `constraints`:
    - `required` True
### `investigator_name`
  - `type` string
  - `constraints`:
    - `required` True
### `email`
  - `type` string
  - `constraints`:
    - `required` True
### `institution_id`
  - `type` string
  - `constraints`:
    - `required` True
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
## `institution`
  - `path` research-study-sample/institution.csv
  - `schema`
      - `primaryKey` ['institution_id']
    - `foreignKeys` []
    - `dialect`
      - `delimiter` ,
### `institution_id`
  - `type` string
  - `constraints`:
    - `required` True
### `institution_name`
  - `type` string
  - `constraints`:
    - `required` True
### `city`
  - `type` string
  - `constraints`:
    - `required` True
### `state`
  - `type` string
  - `constraints`:
    - `required` True
### `country`
  - `type` string
  - `constraints`:
    - `required` True
## `lab`
  - `path` research-study-sample/lab.csv
  - `schema`
      - `primaryKey` ['lab_id']
    - `foreignKeys`
      - [1]
        - `fields` ['study_id']
        - `reference`
          - `resource` study
          - `fields` ['study_id']
      - [2]
        - `fields` ['institution_id']
        - `reference`
          - `resource` institution
          - `fields` ['institution_id']
    - `dialect`
      - `delimiter` ,
### `lab_id`
  - `type` string
  - `constraints`:
    - `required` True
### `lab_name`
  - `type` string
  - `constraints`:
    - `required` True
### `lab_pi`
  - `type` string
  - `constraints`:
    - `required` True
### `institution_id`
  - `type` string
  - `constraints`:
    - `required` True
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
## `author`
  - `path` research-study-sample/author.csv
  - `schema`
      - `primaryKey` ['author_id']
    - `foreignKeys`
      - [1]
        - `field` investigator_id
        - `reference`
          - `table` investigator
          - `field` investigator_id
          - `resource` 
          - `fields` []
        - `fields` []
      - [2]
        - `field` study_id
        - `references`
          - `resource` study
          - `field` study_id
        - `fields` []
        - `reference`
          - `resource` 
          - `fields` []
    - `dialect`
      - `delimiter` ,
### `author_id`
  - `type` string
  - `constraints`:
    - `required` True
### `name`
  - `type` string
  - `constraints`:
    - `required` True
### `email`
  - `type` string
  - `constraints`:
    - `required` True
### `investigator_id`
  - `type` string
  - `constraints`:
    - `required` True
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True
## `publication`
  - `path` research-study-sample/publication.csv
  - `schema`
      - `primaryKey` ['publication_id']
    - `foreignKeys`
      - [1]
        - `field` study_id
        - `reference`
          - `resource` study
          - `field` study_id
          - `fields` []
        - `fields` []
    - `dialect`
      - `delimiter` ,
### `publication_id`
  - `type` string
  - `constraints`:
    - `required` True
### `publication_title`
  - `type` string
  - `constraints`:
    - `required` True
### `digital_object_identifier`
  - `type` string
  - `constraints`:
    - `required` True
### `publication_site`
  - `type` string
  - `constraints`:
    - `required` True
### `study_id`
  - `type` string
  - `constraints`:
    - `required` True