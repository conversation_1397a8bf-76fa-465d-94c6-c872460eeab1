# Study Data Validation using Frictionless

This repository contains Python code for validating study data files based on a JSON schema, following **Frictionless Data** specifications. The files in this project contain metadata and other data related to research studies, participants, and more. This validation ensures that the data adheres to the expected structure, types, and relationships.

## Validation Process

The validation process checks for the following:

1. **File Existence**: 
   - Ensures the required files are present.

2. **Field Completeness**: 
   - Verifies all required fields are included.
   - Checks for missing values in the fields.

3. **Field Types**: 
   - Ensures the correct data types (e.g., date, integer, number) for each field.

4. **File Integrity**: 
   - Checks if the files are empty or contain no data.

5. **Specific Data Validations**: 
   - Additional checks for foreign key (FK) and primary key (PK) relationships.
   - Identifies format issues in the data.

6. **CGM Tracing Files**: 
   - These are critical files for the study.
   - Although they are mandatory, their validation is not handled by Frictionless, as the structure, data types, and filenames may vary.



## File Structure

The project consists of the following key components:

### 1. **Data Files**

The data files contain records related to research studies and participants. These are CSV files, each corresponding to a specific aspect of the study data.

Example file structure:

```
research-study-sample/
    ├── cgm_file_metadata.csv
    ├── participant.csv
    ├── site.csv
    ├── study.csv
    ├── investigator.csv
    ├── institution.csv
    ├── lab.csv
    ├── author.csv
    ├── publication.csv
    ├── cgm_tracing(0-*).csv
```

Each CSV file represents a resource with multiple rows of data. The columns in each file are defined by the schema and must adhere to the expected types, formats, and constraints (e.g., primary keys, foreign keys).



### 2. **Schema JSON File**

The schema is described in a JSON file (`study-validation-datapackage.json`) that specifies the structure, required fields, and data types for each file. 
The validation process is based on a JSON schema that defines the structure and expected fields for each file in the study data package. The schema also specifies field types, formats (e.g., date format), and required fields.


### 3. **Python Script for Validation**

The `validate-study-data-package.py` script performs the validation by reading the schema and checking each data file against the expected structure and constraints.

## Installation

1. **Clone the repository** or download the files to your local machine:


2. **Install required dependencies**:

Ensure you have **Python 3.x** installed, then install the necessary libraries:

```bash
pip install frictionless
pip install python-dateutil
```

## Configuration

1. **Schema JSON File**:
   The `study-validation-datapackage.json` file defines the structure of the data. Customize this file based on your study's data schema, including field names, data types, required fields, and any relationships between data tables (e.g., primary and foreign keys).

2. **Data Files**:
   The study data files should be placed in the `research-study-sample/` folder. Each file should follow the structure defined in the schema, with the correct field names, data types, and formats.

## Running the Validation

### Run the Validation Script

Define primary key (`PK`) and foreign key (`FK`) relationships for each table in your schema.Once the schema and data files are configured, run the validation script using Python:

```bash
python3 validate-study-data-package.py
```

### Expected Output

The script will print validation messages for each file. If there are validation errors, they will be displayed, such as:

```text
Validating file: research-study-sample/cgm_file_metadata.csv
Missing required fields: 'field1', 'field2'
Columns with empty values: 'field3', 'field4'
Duplicate Primary Key values: id
Foreign Key violation: participant_id value 123 not found in participant
Invalid date format: field5 - 2024-31-12

Validation Errors:
File 'research-study-sample/cgm_file_metadata.csv' has data issues.
```

If no issues are found, the script will print:

```text
All files validated successfully.
```

## Detailed Validation Checks

The validation script performs several checks:

1. **File Existence**: Ensures each file listed in the schema exists.
2. **Field Completeness**: Verifies that required fields are present and contain data.
3. **Empty Data**: Identifies columns or rows with empty values.
4. **Primary Key Validation**: Ensures primary key values are unique across the file.
5. **Foreign Key Validation**: Checks that foreign key values exist in the referenced table.
6. **Data Type Validation**: Ensures data matches the expected type (e.g., integer, date).
7. **Date Format Validation**: Ensures date fields match the expected format.


## Conclusion

This project provides an automated way to validate research study data files against a defined schema. Using **Frictionless Data** principles, it ensures that your data is complete, consistent, and ready for analysis or reporting.

