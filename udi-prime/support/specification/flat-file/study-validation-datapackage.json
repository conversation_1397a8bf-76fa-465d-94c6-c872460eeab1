{"name": "study-data", "resources": [{"name": "cgm_file_metadata", "description": "This resource tracks metadata for CGM files, including information like the device used, file details, and associated study identifiers.", "path": "research-study-sample/cgm_file_metadata.csv", "status": "recommended", "schema": {"fields": [{"name": "metadata_id", "type": "string", "description": "A unique identifier for the record", "constraints": {"required": true}}, {"name": "devicename", "type": "string", "description": "Name of the device", "constraints": {"required": true}}, {"name": "device_id", "type": "string", "description": "Unique identifier for the device", "constraints": {"required": true}}, {"name": "source_platform", "type": "string", "description": "Platform or system from which data originated", "constraints": {"required": true}}, {"name": "patient_id", "type": "string", "description": "Unique identifier for the patient", "constraints": {"required": true}}, {"name": "file_name", "type": "string", "description": "Name of the uploaded file", "constraints": {"required": true}}, {"name": "file_format", "type": "string", "description": "Format of the uploaded file (e.g., CSV, excel)", "constraints": {"required": true}}, {"name": "file_upload_date", "type": "date", "description": "Date when the file was uploaded", "format": "%Y-%m-%d", "constraints": {"required": true}}, {"name": "data_start_date", "type": "date", "description": "Start date of the data period covered by the file", "format": "%Y-%m-%d"}, {"name": "data_end_date", "type": "date", "description": "End date of the data period covered by the file", "format": "%Y-%m-%d"}, {"name": "study_id", "type": "string", "description": "Unique identifier for the study associated with the data", "constraints": {"required": true}}], "primaryKey": ["metadata_id"], "foreignKeys": [{"fields": ["study_id"], "reference": {"resource": "study", "fields": ["study_id"]}}], "dialect": {"delimiter": ","}}}, {"name": "participant", "description": "Information about the participants/patients in the study, including identifiers, demographic data, medical details, and the treatment group assigned to them.", "path": "research-study-sample/participant.csv", "status": "required", "schema": {"fields": [{"name": "participant_id", "type": "string", "description": "Unique identifier for the participant/patient", "constraints": {"required": true}}, {"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}, {"name": "site_id", "type": "string", "description": "Identifier for the site where participant is enrolled", "constraints": {"required": true}}, {"name": "diagnosis_icd", "type": "string", "description": "Diagnosis code based on International Classification of Diseases (ICD) system", "constraints": {"required": true}}, {"name": "med_rxnorm", "type": "string", "description": "Medication code based on RxNorm system", "constraints": {"required": true}}, {"name": "treatment_modality", "type": "string", "description": "Modality of treatment for the participant", "constraints": {"required": true}}, {"name": "gender", "type": "string", "description": "Gender of the participant", "constraints": {"required": true}}, {"name": "race_ethnicity", "type": "string", "description": "Race and ethnicity of the participant", "constraints": {"required": true}}, {"name": "age", "type": "integer", "description": "Age of the participant", "constraints": {"required": true}}, {"name": "bmi", "type": "number", "description": "Body Mass Index (BMI) of the participant", "constraints": {"required": true}}, {"name": "baseline_hba1c", "type": "number", "description": "Baseline Hemoglobin A1c level of the participant", "constraints": {"required": true}}, {"name": "diabetes_type", "type": "string", "description": "Type of diabetes diagnosed for the participant", "constraints": {"required": true}}, {"name": "study_arm", "type": "string", "description": "Arm or group to which the participant is assigned in the study", "constraints": {"required": true}}], "primaryKey": ["participant_id"], "foreignKeys": [{"fields": ["study_id"], "reference": {"resource": "study", "fields": ["study_id"]}}, {"fields": ["site_id"], "reference": {"resource": "site", "fields": ["site_id"]}}], "dialect": {"delimiter": ","}}}, {"name": "site", "description": "Describes the study sites where participants are enrolled, including site identifiers, names, and types.", "path": "research-study-sample/site.csv", "status": "optional", "schema": {"fields": [{"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}, {"name": "site_id", "type": "string", "description": "Unique identifier for the site", "constraints": {"required": true}}, {"name": "site_name", "type": "string", "description": "Name of the site", "constraints": {"required": true}}, {"name": "site_type", "type": "string", "description": "Type or category of the site (e.g., hospital, clinic)", "constraints": {"required": true}}], "primaryKey": ["site_id"], "foreignKeys": [{"fields": ["study_id"], "reference": {"resource": "study", "fields": ["study_id"]}}], "dialect": {"delimiter": ","}}}, {"name": "study", "description": "Central information about each study, such as the study name, start and end dates, funding sources, and treatment modalities.", "path": "research-study-sample/study.csv", "status": "recommended", "schema": {"fields": [{"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}, {"name": "study_name", "type": "string", "description": "Name or title of the study", "constraints": {"required": true}}, {"name": "start_date", "type": "date", "description": "Date when the study commences", "format": "%Y-%m-%d", "constraints": {"required": true}}, {"name": "end_date", "type": "date", "description": "Date when the study concludes", "format": "%Y-%m-%d", "constraints": {"required": true}}, {"name": "treatment_modalities", "type": "string", "description": "Different modalities or interventions used in the study", "constraints": {"required": true}}, {"name": "funding_source", "type": "string", "description": "Source(s) of funding for the study", "constraints": {"required": true}}, {"name": "nct_number", "type": "string", "description": "ClinicalTrials.gov identifier for the study", "constraints": {"required": true}}, {"name": "study_description", "type": "string", "description": "Description about Study", "constraints": {"required": true}}], "primaryKey": ["study_id"], "dialect": {"delimiter": ","}}}, {"name": "investigator", "description": "Details about researchers involved in the studies, including their names, institutions, and study affiliations.", "path": "research-study-sample/investigator.csv", "status": "recommended", "schema": {"fields": [{"name": "investigator_id", "type": "string", "description": "The ID of the investigator / researcher", "constraints": {"required": true}}, {"name": "investigator_name", "type": "string", "description": "Name of the Researcher", "constraints": {"required": true}}, {"name": "email", "type": "string", "description": "Researcher email", "constraints": {"required": true}}, {"name": "institution_id", "type": "string", "description": "Unique identifier for the institution", "constraints": {"required": true}}, {"name": "study_id", "type": "string", "description": "ID for the study associated with the researcher", "constraints": {"required": true}}], "primaryKey": ["investigator_id"], "foreignKeys": [{"fields": ["study_id"], "reference": {"resource": "study", "fields": ["study_id"]}}], "dialect": {"delimiter": ","}}}, {"name": "institution", "description": "Information about the institutions where labs or research activities are conducted, including identifiers, names, and locations.", "path": "research-study-sample/institution.csv", "status": "optional", "schema": {"fields": [{"name": "institution_id", "type": "string", "description": "Unique identifier for the institution", "constraints": {"required": true}}, {"name": "institution_name", "type": "string", "description": "Name of the institution", "constraints": {"required": true}}, {"name": "city", "type": "string", "description": "City where the institution is located", "constraints": {"required": true}}, {"name": "state", "type": "string", "description": "State where the institution is located", "constraints": {"required": true}}, {"name": "country", "type": "string", "description": "Country where the institution is located", "constraints": {"required": true}}], "primaryKey": ["institution_id"], "dialect": {"delimiter": ","}}}, {"name": "lab", "description": "Data on the labs involved in the studies, including lab names, principal investigators, and associated institutions.", "path": "research-study-sample/lab.csv", "status": "optional", "schema": {"fields": [{"name": "lab_id", "type": "string", "description": "Unique identifier for the laboratory", "constraints": {"required": true}}, {"name": "lab_name", "type": "string", "description": "Name of the laboratory", "constraints": {"required": true}}, {"name": "lab_pi", "type": "string", "description": "Principal investigator associated with the lab", "constraints": {"required": true}}, {"name": "institution_id", "type": "string", "description": "Unique identifier of the institution the lab belongs to", "constraints": {"required": true}}, {"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}], "primaryKey": ["lab_id"], "foreignKeys": [{"fields": ["study_id"], "reference": {"resource": "study", "fields": ["study_id"]}}, {"fields": ["institution_id"], "reference": {"resource": "institution", "fields": ["institution_id"]}}], "dialect": {"delimiter": ","}}}, {"name": "author", "description": "Information about authors related to the research studies, typically researchers who are affiliated with specific studies and investigators.", "path": "research-study-sample/author.csv", "status": "optional", "schema": {"fields": [{"name": "author_id", "type": "string", "description": "Unique identifier for the author", "constraints": {"required": true}}, {"name": "name", "type": "string", "description": "Name of the author", "constraints": {"required": true}}, {"name": "email", "type": "string", "description": "Email of the author", "constraints": {"required": true}}, {"name": "investigator_id", "type": "string", "description": "Unique identifier of the investigator the author is associated with", "constraints": {"required": true}}, {"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}], "primaryKey": ["author_id"], "foreignKeys": [{"field": "investigator_id", "reference": {"table": "investigator", "field": "investigator_id"}}, {"field": "study_id", "references": {"resource": "study", "field": "study_id"}}], "dialect": {"delimiter": ","}}}, {"name": "publication", "description": "Details about the publications associated with the studies, including titles, digital object identifiers, and the publishing site.", "path": "research-study-sample/publication.csv", "status": "optional", "schema": {"fields": [{"name": "publication_id", "type": "string", "description": "Unique identifier for the publication", "constraints": {"required": true}}, {"name": "publication_title", "type": "string", "description": "Title of the publication", "constraints": {"required": true}}, {"name": "digital_object_identifier", "type": "string", "description": "Identifier for the digital object associated with the publication", "constraints": {"required": true}}, {"name": "publication_site", "type": "string", "description": "Publishing site", "constraints": {"required": true}}, {"name": "study_id", "type": "string", "description": "Unique identifier for the study", "constraints": {"required": true}}], "primaryKey": ["publication_id"], "foreignKeys": [{"field": "study_id", "reference": {"resource": "study", "field": "study_id"}}], "dialect": {"delimiter": ","}}}]}