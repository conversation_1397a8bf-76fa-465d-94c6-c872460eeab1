import { DB } from "https://deno.land/x/sqlite/mod.ts";

// Helper function to write content to a file
async function writeTextFile(filename: string, content: string) {
  await Deno.writeTextFile(filename, content);
  console.log(`File written: ${filename}`);
}

// Open the main database
const db = new DB("main_database.sqlite.db");

// Attach the other databases
const attachedDbs = [
  { alias: 'resource_surveillance_dclp1', path: 'resource-surveillance-dclp1.sqlite.db' },
  { alias: 'resource_surveillance_dclp3', path: 'resource-surveillance-dclp3.sqlite.db' },
  { alias: 'resource_surveillance_dss1', path: 'resource-surveillance-dss1.sqlite.db' },
  { alias: 'resource_surveillance_ntlt', path: 'resource-surveillance-ntlt.sqlite.db' }
];

for (const { alias, path } of attachedDbs) {
  db.execute(`ATTACH DATABASE '${path}' AS ${alias}`);
}

// Collect DDL statements from all attached databases
const ddlStatements: { [key: string]: string[] } = {};

for (const { alias } of attachedDbs) {
  const tables = db.query(`SELECT name FROM ${alias}.sqlite_master WHERE type='table'`);
  ddlStatements[alias] = [];

  for (const [table_name] of tables) {
    const ddl = db.query(`SELECT sql FROM ${alias}.sqlite_master WHERE type='table' AND name=?`, [table_name]);
    if (ddl.length > 0) {
      ddlStatements[alias].push(ddl[0][0] as string);
    }
  }
}

// Write DDL statements to a .sql file
const outputFileName = 'ddl_output.sql';
let outputContent = '';

for (const alias in ddlStatements) {
  outputContent += `-- DDL Statements for ${alias} --\n`;
  ddlStatements[alias].forEach((ddl) => {
    outputContent += `${ddl};\n\n`;
  });
  outputContent += '\n';
}

await writeTextFile(outputFileName, outputContent);

// Detach the databases
for (const { alias } of attachedDbs) {
  db.execute(`DETACH DATABASE ${alias}`);
}

// Close the database connection
db.close();
