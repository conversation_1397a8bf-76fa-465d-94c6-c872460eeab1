
# Sqlite to postgres migration using surveilr and duckdb

- From the individual study folder, Ingest the csv files using `surveilr ingest`

    ```bash
    surveilr ingest files
    ```

- Once the files are ingested, transform the csv content into table using `surveilr transform`

    ```bash
    surveilr transform csv
    ```

<!-- ```
deno run --allow-read --allow-write automate-ddl-sqlite.ts
``` -->
- Copy the `resouce-surveillance-<study_name>.sqlite.db` RSSDs into the `support/surveilr` folder.

### Merge RSSDs

- Initialize a sqlite db (`target.sqlite.db`) using surveilr

    ```bash
    surveilr admin init -d target.sqlite.db -r
    ```

- Using merge script, merge the RSSDs into the `target.sqlite.db`

    ```bash
    cat merge-rssds.sql | sqlite3 target.sqlite.db
    ```

- Migrate the `target.sqlite.db` to postgreSQL db using the migrate script
    ```bash
    cat migrate_sqlite_pg.sql | duckdb ':memory:'
    ```