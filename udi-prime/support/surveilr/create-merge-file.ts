import { Database } from "https://deno.land/x/sqlite/mod.ts";
import { existsSync } from "https://deno.land/std/fs/mod.ts";

// Function to fetch all table names from a SQLite database
async function fetchTableNames(databasePath: string): Promise<string[]> {
    const db = new Database(databasePath);

    const tableNames: string[] = [];

    // Query sqlite_master for all table names
    for (const row of db.query("SELECT name FROM sqlite_master WHERE type='table';")) {
        tableNames.push(row.name);
    }

    await db.close();

    return tableNames;
}

// Function to generate SQL statements
function generateSQLStatements(tableNames: string[], sourceDatabases: string[]): string {
    let sql = '';

    for (const tableName of tableNames) {
        for (const database of sourceDatabases) {
            const statement = `INSERT OR IGNORE INTO ${tableName} SELECT * FROM ${database}.${tableName};\n`;
            sql += statement;
        }
    }

    return sql;
}

// Example usage
const sourceDatabases = [
    'resource_surveillance_dclp1',
    'resource_surveillance_dclp3',
    'resource_surveillance_dss1',
    'resource_surveillance_ntlt'
];

// Fetch all table names from each database
const allTableNames: string[] = [];

for (const database of sourceDatabases) {
    const databasePath = `/path/to/${database}.db`; // Replace with your database path
    const tableNames = await fetchTableNames(databasePath);
    allTableNames.push(...tableNames);
}

// Generate SQL statements
const sqlStatements = generateSQLStatements(allTableNames, sourceDatabases);

// Write SQL statements to a .sql file
const outputFilePath = 'insert_dml.sql';

// Check if the file exists, create it if it doesn't
if (!existsSync(outputFilePath)) {
    const file = await Deno.create(outputFilePath);
    file.close();
}

// Write SQL statements to the file
await Deno.writeTextFile(outputFilePath, sqlStatements);

console.log(`SQL statements written to ${outputFilePath}`);
