import { DB } from "https://deno.land/x/sqlite/mod.ts";

// Open the main database
const db = new DB("main_database.sqlite.db");

// Attach the other databases
db.execute("ATTACH DATABASE 'resource-surveillance-dclp1.sqlite.db' AS resource_surveillance_dclp1");
db.execute("ATTACH DATABASE 'resource-surveillance-dclp3.sqlite.db' AS resource_surveillance_dclp3");
db.execute("ATTACH DATABASE 'resource-surveillance-dss1.sqlite.db' AS resource_surveillance_dss1");
db.execute("ATTACH DATABASE 'resource-surveillance-ntlt.sqlite.db' AS resource_surveillance_ntlt");

// Get list of tables from one of the databases (assuming all have the same structure)
const tables = db.query("SELECT name FROM resource_surveillance_dclp1.sqlite_master WHERE type='table'");

// Generate and execute insert queries for each table
for (const [table_name] of tables) {
  const queries = [
    `INSERT OR IGNORE INTO ${table_name} SELECT * FROM resource_surveillance_dclp1.${table_name}`,
    `INSERT OR IGNORE INTO ${table_name} SELECT * FROM resource_surveillance_dclp3.${table_name}`,
    `INSERT OR IGNORE INTO ${table_name} SELECT * FROM resource_surveillance_dss1.${table_name}`,
    `INSERT OR IGNORE INTO ${table_name} SELECT * FROM resource_surveillance_ntlt.${table_name}`
  ];
  
  for (const query of queries) {
    db.execute(query);
  }
}

// Detach the databases
db.execute("DETACH DATABASE resource_surveillance_dclp1");
db.execute("DETACH DATABASE resource_surveillance_dclp3");
db.execute("DETACH DATABASE resource_surveillance_dss1");
db.execute("DETACH DATABASE resource_surveillance_ntlt");

// Close the database connection
db.close();
