import { DB } from "https://deno.land/x/sqlite/mod.ts";

// Custom function to read file content as a string
async function readFileAsString(filePath: string): Promise<string> {
  const decoder = new TextDecoder("utf-8");
  const data = await Deno.readFile(filePath);
  return decoder.decode(data);
}

// Function to execute views on SQLite databases
async function executeViews(databases: string[], sqlFilePath: string) {
  try {
    // Read the .sql file containing the view definitions
    const sqlContent = await readFileAsString(sqlFilePath);

    for (const dbPath of databases) {
      // Open the database
      const db = new DB(dbPath);

      try {
        // Execute the SQL content (view definitions)
        db.execute(sqlContent);
        console.log(`Views executed successfully for database: ${dbPath}`);
      } catch (error) {
        console.error(`Error executing views for database ${dbPath}:`, error);
      } finally {
        // Close the database
        db.close();
      }
    }
  } catch (error) {
    console.error('Error reading SQL file:', error);
  }
}

// Example usage
const databases = ['DRH.dclp1.sqlite.db', 'DRH.dclp3.sqlite.db', 'DRH.dss1.sqlite.db', 'DRH.ntlt.sqlite.db'];
const sqlFilePath = './drh-individual-study-views.sql'; // Path to your SQL file

executeViews(databases, sqlFilePath).then(() => {
  console.log('Finished executing views for all databases.');
}).catch(error => {
  console.error('Error executing views:', error);
});
