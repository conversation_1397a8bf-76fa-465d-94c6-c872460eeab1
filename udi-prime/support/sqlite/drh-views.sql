ATTACH DATABASE 'DRH.v5.sqlite.db' AS "DRH";

DROP VIEW IF EXISTS DRH.study_vanity_metrics_and_details_view;
CREATE VIEW DRH.study_vanity_metrics_and_details_view AS
WITH study_summary AS (
    SELECT 
        s.study_id,
        s.study_name,
        s.start_date,
        s.end_date,
        s.nct_number,
        s.study_description,
        COUNT(DISTINCT p.participant_id) AS total_number_of_participants,
        ROUND(AVG(p.age), 2) AS average_age,
        FLOOR((CAST(SUM(CASE WHEN p.gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)) * 100) AS percentage_of_females,
        GROUP_CONCAT(i.investigator_name) AS investigators
    FROM 
        uniform_resource_study s
    LEFT JOIN 
        uniform_resource_participant p ON s.study_id = p.study_id
    LEFT JOIN 
        uniform_resource_investigator i ON s.study_id = i.study_id
    GROUP BY 
        s.study_id, s.study_name, s.start_date, s.end_date, s.nct_number, s.study_description
)
SELECT *
FROM study_summary;



----------------------------------------------------------------------------------------------*/
   
DROP VIEW IF EXISTS DRH.all_studies_vanity_metrics; 
CREATE VIEW DRH.all_studies_vanity_metrics AS
SELECT
    COUNT(DISTINCT participant_id) AS total_number_of_participants,
    FLOOR((CAST(SUM(CASE WHEN gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)) * 100) AS percent_female,
    FLOOR(AVG(age)) AS average_age
FROM uniform_resource_participant;

-----------------------------------------------------------------------------------------------------------

DROP VIEW IF EXISTS DRH.total_cgm_wear_all_studies_view;  
CREATE VIEW DRH.total_cgm_wear_all_studies_view AS
WITH agg_cgm_wear AS (
    SELECT        
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM uniform_resource_cgm_tracing_dclp1
    UNION ALL
    SELECT        
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM uniform_resource_cgm_tracing_dclp3
    UNION ALL
    SELECT        
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM uniform_resource_cgm_tracing_dss1
    UNION ALL
    SELECT        
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM uniform_resource_cgm_tracing_ntlt
)
SELECT
    SUM(cgm_wear) AS total_cgm_wear
FROM agg_cgm_wear;

---------------------------------------------------------------------------------

DROP VIEW IF EXISTS DRH.total_data_points_all_studies_view;  
CREATE VIEW DRH.total_data_points_all_studies_view AS
WITH agg_data_points AS (
    SELECT        
        COUNT(*) AS data_points
    FROM uniform_resource_cgm_tracing_dclp1
    UNION ALL
    SELECT        
        COUNT(*) AS data_points
    FROM uniform_resource_cgm_tracing_dclp3
    UNION ALL
    SELECT        
       COUNT(*) AS data_points
    FROM uniform_resource_cgm_tracing_dss1
    UNION ALL
    SELECT        
       COUNT(*) AS data_points
    FROM uniform_resource_cgm_tracing_ntlt
)
SELECT
    SUM(data_points) AS total_data_points
FROM agg_data_points;



/*---------------------------------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS DRH.combined_cgm_tracing;
CREATE VIEW DRH.combined_cgm_tracing AS
select SID,Date_Time ,CGM_Value from uniform_resource_cgm_tracing_dclp1  
union all
select SID,Date_Time ,CGM_Value from uniform_resource_cgm_tracing_dclp3  
union all
select SID,Date_Time ,CGM_Value from uniform_resource_cgm_tracing_dss1
union all
select SID,Date_Time ,CGM_Value from uniform_resource_cgm_tracing_ntlt;


/*--------------------------------------------------------------------*/

DROP VIEW IF EXISTS DRH.study_combined_dashboard_participant_metrics_view;
CREATE VIEW DRH.study_combined_dashboard_participant_metrics_view AS
WITH combined_data AS (
    SELECT 
        CAST(SUBSTR(dg.participant_id, 1, INSTR(dg.participant_id, '-') - 1) AS TEXT) AS study_id,        
        dg.participant_id,
        dg.gender,
        dg.age,
        dg.study_arm,
        dg.baseline_hba1c,
        ROUND(SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 1.0 / COUNT(dc.CGM_Value) * 100, 2) AS tir,
        ROUND(SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) * 1.0 / COUNT(dc.CGM_Value) * 100, 2) AS tar_vh,
        ROUND(SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 1.0 / COUNT(dc.CGM_Value) * 100, 2) AS tar_h,
        ROUND(SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 1.0 / COUNT(dc.CGM_Value) * 100, 2) AS tbr_l,
        ROUND(SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) < 54 THEN 1 ELSE 0 END) * 1.0 / COUNT(dc.CGM_Value) * 100, 2) AS tbr_vl,
        CEIL((AVG(CAST(dc.CGM_Value AS REAL)) * 0.155) + 95) AS gmi,
        ROUND((SQRT(AVG(CAST(dc.CGM_Value AS REAL) * CAST(dc.CGM_Value AS REAL)) - AVG(CAST(dc.CGM_Value AS REAL)) * AVG(CAST(dc.CGM_Value AS REAL))) / AVG(CAST(dc.CGM_Value AS REAL))) * 100, 2) AS percent_gv,
        ROUND((3.0 * ((SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.8 * (SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))) + (1.6 * ((SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.5 * (SUM(CASE WHEN CAST(dc.CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) ))), 2) AS gri,
        COUNT(DISTINCT DATE(dc.Date_Time)) AS days_of_wear,
        MIN(DATE(dc.Date_Time)) AS data_start_date,
        MAX(DATE(dc.Date_Time)) AS data_end_date
    FROM uniform_resource_participant dg 
    JOIN combined_cgm_tracing dc ON dg.participant_id = dc.SID
    GROUP BY study_id, dg.participant_id, dg.gender, dg.age, dg.study_arm, dg.baseline_hba1c
)
SELECT *,
    ROUND(
        COALESCE(
            (days_of_wear * 1.0 / 
            (JULIANDAY(data_end_date) - JULIANDAY(data_start_date) + 1)) * 100, 
            0), 
        2) AS wear_time_percentage FROM combined_data;

/*-------------------------------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS DRH.glycemic_variability_view;
CREATE VIEW DRH.glycemic_variability_view AS 
  WITH CGMValues AS (
    SELECT 
        "SID" AS participant_id,
        CAST(CGM_Value AS REAL) AS CGM_Value,
        Date_Time,
        MAX(Date_Time) OVER (PARTITION BY "SID") AS participant_end_date,
        julianday(MAX(Date_Time) OVER (PARTITION BY "SID")) - julianday(Date_Time) AS days_from_end
    FROM 
        combined_cgm_tracing 
)

SELECT 
    participant_id,
    (SQRT(AVG(CGM_Value * CGM_Value) - AVG(CGM_Value) * AVG(CGM_Value)) / AVG(CGM_Value)) * 100 AS coefficient_of_variation_all_time,
    (SQRT(AVG(CASE WHEN days_from_end <= 1 THEN CGM_Value * CGM_Value ELSE NULL END) - AVG(CASE WHEN days_from_end <= 1 THEN CGM_Value ELSE NULL END) * AVG(CASE WHEN days_from_end <= 1 THEN CGM_Value ELSE NULL END)) / AVG(CASE WHEN days_from_end <= 1 THEN CGM_Value ELSE NULL END)) * 100 AS coefficient_of_variation_last_1_day,
    (SQRT(AVG(CASE WHEN days_from_end <= 7 THEN CGM_Value * CGM_Value ELSE NULL END) - AVG(CASE WHEN days_from_end <= 7 THEN CGM_Value ELSE NULL END) * AVG(CASE WHEN days_from_end <= 7 THEN CGM_Value ELSE NULL END)) / AVG(CASE WHEN days_from_end <= 7 THEN CGM_Value ELSE NULL END)) * 100 AS coefficient_of_variation_last_7_days,
    (SQRT(AVG(CASE WHEN days_from_end <= 14 THEN CGM_Value * CGM_Value ELSE NULL END) - AVG(CASE WHEN days_from_end <= 14 THEN CGM_Value ELSE NULL END) * AVG(CASE WHEN days_from_end <= 14 THEN CGM_Value ELSE NULL END)) / AVG(CASE WHEN days_from_end <= 14 THEN CGM_Value ELSE NULL END)) * 100 AS coefficient_of_variation_last_14_days,
    (SQRT(AVG(CASE WHEN days_from_end <= 30 THEN CGM_Value * CGM_Value ELSE NULL END) - AVG(CASE WHEN days_from_end <= 30 THEN CGM_Value ELSE NULL END) * AVG(CASE WHEN days_from_end <= 30 THEN CGM_Value ELSE NULL END)) / AVG(CASE WHEN days_from_end <= 30 THEN CGM_Value ELSE NULL END)) * 100 AS coefficient_of_variation_last_30_days,
    (SQRT(AVG(CASE WHEN days_from_end <= 90 THEN CGM_Value * CGM_Value ELSE NULL END) - AVG(CASE WHEN days_from_end <= 90 THEN CGM_Value ELSE NULL END) * AVG(CASE WHEN days_from_end <= 90 THEN CGM_Value ELSE NULL END)) / AVG(CASE WHEN days_from_end <= 90 THEN CGM_Value ELSE NULL END)) * 100 AS coefficient_of_variation_last_90_days
FROM 
    CGMValues
GROUP BY 
    participant_id;
   
--------------------------------------------------------------------------------------------

DROP VIEW IF EXISTS DRH.percentage_of_time_cgm_active;
CREATE VIEW percentage_of_time_cgm_active AS 
   WITH last_date AS (
    SELECT
        sid,
        MAX(date_time) AS last_date,
        MIN(date_time) AS first_date
    FROM
        combined_cgm_tracing
    GROUP BY
        sid
),
date_ranges AS (
    SELECT
        u.sid,
        u.date_time,
        ld.last_date,
        ld.first_date,
        julianday(ld.last_date) - julianday(u.date_time) AS days_ago
    FROM
        combined_cgm_tracing u
    JOIN
        last_date ld ON u.sid = ld.sid
)
SELECT
    sid AS participant_id,
    --DATE(first_date) AS original_start_date,
    --DATE(last_date) AS original_end_date,
    ROUND(
        (COUNT(DISTINCT DATE(date_time)) / 
        (julianday(last_date) - julianday(first_date) + 1)) * 100, 2
    ) AS percentage_active_alltime,    
    ROUND(
        (COUNT(DISTINCT CASE WHEN days_ago <= 1 THEN DATE(date_time) ELSE NULL END) / 1.0) * 100, 2
    ) AS percentage_active_1_day,
        ROUND(
        (COUNT(DISTINCT CASE WHEN days_ago <= 7 THEN DATE(date_time) ELSE NULL END) / 7.0) * 100, 2
    ) AS percentage_active_7_days,
    ROUND(
        (COUNT(DISTINCT CASE WHEN days_ago <= 14 THEN DATE(date_time) ELSE NULL END) / 14.0) * 100, 2
    ) AS percentage_active_14_days,
    ROUND(
        (COUNT(DISTINCT CASE WHEN days_ago <= 30 THEN DATE(date_time) ELSE NULL END) / 30.0) * 100, 2
    ) AS percentage_active_30_days,    
    ROUND(
        (COUNT(DISTINCT CASE WHEN days_ago <= 90 THEN DATE(date_time) ELSE NULL END) / 90.0) * 100, 2
    ) AS percentage_active_90_days      
    
FROM
    date_ranges
GROUP BY
    sid, last_date, first_date;

------------------------------------------------------------------------------------------- 
DROP VIEW IF EXISTS DRH.participant_cgm_date_range_view;
CREATE VIEW DRH.participant_cgm_date_range_view AS 
SELECT 
    "SID" AS participant_id,
    CAST(strftime('%d-%m-%Y', MIN(Date_Time)) AS TEXT) AS participant_cgm_start_date,
    CAST(strftime('%d-%m-%Y', MAX(Date_Time)) AS TEXT) AS participant_cgm_end_date,
    CAST(strftime('%d-%m-%Y', DATE(MAX(Date_Time), '-1 day')) AS TEXT) AS end_date_minus_1_day,
    CAST(strftime('%d-%m-%Y', DATE(MAX(Date_Time), '-7 day')) AS TEXT) AS end_date_minus_7_days,
    CAST(strftime('%d-%m-%Y', DATE(MAX(Date_Time), '-14 day')) AS TEXT) AS end_date_minus_14_days,
    CAST(strftime('%d-%m-%Y', DATE(MAX(Date_Time), '-30 day')) AS TEXT) AS end_date_minus_30_days,
    CAST(strftime('%d-%m-%Y', DATE(MAX(Date_Time), '-90 day')) AS TEXT) AS end_date_minus_90_days
FROM 
    combined_cgm_tracing  
GROUP BY 
    "SID";

------------------------------------------------------------------------------------
DROP VIEW IF EXISTS DRH.glucose_risk_indicator_alltime_view;
CREATE VIEW glucose_risk_indicator_alltime_view as
 SELECT 
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(cgm_value AS REAL) > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_above_vh_percentage,    
    (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_above_h_percentage,         
    (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_in_range_percentage,  
    (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_below_low_percentage,         
    (SUM(CASE WHEN CAST(cgm_value AS REAL) < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS time_below_vl_percentage,        
    (SUM(CASE WHEN CAST(cgm_value AS REAL) < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.8 * (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))) AS hypoglycemia_component,
    (SUM(CASE WHEN CAST(cgm_value AS REAL) > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + (0.5 * (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))) AS hyperglycemia_component,
    (3.0 * ((SUM(CASE WHEN CAST(cgm_value AS REAL) < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) +  (0.8 * (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))) + (1.6 * ((SUM(CASE WHEN CAST(cgm_value AS REAL) > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) +  (0.5 * (SUM(CASE WHEN CAST(cgm_value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))) AS gri
FROM 
    combined_cgm_tracing cct  
GROUP BY 
    "SID";


----------------------------------------------------------------------------------
   
   
DROP VIEW  IF EXISTS DRH.mean_glucose_view;
CREATE VIEW mean_glucose_view AS   
   WITH CGMValues AS (
    SELECT 
        "SID" AS participant_id,
        CAST(CGM_Value AS REAL) AS glucose_value,
        Date_Time,
        MAX(Date_Time) OVER (PARTITION BY "SID") AS participant_end_date
    FROM 
        combined_cgm_tracing 
),

TimeRanges AS (
    SELECT 
        participant_id,
        glucose_value,
        Date_Time,
        participant_end_date,
        julianday(participant_end_date) - julianday(Date_Time) AS days_from_end
    FROM 
        CGMValues
)

SELECT 
    participant_id,
    AVG(glucose_value) AS mean_glucose_all_time,
    AVG(CASE WHEN days_from_end <= 1 THEN glucose_value END) AS mean_glucose_last_1_day,
    AVG(CASE WHEN days_from_end <= 7 THEN glucose_value END) AS mean_glucose_last_7_days,
    AVG(CASE WHEN days_from_end <= 14 THEN glucose_value END) AS mean_glucose_last_14_days,
    AVG(CASE WHEN days_from_end <= 30 THEN glucose_value END) AS mean_glucose_last_30_days,
    AVG(CASE WHEN days_from_end <= 90 THEN glucose_value END) AS mean_glucose_last_90_days
FROM 
    TimeRanges
GROUP BY 
    participant_id;

-----------------------------------------------------------------------------------------------------
DROP VIEW IF EXISTS DRH.agp_all_data_view ;
CREATE VIEW DRH.agp_all_data_view AS
WITH date_range AS (
    SELECT 
        date(min(date_time)) AS start_date,
        date(max(date_time)) AS end_date
    FROM combined_cgm_tracing 
),

glucose_data AS (
    SELECT
        gr.SID AS participant_id,
        gr.Date_Time AS timestamp,
        strftime('%Y-%m-%d', gr.Date_Time) AS dayValue,
        gr.CGM_Value AS glucose_level
    FROM
        combined_cgm_tracing gr,
        date_range dr
    WHERE
        date(gr.Date_Time) >= dr.start_date
        AND date(gr.Date_Time) <= dr.end_date
),

hourly_data AS (
    SELECT
        participant_id,
        strftime('%H', timestamp) AS hour,
        glucose_level
    FROM
        glucose_data
),

ranked_data AS (
    SELECT
        participant_id,
        hour,
        glucose_level,
        ROW_NUMBER() OVER (PARTITION BY participant_id, hour ORDER BY glucose_level) AS row_num,
        COUNT(*) OVER (PARTITION BY participant_id, hour) AS total_count
    FROM
        hourly_data
),

percentiles AS (
    SELECT
        participant_id,
        hour,
        MAX(CASE WHEN row_num = CAST(0.05 * total_count AS INT) THEN glucose_level END) AS p5,
        MAX(CASE WHEN row_num = CAST(0.25 * total_count AS INT) THEN glucose_level END) AS p25,
        MAX(CASE WHEN row_num = CAST(0.50 * total_count AS INT) THEN glucose_level END) AS p50,
        MAX(CASE WHEN row_num = CAST(0.75 * total_count AS INT) THEN glucose_level END) AS p75,
        MAX(CASE WHEN row_num = CAST(0.95 * total_count AS INT) THEN glucose_level END) AS p95
    FROM
        ranked_data
    GROUP BY
        participant_id,
        hour
),

final_percentiles AS (
    SELECT
        participant_id,
        hour,
        AVG(p5) AS avg_p5,
        AVG(p25) AS avg_p25,
        AVG(p50) AS avg_p50,
        AVG(p75) AS avg_p75,
        AVG(p95) AS avg_p95
    FROM
        percentiles
    GROUP BY
        participant_id,
        hour
)

SELECT
    f.hour,
    f.participant_id,
    f.avg_p5 AS p5,
    f.avg_p25 AS p25,
    f.avg_p50 AS p50,
    f.avg_p75 AS p75,
    f.avg_p95 AS p95
FROM
    final_percentiles f
ORDER BY
    f.participant_id,
    f.hour;
----------------------------------------------------------------------------------------------------

DROP VIEW IF EXISTS DRH.participant_cgm_trace_file_details_view;
CREATE VIEW DRH.participant_cgm_trace_file_details_view AS
SELECT 
    c.patient_id as participant_id,
    file_name,
    study_id
FROM 
    uniform_resource_cgm_file_metadata c;

---------------------------------------------------------------------------------------------------

DROP VIEW IF EXISTS DRH.number_of_days_cgm_worn_alldata_view;
CREATE VIEW DRH.number_of_days_cgm_worn_alldata_view AS
SELECT
    SID AS participant,
    COUNT(DISTINCT DATE(Date_Time)) AS number_of_days_cgm_worn
FROM
    combined_cgm_tracing
GROUP BY
    participant;


------------------------------------------------------------------------------------------------------
DROP VIEW IF EXISTS DRH.time_in_range_stacked_alldata_view;
CREATE VIEW DRH.time_in_range_stacked_alldata_view
As SELECT
    urp.participant_id,
    td."type",
    td."border_line",
    td."percentage",
    td."time",
    td."time_range_string",
    td."cgm_value"
FROM
    uniform_resource_participant urp
LEFT JOIN (
    SELECT
        participant_id,
        'vhigh' AS "type",
        250 AS "border_line",
        percentage,
        time_above_VH AS "time",
        time_range_string,
        CGM_Value
    FROM
        timeaboverange_veryhigh
    UNION ALL
    SELECT
        participant_id,
        'high' AS "type",
        180 AS "border_line",
        percentage,
        time_above_range_high AS "time",
        time_range_string,
        CGM_Value
    FROM
        time_above_range_high
    UNION ALL
    SELECT
        participant_id,
        'nr' AS "type",
        70 AS "border_line",
        percentage,
        time_in_range AS "time",
        time_range_string,
        CGM_Value
    FROM
        time_in_range
    UNION ALL
    SELECT
        participant_id,
        'low' AS "type",
        54 AS "border_line",
        percentage,
        time_below_range_low AS "time",
        time_range_string,
        CGM_Value
    FROM
        time_below_range_low
    UNION ALL
    SELECT
        participant_id,
        'vlow' AS "type",
        0 AS "border_line",
        percentage,
        time_below_range_very_low AS "time",
        time_range_string,
        CGM_Value
    FROM
        time_below_range_very_low
) AS td ON td.participant_id = urp.participant_id;

----------------------------------------------------------------------------
DROP VIEW IF EXISTS DRH.liability_index_glycemic_episodes;
CREATE VIEW DRH.liability_index_glycemic_episodes AS
WITH max_dates AS (
    SELECT
        SID AS participant_id,
        MAX(DATE(Date_time)) AS end_date
    FROM
        combined_cgm_tracing
    GROUP BY
        SID
),
filtered_data AS (
    SELECT
        c.SID AS participant_id,
        CAST(c.CGM_Value AS REAL) AS CGM_Value,
        DATE(c.Date_time) AS CGM_DateTime,
        m.end_date,
        CASE
            WHEN DATE(c.Date_time) >= DATE(m.end_date, '-1 day') THEN 'past_1_day'
            WHEN DATE(c.Date_time) >= DATE(m.end_date, '-7 days') THEN 'past_7_days'
            WHEN DATE(c.Date_time) >= DATE(m.end_date, '-14 days') THEN 'past_14_days'
            WHEN DATE(c.Date_time) >= DATE(m.end_date, '-30 days') THEN 'past_30_days'
            WHEN DATE(c.Date_time) >= DATE(m.end_date, '-90 days') THEN 'past_90_days'
            ELSE 'all_data'
        END AS time_period
    FROM
        combined_cgm_tracing c
    JOIN
        max_dates m
    ON
        c.SID = m.participant_id
)
SELECT
    participant_id,
    time_period,
    SUM(CASE WHEN CGM_Value < 70 THEN 1 ELSE 0 END) AS hypoglycemic_episodes,
    SUM(CASE WHEN CGM_Value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS euglycemic_episodes,
    SUM(CASE WHEN CGM_Value > 180 THEN 1 ELSE 0 END) AS hyperglycemic_episodes,
    CAST((SUM(CASE WHEN CGM_Value < 70 THEN 1 ELSE 0 END) + SUM(CASE WHEN CGM_Value > 180 THEN 1 ELSE 0 END)) AS REAL) / COUNT(*) AS liability_index
FROM
    filtered_data
GROUP BY
    participant_id, time_period
ORDER BY
    participant_id, time_period;

-------------------------------------------------------------
Drop VIEW IF EXISTS DRH.m_value_all_data_view;
CREATE VIEW DRH.m_value_all_data_view AS
   WITH PatientMinMax AS (
    SELECT 
        SID,
        MIN(CAST(CGM_Value AS REAL)) AS min_glucose,
        MAX(CAST(CGM_Value AS REAL)) AS max_glucose,
        MIN(datetime(Date_Time)) AS start_time,
        MAX(datetime(Date_Time)) AS end_time
    FROM 
        combined_cgm_tracing
    GROUP BY 
        SID 
)
SELECT 
    SID as participant_id,
    (max_glucose - min_glucose) / ((strftime('%s', end_time) - strftime('%s', start_time)) / 60.0) AS m_value
FROM 
    PatientMinMax;

--------------------------------------------------------------------

Drop VIEW IF EXISTS DRH.glycaemic_risk_assessment_diabetes_equation_all_data_view;
CREATE  View DRH.glycaemic_risk_assessment_diabetes_equation_all_data_view As
  WITH risk_scores AS (
    SELECT 
        SID,
        CAST(CGM_Value AS REAL) AS CGM_Value,
        CASE
            WHEN CAST(CGM_Value AS REAL) < 90 THEN 10 * (5 - (CAST(CGM_Value AS REAL) / 18.0)) * (5 - (CAST(CGM_Value AS REAL) / 18.0))
            WHEN CAST(CGM_Value AS REAL) > 180 THEN 10 * ((CAST(CGM_Value AS REAL) / 18.0) - 10) * ((CAST(CGM_Value AS REAL) / 18.0) - 10)
            ELSE 0
        END AS risk_score
    FROM 
        combined_cgm_tracing
),
average_risk AS (
    SELECT 
        SID,
        AVG(risk_score) AS avg_risk_score
    FROM 
        risk_scores
    GROUP BY 
        SID
)
SELECT 
    SID as participant_id,
    avg_risk_score AS glycaemic_risk_assessment_diabetes_equation 
FROM 
    average_risk;

--------------------------------------------------------------

Drop VIEW IF EXISTS DRH.time_above_range_high;
CREATE VIEW DRH.time_above_range_high AS 
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,
    SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) AS time_above_range_high,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing    
GROUP BY
    "SID";

--------------------------------------
DROP VIEW IF EXISTS DRH.time_below_range_low;
CREATE VIEW DRH.time_below_range_low AS 
-- Time below range, low (TBR-L): % of readings and time 54–69 mg/dL (3.0–3.8 mmol/L) 
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,
    SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) AS time_below_range_low,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing
GROUP BY "SID";


---------------------------------------
DROP VIEW IF EXISTS DRH.time_below_range_very_low;
CREATE VIEW DRH.time_below_range_very_low AS 
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL)  < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,
    SUM(CASE WHEN CAST(CGM_Value AS REAL)  < 54 THEN 1 ELSE 0 END) AS time_below_range_very_low,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL)  < 54  THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL)  < 54  THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing
GROUP BY
    "SID";


--------------------------

DROP VIEW IF EXISTS DRH.time_in_range;
CREATE VIEW DRH.time_in_range AS 
-- Time in range (TIR): % of readings and time 70–180 mg/dL (3.9–10.0 mmol/L)
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,
    SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS time_in_range,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing
GROUP BY
    "SID";
-----------------------------------------

DROP VIEW IF EXISTS DRH.time_in_tight_range;
CREATE VIEW DRH.time_in_tight_range AS 
-- Time above range, very high (TAR-VH): % of readings and time >250 mg/dL (>13.9 mmol/L)
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,    
    SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 140 THEN 1 ELSE 0 END) AS time_in_tight_range,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) BETWEEN 70 AND 140 THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing
GROUP BY
    "SID";


-------------------------------

DROP VIEW IF EXISTS DRH.timeaboverange_veryhigh;
CREATE VIEW DRH.timeaboverange_veryhigh AS 
-- Time above range, very high (TAR-VH): % of readings and time >250 mg/dL (>13.9 mmol/L)
SELECT
    "SID" AS participant_id,
    (SUM(CASE WHEN CAST(CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS percentage,
    SUM(CASE WHEN CAST(CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) AS time_above_vh,
    printf('%02d hours, %02d minutes', 
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) * 5) / 60,
        (SUM(CASE WHEN CAST(CGM_Value AS REAL) > 250 THEN 1 ELSE 0 END) * 5) % 60
    ) AS time_range_string,
    CGM_Value
FROM
    combined_cgm_tracing
GROUP BY
    "SID";


-------------------------------------------------------

DROP VIEW IF EXISTS glucose_management_indicator_view;
CREATE VIEW DRH.glucose_management_indicator_view as
WITH cgm_values AS (
    SELECT 
        sid AS participant_id,
        CAST(cgm_value AS REAL) AS cgm_value,
        DATE(date_time) AS date_time,
        MAX(DATE(date_time)) OVER (PARTITION BY sid) AS participant_end_date
    FROM 
        combined_cgm_tracing  
),

gmi_calculations AS (
    SELECT 
        participant_id,
        cgm_value,
        date_time,
        participant_end_date,
        julianday(participant_end_date) - julianday(date_time) AS days_from_end
    FROM 
        cgm_values
)

SELECT 
    participant_id,
    (AVG(cgm_value) * 0.155 + 95) AS gmi_all_time,
    (AVG(CASE WHEN days_from_end <= 1 THEN cgm_value END) * 0.155 + 95) AS gmi_last_1_day,
    (AVG(CASE WHEN days_from_end <= 7 THEN cgm_value END) * 0.155 + 95) AS gmi_last_7_days,
    (AVG(CASE WHEN days_from_end <= 14 THEN cgm_value END) * 0.155 + 95) AS gmi_last_14_days,
    (AVG(CASE WHEN days_from_end <= 30 THEN cgm_value END) * 0.155 + 95) AS gmi_last_30_days,
    (AVG(CASE WHEN days_from_end <= 90 THEN cgm_value END) * 0.155 + 95) AS gmi_last_90_days
FROM 
    gmi_calculations
GROUP BY 
    participant_id;
