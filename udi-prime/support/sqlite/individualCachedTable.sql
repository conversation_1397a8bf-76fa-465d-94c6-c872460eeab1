DROP TABLE IF EXISTS participant_dashboard_cached;

CREATE TABLE participant_dashboard_cached AS
SELECT study_id, participant_id, gender, age, study_arm, baseline_hba1c, tir, tar_vh, tar_h, tbr_l, tbr_vl, tar,tbr, gmi, percent_gv, gri, days_of_wear, wear_time_percentage, data_start_date, data_end_date
FROM study_combined_dashboard_participant_metrics_view;

DROP TABLE IF EXISTS combined_cgm_tracing_cached;

CREATE TABLE combined_cgm_tracing_cached AS
SELECT participant_id, Date_Time, CGM_Value
FROM combined_cgm_tracing;

DROP TABLE IF EXISTS participant_cgm_date_range_cached;

CREATE TABLE participant_cgm_date_range_cached AS
SELECT participant_id, participant_cgm_start_date, participant_cgm_end_date, end_date_minus_1_day, end_date_minus_7_days, end_date_minus_14_days, end_date_minus_30_days, end_date_minus_90_days
FROM participant_cgm_date_range_view;

DROP TABLE IF EXISTS ur_ingest_session_file_issue_cached;

CREATE TABLE ur_ingest_session_file_issue_cached AS
SELECT device_id, ur_ingest_session_id, ur_ingest_session_fs_path_id, root_path, ur_ingest_session_fs_path_entry_id, file_path_abs, ur_status, ur_diagnostics
FROM ur_ingest_session_file_issue;

DROP TABLE IF EXISTS ur_ingest_session_files_stats_cached;

CREATE TABLE ur_ingest_session_files_stats_cached AS
SELECT device_id, ingest_session_id, ingest_session_started_at, ingest_session_finished_at, file_extension, ingest_session_fs_path_id, ingest_session_root_fs_path, total_file_count, file_count_with_content, file_count_with_frontmatter, min_file_size_bytes, average_file_size_bytes, max_file_size_bytes, oldest_file_last_modified_datetime, youngest_file_last_modified_datetime
FROM ur_ingest_session_files_stats;

DROP TABLE IF EXISTS ur_ingest_session_files_stats_latest_cached;

CREATE TABLE ur_ingest_session_files_stats_latest_cached AS
SELECT device_id, ingest_session_id, ingest_session_started_at, ingest_session_finished_at, file_extension, ingest_session_fs_path_id, ingest_session_root_fs_path, total_file_count, file_count_with_content, file_count_with_frontmatter, min_file_size_bytes, average_file_size_bytes, max_file_size_bytes, oldest_file_last_modified_datetime, youngest_file_last_modified_datetime
FROM ur_ingest_session_files_stats_latest;

DROP TABLE IF EXISTS ur_ingest_session_tasks_stats_cached;

CREATE TABLE ur_ingest_session_tasks_stats_cached AS
SELECT device_id, ingest_session_id, ingest_session_started_at, ingest_session_finished_at, ur_status, nature, total_file_count, file_count_with_content, file_count_with_frontmatter, min_file_size_bytes, average_file_size_bytes, max_file_size_bytes, oldest_file_last_modified_datetime, youngest_file_last_modified_datetime
FROM ur_ingest_session_tasks_stats;

DROP TABLE IF EXISTS ur_ingest_session_tasks_stats_latest_cached;

CREATE TABLE ur_ingest_session_tasks_stats_latest_cached AS
SELECT device_id, ingest_session_id, ingest_session_started_at, ingest_session_finished_at, ur_status, nature, total_file_count, file_count_with_content, file_count_with_frontmatter, min_file_size_bytes, average_file_size_bytes, max_file_size_bytes, oldest_file_last_modified_datetime, youngest_file_last_modified_datetime
FROM ur_ingest_session_tasks_stats_latest;

DROP TABLE IF EXISTS study_details_cached;

CREATE TABLE study_details_cached AS
SELECT s.study_id,        
s.study_name,        
s.study_description,        
s.start_date,        
s.end_date,        
s.nct_number,        
COUNT(DISTINCT p.participant_id) AS total_number_of_participants,        
ROUND(AVG(p.age), 2) AS average_age,        
FLOOR((CAST(SUM(CASE WHEN p.gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)) * 100) AS percentage_of_females,        
GROUP_CONCAT(DISTINCT i.investigator_name) AS investigators 
FROM uniform_resource_study s 
LEFT JOIN uniform_resource_participant p ON s.study_id = p.study_id 
LEFT JOIN uniform_resource_investigator i ON s.study_id = i.study_id 
GROUP BY s.study_id, s.study_name, s.study_description, s.start_date, s.end_date, s.nct_number ;

DROP TABLE IF EXISTS cgm_table_name_cached;

CREATE TABLE cgm_table_name_cached AS 
SELECT DISTINCT 
    pdc.study_id, 
    sm.tbl_name AS table_name, 
    REPLACE(sm.tbl_name, 'uniform_resource_', '') || '.' || ur.nature AS file_name
FROM 
    participant_dashboard_cached pdc
JOIN 
    sqlite_master sm
    ON sm.type = 'table'
    AND sm.name LIKE 'uniform_resource%'
    AND sm.name != 'uniform_resource_transform'
    AND sm.name != 'uniform_resource'
JOIN 
    uniform_resource ur 
    ON ur.uri LIKE '%' || REPLACE(sm.tbl_name, 'uniform_resource_', '') || '%';


DROP TABLE IF EXISTS study_cgm_file_count_cached;

CREATE TABLE study_cgm_file_count_cached AS 
SELECT count(*) AS total_count FROM sqlite_master WHERE type = 'table' AND name LIKE 'uniform_resource_cgm_tracing%';