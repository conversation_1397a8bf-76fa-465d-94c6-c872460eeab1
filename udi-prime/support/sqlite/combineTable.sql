ATTACH DATABASE 'DRH.dclp1.sqlite.db' AS "dclp1";

ATTACH DATABASE 'DRH.dclp3.sqlite.db' AS "dclp3";

ATTACH DATABASE 'DRH.dss1.sqlite.db' AS "dss1";

ATTACH DATABASE 'DRH.ntlt.sqlite.db' AS "ntlt";

ATTACH DATABASE 'DRH.dfa.sqlite.db' AS "dfa";

ATTACH DATABASE 'DRH.ctr3.sqlite.db' AS "ctr3";

ATTACH DATABASE 'DRH.ieogc.sqlite.db' AS "ieogc";

ATTACH DATABASE 'DRH.rtccgm.sqlite.db' AS "rtccgm";

ATTACH DATABASE 'DRH.wad1.sqlite.db' AS "wad1";

ATTACH DATABASE 'primary/combined_cached.sqlite.db' AS "combine";

DROP TABLE IF EXISTS combine.agg_cgm_wear_cached;

CREATE TABLE combine.agg_cgm_wear_cached AS WITH agg_cgm_wear AS (
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        dclp1.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        dss1.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        ntlt.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        dclp3.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        dfa.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        ctr3.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        ieogc.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        rtccgm.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear
    FROM
        wad1.combined_cgm_tracing
)
SELECT
    SUM(cgm_wear) AS total_cgm_wear
FROM
    agg_cgm_wear;

DROP TABLE IF EXISTS combine.agg_data_points_cached;

CREATE TABLE combine.agg_data_points_cached AS WITH agg_data_points AS (
    SELECT
        COUNT(*) AS data_points
    FROM
        dclp1.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        dss1.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        ntlt.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        dclp3.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        dfa.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        ctr3.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        ieogc.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        rtccgm.combined_cgm_tracing
    UNION
    ALL
    SELECT
        COUNT(*) AS data_points
    FROM
        wad1.combined_cgm_tracing
)
SELECT
    SUM(data_points) AS total_data_points
FROM
    agg_data_points;

DROP TABLE IF EXISTS combine.all_participants_cached;

CREATE TABLE combine.all_participants_cached AS WITH all_participants AS (
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        dclp1.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        dss1.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        ntlt.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        dclp3.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        dfa.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        ctr3.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        ieogc.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        rtccgm.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        site_id as site_id,
        diagnosis_icd as diagnosis_icd,
        med_rxnorm as med_rxnorm,
        treatment_modality as treatment_modality,
        gender as gender,
        race_ethnicity as race_ethnicity,
        age as age,
        bmi as bmi,
        baseline_hba1c as baseline_hba1c,
        diabetes_type as diabetes_type,
        study_arm as study_arm
    FROM
        wad1.drh_participant
)
SELECT
    COUNT(DISTINCT participant_id) AS total_number_of_participants,
    FLOOR(
        (
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ) / COUNT(*)
        ) * 100
    ) AS percent_female,
    FLOOR(AVG(age)) AS average_age
FROM
    all_participants;

DROP TABLE IF EXISTS combine.all_study_summary_cached;

CREATE TABLE combine.all_study_summary_cached AS WITH all_study_summary AS (
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        dclp1.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        dss1.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        ntlt.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        dclp3.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        dfa.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        ctr3.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        ieogc.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        rtccgm.uniform_resource_study
    UNION
    ALL
    SELECT
        study_id as study_id,
        study_name as study_name,
        start_date as start_date,
        end_date as end_date,
        treatment_modalities as treatment_modalities,
        funding_source as funding_source,
        nct_number as nct_number,
        study_description as study_description
    FROM
        wad1.uniform_resource_study
)
SELECT
    study_id as study_id,
    study_name as study_name,
    start_date as start_date,
    end_date as end_date,
    treatment_modalities as treatment_modalities,
    funding_source as funding_source,
    nct_number as nct_number,
    study_description as study_description
FROM
    all_study_summary;

DROP TABLE IF EXISTS combine.total_cgm_file_count_cached;

CREATE TABLE combine.total_cgm_file_count_cached AS WITH cgm_file_count AS (
    SELECT
        total_count
    FROM
        dclp1.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        dclp3.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        dss1.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        ntlt.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        dfa.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        ctr3.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        ieogc.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        rtccgm.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        total_count
    FROM
        wad1.study_cgm_file_count_cached
)
SELECT
    SUM(total_count) AS total_cgm_file_count
from
    cgm_file_count;

DROP TABLE IF EXISTS combine.total_cgm_file_name_cached;

CREATE TABLE combine.total_cgm_file_name_cached AS WITH cgm_file_name AS (
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        dclp1.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        dclp3.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        dss1.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        ntlt.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        dfa.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        ctr3.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        ieogc.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        rtccgm.raw_cgm_lst_cached
    UNION
    ALL
    SELECT
        tenant_id,
        study_id,
        table_name,
        raw_cgm_file_name as file_name
    FROM
        wad1.raw_cgm_lst_cached
)
SELECT
    tenant_id,
    study_id,
    table_name,
    file_name
from
    cgm_file_name;

DROP TABLE IF EXISTS combine.all_participants_cohort_cached;

CREATE TABLE combine.all_participants_cohort_cached AS WITH all_participants_cohort AS (
    SELECT
        DISTINCT study_id as study_id,
        COUNT(DISTINCT participant_id) AS total_number_of_participants,
        CAST(
            SUM(
                CASE
                    WHEN gender = 'F' THEN 1
                    ELSE 0
                END
            ) AS FLOAT
        ) as total_female,
        SUM(age) as total_age
    FROM
        dclp1.uniform_resource_participant
    UNION
    ALL
    SELECT
        DISTINCT study_id as study_id,
        COUNT(DISTINCT participant_id) AS total_number_of_participants,
        CAST(
            SUM(
                CASE
                    WHEN gender = 'F' THEN 1
                    ELSE 0
                END
            ) AS FLOAT
        ) as total_female,
        SUM(age) as total_age
    FROM
        dss1.uniform_resource_participant
    UNION
    ALL
    SELECT
        DISTINCT study_id as study_id,
        COUNT(DISTINCT participant_id) AS total_number_of_participants,
        CAST(
            SUM(
                CASE
                    WHEN gender = 'F' THEN 1
                    ELSE 0
                END
            ) AS FLOAT
        ) as total_female,
        SUM(age) as total_age
    FROM
        ntlt.uniform_resource_participant
    UNION
    ALL
    SELECT
        DISTINCT study_id as study_id,
        COUNT(DISTINCT participant_id) AS total_number_of_participants,
        CAST(
            SUM(
                CASE
                    WHEN gender = 'F' THEN 1
                    ELSE 0
                END
            ) AS FLOAT
        ) as total_female,
        SUM(age) as total_age
    FROM
        dclp3.uniform_resource_participant
)
SELECT
    study_id,
    total_number_of_participants,
    total_female,
    total_age
FROM
    all_participants_cohort;

DROP TABLE IF EXISTS combine.all_participant_dashboard_cached;

CREATE TABLE combine.all_participant_dashboard_cached AS WITH all_participant_dashboard_cohort AS (
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        dclp1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        dclp3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        dss1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        ntlt.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        dfa.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        ctr3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        ieogc.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        rtccgm.participant_dashboard_cached
    UNION
    ALL
    SELECT
        CAST(study_id AS TEXT) AS study_id,
        CAST(participant_id AS TEXT) AS participant_id,
        CAST(gender AS TEXT) AS gender,
        CAST(age AS INTEGER) AS age,
        CAST(study_arm AS TEXT) AS study_arm,
        CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
        CAST(tir AS FLOAT) AS tir,
        CAST(tar_vh AS FLOAT) AS tar_vh,
        CAST(tar_h AS FLOAT) AS tar_h,
        CAST(tbr_l AS FLOAT) AS tbr_l,
        CAST(tbr_vl AS FLOAT) AS tbr_vl,
        CAST(tar AS FLOAT) AS tar,
        CAST(tbr AS FLOAT) AS tbr,
        CAST(gmi AS FLOAT) AS gmi,
        CAST(percent_gv AS TFLOATXT) AS percent_gv,
        CAST(gri AS FLOAT) AS gri,
        CAST(days_of_wear AS FLOAT) AS days_of_wear,
        CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
        CAST(data_start_date AS TEXT) AS data_start_date,
        CAST(data_end_date AS TEXT) AS data_end_date
    FROM
        wad1.participant_dashboard_cached
)
SELECT
    CAST(study_id AS TEXT) AS study_id,
    CAST(participant_id AS TEXT) AS participant_id,
    CAST(gender AS TEXT) AS gender,
    CAST(age AS INTEGER) AS age,
    CAST(study_arm AS TEXT) AS study_arm,
    CAST(baseline_hba1c AS FLOAT) AS baseline_hba1c,
    CAST(tir AS FLOAT) AS tir,
    CAST(tar_vh AS FLOAT) AS tar_vh,
    CAST(tar_h AS FLOAT) AS tar_h,
    CAST(tbr_l AS FLOAT) AS tbr_l,
    CAST(tbr_vl AS FLOAT) AS tbr_vl,
    CAST(tar AS FLOAT) AS tar,
    CAST(tbr AS FLOAT) AS tbr,
    CAST(gmi AS FLOAT) AS gmi,
    CAST(percent_gv AS TFLOATXT) AS percent_gv,
    CAST(gri AS FLOAT) AS gri,
    CAST(days_of_wear AS FLOAT) AS days_of_wear,
    CAST(wear_time_percentage AS FLOAT) AS wear_time_percentage,
    CAST(data_start_date AS TEXT) AS data_start_date,
    CAST(data_end_date AS TEXT) AS data_end_date
FROM
    all_participant_dashboard_cohort;

DROP TABLE IF EXISTS combine.agg_avg_glucose_cached;

CREATE TABLE combine.agg_avg_glucose_cached AS WITH agg_avg_glucose AS (
    SELECT
        CAST(SUM(CGM_Value) AS FLOAT) AS cgm_value,
        CAST(COUNT(CGM_Value) AS FLOAT) AS cgm_count
    FROM
        dclp1.uniform_resource_cgm_tracing
    UNION
    ALL
    SELECT
        CAST(SUM(CGM_Value) AS FLOAT) AS cgm_value,
        CAST(COUNT(CGM_Value) AS FLOAT) AS cgm_count
    FROM
        dss1.uniform_resource_cgm_tracing
    UNION
    ALL
    SELECT
        CAST(SUM(CGM_Value) AS FLOAT) AS cgm_value,
        CAST(COUNT(CGM_Value) AS FLOAT) AS cgm_count
    FROM
        ntlt.uniform_resource_cgm_tracing
    UNION
    ALL
    SELECT
        CAST(SUM(cgm_Value) AS FLOAT) AS cgm_value,
        CAST(COUNT(cgm_Value) AS FLOAT) AS cgm_count
    FROM
        dclp3.uniform_resource_cgm_tracing
)
SELECT
    SUM(cgm_value) / SUM(cgm_count) AS avg_glucose
FROM
    agg_avg_glucose;

DROP TABLE IF EXISTS combine.total_study_cgm_file_count_cached;

CREATE TABLE combine.total_study_cgm_file_count_cached AS WITH study_cgm_file_count AS (
    SELECT
        'DCLP1' as study_id,
        total_count
    FROM
        dclp1.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'DCLP3' as study_id,
        total_count
    FROM
        dclp3.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'DSS1' as study_id,
        total_count
    FROM
        dss1.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'NTLT' as study_id,
        total_count
    FROM
        ntlt.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'DFA' as study_id,
        total_count
    FROM
        dfa.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'CTR3' as study_id,
        total_count
    FROM
        ctr3.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'IEOGC' as study_id,
        total_count
    FROM
        ieogc.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'RTCCGM' as study_id,
        total_count
    FROM
        rtccgm.study_cgm_file_count_cached
    UNION
    ALL
    SELECT
        'WAD1' as study_id,
        total_count
    FROM
        wad1.study_cgm_file_count_cached
)
SELECT
    study_id,
    total_count
from
    study_cgm_file_count;

DROP TABLE IF EXISTS combine.all_cohort_participants_cached;

CREATE TABLE combine.all_cohort_participants_cached AS WITH all_cohort_participants AS (
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        dclp1.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        dss1.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        ntlt.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        dclp3.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        dfa.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        ctr3.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        ieogc.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        rtccgm.drh_participant
    UNION
    ALL
    SELECT
        participant_id as participant_id,
        study_id as study_id,
        gender as gender,
        age as age
    FROM
        wad1.drh_participant
)
SELECT
    participant_id as participant_id,
    study_id as study_id,
    gender as gender,
    age as age
FROM
    all_cohort_participants;

DROP TABLE IF EXISTS combine.study_participants_metrics_cached;

CREATE TABLE combine.study_participants_metrics_cached AS WITH study_participants_metrics AS (
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        dclp1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        dss1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        ntlt.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        dclp3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        dfa.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        ctr3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        ieogc.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        rtccgm.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(
            FLOOR(
                (
                    CAST(
                        SUM(
                            CASE
                                WHEN gender = 'F' THEN 1
                                ELSE 0
                            END
                        ) AS FLOAT
                    ) /(COUNT(participant_id))
                ) * 100
            ),
            0
        ) AS percent_female,
        COALESCE(FLOOR(SUM(age) /(COUNT(participant_id))), 0) AS average_age
    FROM
        wad1.participant_dashboard_cached
)
SELECT
    total_number_of_participants,
    percent_female,
    average_age
FROM
    study_participants_metrics;

DROP TABLE IF EXISTS combine.all_participants_metrics_cached;

CREATE TABLE combine.all_participants_metrics_cached AS WITH all_participants_metrics AS (
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        dclp1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        dss1.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        ntlt.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        dclp3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        dfa.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        ctr3.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        ieogc.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        rtccgm.participant_dashboard_cached
    UNION
    ALL
    SELECT
        COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants,
        COALESCE(COUNT(participant_id), 0) AS total_count,
        COALESCE(
            CAST(
                SUM(
                    CASE
                        WHEN gender = 'F' THEN 1
                        ELSE 0
                    END
                ) AS FLOAT
            ),
            0
        ) AS total_female,
        COALESCE(SUM(age), 0) AS sum_of_age
    FROM
        wad1.participant_dashboard_cached wad1
)
SELECT
    COALESCE(SUM(total_number_of_participants), 0) AS total_number_of_participants,
    COALESCE(
        FLOOR(SUM(total_female) / SUM(total_count) * 100),
        0
    ) AS percent_female,
    COALESCE(FLOOR(SUM(sum_of_age) / SUM(total_count)), 0) AS average_age
FROM
    all_participants_metrics;