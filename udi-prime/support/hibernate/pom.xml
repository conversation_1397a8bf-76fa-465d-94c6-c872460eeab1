    <project xmlns="http://maven.apache.org/POM/4.0.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
        <modelVersion>4.0.0</modelVersion>
        <groupId>com.example</groupId>
        <artifactId>hibernate-reverse-engineer</artifactId>
        <version>1.0.0</version>
        <packaging>jar</packaging>

        <properties>
            <db.url>${env.DB_URL}</db.url>
            <db.username>${env.DB_USERNAME}</db.username>
            <db.password>${env.DB_PASSWORD}</db.password>
            <db.dialect>org.hibernate.dialect.SQLiteDialect</db.dialect>
            <db.driver>org.sqlite.JDBC</db.driver>

            <hibernate.version>6.5.2.Final</hibernate.version>
        </properties>

        <dependencies>
            <!-- Hibernate dependencies -->
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.7.3</version>
            </dependency>
            <!-- <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>3.46.0.0</version>
            </dependency> -->
            <!-- For annotations -->
            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>3.1.0</version>
            </dependency>
        </dependencies>

        <build>
            <plugins>
                <!-- Hibernate Tools plugin -->
                <plugin>
                    <groupId>org.hibernate.tool</groupId>
                    <artifactId>hibernate-tools-maven-plugin</artifactId>
                    <version>5.6.9.Final</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>hbm2java</goal>
                            </goals>
                            <configuration>
                                <revengstrategy>org.hibernate.cfg.reveng.DefaultReverseEngineeringStrategy</revengstrategy>
                                <jdbcconfiguration>
                                    <property name="hibernate.dialect">${db.dialect}</property>
                                    <property name="hibernate.connection.driver_class">${db.driver}</property>
                                    <property name="hibernate.connection.url">${db.url}</property>
                                    <property name="hibernate.connection.username">${db.username}</property>
                                    <property name="hibernate.connection.password">${db.password}</property>
                                </jdbcconfiguration>
                                <outputDirectory>${project.build.directory}/generated-sources/hibernate3</outputDirectory>
                                <packageName>com.example.generated</packageName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <!-- Compiler plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <generatedSourcesDirectory>${project.build.directory}/generated-sources/hibernate3</generatedSourcesDirectory>
                    </configuration>
                </plugin>

                <!-- Jar plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <mainClass>com.example.Main</mainClass>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </project>
