#!/usr/bin/env -S deno run --allow-read --allow-write --allow-env --allow-run
import { exists, existsSync } from "https://deno.land/std@0.188.0/fs/mod.ts";
import { join } from "https://deno.land/std@0.188.0/path/mod.ts";

// Read the environment variable
const location = Deno.env.get("sandbox_DRH_UDI_DS_PRIME_DB_BASE_PATH");
if (!location) {
  console.error("Environment variable sandbox_DRH_UDI_DS_PRIME_DB_BASE_PATH is not set.");
  Deno.exit(1);
}
console.log(`DB Location is ${location}`);

// Save the current directory
const originalDir = Deno.cwd();
const primaryPath = join(location, "primary/");
const auditPath = join(location, "audit/");
const clientServerDbPath = join(location, "ClientServer/");

// Function to ensure a directory exists
function ensureDirectoryExists(path: string) {
  if (!existsSync(path)) {
    Deno.mkdirSync(path, { recursive: true });
      console.log(`Created directory: ${path}`);
  } else {
      console.log(`Directory already exists: ${path}`);
  }
}

// Ensure all paths exist
ensureDirectoryExists(primaryPath);
ensureDirectoryExists(auditPath);
ensureDirectoryExists(clientServerDbPath);

try {
  // Change to the target directory
  Deno.chdir(primaryPath);
  console.log(`Change To Primary DB Location is ${primaryPath}`);
  // Check if the file exists and delete it
  const combinedCachedPath = "combined_cached.sqlite.db";
  if (await exists(combinedCachedPath)) {
    await Deno.remove(combinedCachedPath);
    console.log("File combined_cached.sqlite.db deleted.");
  } else {
    console.log("File combined_cached.sqlite.db does not exist.");
  }

  // Execute sqlite3 command
  const process = Deno.run({
    cmd: ["sqlite3", "combined_cached.db", ".output combined_cached.sqlite.db"],
    stdout: "piped",
  });
  await process.status();
  console.log("File combined_cached.sqlite.db created.");
  
  // Change back to the original directory
  Deno.chdir(location);
  console.log(`DB Location is ${location}`);
  // Delete SQL files if they exist
  const sqlFiles = ["combineTable.sql"];
  for (const file of sqlFiles) {
    if (await exists(file)) {
      await Deno.remove(file);
      console.log(`File ${file} deleted.`);
    } else {
      console.log(`File ${file} does not exist.`);
    }
  }

  // Change back to the original directory
  Deno.chdir(originalDir);
  console.log("Returned to the original directory.");

  // Copy SQL files to the target directory
  const sourceFiles = [
    "support/sqlite/combineTable.sql",
  ];

  for (const sourceFile of sourceFiles) {
    const destFile = join(location, sourceFile.split('/').pop()!);
    await Deno.copyFile(sourceFile, destFile);
    console.log(`File ${sourceFile.split('/').pop()} copied to ${location}.`);
  }

  // Execute SQL commands on all .sqlite.db files
  Deno.chdir(location);

  // Execute combineTable.sql and redirect output to response.txt
  const combineTablePath = join(location, "combineTable.sql");
  const responsePath = join(location, "response.txt");
  const combineTableContent = await Deno.readTextFile(combineTablePath);
  const combineProcess = Deno.run({
    cmd: ["sqlite3"],
    stdin: "piped",
    stdout: "piped",
  });
  await combineProcess.stdin.write(new TextEncoder().encode(combineTableContent));
  combineProcess.stdin.close();
  const output = await combineProcess.output();
  await Deno.writeFile(responsePath, output);
  console.log("Executed combineTable.sql and saved the output to response.txt.");

  Deno.chdir(originalDir);
  console.log("Returned to the original directory.");

  // Copy SQL files to the target directory
  const metricsSourceFiles = [
    "support/sqlite/metricsDefenition.sql",
  ];

  for (const sourceFile of metricsSourceFiles) {
    const destFile = join(primaryPath, sourceFile.split('/').pop()!);
    await Deno.copyFile(sourceFile, destFile);
    console.log(`File ${sourceFile.split('/').pop()} copied to ${primaryPath}.`);
  }
  for await (const entry of Deno.readDir(primaryPath)) {
    if (entry.isFile && entry.name.startsWith("combined_cached.sqlite.db")) {
      const dbFile = join(primaryPath, entry.name);
      console.log(`Executing SQL commands on ${dbFile}.`);

      const sqlFilesContent = [
        "metricsDefenition.sql"
      ];
Deno.chdir(primaryPath);
      for (const sqlFile of sqlFilesContent) {
        const sqlContent = await Deno.readTextFile(join(sqlFile));
        const process = Deno.run({
          cmd: ["sqlite3", dbFile],
          stdin: "piped",
        });
        await process.stdin.write(new TextEncoder().encode(sqlContent));
        process.stdin.close();
        await process.status();
        console.log(`Executed SQL commands from ${sqlFile} on ${dbFile}.`);
      }
    }
  }
  Deno.chdir(auditPath);
  console.log(`Change To Audit DB Location is ${auditPath}`);
  // Check if the file exists and delete it
  const auditDb = "audit.sqlite.db";
  if (!await exists(auditDb)) {
    console.log("Creating new Sqlite File audit.sqlite.db.");
    // Copy SQL files to the target directory
    Deno.chdir(originalDir);
  console.log("Returned to the original directory.");
  const auditSourceFiles = [
    "support/sqlite/auditTable.sql",
  ];

  for (const sourceFile of auditSourceFiles) {
    const destFile = join(auditPath, sourceFile.split('/').pop()!);
    await Deno.copyFile(sourceFile, destFile);
    console.log(`File ${sourceFile.split('/').pop()} copied to ${auditPath}.`);
  }
  Deno.chdir(auditPath);
  // Execute sqlite3 command
  const process = Deno.run({
    cmd: ["sqlite3", "audit.db", ".output audit.sqlite.db"],
    stdout: "piped",
  });
  await process.status();
  console.log("File audit.sqlite.db created.");
  for await (const entry of Deno.readDir(auditPath)) {
    if (entry.isFile && entry.name.startsWith("audit.sqlite.db")) {
      const dbFile = join(auditPath, entry.name);
      console.log(`Executing SQL commands on ${dbFile}.`);

      const sqlFilesContent = [
        "auditTable.sql"
      ];
      Deno.chdir(auditPath);
      for (const sqlFile of sqlFilesContent) {
        const sqlContent = await Deno.readTextFile(join(sqlFile));
        const process = Deno.run({
          cmd: ["sqlite3", dbFile],
          stdin: "piped",
        });
        await process.stdin.write(new TextEncoder().encode(sqlContent));
        process.stdin.close();
        await process.status();
        console.log(`Executed SQL commands from ${sqlFile} on ${dbFile}.`);
      }
    }
  }
    
  } else {
    console.log("File audit.sqlite.db exists.");
  }

  Deno.chdir(clientServerDbPath);
  console.log(`Change To Client Server DB Location is ${clientServerDbPath}`);
  // Check if the file exists and delete it
  const clientServerDb = "server_client_interaction_read_write.sqlite.db";
  if (!await exists(clientServerDb)) {
    console.log("Creating new Sqlite File server_client_interaction_read_write.sqlite.db.");
    // Copy SQL files to the target directory
    Deno.chdir(originalDir);
  console.log("Returned to the original directory.");
  const clientServerSourceFiles = [
    "support/sqlite/clientServer.sql",
  ];

  for (const sourceFile of clientServerSourceFiles) {
    const destFile = join(clientServerDbPath, sourceFile.split('/').pop()!);
    await Deno.copyFile(sourceFile, destFile);
    console.log(`File ${sourceFile.split('/').pop()} copied to ${clientServerDbPath}.`);
  }
  Deno.chdir(clientServerDbPath);
  // Execute sqlite3 command
  const process = Deno.run({
    cmd: ["sqlite3", "clientServer.db", ".output server_client_interaction_read_write.sqlite.db"],
    stdout: "piped",
  });
  await process.status();
  console.log("File server_client_interaction_read_write.sqlite.db created.");
  for await (const entry of Deno.readDir(clientServerDbPath)) {
    if (entry.isFile && entry.name.startsWith("server_client_interaction_read_write.sqlite.db")) {
      const dbFile = join(clientServerDbPath, entry.name);
      console.log(`Executing SQL commands on ${dbFile}.`);

      const sqlFilesContent = [
        "clientServer.sql"
      ];
      Deno.chdir(clientServerDbPath);
      for (const sqlFile of sqlFilesContent) {
        const sqlContent = await Deno.readTextFile(join(sqlFile));
        const process = Deno.run({
          cmd: ["sqlite3", dbFile],
          stdin: "piped",
        });
        await process.stdin.write(new TextEncoder().encode(sqlContent));
        process.stdin.close();
        await process.status();
        console.log(`Executed SQL commands from ${sqlFile} on ${dbFile}.`);
      }
    }
  }
    
  } else {
    console.log("File server_client_interaction_read_write.sqlite.db exists.");
  }



} catch (error) {
  console.error("An error occurred:", error.message);
  Deno.exit(1);
}

