name: APP Server Test & Site
on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
    
permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'corretto'
        cache: maven

    - name: Build the project artifacts with Maven Site (will test automatically)
      run: |
        cd hub-prime
        mvn site

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'corretto'
        cache: maven

    - name: Build the site
      run: |
        cd hub-prime
        mvn clean site site:stage

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v4
      with:
        github_token: ${{ secrets.ACTIONS_GITHUB_TOKEN }}
        publish_dir: hub-prime/target/site
        publish_branch: gh-pages
